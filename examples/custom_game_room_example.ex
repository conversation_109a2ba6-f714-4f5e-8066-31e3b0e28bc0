defmodule Examples.CustomGameRoomExample do
  @moduledoc """
  示例：展示如何使用新的可重载接口来自定义游戏房间行为

  这个示例展示了三种不同的使用方式：
  1. 完全自定义 handle_player_leave
  2. 部分自定义 + 默认流程
  3. 只自定义游戏逻辑（推荐）
  """

  # ==================== 示例1：完全自定义离开逻辑 ====================

  defmodule CompleteCustomRoom do
    @moduledoc """
    示例1：完全自定义玩家离开逻辑
    
    适用场景：需要完全控制玩家离开流程的游戏
    """
    
    use Cypridina.Teen.GameSystem.RoomBase, game_type: :custom_complete

    @impl true
    def handle_player_leave(state, user_id) do
      player = get_player_by_user_id(state, user_id)

      cond do
        player == nil ->
          {{:error, :not_in_room}, state}

        state.room_state == :settling ->
          # 结算中不允许离开
          {{:error, :cannot_leave_during_settlement}, state}

        is_current_player?(state, user_id) ->
          # 当前操作玩家离开需要特殊处理
          handle_current_player_leave(state, user_id)

        true ->
          # 使用默认流程
          default_handle_player_leave(state, user_id)
      end
    end

    # 辅助函数
    defp is_current_player?(state, user_id) do
      Map.get(state.game_data, :current_player) == user_id
    end

    defp handle_current_player_leave(state, user_id) do
      # 跳过当前玩家的回合
      new_state = skip_to_next_player(state)
      # 然后使用默认流程处理离开
      default_handle_player_leave(new_state, user_id)
    end

    defp skip_to_next_player(state) do
      # 游戏特定的跳过逻辑
      state
    end
  end

  # ==================== 示例2：部分自定义 + 默认流程 ====================

  defmodule PartialCustomRoom do
    @moduledoc """
    示例2：部分自定义 + 默认流程
    
    适用场景：只需要在特定条件下阻止离开的游戏
    """
    
    use Cypridina.Teen.GameSystem.RoomBase, game_type: :custom_partial

    @impl true
    def handle_player_leave(state, user_id) do
      # 一些预检查
      cond do
        state.room_state == :critical_phase ->
          {{:error, :cannot_leave_now}, state}

        in_important_game_moment?(state) ->
          {{:error, :please_wait_for_current_action}, state}

        true ->
          # 使用默认流程
          default_handle_player_leave(state, user_id)
      end
    end

    @impl true
    def handle_player_join(state, user_id, bring_data, from) do
      # 自定义加入条件检查
      if custom_join_condition?(state, user_id) do
        default_handle_player_join(state, user_id, bring_data, from)
      else
        GenServer.reply(from, {:error, :custom_rejection_reason})
        state
      end
    end

    # 辅助函数
    defp in_important_game_moment?(state) do
      # 检查是否在重要的游戏时刻
      Map.get(state.game_data, :phase) in [:dealing, :revealing]
    end

    defp custom_join_condition?(state, user_id) do
      # 自定义加入条件
      player_count = map_size(state.players)
      player_count < state.max_players
    end
  end

  # ==================== 示例3：只自定义游戏逻辑（推荐） ====================

  defmodule GameLogicOnlyRoom do
    @moduledoc """
    示例3：只自定义游戏逻辑
    
    适用场景：大多数游戏，只需要自定义游戏相关的清理逻辑
    推荐使用这种方式，因为它保持了最大的兼容性
    """
    
    use Cypridina.Teen.GameSystem.RoomBase, game_type: :custom_logic_only

    # 不重载 handle_player_join 和 handle_player_leave
    # 只重载游戏逻辑相关的回调

    @impl true
    def on_player_joined(state, player) do
      Logger.info("🎮 [CUSTOM] 玩家加入游戏: #{player.numeric_id}")

      state
      |> assign_player_to_seat(player)
      |> send_game_config_to_player(player)
      |> broadcast_player_enter_message(player)
      |> check_game_start_condition()
    end

    @impl true
    def on_player_left(state, player) do
      Logger.info("🎮 [CUSTOM] 玩家离开游戏: #{player.numeric_id}")

      # 处理游戏特定的清理逻辑
      game_cleaned_state = state
      |> handle_player_quit_in_game(player)
      |> clear_player_game_data(player)
      |> check_game_end_conditions()

      # 调用默认的玩家移除逻辑（广播消息、从玩家列表移除等）
      default_on_player_left(game_cleaned_state, player)
    end

    @impl true
    def handle_game_message(state, player, message) do
      case message do
        %{"subId" => 1001} ->
          handle_bet_request(state, player, message)

        %{"subId" => 1002} ->
          handle_fold_request(state, player, message)

        _ ->
          Logger.warning("🎮 [CUSTOM] 未知消息: #{inspect(message)}")
          state
      end
    end

    # 游戏特定的辅助函数
    defp assign_player_to_seat(state, player) do
      # 分配座位逻辑
      state
    end

    defp send_game_config_to_player(state, player) do
      # 发送游戏配置
      state
    end

    defp broadcast_player_enter_message(state, player) do
      # 广播玩家进入消息
      state
    end

    defp check_game_start_condition(state) do
      # 检查游戏开始条件
      state
    end

    defp handle_player_quit_in_game(state, player) do
      # 处理游戏中玩家退出
      if Map.get(state.game_data, :current_player) == player.numeric_id do
        skip_player_turn(state, player.numeric_id)
      else
        state
      end
    end

    defp clear_player_game_data(state, player) do
      # 清理玩家游戏数据
      game_data = %{state.game_data |
        player_bets: Map.delete(state.game_data.player_bets, player.numeric_id),
        player_cards: Map.delete(state.game_data.player_cards, player.numeric_id)
      }
      %{state | game_data: game_data}
    end

    defp check_game_end_conditions(state) do
      # 检查游戏结束条件
      active_players = count_active_players(state)
      if active_players < 2 do
        end_game(state)
      else
        state
      end
    end

    defp skip_player_turn(state, player_id) do
      # 跳过玩家回合
      state
    end

    defp count_active_players(state) do
      # 计算活跃玩家数量
      map_size(state.players)
    end

    defp end_game(state) do
      # 结束游戏
      %{state | room_state: :finished}
    end

    defp handle_bet_request(state, player, message) do
      # 处理下注请求
      state
    end

    defp handle_fold_request(state, player, message) do
      # 处理弃牌请求
      state
    end
  end
end
