# 游戏账户自动创建示例
#
# 这个示例展示了新的游戏账户功能：
# 1. 在 Ledger 模块中获取游戏账户（不存在时自动创建）
# 2. 在 BalanceCache 中获取游戏账户余额（自动创建账户）

alias Cypridina.Ledger
alias Cypridina.BalanceCache
alias Cypridina.Accounts.AccountIdentifier

# 示例 1: 使用 Ledger 模块获取游戏账户（自动创建）
IO.puts("=== 示例 1: Ledger 模块游戏账户管理 ===")

game_id = "test_game_#{System.unique_integer([:positive])}"
IO.puts("游戏ID: #{game_id}")

# 获取游戏账户（如果不存在会自动创建）
try do
  game_account = Ledger.get_game_account(game_id)
  IO.puts("✅ 游戏账户获取成功:")
  IO.puts("  账户ID: #{game_account.id}")
  IO.puts("  标识符: #{game_account.identifier}")
  IO.puts("  货币: #{game_account.currency}")
  IO.puts("  描述: #{game_account.description}")
rescue
  error ->
    IO.puts("❌ 游戏账户获取失败: #{inspect(error)}")
end

IO.puts("")

# 示例 2: 获取游戏账户余额
IO.puts("=== 示例 2: 获取游戏账户余额 ===")

try do
  balance = Ledger.get_game_balance(game_id)
  IO.puts("✅ 游戏账户余额: #{balance}")
rescue
  error ->
    IO.puts("❌ 获取余额失败: #{inspect(error)}")
end

IO.puts("")

# 示例 3: 使用 BalanceCache 获取游戏账户余额（自动创建账户）
IO.puts("=== 示例 3: BalanceCache 游戏账户余额管理 ===")

new_game_id = "cache_game_#{System.unique_integer([:positive])}"
IO.puts("新游戏ID: #{new_game_id}")

try do
  case BalanceCache.get_game_balance(new_game_id) do
    {:ok, balance} ->
      IO.puts("✅ 从缓存获取游戏账户余额: #{balance}")

      # 验证账户确实被创建了
      identifier = AccountIdentifier.game(new_game_id)
      case Cypridina.Ledger.Account.get_by_identifier(identifier) do
        {:ok, account} ->
          IO.puts("✅ 确认账户已自动创建:")
          IO.puts("  账户ID: #{account.id}")
          IO.puts("  标识符: #{account.identifier}")

        {:error, reason} ->
          IO.puts("❌ 账户未找到: #{inspect(reason)}")
      end

    {:error, reason} ->
      IO.puts("❌ 获取余额失败: #{inspect(reason)}")
  end
rescue
  error ->
    IO.puts("❌ 操作失败: #{inspect(error)}")
end

IO.puts("")

# 示例 4: 游戏账户操作（增加/减少余额）
IO.puts("=== 示例 4: 游戏账户余额操作 ===")

operation_game_id = "operation_game_#{System.unique_integer([:positive])}"
IO.puts("操作游戏ID: #{operation_game_id}")

try do
  # 增加游戏账户余额
  case BalanceCache.add_game_balance(operation_game_id, 1000) do
    {:ok, new_balance} ->
      IO.puts("✅ 增加余额成功，新余额: #{new_balance}")

    {:error, reason} ->
      IO.puts("❌ 增加余额失败: #{inspect(reason)}")
  end

  # 减少游戏账户余额
  case BalanceCache.subtract_game_balance(operation_game_id, 300) do
    {:ok, new_balance} ->
      IO.puts("✅ 减少余额成功，新余额: #{new_balance}")

    {:error, reason} ->
      IO.puts("❌ 减少余额失败: #{inspect(reason)}")
  end

  # 最终余额查询
  case BalanceCache.get_game_balance(operation_game_id) do
    {:ok, final_balance} ->
      IO.puts("✅ 最终余额: #{final_balance}")

    {:error, reason} ->
      IO.puts("❌ 查询最终余额失败: #{inspect(reason)}")
  end
rescue
  error ->
    IO.puts("❌ 余额操作失败: #{inspect(error)}")
end

IO.puts("")

# 示例 5: 多货币支持
IO.puts("=== 示例 5: 多货币游戏账户 ===")

multi_currency_game_id = "multi_game_#{System.unique_integer([:positive])}"
currencies = [:XAA, :USD, :EUR]

Enum.each(currencies, fn currency ->
  IO.puts("处理货币: #{currency}")

  try do
    # 获取特定货币的游戏账户
    account = Ledger.get_game_account(multi_currency_game_id, currency)
    IO.puts("  ✅ #{currency} 账户创建成功: #{account.identifier}")

    # 获取余额
    balance = Ledger.get_game_balance(multi_currency_game_id, currency)
    IO.puts("  ✅ #{currency} 账户余额: #{balance}")
  rescue
    error ->
      IO.puts("  ❌ #{currency} 账户操作失败: #{inspect(error)}")
  end
end)

IO.puts("")

# 示例 6: 特殊情况 - game_id 为 0 时使用系统账户
IO.puts("=== 示例 6: 特殊情况处理 ===")

try do
  # game_id 为 0 时应该返回系统账户
  zero_identifier = AccountIdentifier.game(0)
  IO.puts("game_id=0 的标识符: #{zero_identifier}")

  # 验证这确实是系统账户标识符
  system_identifier = AccountIdentifier.system(:main)
  IO.puts("系统主账户标识符: #{system_identifier}")

  if zero_identifier == system_identifier do
    IO.puts("✅ game_id=0 正确映射到系统主账户")
  else
    IO.puts("❌ game_id=0 映射错误")
  end
rescue
  error ->
    IO.puts("❌ 特殊情况处理失败: #{inspect(error)}")
end

IO.puts("")
IO.puts("=== 游戏账户示例完成 ===")

# 总结新功能的优势
IO.puts("")
IO.puts("🎯 新功能总结:")
IO.puts("1. ✅ 游戏账户自动创建 - 无需手动创建，按需生成")
IO.puts("2. ✅ 统一的 Ledger 接口 - 与系统账户保持一致的API")
IO.puts("3. ✅ 缓存集成 - BalanceCache 自动使用新的 Ledger 函数")
IO.puts("4. ✅ 多货币支持 - 支持不同货币的游戏账户")
IO.puts("5. ✅ 错误处理 - 优雅处理各种异常情况")
IO.puts("6. ✅ 向后兼容 - 不影响现有功能")
