# AccountIdentifier 简化函数名示例
# 
# 这个示例展示了 AccountIdentifier 模块的新简化函数名
# 所有旧函数名仍然可用，保持向后兼容

alias Cypridina.Accounts.AccountIdentifier

IO.puts("=== AccountIdentifier 简化函数名示例 ===")
IO.puts("")

# ========== 生成账户标识符 ==========
IO.puts("1. 生成账户标识符（简化版）")

# 用户账户标识符
user_id = "user_#{System.unique_integer([:positive])}"
user_identifier = AccountIdentifier.user(user_id)
IO.puts("用户标识符: #{user_identifier}")

user_identifier_usd = AccountIdentifier.user(user_id, :USD)
IO.puts("用户USD标识符: #{user_identifier_usd}")

# 系统账户标识符
system_identifier = AccountIdentifier.system(:main)
IO.puts("系统标识符: #{system_identifier}")

system_rewards_identifier = AccountIdentifier.system(:rewards, :EUR)
IO.puts("系统奖励EUR标识符: #{system_rewards_identifier}")

# 游戏账户标识符
game_id = "game_#{System.unique_integer([:positive])}"
game_identifier = AccountIdentifier.game(game_id)
IO.puts("游戏标识符: #{game_identifier}")

game_identifier_btc = AccountIdentifier.game(game_id, :BTC)
IO.puts("游戏BTC标识符: #{game_identifier_btc}")

IO.puts("")

# ========== 生成缓存键 ==========
IO.puts("2. 生成缓存键（简化版）")

user_cache_key = AccountIdentifier.cache_key(:user, user_id)
IO.puts("用户缓存键: #{user_cache_key}")

system_cache_key = AccountIdentifier.cache_key(:system, :main)
IO.puts("系统缓存键: #{system_cache_key}")

game_cache_key = AccountIdentifier.cache_key(:game, game_id)
IO.puts("游戏缓存键: #{game_cache_key}")

IO.puts("")

# ========== 解析标识符 ==========
IO.puts("3. 解析标识符（简化版）")

case AccountIdentifier.parse(user_identifier) do
  {:ok, parsed} ->
    IO.puts("解析用户标识符成功: #{inspect(parsed)}")
  {:error, reason} ->
    IO.puts("解析失败: #{reason}")
end

case AccountIdentifier.parse(system_identifier) do
  {:ok, parsed} ->
    IO.puts("解析系统标识符成功: #{inspect(parsed)}")
  {:error, reason} ->
    IO.puts("解析失败: #{reason}")
end

case AccountIdentifier.parse(game_identifier) do
  {:ok, parsed} ->
    IO.puts("解析游戏标识符成功: #{inspect(parsed)}")
  {:error, reason} ->
    IO.puts("解析失败: #{reason}")
end

IO.puts("")

# ========== 解析缓存键 ==========
IO.puts("4. 解析缓存键（简化版）")

case AccountIdentifier.parse_cache(user_cache_key) do
  {:ok, parsed} ->
    IO.puts("解析用户缓存键成功: #{inspect(parsed)}")
  {:error, reason} ->
    IO.puts("解析失败: #{reason}")
end

case AccountIdentifier.parse_cache(game_cache_key) do
  {:ok, parsed} ->
    IO.puts("解析游戏缓存键成功: #{inspect(parsed)}")
  {:error, reason} ->
    IO.puts("解析失败: #{reason}")
end

IO.puts("")

# ========== 验证标识符 ==========
IO.puts("5. 验证标识符（简化版）")

valid_identifier = AccountIdentifier.valid?(user_identifier)
IO.puts("用户标识符有效: #{valid_identifier}")

invalid_identifier = AccountIdentifier.valid?("invalid:format")
IO.puts("无效标识符有效: #{invalid_identifier}")

valid_cache = AccountIdentifier.valid_cache?(user_cache_key)
IO.puts("用户缓存键有效: #{valid_cache}")

IO.puts("")

# ========== 类型提取 ==========
IO.puts("6. 类型提取（简化版）")

case AccountIdentifier.type(user_identifier) do
  {:ok, account_type} ->
    IO.puts("用户标识符类型: #{account_type}")
  {:error, reason} ->
    IO.puts("提取类型失败: #{reason}")
end

case AccountIdentifier.type(system_identifier) do
  {:ok, account_type} ->
    IO.puts("系统标识符类型: #{account_type}")
  {:error, reason} ->
    IO.puts("提取类型失败: #{reason}")
end

IO.puts("")

# ========== 游戏ID提取 ==========
IO.puts("7. 游戏ID提取（简化版）")

case AccountIdentifier.game_id(game_identifier) do
  {:ok, extracted_game_id} ->
    IO.puts("提取的游戏ID: #{extracted_game_id}")
    IO.puts("原始游戏ID: #{game_id}")
    IO.puts("匹配: #{extracted_game_id == game_id}")
  {:error, reason} ->
    IO.puts("提取游戏ID失败: #{reason}")
end

# 尝试从非游戏标识符提取游戏ID（应该失败）
case AccountIdentifier.game_id(user_identifier) do
  {:ok, extracted_game_id} ->
    IO.puts("意外成功提取游戏ID: #{extracted_game_id}")
  {:error, reason} ->
    IO.puts("从用户标识符提取游戏ID失败（预期）: #{inspect(reason)}")
end

IO.puts("")

# ========== 向后兼容性测试 ==========
IO.puts("8. 向后兼容性测试")

# 旧函数名仍然可用
old_user_identifier = AccountIdentifier.build_user_identifier(user_id)
new_user_identifier = AccountIdentifier.user(user_id)
IO.puts("旧函数结果: #{old_user_identifier}")
IO.puts("新函数结果: #{new_user_identifier}")
IO.puts("结果一致: #{old_user_identifier == new_user_identifier}")

old_parse_result = AccountIdentifier.parse_identifier(user_identifier)
new_parse_result = AccountIdentifier.parse(user_identifier)
IO.puts("旧解析结果: #{inspect(old_parse_result)}")
IO.puts("新解析结果: #{inspect(new_parse_result)}")
IO.puts("解析结果一致: #{old_parse_result == new_parse_result}")

IO.puts("")

# ========== 函数名对比表 ==========
IO.puts("9. 函数名对比表")
IO.puts("")
IO.puts("| 功能 | 旧函数名 | 新函数名 |")
IO.puts("|------|----------|----------|")
IO.puts("| 用户标识符 | build_user_identifier/2 | user/2 |")
IO.puts("| 系统标识符 | build_system_identifier/2 | system/2 |")
IO.puts("| 游戏标识符 | build_game_identifier/2 | game/2 |")
IO.puts("| 缓存键 | build_cache_key/2 | cache_key/2 |")
IO.puts("| 解析标识符 | parse_identifier/1 | parse/1 |")
IO.puts("| 解析缓存键 | parse_cache_key/1 | parse_cache/1 |")
IO.puts("| 验证标识符 | valid_identifier?/1 | valid?/1 |")
IO.puts("| 验证缓存键 | valid_cache_key?/1 | valid_cache?/1 |")
IO.puts("| 提取类型 | extract_account_type/1 | type/1 |")
IO.puts("| 提取游戏ID | extract_game_id/1 | game_id/1 |")

IO.puts("")

# ========== 优势总结 ==========
IO.puts("🎯 简化后的优势:")
IO.puts("1. ✅ 更简洁的函数名 - 减少冗长的命名")
IO.puts("2. ✅ 更直观的API - 函数名直接表达功能")
IO.puts("3. ✅ 向后兼容 - 所有旧函数名仍然可用")
IO.puts("4. ✅ 一致的命名风格 - 统一的简化规则")
IO.puts("5. ✅ 更好的开发体验 - 减少输入和记忆负担")

IO.puts("")
IO.puts("=== 简化函数名示例完成 ===")
