# BalanceCache Account IDs 批量查询示例
#
# 这个示例展示了如何使用新的 get_account_ids_by_identifiers/1 函数
# 该函数返回一个列表，按输入顺序排列，不存在的账户用 nil 填充

alias Cypridina.BalanceCache
alias Cypridina.Accounts.AccountIdentifier

# 示例 1: 基本用法
IO.puts("=== 示例 1: 基本用法 ===")

identifiers = [
  "user:XAA:user_123",
  "system:XAA:main",
  "game:XAA:game_456"
]

case BalanceCache.get_account_ids_by_identifiers(identifiers) do
  {:ok, account_ids} ->
    IO.puts("输入标识符: #{inspect(identifiers)}")
    IO.puts("返回账户ID: #{inspect(account_ids)}")
    IO.puts("结果长度: #{length(account_ids)}")
    IO.puts("输入长度: #{length(identifiers)}")
    IO.puts("长度匹配: #{length(account_ids) == length(identifiers)}")

  {:error, reason} ->
    IO.puts("查询失败: #{inspect(reason)}")
end

IO.puts("")

# 示例 2: 处理不存在的账户
IO.puts("=== 示例 2: 处理不存在的账户 ===")

mixed_identifiers = [
  "user:XAA:existing_user",      # 假设存在
  "user:XAA:non_existent_user",  # 不存在
  "system:XAA:main"              # 假设存在
]

case BalanceCache.get_account_ids_by_identifiers(mixed_identifiers) do
  {:ok, account_ids} ->
    IO.puts("输入标识符: #{inspect(mixed_identifiers)}")
    IO.puts("返回账户ID: #{inspect(account_ids)}")

    # 展示如何处理结果
    Enum.zip(mixed_identifiers, account_ids)
    |> Enum.with_index()
    |> Enum.each(fn {{identifier, account_id}, index} ->
      status = if account_id, do: "存在", else: "不存在"
      IO.puts("  [#{index}] #{identifier} -> #{inspect(account_id)} (#{status})")
    end)

  {:error, reason} ->
    IO.puts("查询失败: #{inspect(reason)}")
end

IO.puts("")

# 示例 3: 处理无效输入
IO.puts("=== 示例 3: 处理无效输入 ===")

invalid_identifiers = [
  nil,                    # 无效
  "",                     # 无效
  123,                    # 无效
  "user:XAA:valid_user"   # 有效
]

case BalanceCache.get_account_ids_by_identifiers(invalid_identifiers) do
  {:ok, account_ids} ->
    IO.puts("输入标识符: #{inspect(invalid_identifiers)}")
    IO.puts("返回账户ID: #{inspect(account_ids)}")

    Enum.zip(invalid_identifiers, account_ids)
    |> Enum.with_index()
    |> Enum.each(fn {{identifier, account_id}, index} ->
      validity = if is_binary(identifier), do: "有效", else: "无效"
      IO.puts("  [#{index}] #{inspect(identifier)} -> #{inspect(account_id)} (#{validity})")
    end)

  {:error, reason} ->
    IO.puts("查询失败: #{inspect(reason)}")
end

IO.puts("")

# 示例 4: 空列表处理
IO.puts("=== 示例 4: 空列表处理 ===")

case BalanceCache.get_account_ids_by_identifiers([]) do
  {:ok, account_ids} ->
    IO.puts("输入: []")
    IO.puts("返回: #{inspect(account_ids)}")
    IO.puts("是否为空列表: #{account_ids == []}")

  {:error, reason} ->
    IO.puts("查询失败: #{inspect(reason)}")
end

IO.puts("")

# 示例 5: 使用 AccountIdentifier 辅助函数
IO.puts("=== 示例 5: 使用 AccountIdentifier 辅助函数 ===")

user_id = "test_user_123"
identifiers_with_helper = [
  AccountIdentifier.user(user_id, :XAA),
  AccountIdentifier.system(:main, :XAA),
  AccountIdentifier.system(:bonus, :XAA)
]

case BalanceCache.get_account_ids_by_identifiers(identifiers_with_helper) do
  {:ok, account_ids} ->
    IO.puts("使用辅助函数构建的标识符:")
    Enum.zip(identifiers_with_helper, account_ids)
    |> Enum.each(fn {identifier, account_id} ->
      IO.puts("  #{identifier} -> #{inspect(account_id)}")
    end)

    # 可以直接按顺序解构
    [user_account_id, main_account_id, bonus_account_id] = account_ids
    IO.puts("")
    IO.puts("解构后的结果:")
    IO.puts("  用户账户ID: #{inspect(user_account_id)}")
    IO.puts("  主账户ID: #{inspect(main_account_id)}")
    IO.puts("  奖金账户ID: #{inspect(bonus_account_id)}")

  {:error, reason} ->
    IO.puts("查询失败: #{inspect(reason)}")
end

IO.puts("")

# 示例 6: 实际应用场景 - 批量余额查询
IO.puts("=== 示例 6: 实际应用场景 - 批量余额查询 ===")

balance_query_identifiers = [
  "user:XAA:player1",
  "user:XAA:player2",
  "system:XAA:main"
]

case BalanceCache.get_account_ids_by_identifiers(balance_query_identifiers) do
  {:ok, account_ids} ->
    IO.puts("批量查询余额:")

    balances = Enum.zip(balance_query_identifiers, account_ids)
    |> Enum.map(fn {identifier, account_id} ->
      balance = if account_id do
        # 这里应该调用实际的余额查询函数
        # BalanceCache.get_balance(account_id)
        # 为了示例，我们使用模拟值
        :rand.uniform(10000)
      else
        0
      end
      {identifier, balance}
    end)

    Enum.each(balances, fn {identifier, balance} ->
      IO.puts("  #{identifier}: #{balance} 积分")
    end)

  {:error, reason} ->
    IO.puts("查询失败: #{inspect(reason)}")
end

IO.puts("")
IO.puts("=== 示例完成 ===")
