# 拼多多邀请提现（Free Cash）资源管理系统

## 概述

基于 Backpex + Ash 框架，为拼多多邀请提现系统实现了完整的增删改查管理功能。该系统提供了独立的邀请奖励配置管理界面，支持多种过滤、批量操作和权限控制。

## 核心功能

### 1. 独立管理界面
- **路径**: `/admin/invite-reward-configs`
- **模块**: `Teen.Live.ActivitySystem.InviteRewardConfigLive`
- **功能**: 提供邀请奖励配置的独立管理，脱离原有的内联编辑模式

### 2. 字段管理
支持以下字段的完整管理：
- **ID**: 唯一标识符（只读）
- **所属活动**: 关联到邀请活动（可搜索）
- **轮次**: 奖励配置的轮次编号
- **任务类型**: 邀请注册、邀请充值、邀请游戏、邀请留存
- **奖励类型**: 金币、积分、现金、转盘次数、道具
- **奖励范围**: 最小奖励和最大奖励（以分为单位）
- **所需进度**: 完成任务所需的进度数量
- **概率**: 获得奖励的概率（0.0-1.0）
- **排序**: 显示顺序
- **时间戳**: 创建和更新时间（只读）

### 3. 高级过滤功能
实现了三个专用过滤器：

#### 任务类型过滤器 (`Teen.Filters.TaskTypeFilter`)
- 支持按任务类型筛选配置
- 选项：邀请注册、邀请充值、邀请游戏、邀请留存

#### 奖励类型过滤器 (`Teen.Filters.RewardTypeFilter`)
- 支持按奖励类型筛选配置
- 选项：金币、积分、现金、转盘次数、道具

#### 活动过滤器 (`Teen.Filters.ActivityIdFilter`)
- 支持按活动筛选配置
- 动态加载所有可用活动

### 4. 批量操作功能

#### 批量删除 (`Teen.ResourceActions.DeleteByActivity`)
- **功能**: 删除指定活动的所有奖励配置
- **安全性**: 包含确认对话框和详细提示
- **实现**: 使用 `destroy_by_activity` 操作

#### 批量更新概率 (`Teen.ResourceActions.BulkUpdateProbability`)
- **功能**: 批量更新选中配置的概率值
- **验证**: 概率值范围验证（0.0-1.0）
- **反馈**: 详细的成功/失败反馈

### 5. 项目操作功能

#### 复制配置 (`Teen.ItemActions.DuplicateConfig`)
- **功能**: 复制现有配置创建新配置
- **智能处理**: 自动递增轮次和排序值
- **数据完整性**: 排除ID和时间戳字段

#### 标准操作
- **查看详情**: 显示配置的完整信息
- **编辑**: 修改配置参数
- **删除**: 删除单个配置（带确认）

## 技术实现

### 1. 资源扩展
在 `Teen.ActivitySystem.InviteRewardConfig` 资源中添加了：
```elixir
destroy :destroy_by_activity do
  argument :activity_id, :uuid, allow_nil?: false
  filter expr(activity_id == ^arg(:activity_id))
  require_atomic? false
end
```

### 2. 路由配置
在 `lib/cypridina_web/router.ex` 中添加：
```elixir
live_resources "/invite-reward-configs", Live.ActivitySystem.InviteRewardConfigLive
```

### 3. 权限控制
实现了基于角色的访问控制：
- `can?/3` 函数控制各种操作权限
- 支持创建、编辑、删除等细粒度权限控制

### 4. 数据预加载
自动预加载关联的活动信息：
```elixir
def query(query, _live_resource) do
  Ash.Query.load(query, :activity)
end
```

## 用户界面特性

### 1. 响应式设计
- 适配不同屏幕尺寸
- 移动端友好的操作界面

### 2. 用户体验优化
- 直观的中文界面
- 清晰的操作反馈
- 详细的确认对话框

### 3. 数据展示
- 表格形式展示配置列表
- 支持排序和分页
- 实时搜索功能

## 安全性考虑

### 1. 数据验证
- 概率值范围验证
- 必填字段检查
- 数据类型验证

### 2. 操作确认
- 删除操作需要确认
- 批量操作有详细提示
- 错误处理和回滚机制

### 3. 权限控制
- 基于用户角色的访问控制
- 操作级别的权限检查
- 安全的数据访问模式

## 使用指南

### 1. 访问管理界面
- 登录管理后台
- 导航到 "活动系统" -> "邀请奖励配置"
- 或直接访问 `/admin/invite-reward-configs`

### 2. 创建新配置
1. 点击 "新建" 按钮
2. 选择所属活动
3. 设置轮次和任务类型
4. 配置奖励参数
5. 保存配置

### 3. 批量操作
1. 选择需要操作的配置项
2. 选择批量操作类型
3. 填写操作参数
4. 确认执行

### 4. 过滤和搜索
1. 使用顶部过滤器按条件筛选
2. 在搜索框中输入关键词
3. 结合多个过滤条件精确查找

## 维护和扩展

### 1. 添加新的过滤器
1. 创建新的过滤器模块
2. 实现 `Backpex.Filter` 行为
3. 在 LiveResource 中注册

### 2. 扩展批量操作
1. 创建新的 ResourceAction 模块
2. 实现必要的回调函数
3. 添加到 `resource_actions` 列表

### 3. 自定义字段显示
1. 修改 `fields/0` 函数
2. 添加新的字段配置
3. 实现自定义渲染逻辑

## 总结

该系统为拼多多邀请提现功能提供了完整的后台管理解决方案，具有以下优势：

- **功能完整**: 支持完整的CRUD操作
- **用户友好**: 直观的中文界面和操作流程
- **性能优化**: 高效的数据查询和批量操作
- **安全可靠**: 完善的权限控制和数据验证
- **易于扩展**: 模块化设计便于功能扩展

通过这个管理系统，运营人员可以高效地管理邀请奖励配置，支持各种营销活动的快速部署和调整。
