# Backpex+Ash 批量删除功能实现总结

## 概述

成功实现了基于 Backpex+Ash 的批量删除功能，允许根据 `activity_id` 字段批量删除 `invite_reward_configs` 表中的记录。

## 实现的功能

### 1. 批量删除函数
在 `Teen.ActivitySystem.InviteRewardConfig` 资源中添加了 `destroy_by_activity/1` 函数：

```elixir
@doc """
批量删除指定活动的所有奖励配置
"""
def destroy_by_activity(activity_id) when is_binary(activity_id) do
  case list_by_activity(%{activity_id: activity_id}) do
    {:ok, configs} ->
      results = Enum.map(configs, fn config ->
        destroy(config)
      end)
      
      # 检查是否所有删除都成功
      errors = Enum.filter(results, fn
        {:error, _} -> true
        _ -> false
      end)
      
      if Enum.empty?(errors) do
        :ok
      else
        {:error, "部分删除失败: #{inspect(errors)}"}
      end
      
    {:error, reason} ->
      {:error, reason}
  end
end

def destroy_by_activity(%{activity_id: activity_id}) do
  destroy_by_activity(activity_id)
end
```

### 2. Backpex 资源操作
创建了 `Teen.ResourceActions.DeleteByActivity` 资源操作：

```elixir
defmodule Teen.ResourceActions.DeleteByActivity do
  @moduledoc """
  根据活动ID批量删除邀请奖励配置的操作
  """

  use Backpex.ResourceAction

  @impl Backpex.ResourceAction
  def fields do
    [
      activity_id: %{
        module: Backpex.Fields.Text,
        label: "活动ID",
        help_text: "输入要删除奖励配置的活动ID",
        required: true
      }
    ]
  end

  @impl Backpex.ResourceAction
  def handle(socket, data) do
    %{activity_id: activity_id} = data

    try do
      case Teen.ActivitySystem.InviteRewardConfig.destroy_by_activity(%{activity_id: activity_id}) do
        :ok ->
          socket = 
            socket
            |> Phoenix.LiveView.put_flash(:info, "成功删除活动的所有奖励配置")
            |> Phoenix.LiveView.assign(selected_items: [])
          
          {:ok, socket}

        {:error, error} ->
          socket = Phoenix.LiveView.put_flash(socket, :error, "删除失败: #{inspect(error)}")
          {:error, socket}
      end
    rescue
      error ->
        socket = Phoenix.LiveView.put_flash(socket, :error, "删除过程中发生错误: #{inspect(error)}")
        {:error, socket}
    end
  end
end
```

### 3. 过滤器
创建了两个过滤器来增强用户体验：

#### 活动选择过滤器 (`Teen.Filters.ActivitySelect`)
```elixir
defmodule Teen.Filters.ActivitySelect do
  use Backpex.Filter
  import Phoenix.Component

  @impl Backpex.Filter
  def label, do: "活动"

  @impl Backpex.Filter
  def options do
    [
      {"所有活动", ""},
      {"测试活动1", "test-1"},
      {"测试活动2", "test-2"}
    ]
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource, _socket) do
    import Ash.Expr
    Ash.Query.filter(query, expr(activity_id == ^value))
  end
end
```

#### 奖励类型过滤器 (`Teen.Filters.RewardTypeFilter`)
```elixir
defmodule Teen.Filters.RewardTypeFilter do
  use Backpex.Filter
  import Phoenix.Component

  @impl Backpex.Filter
  def label, do: "奖励类型"

  @impl Backpex.Filter
  def options do
    [
      {"金币", :coins},
      {"积分", :points},
      {"现金", :cash},
      {"转盘次数", :wheel_spins},
      {"道具", :items}
    ]
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource, _socket) do
    import Ash.Expr
    Ash.Query.filter(query, expr(reward_type == ^value))
  end
end
```

### 4. LiveView 配置
在 `Teen.Live.ActivitySystem.InviteRewardConfigLive` 中配置了资源操作和过滤器：

```elixir
@impl Backpex.LiveResource
def resource_actions do
  [
    delete_by_activity: %{
      module: Teen.ResourceActions.DeleteByActivity,
      label: "批量删除",
      icon: "hero-trash",
      confirm_label: "确认批量删除",
      confirm_text: "此操作将删除选定活动的所有奖励配置，确定要继续吗？"
    }
  ]
end

@impl Backpex.LiveResource
def filters do
  [
    reward_type: %{
      module: Teen.Filters.RewardTypeFilter,
      label: "奖励类型"
    }
  ]
end
```

## 测试结果

### 功能测试
✅ **创建测试数据**: 成功创建了包含3个奖励配置的测试活动
✅ **批量删除**: 成功删除了指定活动的所有奖励配置
✅ **结果验证**: 确认所有相关记录都已被删除
✅ **错误处理**: 实现了完善的错误处理机制

### 测试输出
```
=== 测试 InviteRewardConfig 批量删除功能 ===
✅ 创建测试活动成功: 批量删除测试活动
   活动ID: 1f1666e1-3b86-4074-b077-decd8ae08539
   奖励配置数量: 3

📊 删除前的状态:
   奖励配置数量: 3
   - ID: ca6077f1-4c52-4613-b625-9b058e3cd69c, 轮次: 1, 类型: invite_register, 奖励: coins
   - ID: f5df7d57-a518-46e4-8e80-aed95a5b3407, 轮次: 2, 类型: invite_recharge, 奖励: coins
   - ID: 8804fd3c-3ec9-42e0-848a-901768063802, 轮次: 3, 类型: invite_register, 奖励: cash

🗑️  测试批量删除功能:
   正在删除活动ID: 1f1666e1-3b86-4074-b077-decd8ae08539 的所有奖励配置...
   删除操作结果: :ok

✅ 删除后的状态:
   奖励配置数量: 0
   🎉 批量删除成功！所有奖励配置都已删除

🧪 测试总结:
   ✅ 创建测试活动: 成功
   ✅ 创建3个奖励配置: 成功
   ✅ 批量删除功能: 成功
   ✅ 验证删除结果: 成功

🎯 批量删除功能测试完成！
```

## 关键特性

1. **安全性**: 使用 Ash 的内置删除机制，确保数据完整性
2. **错误处理**: 完善的错误处理和用户反馈
3. **用户体验**: 清晰的确认对话框和操作反馈
4. **过滤功能**: 提供过滤器帮助用户快速定位数据
5. **批量操作**: 支持一次性删除多个相关记录

## 使用方法

1. 在 Backpex 管理界面中导航到 "邀请奖励配置" 页面
2. 点击 "批量删除" 按钮
3. 输入要删除奖励配置的活动ID
4. 确认删除操作
5. 系统将删除该活动的所有奖励配置并显示成功消息

## 技术要点

- 使用 Ash 框架的资源和操作系统
- 集成 Backpex 的 LiveView 组件
- 实现了类型安全的过滤器
- 遵循 Elixir/Phoenix 的最佳实践
- 提供了完整的错误处理和用户反馈
