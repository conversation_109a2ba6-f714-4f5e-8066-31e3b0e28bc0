# 测试数据库连接和迁移状态

defmodule TestDbConnection do
  def test_connection do
    IO.puts("🔍 测试数据库连接状态")
    IO.puts("=" <> String.duplicate("=", 30))
    
    # 检查环境变量
    database_url = System.get_env("DATABASE_URL")
    IO.puts("DATABASE_URL: #{inspect(database_url)}")
    
    if database_url do
      IO.puts("✅ 数据库URL已设置")
    else
      IO.puts("❌ 数据库URL未设置")
      IO.puts("请设置环境变量：")
      IO.puts("export DATABASE_URL=\"ecto://postgres:postgres@localhost/cypridina_dev\"")
    end
    
    IO.puts("")
    
    # 检查迁移状态
    IO.puts("📋 迁移文件状态:")
    
    migration_files = [
      "priv/repo/migrations/20250707065700_init_db.exs",
      "priv/repo/migrations/20250709140000_add_enhanced_fields_to_recharge_wheel.exs"
    ]
    
    Enum.each(migration_files, fn file ->
      if File.exists?(file) do
        IO.puts("✅ #{Path.basename(file)}")
      else
        IO.puts("❌ #{Path.basename(file)} - 文件不存在")
      end
    end)
    
    IO.puts("")
    IO.puts("🎯 下一步操作:")
    IO.puts("1. 确保 PostgreSQL 服务运行")
    IO.puts("2. 设置 DATABASE_URL 环境变量")
    IO.puts("3. 运行 mix ecto.create（如果数据库不存在）")
    IO.puts("4. 运行 mix ecto.migrate")
    IO.puts("5. 恢复被注释的功能（参考 restore_wheel_features.md）")
  end
  
  def check_table_structure do
    IO.puts("📊 当前转盘表结构（基于原始设计）:")
    IO.puts("recharge_wheels 表:")
    IO.puts("- id (uuid, primary key)")
    IO.puts("- cumulative_recharge (decimal, not null)")
    IO.puts("- wheel_spins (bigint, not null, default: 1)")
    IO.puts("- status (text, not null, default: 'enabled')")
    IO.puts("- inserted_at (utc_datetime_usec)")
    IO.puts("- updated_at (utc_datetime_usec)")
    IO.puts("")
    IO.puts("迁移后将添加:")
    IO.puts("- title (text, not null)")
    IO.puts("- description (text)")
    IO.puts("- jackpot_pool (decimal, not null, default: 0)")
    IO.puts("- start_date (date)")
    IO.puts("- end_date (date)")
  end
  
  def run_tests do
    test_connection()
    IO.puts("")
    check_table_structure()
  end
end

TestDbConnection.run_tests()
