# 邀请奖励活动增删改查操作测试脚本

# 启动应用
Application.ensure_all_started(:cypridina)

IO.puts("🧪 开始测试邀请奖励活动的增删改查操作...")

alias Teen.ActivitySystem.InviteCashActivity
alias Teen.ActivitySystem.InviteRewardConfig

# 清理测试数据
IO.puts("\n🧹 清理现有测试数据...")
try do
  InviteCashActivity
  |> Ash.Query.filter(title: "测试活动")
  |> Ash.read!()
  |> Enum.each(fn activity ->
    InviteCashActivity.destroy!(activity)
  end)
  IO.puts("✅ 清理完成")
rescue
  _ -> IO.puts("⚠️ 清理过程中出现错误，继续测试")
end

# 1. 测试创建操作
IO.puts("\n1️⃣ 测试创建操作...")

create_params = %{
  title: "测试活动",
  description: "这是一个测试活动",
  total_reward: 10000,  # 100元（分）
  initial_min: 100,     # 1元（分）
  initial_max: 500,     # 5元（分）
  start_date: Date.utc_today(),
  end_date: Date.add(Date.utc_today(), 30),
  status: :enabled
}

case InviteCashActivity.create(create_params) do
  {:ok, activity} ->
    IO.puts("✅ 创建成功: #{activity.title} (ID: #{activity.id})")

    # 2. 测试读取操作
    IO.puts("\n2️⃣ 测试读取操作...")

    # 测试基础读取
    case InviteCashActivity.read(activity.id) do
      {:ok, read_activity} ->
        IO.puts("✅ 基础读取成功: #{read_activity.title}")
      {:error, error} ->
        IO.puts("❌ 基础读取失败: #{inspect(error)}")
    end

    # 测试按状态查询
    case InviteCashActivity.list_by_status(%{status: :enabled}) do
      {:ok, activities} ->
        IO.puts("✅ 按状态查询成功，找到 #{length(activities)} 个启用的活动")
      {:error, error} ->
        IO.puts("❌ 按状态查询失败: #{inspect(error)}")
    end

    # 测试按标题查询
    case InviteCashActivity.get_by_title(%{title: "测试活动"}) do
      {:ok, found_activity} ->
        IO.puts("✅ 按标题查询成功: #{found_activity.title}")
      {:error, error} ->
        IO.puts("❌ 按标题查询失败: #{inspect(error)}")
    end

    # 测试当前活跃活动查询
    case InviteCashActivity.list_current_active() do
      {:ok, active_activities} ->
        IO.puts("✅ 当前活跃活动查询成功，找到 #{length(active_activities)} 个活动")
      {:error, error} ->
        IO.puts("❌ 当前活跃活动查询失败: #{inspect(error)}")
    end

    # 3. 测试更新操作
    IO.puts("\n3️⃣ 测试更新操作...")

    update_params = %{
      description: "更新后的描述",
      total_reward: 15000  # 150元（分）
    }

    case InviteCashActivity.update(activity, update_params) do
      {:ok, updated_activity} ->
        IO.puts("✅ 更新成功: #{updated_activity.description}")
        IO.puts("   新的奖励总额: #{updated_activity.total_reward}")

        # 4. 测试状态切换操作
        IO.puts("\n4️⃣ 测试状态切换操作...")

        # 禁用活动
        case InviteCashActivity.disable_activity(updated_activity) do
          {:ok, disabled_activity} ->
            IO.puts("✅ 禁用成功，状态: #{disabled_activity.status}")

            # 启用活动
            case InviteCashActivity.enable_activity(disabled_activity) do
              {:ok, enabled_activity} ->
                IO.puts("✅ 启用成功，状态: #{enabled_activity.status}")

                # 5. 测试带配置的创建操作
                IO.puts("\n5️⃣ 测试带配置的创建操作...")

                create_with_configs_params = %{
                  title: "测试活动带配置",
                  description: "带奖励配置的测试活动",
                  total_reward: 20000,
                  initial_min: 200,
                  initial_max: 800,
                  start_date: Date.utc_today(),
                  end_date: Date.add(Date.utc_today(), 60),
                  status: :enabled,
                  invite_reward_configs: [
                    %{
                      round_number: 1,
                      task_type: :invite_register,
                      reward_type: :coins,
                      reward_amount: 100,
                      min_reward: 50,
                      max_reward: 150,
                      required_progress: 1,
                      probability: 1.0,
                      description: "第一轮邀请奖励",
                      sort_order: 1
                    },
                    %{
                      round_number: 2,
                      task_type: :invite_register,
                      reward_type: :coins,
                      reward_amount: 200,
                      min_reward: 100,
                      max_reward: 300,
                      required_progress: 3,
                      probability: 1.0,
                      description: "第二轮邀请奖励",
                      sort_order: 2
                    }
                  ]
                }

                case InviteCashActivity.create_with_configs(create_with_configs_params) do
                  {:ok, activity_with_configs} ->
                    IO.puts("✅ 带配置创建成功: #{activity_with_configs.title}")

                    # 验证配置是否创建成功
                    case InviteCashActivity.get_with_configs(activity_with_configs.id) do
                      {:ok, loaded_activity} ->
                        config_count = length(loaded_activity.invite_reward_configs)
                        IO.puts("✅ 配置加载成功，共 #{config_count} 个奖励配置")

                        # 6. 测试删除操作
                        IO.puts("\n6️⃣ 测试删除操作...")

                        # 先禁用活动（删除前验证要求）
                        case InviteCashActivity.disable_activity(loaded_activity) do
                          {:ok, disabled_for_delete} ->
                            IO.puts("✅ 活动已禁用，准备删除")

                            case InviteCashActivity.destroy(disabled_for_delete) do
                              {:ok, _} ->
                                IO.puts("✅ 删除成功（包括关联的奖励配置）")

                                # 验证关联配置是否也被删除
                                case InviteRewardConfig.list_by_activity(%{activity_id: loaded_activity.id}) do
                                  {:ok, remaining_configs} ->
                                    if length(remaining_configs) == 0 do
                                      IO.puts("✅ 关联配置已正确清理")
                                    else
                                      IO.puts("❌ 关联配置未完全清理，剩余 #{length(remaining_configs)} 个")
                                    end
                                  {:error, error} ->
                                    IO.puts("⚠️ 验证关联配置清理时出错: #{inspect(error)}")
                                end

                              {:error, error} ->
                                IO.puts("❌ 删除失败: #{inspect(error)}")
                            end
                          {:error, error} ->
                            IO.puts("❌ 禁用活动失败: #{inspect(error)}")
                        end

                      {:error, error} ->
                        IO.puts("❌ 加载配置失败: #{inspect(error)}")
                    end

                  {:error, error} ->
                    IO.puts("❌ 带配置创建失败: #{inspect(error)}")
                end

                # 清理第一个测试活动
                case InviteCashActivity.disable_activity(enabled_activity) do
                  {:ok, final_disabled} ->
                    InviteCashActivity.destroy!(final_disabled)
                    IO.puts("✅ 第一个测试活动已清理")
                  {:error, _} ->
                    IO.puts("⚠️ 清理第一个测试活动时出错")
                end

              {:error, error} ->
                IO.puts("❌ 启用失败: #{inspect(error)}")
            end
          {:error, error} ->
            IO.puts("❌ 禁用失败: #{inspect(error)}")
        end

      {:error, error} ->
        IO.puts("❌ 更新失败: #{inspect(error)}")
    end

  {:error, error} ->
    IO.puts("❌ 创建失败: #{inspect(error)}")
end

IO.puts("\n🎉 邀请奖励活动增删改查操作测试完成！")
IO.puts("\n✅ 测试总结:")
IO.puts("  - 基础 CRUD 操作（创建、读取、更新、删除）")
IO.puts("  - 状态管理操作（启用、禁用）")
IO.puts("  - 带配置的复合操作（create_with_configs）")
IO.puts("  - 多种查询操作（按状态、按标题、当前活跃等）")
IO.puts("  - 关联数据的级联删除")
IO.puts("  - 数据验证和约束检查")
