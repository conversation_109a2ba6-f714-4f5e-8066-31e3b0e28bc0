# lib/racing_game/race_controller.ex
defmodule RacingGame.RaceController do
  @moduledoc """
  动物运动会比赛控制器

  功能：
  - 比赛流程控制（开始、进行、结束）
  - 下注处理和结算
  - 排名控制（自动排名、用户预设排名）
  - 股票管理和强制平仓
  - 实时数据广播
  """

  use GenServer

  alias RacingGame.{Race, Racer, Leaderboard, Bet}
  alias RacingGame.Simulator
  alias Phoenix.PubSub
  require Logger

  @pubsub Cypridina.PubSub
  @race_topic "race_updates"
  @race_duration 180
  # 押中冠军获得6倍积分
  @bet_multiplier 6

  @animals [
    %{id: "A", name: "饿小宝", pinyin: "<PERSON><PERSON><PERSON><PERSON>", bet_amount: 160},
    %{id: "B", name: "盒马", pinyin: "Fresh<PERSON><PERSON>", bet_amount: 160},
    %{id: "C", name: "票票", pinyin: "<PERSON><PERSON><PERSON><PERSON>", bet_amount: 160},
    %{id: "D", name: "虾仔", pinyin: "Xiaz<PERSON>", bet_amount: 160},
    %{id: "E", name: "支小宝", pinyin: "<PERSON><PERSON><PERSON><PERSON><PERSON>", bet_amount: 160},
    %{id: "F", name: "欢猩", pinyin: "Huanxing", bet_amount: 160}
  ]

  # 定义名次奖励积分
  @position_adjustments %{
    1 => 10,
    2 => 3,
    3 => 2,
    4 => -4,
    5 => -5,
    6 => -6
  }

  def bet_multiplier, do: @bet_multiplier
  def animals, do: @animals
  def race_topic, do: @race_topic

  # 公共API
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  def get_current_race_result do
    GenServer.call(__MODULE__, :get_current_race_result)
  end

  def get_current_race do
    GenServer.call(__MODULE__, :get_current_race)
  end

  @doc """
  启动新比赛（由调度器调用）
  """
  def start_new_race do
    GenServer.cast(__MODULE__, :start_new_race)
  end

  @doc """
  停止比赛系统（当前比赛结束后不再开启新比赛）
  """
  def stop_racing_system do
    GenServer.call(__MODULE__, :stop_racing_system)
  end

  @doc """
  启动比赛系统（重新开启自动比赛）
  """
  def start_racing_system do
    GenServer.call(__MODULE__, :start_racing_system)
  end

  @doc """
  获取比赛系统状态
  """
  def get_racing_system_status do
    GenServer.call(__MODULE__, :get_racing_system_status)
  end

  def get_current_game_info do
    GenServer.call(__MODULE__, :get_current_game_info)
  end

  def get_latest_race_data do
    GenServer.call(__MODULE__, :get_latest_race_data)
  end

  def get_current_race_status do
    GenServer.call(__MODULE__, :get_current_race_status)
  end

  # 新增获取最近100场比赛结果和统计数据的API
  def get_recent_race_results do
    GenServer.call(__MODULE__, :get_recent_race_results)
  end

  # 新增下注相关API
  def place_bet(user_id, selection, amount) do
    GenServer.call(__MODULE__, {:place_bet, user_id, selection, amount})
  end

  def get_user_bets(user_id, race_issue \\ nil) do
    if race_issue do
      GenServer.call(__MODULE__, {:get_user_race_bets, user_id, race_issue})
    else
      GenServer.call(__MODULE__, {:get_user_bets, user_id})
    end
  end

  # 新增排名控制相关API
  def set_user_preset_ranking(ranking) do
    GenServer.call(__MODULE__, {:set_user_preset_ranking, ranking})
  end

  def clear_user_preset_ranking do
    GenServer.call(__MODULE__, :clear_user_preset_ranking)
  end

  def get_user_preset_ranking do
    GenServer.call(__MODULE__, :get_user_preset_ranking)
  end

  def get_ranking_control_status do
    GenServer.call(__MODULE__, :get_ranking_control_status)
  end

  # 新增游戏控制相关API
  def reset_animal_prices do
    GenServer.call(__MODULE__, :reset_animal_prices)
  end

  def force_liquidate_all_stocks do
    GenServer.call(__MODULE__, :force_liquidate_all_stocks)
  end

  # 获取当前比赛的总下注统计（从内存中获取）
  def get_total_bets do
    GenServer.call(__MODULE__, :get_total_bets)
  end

  @doc """
  优雅关机：立即结束当前游戏并进行结算
  """
  def graceful_shutdown do
    GenServer.call(__MODULE__, :graceful_shutdown)
  end

  # 回调函数
  @impl true
  def init(_opts) do
    Logger.info("GameManager started")

    # 从数据库中获取最近100场比赛记录
    recent_races = Race.get_recent!()

    schedule_next_race()
    # 使用获取的最近比赛记录初始化状态
    {:ok,
     %{
       current_race: nil,
       reset_bet_amount_map: false,
       race_simulation: nil,
       recent_races: recent_races,
       auto_start_enabled: true,  # 控制是否自动开启新比赛
       # 新增排名控制状态
       ranking_control: %{
         auto_ranking_enabled: true,
         user_preset_ranking: nil,
         auto_ranking: nil
       },
       # 新增总下注统计（内存中实时统计）
       total_bets: %{
         "A" => 0,
         "B" => 0,
         "C" => 0,
         "D" => 0,
         "E" => 0,
         "F" => 0
       }
     }}
  end

  @impl true
  def handle_call(:get_current_race_result, _from, state) do
    {:reply, build_race_result(state.current_race), state}
  end

  @impl true
  def handle_call(:get_current_race, _from, state), do: {:reply, state.current_race, state}

  @impl true
  def handle_call(:get_current_game_info, _from, state) do
    case state.current_race do
      nil ->
        {:reply, {:error, :no_race}, state}

      race ->
        # 检查当前时间是否超过比赛截止时间
        # now = DateTime.utc_now()
        # if DateTime.compare(now, race.order_end_time) == :gt do
        #   # 当前时间超过比赛截止时间，返回无比赛状态
        #   {:reply, {:error, :no_race}, state}
        # else
          {:reply, {:ok, build_game_info(race, state)}, state}
        # end
    end
  end

  @impl true
  def handle_call(:get_latest_race_data, _from, state) do
    data = get_latest_race_details(state)
    {:reply, data, state}
  end

  @impl true
  def handle_call(:get_current_race_status, _from, state) do
    status =
      case state.race_simulation do
        nil -> get_default_race_status()
        simulation -> simulation
      end

    {:reply, status, state}
  end

  @impl true
  def handle_call(:get_recent_race_results, _from, state) do
    # 直接返回存储在状态中的最近100场比赛结果
    {:reply, state.recent_races, state}
  end

  def get_champion_statistics(state) do
    # 统计各个选手在最近100场比赛中获得冠军的次数

    Logger.debug(
      "获取冠军统计数据 #{inspect(state.recent_races |> Enum.frequencies_by(fn race -> List.first(race.positions) end))}"
    )

    state.recent_races
    |> Enum.frequencies_by(fn race -> List.first(race.positions) end)
    |> Enum.map(fn {code, count} ->
      "#{code}_#{count}"
    end)
  end

  @impl true
  def handle_call({:get_user_bets, user_id}, _from, state) do
    bets = Bet.get_user_race_bets!(%{user_id: user_id})
    {:reply, bets, state}
  end

  @impl true
  def handle_call({:get_user_race_bets, user_id, race_issue}, _from, state) do
    bets =
      Bet.get_user_race_bets!(%{
        user_id: user_id,
        race_issue: race_issue
      })

    {:reply, bets, state}
  end

  @impl true
  def handle_call({:set_user_preset_ranking, ranking}, _from, state) do
    new_ranking_control = %{state.ranking_control | user_preset_ranking: ranking}
    Logger.info("设置用户预设排名: #{inspect(new_ranking_control)}")
    new_state = %{state | ranking_control: new_ranking_control}

    # 广播用户预设排名变化事件
    broadcast_game_data_update(new_state, :user_preset_ranking_changed, %{ranking: ranking})

    {:reply, :ok, new_state}
  end

  @impl true
  def handle_call(:clear_user_preset_ranking, _from, state) do
    new_ranking_control = %{state.ranking_control | user_preset_ranking: []}
    new_state = %{state | ranking_control: new_ranking_control}

    # 广播用户预设排名清除事件
    broadcast_game_data_update(new_state, :user_preset_ranking_cleared, %{})

    {:reply, :ok, new_state}
  end

  @impl true
  def handle_call(:get_user_preset_ranking, _from, state) do
    {:reply, state.ranking_control.user_preset_ranking, state}
  end

  @impl true
  def handle_call(:get_ranking_control_status, _from, state) do
    {:reply, state.ranking_control, state}
  end

  @impl true
  def handle_call(:reset_animal_prices, _from, state) do
    case reset_animal_prices_to_default(state) do
      {:ok, new_state} ->
        {:reply, :ok, new_state}

      {:error, reason} ->
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_call(:force_liquidate_all_stocks, _from, state) do
    case force_liquidate_all_user_stocks(state) do
      {:ok, liquidation_result} ->
        {:reply, {:ok, liquidation_result}, state}

      {:error, reason} ->
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_call(:get_total_bets, _from, state) do
    {:reply, state.total_bets, state}
  end

  @impl true
  def handle_call(:graceful_shutdown, _from, state) do
    Logger.info("收到优雅关机信号，开始处理当前游戏...")

    case handle_graceful_shutdown(state) do
      {:ok, new_state} ->
        Logger.info("优雅关机处理完成")
        {:reply, :ok, new_state}

      {:error, reason} ->
        Logger.error("优雅关机处理失败: #{inspect(reason)}")
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_call(:stop_racing_system, _from, state) do
    Logger.info("停止比赛系统，当前比赛结束后不再开启新比赛")
    new_state = %{state | auto_start_enabled: false}
    {:reply, :ok, new_state}
  end

  @impl true
  def handle_call(:start_racing_system, _from, state) do
    Logger.info("启动比赛系统，重新开启自动比赛")
    new_state = %{state | auto_start_enabled: true}

    # 如果当前没有比赛，立即开始新比赛
    if is_nil(new_state.current_race) do
      send(self(), :start_new_race)
    end

    {:reply, :ok, new_state}
  end

  @impl true
  def handle_call(:get_racing_system_status, _from, state) do
    status = %{
      auto_start_enabled: state.auto_start_enabled,
      current_race: state.current_race,
      has_current_race: not is_nil(state.current_race)
    }
    {:reply, status, state}
  end

  @impl true
  def handle_call({:place_bet, user_id, selection, amount}, _from, state) do
    # 检查比赛是否在下注阶段
    current_race = state.current_race

    if current_race && current_race.status == 0 &&
         DateTime.compare(DateTime.utc_now(), current_race.order_end_time) == :lt do
      # 检查用户是否有足够积分
      user_points = Cypridina.Accounts.get_user_points(user_id)
      race_issue = current_race.issue

      if user_points >= amount do
        # 使用事务确保数据一致性
        case place_bet_transaction(user_id, race_issue, selection, amount) do
          {:ok, result} ->
            # 更新内存中的总下注统计
            updated_total_bets = Map.update!(state.total_bets, selection, &(&1 + amount))

            # 更新状态
            new_state = %{state | total_bets: updated_total_bets}

            # 重新计算自动排名（使用内存中的数据）
            new_state = update_auto_ranking_with_bets(new_state, current_race, updated_total_bets)

            # 广播游戏数据更新事件
            broadcast_game_data_update(new_state, :bet_placed, %{
              user_id: user_id,
              selection: selection,
              amount: amount
            })

            {:reply, {:ok, result}, new_state}

          {:error, reason} ->
            Logger.error("下注事务失败: #{inspect(reason)}")
            {:reply, {:error, reason}, state}
        end
      else
        {:reply, {:error, "积分不足"}, state}
      end
    else
      {:reply, {:error, "下注阶段已结束"}, state}
    end
  end

  @impl true
  def handle_cast(:start_new_race, state) do
    # 转发给 handle_info 处理
    send(self(), :start_new_race)
    {:noreply, state}
  end

  @impl true
  def handle_cast(:stop_racing_system, state) do
    Logger.info("停止比赛系统")
    new_state = %{state | auto_start_enabled: false}
    {:noreply, new_state}
  end

  @impl true
  def handle_info(:start_new_race, state) do
    # 创建新的比赛期号
    issue = generate_issue()

    latest_race = List.first(state.recent_races)
    # 根据比赛结果更新动物下注额
    bet_amount_map =
      if latest_race == nil || state.reset_bet_amount_map == true ||
           latest_race.positions == [] || latest_race.positions == nil do
        # 如果没有最近比赛，则使用默认下注额
        Enum.map(@animals, fn animal ->
          {animal.id, animal.bet_amount}
        end)
        |> Enum.into(%{})
      else
        Enum.map(@animals, fn animal ->
          # 安全地获取位置，如果找不到则默认为最后一名

          position_index =
            Enum.find_index(latest_race.positions, fn code ->
              code == animal.id
            end)

          position = if position_index != nil, do: position_index + 1, else: 6

          # 根据名次获取积分调整值
          adjustment = Map.get(@position_adjustments, position, 0)
          # 获取当前下注额，如果为nil则使用默认值
          current_bet_amount = Map.get(latest_race.bet_amount_map, animal.id, 100)
          # 下注额不能小于1
          adjust_bet_amount = max(1, current_bet_amount + adjustment)
          # adjust_bet_amount = 166
          {animal.id, adjust_bet_amount}
        end)
        |> Enum.into(%{})
      end

    {:ok, race} =
      Race.create(%{
        issue: issue,
        issue_id: issue,
        status: 0,
        bet_amount_map: bet_amount_map,
        start_time: DateTime.utc_now(),
        end_time: DateTime.utc_now() |> DateTime.add(@race_duration, :second),
        issue_end_time: DateTime.utc_now() |> DateTime.add(@race_duration - 20, :second),
        order_end_time: DateTime.utc_now() |> DateTime.add(@race_duration - 20, :second)
      })

    # 身价更新时触发强制平仓
    Logger.info("身价更新，检查是否需要强制平仓...")

    case RacingGame.check_and_trigger_force_liquidation(race) do
      {:ok, result} ->
        Logger.info("身价更新强制平仓检查完成: #{inspect(result)}")

        # 如果执行了强制平仓，广播事件
        case result do
          %{total_users: users} when users > 0 ->
            broadcast_game_data_update(state, :force_liquidation_completed, result)

          _ ->
            :ok
        end

      {:error, reason} ->
        Logger.error("身价更新强制平仓检查失败: #{inspect(reason)}")
    end

    # 安排比赛阶段
    # 2分40秒后
    Process.send_after(self(), {:start_racing, race.id}, (@race_duration - 20) * 1000)

    # 重置总下注统计
    reset_total_bets = %{
      "A" => 0,
      "B" => 0,
      "C" => 0,
      "D" => 0,
      "E" => 0,
      "F" => 0
    }

    auto_ranking = ["A", "B", "C", "D", "E", "F"] |> Enum.shuffle()

    # 重置排名控制：自动控制为随机，用户预设控制为空列表
    new_ranking_control = %{
      state.ranking_control
      | auto_ranking: auto_ranking,
        # 重置为随机模式（关闭自动控制）
        auto_ranking_enabled: false,
        # 清空用户预设排名
        user_preset_ranking: []
    }

    new_state =
      %{
        state
        | current_race: race,
          reset_bet_amount_map: false,
          total_bets: reset_total_bets,
          ranking_control: new_ranking_control
      }

    # 广播游戏数据更新事件
    broadcast_game_data_update(new_state, :new_race, %{})

    {:noreply, new_state}
  end

  @impl true
  def handle_info({:start_racing, race_id}, state) do
    # 更新比赛状态为进行中
    #
    # 获取最终排名：优先用户预设 > 自动排名 > 随机
    final_ranking = get_final_ranking_for_race(state)

    race =
      RacingGame.Race
      |> Ash.get!(race_id)
      |> Race.set_result!(%{
        status: 1,
        positions: final_ranking,
        end_times: [],
        speeds: []
      })

    # 安排比赛结束
    Process.send_after(self(), {:end_race, race_id}, 20 * 1000)

    new_state = %{state | current_race: race}

    # 广播游戏数据更新事件
    broadcast_game_data_update(new_state, :race_started, %{})

    {:noreply, new_state}
  end

  @impl true
  def handle_info({:end_race, race_id}, state) do
    Logger.info("比赛结束")

    race =
      RacingGame.Race
      |> Ash.get!(race_id)
      |> Race.set_result!(%{
        status: 2,
        end_times: ["16.95", "15.46", "17.92", "16.26", "17.15", "17.73"],
        speeds: ["5.9", "6.47", "5.58", "6.15", "5.83", "5.64"]
      })

    # 处理下注结果
    process_bet_results(race)

    # 将当前比赛添加到最近比赛列表中
    updated_recent_races =
      if race do
        [race | state.recent_races]
        |> Enum.take(100)
      else
        state.recent_races
      end

    # 检查是否应该安排下一场比赛
    if state.auto_start_enabled do
      Logger.info("比赛系统已启用，安排下一场比赛")
      Process.send_after(self(), :start_new_race, 0 * 1000)
    else
      Logger.info("比赛系统已停止，不安排新比赛")
    end

    new_state = %{
      state
      | race_simulation: nil,
        recent_races: updated_recent_races,
        current_race: nil
    }

    # 广播游戏数据更新事件
    broadcast_game_data_update(new_state, :race_ended, %{})

    {:noreply, new_state}
  end

  @impl true
  def handle_info(:clear_current_race, state) do
    Logger.info("清除当前比赛")
    new_state = %{state | current_race: nil}
    {:noreply, new_state}
  end

  # 私有辅助函数
  defp schedule_next_race do
    # 启动时立即开始第一场比赛
    send(self(), :start_new_race)
  end

  defp generate_issue do
    shanghai_time = DateTime.now!("Asia/Shanghai", Tzdata.TimeZoneDatabase)
    # 提取年月日
    %{year: year, month: month, day: day, hour: hour, minute: minute} = shanghai_time
    # 计算当天的比赛序号（假设从0点开始，每3分钟一场）
    race_count = hour * 60 + minute
    race_number = div(race_count, div(@race_duration, 60))

    # 格式：YYYYMMDDxxxx，xxxx为当天的比赛序号，从0001开始
    "#{year}#{String.pad_leading("#{month}", 2, "0")}#{String.pad_leading("#{day}", 2, "0")}#{String.pad_leading("#{race_number + 1}", 4, "0")}"
  end

  # 处理下注结果
  defp process_bet_results(race) do
    # 获取冠军
    winner_position = List.first(race.positions)
    # 获取该比赛的所有下注
    bets = Bet.get_race_bets!(%{race_issue: race.issue})
    Logger.debug("下注结果：#{inspect(bets)}")

    # 处理每个下注
    Enum.each(bets, fn bet ->
      if bet.selection == winner_position do
        raw_payout = bet.amount * @bet_multiplier

        # 计算抽水后的实际收入（向下取整）
        {actual_payout, commission_amount, commission_rate} =
          calculate_commission_and_income_for_bet_with_rate(bet.user_id, raw_payout)

        Logger.info(
          "中奖！用户ID: #{bet.user_id}，下注金额: #{bet.amount}，原始奖金: #{raw_payout}，实际奖金: #{actual_payout}，抽水: #{commission_amount}"
        )

        # 使用事务处理获奖
        case process_bet_win_transaction(
               bet,
               actual_payout,
               commission_amount,
               commission_rate,
               raw_payout
             ) do
          {:ok, _result} ->
            # 发送中奖消息
            PubSub.broadcast(
              @pubsub,
              "user:#{bet.user_id}",
              {:bet_won, %{bet: bet, payout: actual_payout, commission: commission_amount}}
            )

          {:error, reason} ->
            Logger.error("获奖事务处理失败: #{inspect(reason)}")
        end
      else
        # 未中奖：更新下注状态为输
        bet
        |> Ash.Changeset.for_update(:set_bet_result, %{
          status: 2,
          payout: 0
        })
        |> Ash.update!()
      end
    end)
  end

  defp build_race_result(race) do
    latest_race = get_latest_race()

    %{
      bonusAmount: 0,
      bonusStatus: 3,
      endTime:
        Enum.join(race.end_times || ["16.95", "15.46", "17.92", "16.26", "17.15", "17.73"], ","),
      extPackageInfo: nil,
      extPrizeInfo: nil,
      issue: race.issue,
      issueId: race.issue_id,
      lastIssue: latest_race.issue,
      lastLotteryCode: build_lottery_code(latest_race),
      lotteryCode: race.positions,
      nextIssue: generate_next_issue(race.issue),
      nextIssueId: generate_next_issue(race.issue),
      nextIssueStatus: 0,
      nextOrderEndTime: race.order_end_time |> DateTime.to_unix(:millisecond),
      serverTime: DateTime.utc_now() |> DateTime.to_unix(:millisecond),
      speed: Enum.join(race.speeds || ["5.9", "6.47", "5.58", "6.15", "5.83", "5.64"], ",")
    }
  end

  defp build_game_info(race, state) do
    # 计算倒计时
    now = DateTime.utc_now()

    # 到比赛结束的倒计时
    countdown1 = DateTime.diff(race.end_time, now, :second)

    # 到下单截止的倒计时
    countdown = DateTime.diff(race.order_end_time, now, :second)

    %{
      betItem: %{
        "1" => []
      },
      betNumber: ["20"],
      betMap: race.bet_amount_map,
      countdown: max(0, countdown),
      countdown1: max(0, countdown1),
      fsdf: "ydh3fpk6",
      issue: race.issue,
      issueDesc: "猜中冠军，限量得现金红包",
      issueEndTime: race.issue_end_time |> DateTime.to_unix(:millisecond),
      issueId: race.issue_id,
      issueStatus: race.status,
      leaderboard: get_champion_statistics(state),
      orderEndTime: race.order_end_time |> DateTime.to_unix(:millisecond),
      props: %{totalCount: 0},
      serverTime: DateTime.utc_now() |> DateTime.to_unix(:millisecond)
    }
  end

  defp get_latest_race_details(state) do
    latest_race = get_latest_race()

    %{
      # odds: racer.current_odds,
      histories: [
        %{
          issue: latest_race.issue,
          issueTime: latest_race.end_time |> DateTime.to_string(),
          result: build_result_string(latest_race)
        }
      ],
      record: [
        %{
          issue: latest_race.issue,
          issueId: latest_race.issue_id,
          result: latest_race.positions || ["D", "B", "F", "E", "C", "A"]
        }
      ],
      total: get_champion_statistics(state)
    }
  end

  defp get_latest_race do
    case Race.get_latest() do
      {:ok, [race]} ->
        race

      _ ->
        %{
          issue: "202504210438",
          issue_id: "202504210438",
          positions: ["D", "B", "F", "E", "C", "A"],
          end_time: DateTime.utc_now()
        }
    end
  end

  defp build_lottery_code(race) do
    # 从位置数组转换为数字表示
    race.positions
    |> Enum.map(&get_racer_number_by_code/1)
    |> Enum.join(",")
  end

  defp build_result_string(race) do
    build_lottery_code(race)
  end

  defp get_racer_number_by_code(code) do
    case code do
      "A" -> 1
      "B" -> 2
      "C" -> 3
      "D" -> 4
      "E" -> 5
      "F" -> 6
      _ -> 0
    end
  end

  defp generate_next_issue(current_issue) do
    {main_part, last_digit} = String.split_at(current_issue, -1)
    next_digit = String.to_integer(last_digit) + 1

    if next_digit > 9 do
      {prefix, second_last_digit} = String.split_at(main_part, -1)
      next_second_digit = String.to_integer(second_last_digit) + 1
      "#{prefix}#{next_second_digit}0"
    else
      "#{main_part}#{next_digit}"
    end
  end

  defp get_default_race_status do
    %{
      gid: "1",
      uuid: "#{DateTime.utc_now() |> DateTime.to_unix(:millisecond)}-#{Ecto.UUID.generate()}",
      data: [
        %{speed: 630, dist: 7631, idx: 1},
        %{speed: 615, dist: 7386, idx: 2},
        %{speed: 670, dist: 7606, idx: 3},
        %{speed: 635, dist: 7497, idx: 4},
        %{speed: 620, dist: 7584, idx: 5},
        %{speed: 625, dist: 7506, idx: 6}
      ]
    }
  end

  # 更新自动排名（使用内存中的下注数据）
  defp update_auto_ranking_with_bets(state, race, total_bets) do
    if state.ranking_control.auto_ranking_enabled do
      auto_ranking = calculate_auto_ranking_for_race(race, total_bets)

      new_ranking_control = %{state.ranking_control | auto_ranking: auto_ranking}
      %{state | ranking_control: new_ranking_control}
    else
      state
    end
  end

  # 计算自动排名
  defp calculate_auto_ranking_for_race(_race, total_bets) when map_size(total_bets) == 0 do
    # 如果没有下注数据，返回随机排序
    @animals |> Enum.map(& &1.id) |> Enum.shuffle()
  end

  defp calculate_auto_ranking_for_race(_race, total_bets) do
    # 计算总投注额
    total_bet_amount = total_bets |> Map.values() |> Enum.sum()

    # 计算每个动物如果获胜时的平台赔付和收益
    animal_analysis =
      Enum.map(@animals, fn animal ->
        total_bet_on_animal = Map.get(total_bets, animal.id, 0)
        payout = total_bet_on_animal * @bet_multiplier
        # 平台收益 = 总投注额 - 赔付金额
        platform_profit = total_bet_amount - payout
        # 赔付倍数 = 赔付金额 / 总投注额
        payout_ratio = if total_bet_amount > 0, do: payout / total_bet_amount, else: 0

        {animal.id,
         %{
           payout: payout,
           profit: platform_profit,
           payout_ratio: payout_ratio,
           bet_amount: total_bet_on_animal
         }}
      end)

    # 随机决定策略：50%概率利益最大化，50%概率让玩家赢但控制赔付
    strategy = if :rand.uniform() < 0.5, do: :maximize_profit, else: :controlled_loss

    Logger.info("自动排名策略: #{strategy}, 总投注额: #{total_bet_amount}")

    case strategy do
      :maximize_profit ->
        # 利益最大化：选择平台收益最高的动物获胜
        animal_analysis
        |> Enum.sort_by(fn {_id, analysis} -> -analysis.profit end)
        |> Enum.map(fn {animal_id, analysis} ->
          Logger.debug("动物 #{animal_id}: 收益 #{analysis.profit}, 赔付 #{analysis.payout}")
          animal_id
        end)

      :controlled_loss ->
        # 控制性输钱：让玩家赢，但确保总赔付不超过投注额的2倍
        safe_animals =
          animal_analysis
          |> Enum.filter(fn {_id, analysis} -> analysis.payout_ratio <= 2.0 end)
          # 优先让下注多的动物赢
          |> Enum.sort_by(fn {_id, analysis} -> -analysis.bet_amount end)

        case safe_animals do
          [] ->
            # 如果所有动物的赔付都超过2倍，选择赔付最少的
            Logger.warning("所有动物赔付都超过2倍，选择最安全的")

            animal_analysis
            |> Enum.sort_by(fn {_id, analysis} -> analysis.payout end)
            |> Enum.map(fn {animal_id, analysis} ->
              Logger.debug("动物 #{animal_id}: 赔付倍数 #{Float.round(analysis.payout_ratio, 2)}")
              animal_id
            end)

          safe_list ->
            # 从安全范围内随机选择一个获胜者，其他随机排序
            winner = safe_list |> List.first() |> elem(0)

            others =
              @animals |> Enum.map(& &1.id) |> Enum.reject(&(&1 == winner)) |> Enum.shuffle()

            winner_analysis = safe_list |> List.first() |> elem(1)

            Logger.info(
              "选择动物 #{winner} 获胜, 赔付倍数: #{Float.round(winner_analysis.payout_ratio, 2)}"
            )

            [winner | others]
        end
    end
  end

  # 获取最终排名：优先级 用户预设 > 自动排名 > 随机
  defp get_final_ranking_for_race(state) do
    cond do
      state.ranking_control.user_preset_ranking != [] ->
        Logger.info("使用用户预设排名: #{inspect(state.ranking_control.user_preset_ranking)}")
        state.ranking_control.user_preset_ranking

      state.ranking_control.auto_ranking != nil ->
        Logger.info("使用系统自动排名: #{inspect(state.ranking_control.auto_ranking)}")
        state.ranking_control.auto_ranking

      true ->
        random_ranking = @animals |> Enum.map(& &1.id) |> Enum.shuffle()
        Logger.info("使用随机排名: #{inspect(random_ranking)}")
        random_ranking
    end
  end

  # 重置动物身价到预设值
  defp reset_animal_prices_to_default(state) do
    state = %{state | reset_bet_amount_map: true}
    Logger.info("动物身价已重置到预设值")
    {:ok, state}
  end

  # 强制平仓所有用户股票
  defp force_liquidate_all_user_stocks(state) do
    current_race = state.current_race

    case RacingGame.force_liquidate_all_stocks(current_race) do
      {:ok, result} ->
        # 广播游戏数据更新事件
        broadcast_game_data_update(state, :force_liquidation_completed, result)
        {:ok, result}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # 统一的游戏数据更新广播函数
  defp broadcast_game_data_update(state, event_type, extra_data \\ %{}) do
    # 构建统一的游戏数据
    game_data = %{
      current_race: state.current_race,
      total_bets: state.total_bets,
      ranking_control: state.ranking_control,
      # 只发送最近5场比赛
      recent_races: Enum.take(state.recent_races, 5),
      event_type: event_type,
      extra_data: extra_data,
      timestamp: DateTime.utc_now()
    }

    # 广播统一的游戏数据更新事件
    PubSub.broadcast(@pubsub, @race_topic, {:game_data_update, game_data})
  end

  # 私有函数：根据选择获取动物名称
  defp get_animal_name(selection) do
    animal = Enum.find(@animals, fn animal -> animal.id == selection end)
    if animal, do: animal.name, else: "未知动物"
  end

  # 私有函数：计算投注获奖的抽水后实际收入和抽水金额
  defp calculate_commission_and_income_for_bet(user_id, gross_payout) do
    case Cypridina.Accounts.get_user_agent_relationship(user_id) do
      {:ok, agent_relationship} ->
        # 计算抽水金额（向下取整）
        commission_amount =
          gross_payout
          |> Decimal.mult(agent_relationship.commission_rate)
          # 向下取整到整数
          |> Decimal.round(0, :down)
          |> Decimal.to_integer()

        # 实际收入 = 总奖金 - 抽水金额
        actual_payout = gross_payout - commission_amount

        {actual_payout, commission_amount}

      {:error, :no_agent} ->
        # 用户没有代理，不需要抽水
        {gross_payout, 0}

      {:error, _reason} ->
        # 获取代理关系失败，不抽水
        {gross_payout, 0}
    end
  end

  # 私有函数：计算投注获奖的抽水后实际收入、抽水金额和抽水比例
  defp calculate_commission_and_income_for_bet_with_rate(user_id, gross_payout) do
    case Cypridina.Accounts.get_user_agent_relationship(user_id) do
      {:ok, agent_relationship} ->
        # 计算抽水金额（向下取整）
        commission_amount =
          gross_payout
          |> Decimal.mult(agent_relationship.commission_rate)
          # 向下取整到整数
          |> Decimal.round(0, :down)
          |> Decimal.to_integer()

        # 实际收入 = 总奖金 - 抽水金额
        actual_payout = gross_payout - commission_amount

        {actual_payout, commission_amount, agent_relationship.commission_rate}

      {:error, :no_agent} ->
        # 用户没有代理，不需要抽水
        {gross_payout, 0, Decimal.new(0)}

      {:error, _reason} ->
        # 获取代理关系失败，不抽水
        {gross_payout, 0, Decimal.new(0)}
    end
  end

  # 事务化的投注操作
  defp place_bet_transaction(user_id, race_issue, selection, amount) do
    Cypridina.Repo.transaction(fn ->
      # 1. 创建下注记录 - 添加 return_notifications?: true
      case Bet.place_bet!(%{
             user_id: user_id,
             race_issue: race_issue,
             selection: selection,
             amount: amount
           }, return_notifications?: true) do
        {%{id: bet_id} = bet_result, notifications} ->
          # 2. 扣除用户积分
          case Cypridina.Ledger.game_bet(0, user_id, amount,
                 description: "下注: #{get_animal_name(selection)}",
                 metadata: %{
                   bet_id: bet_id,
                   selection: selection,
                   selection_name: get_animal_name(selection),
                   bet_amount: amount,
                   racing_game: true
                 }
               ) do
            {:ok, _new_points} ->
              Logger.info("投注事务成功: 用户 #{user_id}, 选择 #{selection}, 金额 #{amount}, 投注ID #{bet_id}")

              # 在事务外发送通知
              Task.start(fn ->
                Ash.Notifier.notify(notifications)
              end)

              bet_result

            {:error, reason} ->
              Logger.error("积分扣除失败: #{inspect(reason)}")
              Cypridina.Repo.rollback("积分扣除失败: #{inspect(reason)}")
          end

        error ->
          Logger.error("创建投注记录失败: #{inspect(error)}")
          Cypridina.Repo.rollback("创建投注记录失败")
      end
    end)
  end

  # 事务化的获奖处理操作
  defp process_bet_win_transaction(
         bet,
         actual_payout,
         commission_amount,
         commission_rate,
         raw_payout
       ) do
    Cypridina.Repo.transaction(fn ->
      # 1. 更新下注状态为赢
      updated_bet =
        bet
        |> Ash.Changeset.for_update(:set_bet_result, %{
          status: 1,
          payout: actual_payout
        })
        |> Ash.update!()

      # 2. 给用户增加积分（扣除抽水后的金额）
      case Cypridina.Ledger.game_win(0, bet.user_id, actual_payout,
             description: "获奖: #{get_animal_name(bet.selection)}",
             metadata: %{
               bet_id: bet.id,
               selection: bet.selection,
               selection_name: get_animal_name(bet.selection),
               original_bet: bet.amount,
               payout_amount: actual_payout,
               multiplier: @bet_multiplier,
               original_payout: raw_payout,
               commission_amount: commission_amount,
               racing_game: true
             }
           ) do
        {:ok, new_points} ->
          # 3. 处理代理抽水（如果有）
          if commission_amount > 0 do
            case Cypridina.Accounts.get_user_agent_relationship(bet.user_id) do
              {:ok, agent_relationship} ->
                agent_id = agent_relationship.agent_id

                case Cypridina.Accounts.add_points(agent_id, commission_amount,
                       transaction_type: :commission,
                       description: "投注抽水: #{get_animal_name(bet.selection)}",
                       metadata: %{
                         commission_type: "bet",
                         original_amount: raw_payout,
                         commission_rate: commission_rate,
                         source_user_id: bet.user_id,
                         from_user_id: bet.user_id,
                         from_username: Cypridina.Accounts.get_username(bet.user_id),
                         racing_game: true
                       }
                     ) do
                  {:ok, _agent_points} ->
                    Logger.info(
                      "获奖事务成功: 用户 #{bet.user_id}, 奖金 #{actual_payout}, 抽水 #{commission_amount}"
                    )

                    %{bet: updated_bet, payout: actual_payout, commission: commission_amount}

                  {:error, reason} ->
                    Logger.error("代理抽水失败: #{inspect(reason)}")
                    Cypridina.Repo.rollback("代理抽水失败: #{inspect(reason)}")
                end

              {:error, :no_agent} ->
                # 用户没有代理，不需要抽水

                Logger.info("获奖事务成功: 用户 #{bet.user_id}, 奖金 #{actual_payout}")
                %{bet: updated_bet, payout: actual_payout, commission: commission_amount}

              {:error, reason} ->
                Logger.error("获取代理关系失败: #{inspect(reason)}")
                Cypridina.Repo.rollback("获取代理关系失败: #{inspect(reason)}")
            end
          else
            # 没有抽水

            Logger.info("获奖事务成功: 用户 #{bet.user_id}, 奖金 #{actual_payout}")
            %{bet: updated_bet, payout: actual_payout, commission: commission_amount}
          end

        {:error, reason} ->
          Logger.error("积分增加失败: #{inspect(reason)}")
          Cypridina.Repo.rollback("积分增加失败: #{inspect(reason)}")
      end
    end)
  end

  @impl true
  def terminate(reason, state) do
    Logger.info("RaceController 进程即将终止，原因: #{inspect(reason)}")

    # 尝试优雅关机处理
    case handle_graceful_shutdown(state) do
      {:ok, _new_state} ->
        Logger.info("优雅关机处理完成")

      {:error, error_reason} ->
        Logger.error("优雅关机处理失败: #{inspect(error_reason)}")
    end

    :ok
  end

  # 处理优雅关机的私有函数
  defp handle_graceful_shutdown(state) do
    try do
      case state.current_race do
        nil ->
          Logger.info("没有正在进行的比赛，无需处理")
          {:ok, state}

        race when race.status == 2 ->
          Logger.info("当前比赛已结束，无需处理")
          {:ok, state}

        race ->
          Logger.info("发现正在进行的比赛 #{race.issue}，状态: #{race.status}，开始强制结算...")
          force_end_current_race(race, state)
      end
    rescue
      error ->
        Logger.error("优雅关机处理异常: #{inspect(error)}")
        {:error, error}
    end
  end

  # 强制结束当前比赛并进行结算
  defp force_end_current_race(race, state) do
    try do
      # 1. 取消所有待处理的定时器消息
      cancel_pending_timers()

      # 2. 根据比赛状态决定处理方式
      case race.status do
        0 ->
          # 下注阶段：立即开始比赛并结算
          Logger.info("比赛在下注阶段，立即开始比赛并结算")
          force_start_and_end_race(race, state)

        1 ->
          # 比赛进行中：立即结束比赛并结算
          Logger.info("比赛进行中，立即结束比赛并结算")
          force_end_race(race, state)

        _ ->
          Logger.info("比赛状态异常: #{race.status}")
          {:ok, state}
      end
    rescue
      error ->
        Logger.error("强制结束比赛异常: #{inspect(error)}")
        {:error, error}
    end
  end

  # 取消待处理的定时器消息
  defp cancel_pending_timers do
    # 清空进程消息队列中的定时器消息
    receive do
      {:start_racing, _race_id} ->
        Logger.info("取消待处理的开始比赛消息")
        cancel_pending_timers()

      {:end_race, _race_id} ->
        Logger.info("取消待处理的结束比赛消息")
        cancel_pending_timers()

      :start_new_race ->
        Logger.info("取消待处理的新比赛消息")
        cancel_pending_timers()
    after
      0 -> :ok
    end
  end

  # 强制开始并结束比赛
  defp force_start_and_end_race(race, state) do
    # 1. 获取最终排名
    final_ranking = get_final_ranking_for_race(state)

    # 2. 更新比赛状态为已结束，直接设置结果
    updated_race =
      race
      |> Race.set_result!(%{
        status: 2,
        positions: final_ranking,
        end_times: ["16.95", "15.46", "17.92", "16.26", "17.15", "17.73"],
        speeds: ["5.9", "6.47", "5.58", "6.15", "5.83", "5.64"]
      })

    # 3. 处理下注结果
    process_bet_results(updated_race)

    # 4. 更新状态
    new_state = %{state | current_race: updated_race}

    # 5. 广播比赛强制结束事件
    broadcast_game_data_update(new_state, :race_force_ended, %{
      reason: "graceful_shutdown",
      original_status: race.status
    })

    Logger.info("比赛 #{race.issue} 已强制结束并完成结算")
    {:ok, new_state}
  end

  # 强制结束正在进行的比赛
  defp force_end_race(race, state) do
    # 1. 更新比赛状态为已结束
    updated_race =
      race
      |> Race.set_result!(%{
        status: 2,
        end_times: ["16.95", "15.46", "17.92", "16.26", "17.15", "17.73"],
        speeds: ["5.9", "6.47", "5.58", "6.15", "5.83", "5.64"]
      })

    # 2. 处理下注结果
    process_bet_results(updated_race)

    # 3. 更新状态
    new_state = %{state | current_race: updated_race}

    # 4. 广播比赛强制结束事件
    broadcast_game_data_update(new_state, :race_force_ended, %{
      reason: "graceful_shutdown",
      original_status: race.status
    })

    Logger.info("比赛 #{race.issue} 已强制结束并完成结算")
    {:ok, new_state}
  end
end
