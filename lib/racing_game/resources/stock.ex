defmodule RacingGame.Stock do
  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: RacingGame,
    notifiers: [Ash.Notifier.PubSub]

  postgres do
    table "racing_game_stocks"
    repo Cypridina.Repo
  end

  code_interface do
    define :add
    define :subtract
    define :get_user_stocks
    define :get_stock
    define :liquidate_all_stocks
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    create :add do
      accept [:user_id, :racer_id, :transaction_target_id, :transaction_target_type]
      upsert? true
      upsert_identity :unique_user_stock
      argument :amount, :integer, allow_nil?: false
      argument :cost, :decimal, allow_nil?: false

      # 创建走这里
      change set_attribute(:quantity, arg(:amount))
      change set_attribute(:total_cost, arg(:cost))
      # 更新走这里
      change atomic_update(:quantity, expr(quantity + ^arg(:amount)))
      change atomic_update(:total_cost, expr(total_cost + ^arg(:cost)))
    end

    create :subtract do
      accept [:user_id, :racer_id, :transaction_target_id, :transaction_target_type]
      upsert? true
      upsert_identity :unique_user_stock
      argument :amount, :integer, allow_nil?: false

      change atomic_update(:quantity, expr(quantity - ^arg(:amount)))

      # 当清仓时（数量降为0），清零总成本
      change fn changeset, _context ->
        amount = Ash.Changeset.get_argument(changeset, :amount)
        user_id = Ash.Changeset.get_attribute(changeset, :user_id)
        racer_id = Ash.Changeset.get_attribute(changeset, :racer_id)

        # 从数据库获取当前的实际股票数量
        case __MODULE__.get_stock(%{user_id: user_id, racer_id: racer_id}) do
          {:ok, stock} ->
            current_quantity = stock.quantity || 0

            if current_quantity == amount do
              # 清仓，重置总成本为0
              Ash.Changeset.change_attribute(changeset, :total_cost, Decimal.new(0))
            else
              changeset
            end

          {:error, _} ->
            # 如果查询失败，保持原有逻辑
            changeset
        end
      end

      validate fn changeset, _context ->
        amount = Ash.Changeset.get_argument(changeset, :amount)
        user_id = Ash.Changeset.get_attribute(changeset, :user_id)
        racer_id = Ash.Changeset.get_attribute(changeset, :racer_id)

        # 从数据库获取当前的股票数量
        case __MODULE__.get_stock(%{user_id: user_id, racer_id: racer_id}) do
          {:ok, stock} ->
            current_quantity = stock.quantity || 0

            if current_quantity < amount do
              {:error, field: :amount, message: "股票数量不足"}
            else
              :ok
            end

          {:error, %Ash.Error.Query.NotFound{}} ->
            # 没有找到股票记录，说明数量为0
            if amount > 0 do
              {:error, field: :amount, message: "股票数量不足"}
            else
              :ok
            end

          {:error, _reason} ->
            # 查询失败，为了安全起见，拒绝操作
            {:error, field: :amount, message: "无法验证股票数量"}
        end
      end
    end

    read :get_user_stocks do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end

    read :get_stock do
      argument :user_id, :uuid, allow_nil?: false
      argument :racer_id, :string, allow_nil?: false
      filter expr(user_id == ^arg(:user_id) and racer_id == ^arg(:racer_id))
      get? true
    end

    action :liquidate_all_stocks, :struct do
      argument :user_id, :uuid, allow_nil?: false

      run fn input, _context ->
        user_id = input.arguments.user_id

        # 获取用户所有股票
        case __MODULE__.get_user_stocks(%{user_id: user_id}) do
          {:ok, stocks} ->
            # 获取当前比赛信息以获取市场价格
            current_race = RacingGame.RaceController.get_current_game_info()

            # 计算总价值并执行清仓（基于当前市场价格）
            total_value =
              stocks
              |> Enum.map(fn stock ->
                # 使用当前市场价格计算
                current_market_price = current_race.betMap[stock.racer_id] || 0
                Decimal.mult(Decimal.new(current_market_price), stock.quantity)
              end)
              |> Enum.reduce(Decimal.new(0), &Decimal.add/2)

            # 使用 subtract action 清空所有股票（这样会触发成本归零逻辑）
            liquidation_results =
              Enum.map(stocks, fn stock ->
                case __MODULE__.subtract(%{
                       user_id: user_id,
                       racer_id: stock.racer_id,
                       amount: stock.quantity,
                       # 系统操作
                       transaction_target_id: "-1",
                       transaction_target_type: :system
                     }) do
                  {:ok, updated_stock} -> {:ok, updated_stock}
                  {:error, reason} -> {:error, reason}
                end
              end)

            # 检查是否有失败的操作
            failed_operations =
              Enum.filter(liquidation_results, fn
                {:error, _} -> true
                _ -> false
              end)

            if Enum.empty?(failed_operations) do
              # 构建清仓股票的详细信息
              liquidated_stock_details = Enum.map(stocks, fn stock ->
                %{
                  racer_id: stock.racer_id,
                  quantity: stock.quantity,
                  total_cost: stock.total_cost,
                  # 清仓时按原价返还，所以价格等于平均成本
                  average_price: (if stock.quantity > 0, do: Decimal.div(stock.total_cost, stock.quantity), else: Decimal.new(0))
                }
              end)

              # 构建清仓描述
              stock_details = Enum.map_join(liquidated_stock_details, "，", fn stock ->
                racer_name = get_racer_name(stock.racer_id)
                avg_price = Decimal.round(stock.average_price, 2)
                "#{racer_name}#{stock.quantity}股@#{avg_price}"
              end)

              description = "用户主动清仓：#{stock_details}"

              # 返还积分给用户，记录详细的股票信息
              # 将 Decimal 转换为整数
              total_value_int = Decimal.to_integer(total_value)
              case Cypridina.Accounts.add_points(user_id, total_value_int,
                     transaction_type: :liquidate_all_stocks,
                     description: description,
                     metadata: %{
                       liquidated_stocks: liquidated_stock_details,
                       total_stocks_count: length(stocks),
                       total_value: total_value,
                       liquidation_type: "user_initiated",
                       racing_game: true
                     }
                   ) do
                {:ok, _} ->
                  {:ok,
                   %{
                     liquidated_stocks: length(stocks),
                     total_value: total_value,
                     message: "成功清仓所有股票"
                   }}

                {:error, reason} ->
                  {:error, "清仓成功但返还积分失败: #{inspect(reason)}"}
              end
            else
              {:error, "部分股票清仓失败"}
            end

          {:error, reason} ->
            {:error, "获取用户股票失败: #{inspect(reason)}"}
        end
      end
    end
  end

  # 获取选手名称的辅助函数
  defp get_racer_name(racer_id) do
    case racer_id do
      "A" -> "饿小宝"
      "B" -> "盒马"
      "C" -> "票票"
      "D" -> "虾仔"
      "E" -> "支小宝"
      "F" -> "欢猩"
      _ -> racer_id
    end
  end

  # 添加pub_sub通知，当股票持仓变化时通知相关组件
  pub_sub do
    module CypridinaWeb.Endpoint
    prefix "stock"

    # 发布股票变化事件，使用 publish 来发布自定义动作的事件
    # 这将发布到 "stock" 主题
    publish :add, ["stock"]
    publish :subtract, ["stock"]

    # 也发布标准的 create/update 事件
    publish_all :create, [[:user_id, nil]]
    publish_all :update, [[:user_id, nil]]
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
    end

    attribute :racer_id, :string do
      allow_nil? false
    end

    attribute :quantity, :integer do
      allow_nil? false
      default 0
      constraints min: 0
    end

    attribute :total_cost, :decimal do
      allow_nil? false
      default 0
      description "总成本 - 股票买入的花费总和"
    end

    attribute :transaction_target_id, :string do
      allow_nil? true
      description "交易对象ID：玩家转账为玩家ID，抽水为代理/下线ID，系统操作为-1"
    end

    attribute :transaction_target_type, :atom do
      allow_nil? true
      constraints one_of: [:player, :agent, :downline, :system]
      description "交易对象类型：player(玩家), agent(代理), downline(下线), system(系统)"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      source_attribute :user_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_user_stock, [:user_id, :racer_id]
  end
end
