defmodule RacingGame.Bet do
  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: RacingGame

  postgres do
    table "racing_game_bets"
    repo Cypridina.Repo
  end

  code_interface do
    define :place_bet
    define :get_race_bets
    define :get_user_race_bets
    # define :get_user_bets
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    create :place_bet do
      primary? true
      accept [:user_id, :race_issue, :selection, :amount, :status]

      validate present([:user_id, :race_issue, :selection, :amount])
      # 0: pending, 1: won, 2: lost
      validate one_of(:status, [0, 1, 2])

      argument :user_id, :uuid do
        allow_nil? false
      end

      argument :race_issue, :string do
        allow_nil? false
      end

      argument :selection, :string do
        allow_nil? false
      end

      argument :amount, :integer do
        allow_nil? false
        constraints min: 1
      end

      change set_attribute(:user_id, arg(:user_id))
      change set_attribute(:race_issue, arg(:race_issue))
      change set_attribute(:selection, arg(:selection))
      change set_attribute(:amount, arg(:amount))
      # 默认状态为pending
      change set_attribute(:status, 0)
    end

    read :get_user_bets do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end

    read :get_race_bets do
      argument :race_issue, :string, allow_nil?: false
      filter expr(race_issue == ^arg(:race_issue))
    end

    read :get_user_race_bets do
      argument :user_id, :uuid, allow_nil?: false
      argument :race_issue, :string, allow_nil?: false
      filter expr(user_id == ^arg(:user_id) and race_issue == ^arg(:race_issue))
    end

    update :set_bet_result do
      accept [:status, :payout]

      argument :status, :integer do
        allow_nil? false
        # constraints one_of: [1, 2] # 1: won, 2: lost
      end

      argument :payout, :integer do
        allow_nil? true
        default 0
      end

      change set_attribute(:status, arg(:status))
      change set_attribute(:payout, arg(:payout))
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
    end

    attribute :race_issue, :string do
      allow_nil? false
    end

    attribute :selection, :string do
      allow_nil? false
    end

    attribute :amount, :integer do
      allow_nil? false
    end

    attribute :status, :integer do
      allow_nil? false
      default 0
    end

    attribute :payout, :integer do
      allow_nil? false
      default 0
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      source_attribute :user_id
      destination_attribute :id
    end
  end
end
