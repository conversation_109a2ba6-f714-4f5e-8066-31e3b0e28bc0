defmodule RacingGame.Live.AdminPanel.UserManagementComponent do
  @moduledoc """
  用户管理组件 - 仅限管理员使用
  """
  use CypridinaWeb, :live_component

  alias CypridinaWeb.AuthHelper
  alias Cypridina.Accounts.User
  alias <PERSON><PERSON>ridina.Accounts.AgentRelationship
  alias Cypridina.Utils.TimeHelper
  alias CypridinaWeb.Components.PointsHistoryComponent
  require Ash.Query

  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_defaults()
      |> load_users_data()

    {:ok, socket}
  end

  defp assign_defaults(socket) do
    socket
    |> assign(:search_query, "")
    |> assign(:search_mode, "normal")  # "normal" 或 "agent"
    |> assign(:page, 1)
    |> assign(:per_page, 20)
    |> assign(:page_info, nil)
    |> assign(:show_create_modal, false)
    |> assign(:show_edit_modal, false)
    |> assign(:selected_user, nil)
    |> assign(:create_form, %{})
    |> assign(:edit_form, %{})
  end

  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    socket =
      socket
      |> assign(:search_query, query)
      |> assign(:search_mode, "normal")
      |> assign(:page, 1)
      |> load_users_data()

    {:noreply, socket}
  end

  def handle_event("search_agents", %{"search" => %{"query" => query}}, socket) do
    socket =
      socket
      |> assign(:search_query, query)
      |> assign(:search_mode, "agent")
      |> assign(:page, 1)
      |> load_users_data()

    {:noreply, socket}
  end

  def handle_event("clear_search", _params, socket) do
    socket =
      socket
      |> assign(:search_query, "")
      |> assign(:search_mode, "normal")
      |> assign(:page, 1)
      |> load_users_data()

    {:noreply, socket}
  end

  def handle_event("page_change", %{"page" => page}, socket) do
    page = String.to_integer(page)

    socket =
      socket
      |> assign(:page, page)
      |> load_users_data()

    {:noreply, socket}
  end

  def handle_event("show_create_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_create_modal, true)
      |> assign(:create_form, %{
        "username" => "",
        "password" => "",
        "password_confirmation" => "",
        "permission_level" => "0",
        "agent_level" => "-1",
        "initial_points" => "0"
      })

    {:noreply, socket}
  end

  def handle_event("hide_create_modal", _params, socket) do
    {:noreply, assign(socket, :show_create_modal, false)}
  end

  def handle_event("show_edit_modal", %{"user_id" => user_id}, socket) do
    case get_user_by_id(user_id) do
      {:ok, user} ->
        # 根据用户的 agent_level 确定 user_type
        user_type = if user.agent_level >= 0, do: "agent", else: "normal"

        socket =
          socket
          |> assign(:show_edit_modal, true)
          |> assign(:selected_user, user)
          |> assign(:edit_form, %{
            "permission_level" => to_string(user.permission_level),
            "user_type" => user_type,
            "points_adjustment" => "0",
            "adjustment_reason" => ""
          })

        {:noreply, socket}

      {:error, _} ->
        {:noreply, socket}
    end
  end

  def handle_event("hide_edit_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_edit_modal, false)
      |> assign(:selected_user, nil)

    {:noreply, socket}
  end

  def handle_event("create_user", %{"user" => user_params}, socket) do
    current_user = socket.assigns.current_user

    case create_new_user(user_params, current_user) do
      {:ok, _user} ->
        socket =
          socket
          |> assign(:show_create_modal, false)
          |> load_users_data()

        {:noreply, socket}

      {:error, _changeset} ->
        # 这里可以添加错误处理
        {:noreply, socket}
    end
  end

  def handle_event("update_user", %{"user" => user_params}, socket) do
    user = socket.assigns.selected_user
    current_user = socket.assigns.current_user

    case update_user_info(user, user_params, current_user) do
      {:ok, _updated_user} ->
        # 发送消息给父LiveView来显示flash消息
        send(self(), {:flash, :info, "用户信息更新成功"})

        # 如果有积分调整，等待一下让缓存刷新完成
        points_adjustment = String.to_integer(user_params["points_adjustment"] || "0")

        if points_adjustment != 0 do
          # 等待缓存刷新完成
          Process.sleep(500)
        end

        socket =
          socket
          |> assign(:show_edit_modal, false)
          |> assign(:selected_user, nil)
          |> load_users_data()

        {:noreply, socket}

      {:error, error} ->
        # 发送错误消息给父LiveView
        error_message =
          case error do
            %Ash.Error.Invalid{} -> "输入数据无效，请检查后重试"
            _ -> "用户信息更新失败，请稍后重试"
          end

        send(self(), {:flash, :error, error_message})

        {:noreply, socket}
    end
  end

  defp load_users_data(socket) do
    user = socket.assigns.current_user

    if AuthHelper.has_permission?(user, :admin) do
      search_query = socket.assigns.search_query
      search_mode = socket.assigns.search_mode
      page = socket.assigns.page
      per_page = socket.assigns.per_page

      case search_mode do
        "agent" ->
          load_agent_search_data(socket, search_query, page, per_page)

        _ ->
          load_normal_search_data(socket, search_query, page, per_page)
      end
    else
      per_page = socket.assigns.per_page
      socket
      |> assign(:users_data, [])
      |> assign(:page_info, %{total_count: 0, page: 1, per_page: per_page})
    end
  end

  # 普通搜索
  defp load_normal_search_data(socket, search_query, page, per_page) do
    # 构建查询，加载积分账户和代理关系信息
    query = User |> Ash.Query.load([:subordinate_relationships])

    # 添加搜索条件
    query =
      if search_query != "" do
        Ash.Query.filter(query, contains(username, ^search_query))
      else
        query
      end

    # 使用 Ash.Query.page 进行分页查询并按插入时间倒序排列
    case query
         |> Ash.Query.sort(inserted_at: :desc)
         |> Ash.Query.page(count: true, limit: per_page, offset: (page - 1) * per_page)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: users, count: total_count}} ->
        # 为每个用户加载上级代理信息
        users_with_agent_info = Enum.map(users, &load_user_agent_info/1)

        socket
        |> assign(:users_data, users_with_agent_info)
        |> assign(:page_info, %{total_count: total_count, page: page, per_page: per_page})

        {:error, error} ->
          require Logger
          Logger.error("Failed to load users data: #{inspect(error)}")

          socket
          |> assign(:users_data, [])
          |> assign(:page_info, %{total_count: 0, page: 1, per_page: per_page})
      end
  end

  # 代理搜索 - 显示代理及其直接下线
  defp load_agent_search_data(socket, search_query, page, per_page) do
    require Ash.Query
    import Ash.Expr

    # 构建查询，只查找代理用户（agent_level >= 0）
    query = User
    |> Ash.Query.filter(expr(agent_level >= 0))
    |> Ash.Query.load([:subordinate_relationships])

    # 添加搜索条件
    query =
      if search_query != "" do
        Ash.Query.filter(query, expr(contains(username, ^search_query)))
      else
        query
      end

    # 使用 Ash.Query.page 进行分页查询并按插入时间倒序排列
    case query
         |> Ash.Query.sort(inserted_at: :desc)
         |> Ash.Query.page(count: true, limit: per_page, offset: (page - 1) * per_page)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: agents, count: total_count}} ->
        # 为每个代理加载其直接下线信息
        agents_with_subordinates = Enum.map(agents, &load_agent_with_subordinates/1)

        socket
        |> assign(:users_data, agents_with_subordinates)
        |> assign(:page_info, %{total_count: total_count, page: page, per_page: per_page})

      {:error, error} ->
        require Logger
        Logger.error("Failed to load agents data: #{inspect(error)}")

        socket
        |> assign(:users_data, [])
        |> assign(:page_info, %{total_count: 0, page: 1, per_page: per_page})
    end
  end

  # 加载代理及其直接下线信息
  defp load_agent_with_subordinates(agent) do
    # 获取直接下线列表
    subordinates =
      case AgentRelationship
           |> Ash.Query.filter(agent_id == ^agent.id and status == 1 and level == 1)
           |> Ash.Query.load([:subordinate])
           |> Ash.read() do
        {:ok, relationships} ->
          Enum.map(relationships, fn rel ->
            %{
              user: rel.subordinate,
              commission_rate: rel.commission_rate,
              created_at: rel.inserted_at
            }
          end)

        {:error, _} ->
          []
      end

    # 为代理添加下线信息
    Map.put(agent, :direct_subordinates, subordinates)
  end

  # 加载用户的上级代理信息
  defp load_user_agent_info(user) do
    agent_info =
      case AgentRelationship
           |> Ash.Query.filter(subordinate_id == ^user.id and status == 1)
           |> Ash.Query.load([:agent])
           |> Ash.read() do
        {:ok, [relationship | _]} ->
          %{
            has_agent: true,
            agent: relationship.agent,
            commission_rate: relationship.commission_rate,
            level: relationship.level
          }

        {:ok, []} ->
          %{has_agent: false, agent: nil, commission_rate: nil, level: nil}

        _ ->
          %{has_agent: false, agent: nil, commission_rate: nil, level: nil}
      end

    Map.put(user, :agent_info, agent_info)
  end

  # 创建新用户
  defp create_new_user(params, _current_user) do
    # 对于新用户，如果设置为代理，默认为根代理（0级）
    agent_level =
      case params["user_type"] do
        # 新创建的代理默认为根代理
        "agent" -> 0
        # 普通用户
        _ -> -1
      end

    # 准备用户参数
    user_params = %{
      username: params["username"],
      password: params["password"],
      password_confirmation: params["password_confirmation"],
      permission_level: String.to_integer(params["permission_level"]),
      agent_level: agent_level,
      # confirmed_at: DateTime.utc_now(),
      asset: %{
        points: String.to_integer(params["initial_points"] || "0")
      }
    }

    # 创建用户
    case User |> Ash.Changeset.for_create(:register_with_username, user_params) |> Ash.create() do
      {:ok, user} ->
        {:ok, user}

      {:error, changeset} ->
        {:error, changeset}
    end
  end

  # 更新用户信息
  defp update_user_info(user, params, current_user) do
    # 更新权限级别和代理等级
    permission_level = String.to_integer(params["permission_level"])
    # 根据用户类型和用户的上线计算 agent_level
    agent_level = calculate_agent_level(params["user_type"], user)

    # 先更新权限级别
    case user
         |> Ash.Changeset.for_update(:update_permission_level, %{
           permission_level: permission_level
         })
         |> Ash.update() do
      {:ok, updated_user} ->
        # 更新代理等级
        case updated_user
             |> Ash.Changeset.for_update(:update_agent_level, %{agent_level: agent_level})
             |> Ash.update() do
          {:ok, final_user} ->
            # 处理积分调整
            points_adjustment = String.to_integer(params["points_adjustment"] || "0")

            if points_adjustment != 0 do
              case adjust_user_points(user.id, points_adjustment, current_user.id) do
                %{points: _} ->
                  :ok

                # 积分调整失败不影响主要更新
                {:error, _error} ->
                  :ok

                # 其他情况也不影响主要更新
                _ ->
                  :ok
              end
            end

            {:ok, final_user}

          {:error, changeset} ->
            {:error, changeset}
        end

      {:error, changeset} ->
        {:error, changeset}
    end
  end

  # 获取用户信息
  defp get_user_by_id(user_id) do
    user =
      User
      |> Ash.Query.filter(id == ^user_id)
      |> Ash.read_one!()

    {:ok, user}
  end

  # 根据用户类型和用户的上线计算 agent_level
  defp calculate_agent_level(user_type, user) do
    case user_type do
      "agent" ->
        # 设置为代理：需要根据用户的上线来计算
        case get_user_agent_level_from_superior(user) do
          {:ok, superior_level} ->
            # 上线的 agent_level + 1
            superior_level + 1

          {:error, :no_agent} ->
            # 如果没有上线，设置为根代理（0级）
            0

          _ ->
            # 出错时默认为根代理
            0
        end

      "normal" ->
        # 设置为普通用户：-1
        -1

      _ ->
        # 默认为普通用户
        -1
    end
  end

  # 获取用户上线的 agent_level
  defp get_user_agent_level_from_superior(user) do
    case Cypridina.Accounts.get_user_agent(user.id) do
      {:ok, agent} ->
        {:ok, agent.agent_level}

      {:error, :no_agent} ->
        {:error, :no_agent}

      error ->
        error
    end
  end

  # 调整用户积分 - 使用转账而非系统加减积分
  defp adjust_user_points(user_id, amount, current_user_id) do
    # 获取当前管理员ID
    admin_id = current_user_id

    if amount > 0 do
      # 从管理员转账给用户
      Cypridina.Accounts.transfer_points(admin_id, user_id, amount,
        "管理员转账"
      )
    else
      # 从用户转账给管理员
      Cypridina.Accounts.transfer_points(user_id, admin_id, abs(amount),
        "管理员扣除积分"
      )
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <!-- 页面头部 -->
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold">用户管理</h2>
        <button phx-click="show_create_modal" phx-target={@myself} class="btn btn-primary btn-sm">
          <.icon name="hero-plus" class="w-4 h-4" /> 新建用户
        </button>
      </div>

    <!-- 搜索和统计 -->
      <div class="flex justify-between items-center mb-4">
        <div class="flex space-x-2">
          <!-- 普通搜索 -->
          <form phx-submit="search" phx-target={@myself} class="flex space-x-2">
            <input
              type="text"
              name="search[query]"
              value={@search_query}
              placeholder="按用户名搜索..."
              class="input input-bordered input-sm w-64"
            />
            <button type="submit" class="btn btn-outline btn-sm">
              <.icon name="hero-magnifying-glass" class="w-4 h-4" /> 搜索用户
            </button>
          </form>

          <!-- 代理搜索 -->
          <form phx-submit="search_agents" phx-target={@myself} class="flex space-x-2">
            <input
              type="text"
              name="search[query]"
              value={@search_query}
              placeholder="按代理名搜索..."
              class="input input-bordered input-sm w-64"
            />
            <button type="submit" class="btn btn-info btn-sm">
              <.icon name="hero-users" class="w-4 h-4" /> 搜索代理
            </button>
          </form>

          <!-- 清除搜索 -->
          <%= if @search_query != "" do %>
            <button phx-click="clear_search" phx-target={@myself} class="btn btn-ghost btn-sm">
              <.icon name="hero-x-mark" class="w-4 h-4" /> 清除
            </button>
          <% end %>
        </div>

        <div class="text-sm text-base-content/60">
          <%= if @page_info do %>
            <%= if @search_mode == "agent" do %>
              共 {@page_info.total_count} 个代理，第 {@page_info.page} 页
            <% else %>
              共 {@page_info.total_count} 个用户，第 {@page_info.page} 页
            <% end %>
          <% else %>
            加载中...
          <% end %>
        </div>
      </div>

    <!-- 用户列表 -->
      <%= if @users_data && length(@users_data) > 0 do %>
        <%= if @search_mode == "agent" do %>
          <!-- 代理模式显示 -->
          <div class="space-y-6">
            <%= for agent <- @users_data do %>
              <div class="card bg-base-100 shadow-sm border border-base-300">
                <div class="card-body p-4">
                  <!-- 代理信息 -->
                  <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center space-x-4">
                      <div>
                        <h3 class="font-semibold text-lg">{to_string(agent.username)}</h3>
                        <div class="text-sm text-base-content/60">
                          ID: {agent.numeric_id} |
                          <span class="badge badge-info badge-sm">代理L{agent.agent_level}</span> |
                          积分: {Cypridina.Accounts.get_user_points(agent.id)}
                        </div>
                      </div>
                    </div>
                    <div class="flex space-x-2">
                      <button
                        phx-click="show_edit_modal"
                        phx-value-user_id={agent.id}
                        phx-target={@myself}
                        class="btn btn-ghost btn-xs"
                      >
                        <.icon name="hero-pencil" class="w-3 h-3" /> 编辑
                      </button>
                      <.live_component
                        module={PointsHistoryComponent}
                        id={"points_history_#{agent.id}"}
                        user_id={agent.id}
                        current_user={@current_user}
                        show_admin_actions={true}
                        user_info={
                          %{
                            username: agent.username,
                            numeric_id: agent.numeric_id,
                            current_points: Cypridina.Accounts.get_user_points(agent.id)
                          }
                        }
                      />
                    </div>
                  </div>

                  <!-- 直接下线列表 -->
                  <%= if Map.get(agent, :direct_subordinates) && length(agent.direct_subordinates) > 0 do %>
                    <div>
                      <h4 class="font-medium text-sm mb-2 text-base-content/80">
                        直接下线 ({length(agent.direct_subordinates)}人)
                      </h4>
                      <div class="overflow-x-auto">
                        <table class="table table-xs">
                          <thead>
                            <tr>
                              <th>用户名</th>
                              <th>数字ID</th>
                              <th>积分</th>
                              <th>抽水比例</th>
                              <th>加入时间</th>
                              <th>操作</th>
                            </tr>
                          </thead>
                          <tbody>
                            <%= for subordinate_info <- agent.direct_subordinates do %>
                              <% subordinate = subordinate_info.user %>
                              <tr>
                                <td class="font-medium">{to_string(subordinate.username)}</td>
                                <td class="font-mono text-xs">{subordinate.numeric_id}</td>
                                <td class="font-mono">{Cypridina.Accounts.get_user_points(subordinate.id)}</td>
                                <td>
                                  <span class="badge badge-outline badge-xs">
                                    {Decimal.to_string(Decimal.mult(subordinate_info.commission_rate, 100))}%
                                  </span>
                                </td>
                                <td class="text-xs">
                                  {TimeHelper.format_local_datetime(subordinate_info.created_at)}
                                </td>
                                <td>
                                  <button
                                    phx-click="show_edit_modal"
                                    phx-value-user_id={subordinate.id}
                                    phx-target={@myself}
                                    class="btn btn-ghost btn-xs"
                                  >
                                    <.icon name="hero-pencil" class="w-3 h-3" />
                                  </button>
                                </td>
                              </tr>
                            <% end %>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  <% else %>
                    <div class="text-sm text-base-content/60">
                      暂无直接下线
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <!-- 普通模式显示 -->
          <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>数字ID</th>
                <th>用户名</th>
                <th>权限</th>
                <th>身份</th>
                <th>上线</th>
                <th>积分</th>
                <th>注册时间</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <%= for user <- @users_data do %>
                <tr>
                  <td class="font-mono">{user.numeric_id}</td>
                  <td class="font-medium">{to_string(user.username)}</td>
                  <td>
                    <%= case user.permission_level do %>
                      <% 2 -> %>
                        <span class="badge badge-error">超级管理员</span>
                      <% 1 -> %>
                        <span class="badge badge-warning">管理员</span>
                      <% 0 -> %>
                        <span class="badge badge-ghost">普通权限</span>
                    <% end %>
                  </td>
                  <td>
                    <%= if user.agent_level >= 0 do %>
                      <span class="badge badge-info">代理L{user.agent_level}</span>
                    <% else %>
                      <span class="badge badge-ghost">普通用户</span>
                    <% end %>
                  </td>
                  <td>
                    <%= if user.agent_info && user.agent_info.has_agent do %>
                      <div class="flex flex-col">
                        <span class="font-medium text-blue-600">
                          {user.agent_info.agent.username}
                        </span>
                        <span class="text-xs text-base-content/60">
                          ID: {user.agent_info.agent.numeric_id}
                        </span>
                      </div>
                    <% else %>
                      <span class="text-base-content/40">无上线</span>
                    <% end %>
                  </td>
                  <td class="font-medium">
                    {Cypridina.Accounts.get_user_points(user.id)}
                  </td>
                  <td>{TimeHelper.format_local_date(user.inserted_at)}</td>
                  <td>
                    <%= if user.confirmed_at do %>
                      <span class="badge badge-success badge-sm">已激活</span>
                    <% else %>
                      <span class="badge badge-warning badge-sm">未激活</span>
                    <% end %>
                  </td>
                  <td>
                    <div class="flex space-x-1">
                      <button
                        phx-click="show_edit_modal"
                        phx-value-user_id={user.id}
                        phx-target={@myself}
                        class="btn btn-ghost btn-xs"
                      >
                        <.icon name="hero-pencil" class="w-3 h-3" /> 编辑
                      </button>
                      <.live_component
                        module={PointsHistoryComponent}
                        id={"points_history_#{user.id}"}
                        user_id={user.id}
                        current_user={@current_user}
                        show_admin_actions={true}
                        user_info={
                          %{
                            username: user.username,
                            numeric_id: user.numeric_id,
                            current_points: Cypridina.Accounts.get_user_points(user.id)
                          }
                        }
                      />
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
        <% end %>

    <!-- 分页控件 -->
        <%= if @page_info && @page_info.total_count > @page_info.per_page do %>
          <div class="flex justify-center mt-6">
            <div class="join">
              <%= if @page_info.page > 1 do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page_info.page - 1}
                  phx-target={@myself}
                  class="join-item btn btn-sm"
                >
                  «
                </button>
              <% end %>

              <%= for page_num <- max(1, @page_info.page - 2)..min(ceil(@page_info.total_count / @page_info.per_page), @page_info.page + 2) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={page_num}
                  phx-target={@myself}
                  class={[
                    "join-item btn btn-sm",
                    if(page_num == @page_info.page, do: "btn-active", else: "")
                  ]}
                >
                  {page_num}
                </button>
              <% end %>

              <%= if @page_info.page < ceil(@page_info.total_count / @page_info.per_page) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page_info.page + 1}
                  phx-target={@myself}
                  class="join-item btn btn-sm"
                >
                  »
                </button>
              <% end %>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="text-center py-12">
          <div class="text-base-content/40 mb-4">
            <.icon name="hero-users" class="w-16 h-16 mx-auto" />
          </div>
          <p class="text-base-content/60">
            <%= if @search_query != "" do %>
              未找到匹配的用户
            <% else %>
              暂无用户数据
            <% end %>
          </p>
        </div>
      <% end %>

    <!-- 创建用户模态框 -->
      <%= if @show_create_modal do %>
        <div class="modal modal-open">
          <div class="modal-box w-11/12 max-w-2xl">
            <h3 class="font-bold text-lg mb-4">新建用户</h3>

            <form phx-submit="create_user" phx-target={@myself} class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- 用户名 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">用户名 *</span>
                  </label>
                  <input
                    type="text"
                    name="user[username]"
                    value={@create_form["username"]}
                    placeholder="请输入用户名"
                    class="input input-bordered"
                    required
                  />
                </div>

    <!-- 密码 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">密码 *</span>
                  </label>
                  <input
                    type="password"
                    name="user[password]"
                    value={@create_form["password"]}
                    placeholder="请输入密码"
                    class="input input-bordered"
                    required
                  />
                </div>

    <!-- 确认密码 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">确认密码 *</span>
                  </label>
                  <input
                    type="password"
                    name="user[password_confirmation]"
                    value={@create_form["password_confirmation"]}
                    placeholder="请再次输入密码"
                    class="input input-bordered"
                    required
                  />
                </div>

    <!-- 权限级别 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">权限级别</span>
                  </label>
                  <select name="user[permission_level]" class="select select-bordered">
                    <option value="0" selected={@create_form["permission_level"] == "0"}>普通用户</option>
                    <option value="1" selected={@create_form["permission_level"] == "1"}>管理员</option>
                    <option value="2" selected={@create_form["permission_level"] == "2"}>
                      超级管理员
                    </option>
                  </select>
                </div>

    <!-- 身份 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">身份</span>
                  </label>
                  <select name="user[user_type]" class="select select-bordered">
                    <option value="normal" selected={@create_form["user_type"] == "normal"}>
                      普通用户
                    </option>
                    <option value="agent" selected={@create_form["user_type"] == "agent"}>代理</option>
                  </select>
                </div>

    <!-- 初始积分 -->
                <div class="form-control md:col-span-2">
                  <label class="label">
                    <span class="label-text">初始积分</span>
                  </label>
                  <input
                    type="number"
                    name="user[initial_points]"
                    value={@create_form["initial_points"]}
                    placeholder="0"
                    class="input input-bordered"
                    min="0"
                  />
                </div>
              </div>

              <div class="modal-action">
                <button
                  type="button"
                  phx-click="hide_create_modal"
                  phx-target={@myself}
                  class="btn btn-ghost"
                >
                  取消
                </button>
                <button type="submit" class="btn btn-primary">创建用户</button>
              </div>
            </form>
          </div>
        </div>
      <% end %>

    <!-- 编辑用户模态框 -->
      <%= if @show_edit_modal and @selected_user do %>
        <div class="modal modal-open">
          <div class="modal-box w-11/12 max-w-2xl">
            <h3 class="font-bold text-lg mb-4">编辑用户 - {to_string(@selected_user.username)}</h3>

    <!-- 用户基本信息 -->
            <div class="bg-base-200 p-4 rounded-lg mb-4">
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-base-content/60">数字ID:</span>
                  <span class="font-mono">{@selected_user.numeric_id}</span>
                </div>

                <div>
                  <span class="text-base-content/60">当前积分:</span>
                  <span class="font-medium">
                    {Cypridina.Accounts.get_user_points(@selected_user.id)}
                  </span>
                </div>
                <div>
                  <span class="text-base-content/60">注册时间:</span>
                  <span>{TimeHelper.format_local_date(@selected_user.inserted_at)}</span>
                </div>
              </div>
            </div>

            <form phx-submit="update_user" phx-target={@myself} class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- 权限级别 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">权限级别</span>
                  </label>
                  <select name="user[permission_level]" class="select select-bordered">
                    <option value="0" selected={@edit_form["permission_level"] == "0"}>普通用户</option>
                    <option value="1" selected={@edit_form["permission_level"] == "1"}>管理员</option>
                    <option value="2" selected={@edit_form["permission_level"] == "2"}>超级管理员</option>
                  </select>
                </div>

    <!-- 身份 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">身份</span>
                  </label>
                  <select name="user[user_type]" class="select select-bordered">
                    <option value="normal" selected={@edit_form["user_type"] == "normal"}>
                      普通用户
                    </option>
                    <option value="agent" selected={@edit_form["user_type"] == "agent"}>代理</option>
                  </select>
                </div>

    <!-- 积分调整 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">积分调整</span>
                    <span class="label-text-alt">正数增加，负数减少</span>
                  </label>
                  <input
                    type="number"
                    name="user[points_adjustment]"
                    value={@edit_form["points_adjustment"]}
                    placeholder="0"
                    class="input input-bordered"
                  />
                </div>

    <!-- 调整原因 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">调整原因</span>
                  </label>
                  <input
                    type="text"
                    name="user[adjustment_reason]"
                    value={@edit_form["adjustment_reason"]}
                    placeholder="请输入调整原因"
                    class="input input-bordered"
                  />
                </div>
              </div>

              <div class="modal-action">
                <button
                  type="button"
                  phx-click="hide_edit_modal"
                  phx-target={@myself}
                  class="btn btn-ghost"
                >
                  取消
                </button>
                <button type="submit" class="btn btn-primary">保存更改</button>
              </div>
            </form>
          </div>
        </div>
      <% end %>
    </div>
    """
  end
end
