defmodule RacingGame.Live.RaceControlLive do
  @moduledoc """
  比赛控制页面 - LiveView 实现，支持自动排名控制和用户拖拽排名
  """
  use CypridinaWeb, :live_view
  alias RacingGame.{Race, RaceController, Bet, StockManagementService}
  require Logger
  import Ash.Query

  # 动物信息
  @animals [
    %{id: "A", name: "饿小宝", image: "饿小宝.png"},
    %{id: "B", name: "盒马", image: "盒马.png"},
    %{id: "C", name: "票票", image: "票票.png"},
    %{id: "D", name: "虾仔", image: "虾仔.png"},
    %{id: "E", name: "支小宝", image: "支小宝.png"},
    %{id: "F", name: "欢猩", image: "欢猩.png"}
  ]

  def mount(_params, _session, socket) do
    # 订阅比赛相关消息和股票变化消息
    if connected?(socket) do
      Phoenix.PubSub.subscribe(Cypridina.PubSub, RaceController.race_topic())
      # 订阅股票变化事件
      Phoenix.PubSub.subscribe(Cypridina.PubSub, "stock")
    end

    # 获取当前比赛
    current_race = RaceController.get_current_race()

    # 获取比赛系统状态
    racing_system_status = RaceController.get_racing_system_status()

    # 获取股票统计数据
    stock_statistics = get_stock_statistics()

    {:ok,
     socket
     |> assign(:animals, @animals)
     |> assign(:current_race, current_race)
     |> assign(:total_bets, RaceController.get_total_bets())
     |> assign(:auto_ranking, [])
     |> assign(:user_preset_ranking, RaceController.get_user_preset_ranking())
     |> assign(:control_form, to_form(%{}, as: :control))
     |> assign(:stock_statistics, stock_statistics)
     |> assign(:racing_system_status, racing_system_status)}
  end

  def render(assigns) do
    ~H"""
    <div class="race-control-panel">
      <!-- 返回按钮 -->
      <div class="mb-4">
        <.link
          href="/admin_panel"
          class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <!-- 返回箭头图标 -->
          <svg
            class="w-4 h-4 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            />
          </svg>
          返回管理后台
        </.link>
      </div>

    <!-- 页面标题 -->
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">比赛控制面板</h1>
        <p class="text-gray-600">管理当前比赛状态和控制游戏流程</p>
      </div>

    <!-- 当前比赛状态 - 模仿champion-selection样式 -->
      <div class="current-race-status mb-6">
        <h3 class="text-lg font-semibold mb-2">当前比赛状态</h3>
        <%= if @current_race do %>
          <div class="champion-selection">
            <div class="animal-grid">
              <%= for animal <- @animals do %>
                <div class="animal-card">
                  <div class="animal-display-area">
                    <img
                      src={"/images/#{animal.image}"}
                      class="animal-avatar"
                      alt={"#{animal.name}头像"}
                    />
                    <div class="animal-name">
                      {animal.name}
                    </div>

                    <div class="animal-bet-amount highlight-price">
                      <span class="bet-label">身价:</span>
                      <span class="bet-value">
                        {Map.get(@current_race.bet_amount_map || %{}, animal.id, 100)}
                      </span>
                    </div>

                    <div class="total-bet-amount">
                      <span class="bet-label">总下注:</span>
                      <span class="bet-value">
                        {Map.get(@total_bets, animal.id, 0)}
                      </span>
                    </div>

    <!-- 股票统计信息 -->
                    <% stock_stat = Map.get(@stock_statistics, animal.id, %{}) %>
                    <div class="stock-statistics">
                      <div class="stock-stat-row">
                        <span class="stat-label">持仓:</span>
                        <span class="stat-value net-position">
                          {Map.get(stock_stat, :net_position, 0)} 股
                        </span>
                      </div>
                      <div class="stock-stat-row highlight-average-cost">
                        <span class="stat-label">平均成本:</span>
                        <span class="stat-value average-cost">
                          {Float.round(Map.get(stock_stat, :average_cost, 0.0), 1)} 积分
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% else %>
          <p class="text-gray-500">暂无进行中的比赛</p>
        <% end %>
      </div>

    <!-- 比赛结果控制 -->
      <div class="race-control mb-6">
        <h3 class="text-lg font-semibold mb-2">比赛结果控制</h3>

    <!-- 自动排名显示 -->
        <%= if @auto_ranking != [] do %>
          <div class={"auto-ranking mb-4 p-4 bg-green-50 rounded-lg #{if @user_preset_ranking == [] or @user_preset_ranking == nil, do: "highlight-active", else: ""}"}>
            <h4 class="font-medium mb-2 text-green-800">系统自动排名 (基于总下注最少赔付)</h4>
            <div class="ranking-display flex flex-wrap gap-2">
              <%= for {animal_id, position} <- Enum.with_index(@auto_ranking, 1) do %>
                <% animal = Enum.find(@animals, &(&1.id == animal_id)) %>
                <div class="ranking-item flex items-center bg-white p-2 rounded border">
                  <span class="ranking-number bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-2">
                    {position}
                  </span>
                  <img src={"/images/#{animal.image}"} class="w-8 h-8 mr-2" alt={animal.name} />
                  <span class="text-sm">{animal.name}</span>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>

    <!-- 用户拖拽排名 -->
        <div class={"user-ranking mb-4 p-4 bg-blue-50 rounded-lg #{if @user_preset_ranking != [] and @user_preset_ranking != nil, do: "highlight-active", else: ""}"}>
          <h4 class="font-medium mb-2 text-blue-800">用户预设排名 (拖拽调整)</h4>
          <div class="ranking-control">
            <div
              id="sortable-ranking"
              class="sortable-container flex flex-wrap gap-2"
              phx-hook="SortableRanking"
            >
              <%= if @user_preset_ranking != [] do %>
                <%= for animal_id <- @user_preset_ranking do %>
                  <% animal = Enum.find(@animals, fn a -> a.id == animal_id end) %>
                  <%= if animal do %>
                    <div
                      class="draggable-animal bg-white p-3 rounded border cursor-move hover:shadow-md transition-shadow"
                      data-animal-id={animal_id}
                    >
                      <div class="flex items-center">
                        <img src={"/images/#{animal.image}"} class="w-10 h-10 mr-2" alt={animal.name} />
                        <div>
                          <div class="font-medium text-sm">{animal.name}</div>
                          <div class="text-xs text-gray-500">拖拽排序</div>
                        </div>
                      </div>
                    </div>
                  <% end %>
                <% end %>
              <% else %>
                <%= for animal <- @animals do %>
                  <div
                    class="draggable-animal bg-white p-3 rounded border cursor-move hover:shadow-md transition-shadow"
                    data-animal-id={animal.id}
                  >
                    <div class="flex items-center">
                      <img src={"/images/#{animal.image}"} class="w-10 h-10 mr-2" alt={animal.name} />
                      <div>
                        <div class="font-medium text-sm">{animal.name}</div>
                        <div class="text-xs text-gray-500">拖拽排序</div>
                      </div>
                    </div>
                  </div>
                <% end %>
              <% end %>
            </div>
            <div class="mt-2 flex gap-2">
              <%!-- <button
                phx-click="apply_auto_ranking"
                class="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
              >
                应用自动排名
              </button> --%>
              <button phx-click="clear_user_ranking" class="control-btn control-btn-secondary">
                清除用户预设排名
              </button>
            </div>
          </div>
        </div>

    <!-- 游戏控制功能 -->
          <div class="game-control mb-4 p-4 bg-red-50 rounded-lg">
            <h4 class="font-medium mb-3 text-red-800">游戏控制</h4>
            <div class="control-buttons flex flex-wrap gap-3">
              <button
                phx-click="reset_animal_prices"
                class="control-btn control-btn-primary"
                onclick="return confirm('确定要重置所有动物身价到预设值吗？')"
              >
                重置身价
              </button>

              <button
                phx-click="force_liquidate_stocks"
                class="control-btn control-btn-warning"
                onclick="return confirm('确定要强制所有用户平仓吗？此操作将卖出所有股票并返还积分！')"
              >
                强制平仓
              </button>

              <!-- 比赛控制按钮 -->
              <div class="race-control-buttons">
                <%= if @current_race do %>
                  <%= case @current_race.status do %>
                    <% 0 -> %>
                      <!-- 比赛等待中，可以开始比赛 -->
                      <%!-- <button
                        phx-click="start_race"
                        class="control-btn control-btn-primary"
                        onclick="return confirm('确定要开始比赛吗？')"
                      >
                        开始比赛
                      </button> --%>
                      <%= if @racing_system_status.auto_start_enabled do %>
                        <button
                          phx-click="stop_racing_system"
                          class="control-btn control-btn-warning"
                          onclick="return confirm('确定要停止比赛系统吗？当前比赛结束后将不再开启新比赛。')"
                        >
                          停止比赛
                        </button>
                      <% else %>
                        <button
                          phx-click="start_racing_system"
                          class="control-btn control-btn-primary"
                          onclick="return confirm('确定要启动比赛系统吗？将重新开启自动比赛。')"
                        >
                          启动比赛
                        </button>
                      <% end %>
                    <% 1 -> %>
                      <!-- 比赛进行中，只能停止比赛系统 -->
                      <%= if @racing_system_status.auto_start_enabled do %>
                        <button
                          phx-click="stop_racing_system"
                          class="control-btn control-btn-warning"
                          onclick="return confirm('确定要停止比赛系统吗？当前比赛结束后将不再开启新比赛。')"
                        >
                          停止比赛
                        </button>
                      <% else %>
                        <span class="control-btn control-btn-secondary" style="cursor: default;">
                          比赛系统已停止
                        </span>
                        <button
                          phx-click="start_racing_system"
                          class="control-btn control-btn-primary"
                          onclick="return confirm('确定要启动比赛系统吗？将重新开启自动比赛。')"
                        >
                          启动比赛
                        </button>
                      <% end %>
                    <% 2 -> %>
                      <!-- 比赛已结束 -->
                      <%= if @racing_system_status.auto_start_enabled do %>
                        <span class="control-btn control-btn-secondary" style="cursor: default;">
                          比赛已结束
                        </span>
                        <button
                          phx-click="stop_racing_system"
                          class="control-btn control-btn-warning"
                          onclick="return confirm('确定要停止比赛系统吗？将不再开启新比赛。')"
                        >
                          停止比赛
                        </button>
                      <% else %>
                        <span class="control-btn control-btn-secondary" style="cursor: default;">
                          比赛系统已停止
                        </span>
                        <button
                          phx-click="start_racing_system"
                          class="control-btn control-btn-primary"
                          onclick="return confirm('确定要启动比赛系统吗？将重新开启自动比赛。')"
                        >
                          启动比赛
                        </button>
                      <% end %>
                    <% _ -> %>
                      <!-- 未知状态 -->
                      <span class="control-btn control-btn-secondary" style="cursor: default;">
                        状态未知
                      </span>
                  <% end %>
                <% else %>
                  <!-- 没有当前比赛 -->
                  <%= if @racing_system_status.auto_start_enabled do %>
                    <button
                      phx-click="create_new_race"
                      class="control-btn control-btn-primary"
                      onclick="return confirm('确定要创建新比赛吗？')"
                    >
                      创建新比赛
                    </button>
                    <button
                      phx-click="stop_racing_system"
                      class="control-btn control-btn-warning"
                      onclick="return confirm('确定要停止比赛系统吗？将不再开启新比赛。')"
                    >
                      停止比赛
                    </button>
                  <% else %>
                    <span class="control-btn control-btn-secondary" style="cursor: default;">
                      比赛系统已停止
                    </span>
                    <button
                      phx-click="start_racing_system"
                      class="control-btn control-btn-primary"
                      onclick="return confirm('确定要启动比赛系统吗？将重新开启自动比赛。')"
                    >
                      启动比赛
                    </button>
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>
      </div>
      <!-- 添加样式 -->
      <style>
        /* 比赛控制面板样式 */
        .race-control-panel {
          padding: 20px;
          max-width: 1200px;
        }

        .control-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          padding-bottom: 10px;
          border-bottom: 1px solid #ddd;
        }

        .race-status {
          font-weight: bold;
        }

        .status-waiting { color: #6c757d; }
        .status-betting { color: #007bff; }
        .status-running { color: #28a745; }
        .status-ended { color: #dc3545; }

        .current-race-info {
          background: #f8f9fa;
          padding: 15px;
          border-radius: 5px;
          margin-bottom: 20px;
        }

        .control-buttons {
          display: flex;
          gap: 10px;
          margin-bottom: 30px;
        }

        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        }

        .btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-info { background: #17a2b8; color: white; }

        .recent-races {
          margin-top: 20px;
        }

        .table {
          width: 100%;
          border-collapse: collapse;
          margin-top: 10px;
        }

        .table th,
        .table td {
          padding: 8px 12px;
          text-align: left;
          border-bottom: 1px solid #ddd;
        }

        .table th {
          background: #f8f9fa;
          font-weight: bold;
        }

        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal-content {
          background: white;
          padding: 20px;
          border-radius: 5px;
          min-width: 400px;
        }

        .form-group {
          margin-bottom: 15px;
        }

        .form-group label {
          display: block;
          margin-bottom: 5px;
          font-weight: bold;
        }

        .form-control {
          width: 100%;
          padding: 8px;
          border: 1px solid #ddd;
          border-radius: 4px;
        }

        .modal-actions {
          display: flex;
          gap: 10px;
          justify-content: flex-end;
          margin-top: 20px;
        }

        .champion-selection {
          padding: 8px;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(255, 140, 56, 0.2);
          overflow: visible;
          height: auto;
        }

        .animal-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;
          padding: 8px;
        }

        .animal-card {
          background: linear-gradient(135deg, #fff8f0 0%, #fff4e6 100%);
          border: 2px solid #ffb273;
          border-radius: 12px;
          padding: 12px;
          text-align: center;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .animal-display-area {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
        }

        .animal-avatar {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          border: 3px solid #ff8c38;
          object-fit: cover;
        }

        .animal-name {
          font-weight: bold;
          color: #ff7a00;
          font-size: 14px;
        }

        .animal-bet-amount, .total-bet-amount {
          display: flex;
          justify-content: space-between;
          width: 100%;
          font-size: 12px;
        }

        .bet-label {
          color: #666;
        }

        /* 身价高亮样式 */
        .highlight-price {
          background: linear-gradient(135deg, rgba(255, 122, 0, 0.1), rgba(255, 149, 0, 0.05));
          border: 2px solid rgba(255, 122, 0, 0.3);
          border-radius: 8px;
          padding: 8px 12px;
          margin: 4px 0;
          box-shadow: 0 2px 8px rgba(255, 122, 0, 0.2);
          animation: pulse-orange 2s infinite;
        }

        @keyframes pulse-orange {
          0% { box-shadow: 0 2px 8px rgba(255, 122, 0, 0.2); }
          50% { box-shadow: 0 4px 16px rgba(255, 122, 0, 0.4); }
          100% { box-shadow: 0 2px 8px rgba(255, 122, 0, 0.2); }
        }

        .bet-value {
          font-weight: bold;
          color: #ff7a00;
          font-size: 14px;
          background: linear-gradient(135deg, #ff7a00, #ff9500);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          text-shadow: 0 0 10px rgba(255, 122, 0, 0.3);
        }

        /* 股票统计样式 */
        .stock-statistics {
          width: 100%;
          margin-top: 8px;
          padding-top: 8px;
          border-top: 1px solid #ffb273;
        }

        .stock-stat-row {
          display: flex;
          justify-content: space-between;
          width: 100%;
          font-size: 11px;
          margin-bottom: 2px;
        }

        .stat-label {
          color: #666;
          font-size: 10px;
        }

        .stat-value {
          font-weight: bold;
          font-size: 11px;
        }

        .stat-value.net-position {
          color: #2563eb;
        }

        .stat-value.average-cost {
          color: #7c3aed;
          font-size: 12px;
          font-weight: 900;
          background: linear-gradient(135deg, #7c3aed, #a855f7);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          text-shadow: 0 0 8px rgba(124, 58, 237, 0.4);
          border: 1px solid rgba(124, 58, 237, 0.2);
          padding: 2px 6px;
          border-radius: 4px;
          background-color: rgba(124, 58, 237, 0.05);
        }

        /* 平均成本高亮样式 */
        .highlight-average-cost {
          background: linear-gradient(135deg, rgba(124, 58, 237, 0.1), rgba(168, 85, 247, 0.05));
          border: 2px solid rgba(124, 58, 237, 0.3);
          border-radius: 6px;
          padding: 6px 10px;
          margin: 2px 0;
          box-shadow: 0 2px 6px rgba(124, 58, 237, 0.2);
          animation: pulse-purple 2.5s infinite;
        }

        @keyframes pulse-purple {
          0% { box-shadow: 0 2px 6px rgba(124, 58, 237, 0.2); }
          50% { box-shadow: 0 3px 12px rgba(124, 58, 237, 0.4); }
          100% { box-shadow: 0 2px 6px rgba(124, 58, 237, 0.2); }
        }

        .sortable-container {
          min-height: 100px;
          border: 2px dashed #ddd;
          border-radius: 8px;
          padding: 10px;
        }

        .draggable-animal {
          transition: transform 0.2s ease;
        }

        .draggable-animal:hover {
          transform: translateY(-2px);
        }

        .draggable-animal.sortable-ghost {
          opacity: 0.5;
        }

        /* 高亮激活状态的红圈效果 */
        .highlight-active {
          position: relative;
          border: 3px solid #ef4444 !important;
          box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2), 0 0 20px rgba(239, 68, 68, 0.3) !important;
          animation: pulse-red 2s infinite;
        }

        .highlight-active::before {
          content: '';
          position: absolute;
          top: -6px;
          left: -6px;
          right: -6px;
          bottom: -6px;
          border: 2px solid #ef4444;
          border-radius: 12px;
          animation: pulse-border 2s infinite;
          pointer-events: none;
        }

        @keyframes pulse-red {
          0%, 100% {
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2), 0 0 20px rgba(239, 68, 68, 0.3);
          }
          50% {
            box-shadow: 0 0 0 6px rgba(239, 68, 68, 0.1), 0 0 30px rgba(239, 68, 68, 0.4);
          }
        }

        @keyframes pulse-border {
          0%, 100% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.7;
            transform: scale(1.02);
          }
        }

        /* 自定义按钮样式 - 确保高对比度和可见性 */
        .control-btn {
          padding: 8px 16px;
          border: none;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          border: 1px solid transparent;
          text-decoration: none;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          min-height: 36px;
        }

        .control-btn:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .control-btn:active {
          transform: translateY(0);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .control-btn-primary {
          background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
          color: white;
          border-color: #2563eb;
        }

        .control-btn-primary:hover {
          background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
          border-color: #1d4ed8;
        }

        .control-btn-warning {
          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
          color: white;
          border-color: #d97706;
        }

        .control-btn-warning:hover {
          background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
          border-color: #b45309;
        }

        .control-btn-danger {
          background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
          color: white;
          border-color: #dc2626;
        }

        .control-btn-danger:hover {
          background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
          border-color: #b91c1c;
        }

        .control-btn-secondary {
          background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
          color: white;
          border-color: #4b5563;
        }

        .control-btn-secondary:hover {
          background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
          border-color: #374151;
        }

        /* 确保按钮在所有主题下都有足够的对比度 */
        .control-btn {
          opacity: 1 !important;
          visibility: visible !important;
        }

        /* 禁用状态 */
        .control-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .control-btn:disabled:hover {
          transform: none;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        /* 比赛控制按钮样式 */
        .race-control-buttons {
          display: flex;
          gap: 10px;
          margin-top: 15px;
          flex-wrap: wrap;
        }

        .race-control-buttons button {
          transition: all 0.2s ease;
          font-weight: 500;
          border: none;
          cursor: pointer;
        }

        .race-control-buttons button:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .race-control-buttons button:active {
          transform: translateY(0);
        }

        .race-control-buttons span {
          display: inline-block;
          padding: 8px 16px;
          border-radius: 4px;
          font-weight: 500;
        }
      </style>
    </div>
    """
  end

  def handle_event("clear_user_ranking", _params, socket) do
    # 清除 RaceController 中的用户预设
    RaceController.clear_user_preset_ranking()

    {:noreply, assign(socket, :user_preset_ranking, [])}
  end

  def handle_event("update_ranking", %{"ranking" => ranking_list}, socket) do
    # 处理拖拽排序结果，设置到 RaceController
    RaceController.set_user_preset_ranking(ranking_list)

    {:noreply, assign(socket, :user_preset_ranking, ranking_list)}
  end

  def handle_event("apply_auto_ranking", _params, socket) do
    # 应用自动排名到用户预设
    auto_ranking = socket.assigns.auto_ranking
    RaceController.set_user_preset_ranking(auto_ranking)

    {:noreply, assign(socket, :user_preset_ranking, auto_ranking)}
  end

  def handle_event("reset_animal_prices", _params, socket) do
    case RaceController.reset_animal_prices() do
      :ok ->
        {:noreply,
         socket
         |> put_flash(:info, "身价将在下一把重置到预设值")}

      {:error, reason} ->
        Logger.error("重置动物身价失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "重置失败，请重试")}
    end
  end

  def handle_event("force_liquidate_stocks", _params, socket) do
    case RaceController.force_liquidate_all_stocks() do
      {:ok, result} ->
        base_message =
          "强制平仓完成！清算了 #{result.total_users} 个用户的 #{result.total_stocks_liquidated} 份股票，返还 #{result.total_points_returned} 积分"

        # 记录详细的强制平仓信息到日志
        Logger.info("管理员强制平仓执行完成:")
        Logger.info("- 受影响用户数: #{result.total_users}")
        Logger.info("- 清算股票总数: #{result.total_stocks_liquidated}")
        Logger.info("- 返还积分总额: #{result.total_points_returned}")
        Logger.info("- 触发原因: #{result.trigger_reason}")

        if length(result.failed_liquidations) > 0 do
          Logger.warning("强制平仓失败的用户: #{inspect(result.failed_liquidations)}")
        end

        {:noreply,
         socket
         |> put_flash(:info, base_message)
         |> assign(:current_race, RaceController.get_current_race())
         |> assign(:total_bets, RaceController.get_total_bets())}

      {:error, reason} ->
        Logger.error("强制平仓失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "强制平仓失败，请重试")}
    end
  end

  def handle_event("start_race", _params, socket) do
    case socket.assigns.current_race do
      nil ->
        {:noreply, put_flash(socket, :error, "当前没有比赛可以开始")}

      race when race.status != 0 ->
        {:noreply, put_flash(socket, :error, "比赛已经开始或已结束")}

      race ->
        case start_race_immediately(race) do
          {:ok, updated_race} ->
            {:noreply,
             socket
             |> put_flash(:info, "比赛已开始")
             |> assign(:current_race, updated_race)}

          {:error, reason} ->
            Logger.error("开始比赛失败: #{inspect(reason)}")
            {:noreply, put_flash(socket, :error, "开始比赛失败，请重试")}
        end
    end
  end

  def handle_event("stop_race", _params, socket) do
    case socket.assigns.current_race do
      nil ->
        {:noreply, put_flash(socket, :error, "当前没有进行中的比赛")}

      race when race.status != 1 ->
        {:noreply, put_flash(socket, :error, "比赛未在进行中")}

      race ->
        # 获取最终排名（优先级：用户预设 > 自动排名 > 随机）
        final_ranking = get_final_ranking(socket.assigns)

        case stop_race_immediately(race, final_ranking) do
          {:ok, updated_race} ->
            {:noreply,
             socket
             |> put_flash(:info, "比赛已停止，排名：#{Enum.join(final_ranking, " > ")}")
             |> assign(:current_race, updated_race)}

          {:error, reason} ->
            Logger.error("停止比赛失败: #{inspect(reason)}")
            {:noreply, put_flash(socket, :error, "停止比赛失败，请重试")}
        end
    end
  end

  def handle_event("create_new_race", _params, socket) do
    case socket.assigns.current_race do
      nil ->
        case create_new_race_immediately() do
          {:ok, new_race} ->
            {:noreply,
             socket
             |> put_flash(:info, "新比赛已创建")
             |> assign(:current_race, new_race)
             |> assign(:total_bets, RaceController.get_total_bets())}

          {:error, reason} ->
            Logger.error("创建新比赛失败: #{inspect(reason)}")
            {:noreply, put_flash(socket, :error, "创建新比赛失败，请重试")}
        end

      _race ->
        {:noreply, put_flash(socket, :error, "已有比赛在进行中，无法创建新比赛")}
    end
  end

  def handle_event("stop_racing_system", _params, socket) do
    case RaceController.stop_racing_system() do
      :ok ->
        # 更新本地状态
        racing_system_status = RaceController.get_racing_system_status()

        {:noreply,
         socket
         |> put_flash(:info, "比赛系统已停止，当前比赛结束后将不再开启新比赛")
         |> assign(:racing_system_status, racing_system_status)}

      {:error, reason} ->
        Logger.error("停止比赛系统失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "操作失败，请重试")}
    end
  end

  def handle_event("start_racing_system", _params, socket) do
    case RaceController.start_racing_system() do
      :ok ->
        # 更新本地状态
        racing_system_status = RaceController.get_racing_system_status()

        {:noreply,
         socket
         |> put_flash(:info, "比赛系统已启动，将重新开启自动比赛")
         |> assign(:racing_system_status, racing_system_status)}

      {:error, reason} ->
        Logger.error("启动比赛系统失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "操作失败，请重试")}
    end
  end

  # 处理统一的游戏数据更新事件
  def handle_info({:game_data_update, game_data}, socket) do
    Logger.info(
      "🔄 [GAME_DATA_UPDATE] 收到游戏数据更新: #{inspect(game_data.event_type)}, 当前比赛: #{inspect(game_data.current_race)}"
    )

    # 获取最新的比赛系统状态
    racing_system_status = RaceController.get_racing_system_status()

    case game_data.event_type do
      :new_race ->
        {:noreply,
         socket
         |> assign(current_race: game_data.current_race)
         |> assign(total_bets: game_data.total_bets)
         |> assign(auto_ranking: game_data.ranking_control.auto_ranking || [])
         |> assign(user_preset_ranking: game_data.ranking_control.user_preset_ranking || [])
         |> assign(racing_system_status: racing_system_status)}

      :race_started ->
        {:noreply,
         socket
         |> assign(current_race: game_data.current_race)
         |> assign(racing_system_status: racing_system_status)}

      :race_ended ->
        {:noreply,
         socket
         |> assign(current_race: game_data.current_race)
         |> assign(total_bets: game_data.total_bets)
         |> assign(auto_ranking: [])
         |> assign(user_preset_ranking: [])
         |> assign(racing_system_status: racing_system_status)}

      :race_force_ended ->
        # 比赛被强制结束
        Logger.info("🔄 [RACE_CONTROL] 收到比赛强制结束事件")

        {:noreply,
         socket
         |> assign(current_race: game_data.current_race)
         |> assign(total_bets: game_data.total_bets)
         |> assign(auto_ranking: [])
         |> assign(user_preset_ranking: [])
         |> put_flash(:info, "比赛已强制结束")}

      :animal_prices_reset ->
        {:noreply,
         socket
         |> assign(current_race: game_data.current_race)
         |> assign(total_bets: game_data.total_bets)}

      :force_liquidation_completed ->
        result = game_data.extra_data

        message =
          "系统强制平仓完成：#{result.total_users} 用户，#{result.total_stocks_liquidated} 股票，#{result.total_points_returned} 积分"

        # 强制平仓后需要重新获取股票统计
        updated_stock_statistics = get_stock_statistics()

        {:noreply,
         socket
         |> put_flash(:info, message)
         |> assign(total_bets: game_data.total_bets)
         |> assign(stock_statistics: updated_stock_statistics)}

      :bet_placed ->
        # 当有新的下注时，实时更新总下注统计和自动排名
        current_race = socket.assigns.current_race

        if current_race && current_race.issue == game_data.current_race.issue do
          {:noreply,
           socket
           |> assign(total_bets: game_data.total_bets)
           |> assign(auto_ranking: game_data.ranking_control.auto_ranking || [])
           |> assign(user_preset_ranking: game_data.ranking_control.user_preset_ranking || [])}
        else
          {:noreply, socket}
        end

      :user_preset_ranking_changed ->
        # 用户预设排名发生变化
        ranking = game_data.extra_data.ranking
        Logger.info("🔄 [RACE_CONTROL] 收到用户预设排名变化: #{inspect(ranking)}")

        {:noreply,
         socket
         |> assign(user_preset_ranking: ranking)
         |> put_flash(:info, "用户预设排名已更新：#{Enum.join(ranking, " > ")}")}

      :user_preset_ranking_cleared ->
        # 用户预设排名被清除
        Logger.info("🔄 [RACE_CONTROL] 收到用户预设排名清除事件")

        {:noreply,
         socket
         |> assign(user_preset_ranking: [])
         |> put_flash(:info, "用户预设排名已清除")}

      _ ->
        {:noreply, socket}
    end
  end

  # 处理直接的比赛强制结束事件（兼容性处理）
  def handle_info({:race_force_ended, race}, socket) do
    Logger.info("🔄 [RACE_CONTROL] 收到直接比赛强制结束事件: #{inspect(race.issue)}")

    {:noreply,
     socket
     |> assign(current_race: nil)
     |> assign(total_bets: %{})
     |> assign(auto_ranking: [])
     |> assign(user_preset_ranking: [])
     |> put_flash(:info, "比赛已强制结束，排名：#{Enum.join(race.positions, " > ")}")}
  end

  def handle_info(
        %Phoenix.Socket.Broadcast{topic: "stock", event: event, payload: notification},
        socket
      ) do
    Logger.info(
      "🔄 [RACE_CONTROL] 收到股票变化PubSub消息: #{inspect(event)}, 动物: #{inspect(notification.data.racer_id)}"
    )

    # 使用通知数据增量更新股票统计
    updated_statistics =
      update_stock_statistics_from_notification(
        socket.assigns.stock_statistics,
        notification
      )

    {:noreply, assign(socket, :stock_statistics, updated_statistics)}
  end

  # 私有函数
  defp get_final_ranking(assigns) do
    cond do
      assigns.user_preset_ranking != [] ->
        assigns.user_preset_ranking

      assigns.auto_ranking != [] ->
        assigns.auto_ranking

      true ->
        # 随机排名
        Enum.shuffle(["A", "B", "C", "D", "E", "F"])
    end
  end

  defp force_end_race_with_ranking(race, ranking) do
    # 生成默认的速度和时间数据
    default_speeds = %{
      "A" => 50.0,
      "B" => 48.0,
      "C" => 46.0,
      "D" => 44.0,
      "E" => 42.0,
      "F" => 40.0
    }

    default_times = %{
      "A" => 120.0,
      "B" => 125.0,
      "C" => 130.0,
      "D" => 135.0,
      "E" => 140.0,
      "F" => 145.0
    }

    updated_race =
      race
      |> Race.set_result!(%{
        positions: ranking,
        speeds: default_speeds,
        end_times: default_times,
        status: 2
      })

    # 比赛强制结束时检查并触发强制平仓
    Logger.info("比赛强制结束，检查是否需要强制平仓...")

    case RacingGame.check_and_trigger_force_liquidation(updated_race) do
      {:ok, result} ->
        Logger.info("比赛强制结束强制平仓检查完成: #{inspect(result)}")

        # 如果执行了强制平仓，广播事件
        case result do
          %{total_users: users} when users > 0 ->
            Phoenix.PubSub.broadcast(
              Cypridina.PubSub,
              RaceController.race_topic(),
              {:force_liquidation_completed, result}
            )

          _ ->
            :ok
        end

      {:error, reason} ->
        Logger.error("比赛强制结束强制平仓检查失败: #{inspect(reason)}")
    end

    # 广播比赛强制结束事件
    Phoenix.PubSub.broadcast(
      Cypridina.PubSub,
      RaceController.race_topic(),
      {:race_force_ended, updated_race}
    )

    {:ok, updated_race}
  end

  # 使用通知数据增量更新股票统计
  defp update_stock_statistics_from_notification(current_statistics, notification) do
    %{data: stock_data, action: action} = notification
    racer_id = stock_data.racer_id

    # 获取当前该动物的统计数据
    current_stats =
      Map.get(current_statistics, racer_id, %{
        total_bought: 0,
        total_sold: 0,
        total_cost: 0.0,
        total_revenue: 0.0,
        net_position: 0,
        average_cost: 0.0
      })

    # 根据动作类型更新统计数据
    updated_stats =
      case action.name do
        :add ->
          # 买入操作：增加持仓和成本
          new_quantity = stock_data.quantity
          new_total_cost = Decimal.to_float(stock_data.total_cost)

          # 计算新的平均成本
          new_average_cost =
            if new_quantity > 0 do
              new_total_cost / new_quantity
            else
              0.0
            end

          %{
            current_stats
            | total_cost: new_total_cost,
              net_position: new_quantity,
              average_cost: new_average_cost
          }

        :subtract ->
          # 卖出操作：减少持仓，但保持总成本不变（因为是按比例减少）
          new_quantity = stock_data.quantity
          new_total_cost = Decimal.to_float(stock_data.total_cost)

          # 计算新的平均成本
          new_average_cost =
            if new_quantity > 0 do
              new_total_cost / new_quantity
            else
              0.0
            end

          %{
            current_stats
            | total_cost: new_total_cost,
              net_position: new_quantity,
              average_cost: new_average_cost
          }

        _ ->
          # 未知操作，保持原状
          current_stats
      end

    # 更新统计数据
    Map.put(current_statistics, racer_id, updated_stats)
  end

  # 获取股票统计数据 - 基于玩家股票持仓记录的总和
  defp get_stock_statistics do
    # 获取所有动物的股票统计
    @animals
    |> Enum.map(fn animal ->
      # 从玩家股票持仓记录中统计数据
      case get_stock_holdings_statistics(animal.id) do
        %{total_quantity: total_quantity, total_cost: total_cost} ->
          # 计算平均成本 = 总成本 / 总持仓
          average_cost =
            if total_quantity > 0 do
              Decimal.to_float(total_cost) / total_quantity
            else
              0.0
            end

          {animal.id,
           %{
             # 这些字段保留兼容性，但不再使用
             total_bought: 0,
             total_sold: 0,
             total_cost: Decimal.to_float(total_cost),
             total_revenue: 0.0,
             # 净持仓就是当前总持仓
             net_position: total_quantity,
             average_cost: average_cost
           }}

        _ ->
          {animal.id,
           %{
             total_bought: 0,
             total_sold: 0,
             total_cost: 0.0,
             total_revenue: 0.0,
             net_position: 0,
             average_cost: 0.0
           }}
      end
    end)
    |> Enum.into(%{})
  end

  # 获取指定动物的股票持仓统计
  defp get_stock_holdings_statistics(racer_id) do
    import Ash.Query

    case RacingGame.Stock
         |> filter(racer_id == ^racer_id)
         # 只统计有持仓的记录
         |> filter(quantity > 0)
         |> Ash.read() do
      {:ok, stocks} ->
        # 计算总持仓数量和总成本
        total_quantity = Enum.sum(Enum.map(stocks, & &1.quantity))

        total_cost =
          stocks
          |> Enum.map(& &1.total_cost)
          |> Enum.reduce(Decimal.new(0), &Decimal.add/2)

        %{total_quantity: total_quantity, total_cost: total_cost}

      {:error, _} ->
        %{total_quantity: 0, total_cost: Decimal.new(0)}
    end
  end

  # 立即开始比赛
  defp start_race_immediately(race) do
    try do
      # 获取最终排名（优先级：用户预设 > 自动排名 > 随机）
      final_ranking = get_final_ranking_for_race()

      # 更新比赛状态为进行中
      updated_race =
        race
        |> Race.set_result!(%{
          status: 1,
          positions: final_ranking,
          end_times: [],
          speeds: []
        })

      # 广播比赛开始事件
      Phoenix.PubSub.broadcast(
        Cypridina.PubSub,
        RaceController.race_topic(),
        {:race_started, updated_race}
      )

      # 安排比赛在20秒后自动结束
      Process.send_after(self(), {:auto_end_race, updated_race.id}, 20 * 1000)

      {:ok, updated_race}
    rescue
      error ->
        Logger.error("开始比赛失败: #{inspect(error)}")
        {:error, error}
    end
  end

  # 立即停止比赛
  defp stop_race_immediately(race, final_ranking) do
    try do
      # 更新比赛状态为已结束
      updated_race =
        race
        |> Race.set_result!(%{
          status: 2,
          positions: final_ranking,
          end_times: ["16.95", "15.46", "17.92", "16.26", "17.15", "17.73"],
          speeds: ["5.9", "6.47", "5.58", "6.15", "5.83", "5.64"]
        })

      # 处理下注结果（需要通过RaceController调用）
      send(RaceController, {:process_bet_results, updated_race})

      # 广播比赛结束事件
      Phoenix.PubSub.broadcast(
        Cypridina.PubSub,
        RaceController.race_topic(),
        {:race_ended, updated_race}
      )

      {:ok, updated_race}
    rescue
      error ->
        Logger.error("停止比赛失败: #{inspect(error)}")
        {:error, error}
    end
  end

  # 立即创建新比赛
  defp create_new_race_immediately() do
    try do
      # 调用RaceController创建新比赛
      RaceController.start_new_race()

      # 等待一小段时间让比赛创建完成
      Process.sleep(100)

      # 获取新创建的比赛
      case RaceController.get_current_race() do
        nil ->
          {:error, "创建比赛后无法获取比赛信息"}

        new_race ->
          {:ok, new_race}
      end
    rescue
      error ->
        Logger.error("创建新比赛失败: #{inspect(error)}")
        {:error, error}
    end
  end

  # 获取最终排名的辅助函数（用于比赛控制）
  defp get_final_ranking_for_race() do
    # 这里可以实现更复杂的排名逻辑
    # 目前使用随机排名
    ["A", "B", "C", "D", "E", "F"] |> Enum.shuffle()
  end
end
