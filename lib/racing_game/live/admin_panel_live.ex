defmodule RacingGame.Live.AdminPanelLive do
  @moduledoc """
  新的管理后台主页面 - 使用 DaisyUI 实现左侧菜单和右侧内容区域

  功能模块已拆分为独立的 LiveComponent：
  1. 个人信息页 - ProfileComponent
  2. 用户管理（仅限管理员）- UserManagementComponent
  3. 下线管理（仅限代理）- SubordinateManagementComponent
  4. 股票持仓（仅限管理员）- StockHoldingsComponent
  5. 股票买卖记录 - StockTransactionsComponent
  6. 下注记录 - BetRecordsComponent
  """

  use CypridinaWeb, :live_view

  alias CypridinaWeb.AuthHelper

  def mount(_params, _session, socket) do
    user = socket.assigns.current_user
    role = AuthHelper.get_user_role(user)

    # 根据路由action确定当前页面
    current_page =
      case socket.assigns.live_action do
        # 默认跳转到profile
        :index -> "profile"
        action -> Atom.to_string(action)
      end

    socket =
      socket
      |> assign(:current_page, current_page)
      |> assign(:user_role, role)
      |> assign(:page_title, "管理后台")
      # 默认在移动端收起侧边栏
      |> assign(:sidebar_open, false)
      # 根据当前页面设置展开状态
      |> assign(:expanded_categories, get_initial_expanded_categories(current_page))

    # 如果是index路由，重定向到profile
    if socket.assigns.live_action == :index do
      {:ok, push_navigate(socket, to: ~p"/admin_panel/profile")}
    else
      {:ok, socket}
    end
  end

  def handle_params(_params, _url, socket) do
    # 当URL参数变化时更新当前页面
    current_page =
      case socket.assigns.live_action do
        :index -> "profile"
        action -> Atom.to_string(action)
      end

    socket =
      socket
      |> assign(:current_page, current_page)
      |> assign(:expanded_categories, get_initial_expanded_categories(current_page))
      # URL变化后自动收起侧边栏
      |> assign(:sidebar_open, false)

    {:noreply, socket}
  end

  def handle_event("navigate", %{"page" => page}, socket) do
    # 使用push_navigate进行URL导航
    path = "/admin_panel/#{page}"
    {:noreply, push_navigate(socket, to: path)}
  end

  def handle_event("toggle_sidebar", _params, socket) do
    socket = assign(socket, :sidebar_open, !socket.assigns.sidebar_open)
    {:noreply, socket}
  end

  def handle_event("toggle_category", %{"category" => category}, socket) do
    expanded_categories = socket.assigns.expanded_categories

    new_expanded =
      if MapSet.member?(expanded_categories, category) do
        MapSet.delete(expanded_categories, category)
      else
        MapSet.put(expanded_categories, category)
      end

    socket = assign(socket, :expanded_categories, new_expanded)
    {:noreply, socket}
  end

  def handle_info({:flash, type, message}, socket) do
    socket = put_flash(socket, type, message)
    {:noreply, socket}
  end

  # 获取导航菜单结构
  def get_navigation_menu(user) do
    race_menu_items =
      if Cypridina.ProjectMode.current() == :race,
        do: [
          %{
            id: "game_data",
            title: "游戏数据",
            icon: "fas fa-gamepad",
            type: :category,
            visible: true,
            children: [
              %{
                id: "stocks",
                title: "股票持仓",
                icon: "fas fa-chart-area",
                page: "stocks",
                visible: AuthHelper.has_permission?(user, :admin)
              },
              %{
                id: "bet_records",
                title: "下注记录",
                icon: "fas fa-ticket-alt",
                page: "bet_records",
                visible: true
              }
            ]
          }
        ],
        else: []

    (
       [
         %{
           id: "dashboard",
           title: "仪表盘",
           icon: "fas fa-tachometer-alt",
           type: :single,
           page: "profile",
           visible: true
         },
         %{
           id: "user_management",
           title: "用户管理",
           icon: "fas fa-users",
           type: :category,
           visible:
             AuthHelper.has_permission?(user, :admin) or AuthHelper.has_permission?(user, :agent),
           children: [
             %{
               id: "users",
               title: "用户列表",
               icon: "fas fa-user-friends",
               page: "users",
               visible: AuthHelper.has_permission?(user, :admin)
             },
             %{
               id: "subordinates",
               title: "下线管理",
               icon: "fas fa-sitemap",
               page: "subordinates",
               visible: AuthHelper.has_permission?(user, :agent)
             }
           ]
         }
       ] ++ race_menu_items)
    |> filter_visible_items()
  end

  # 过滤可见的菜单项
  defp filter_visible_items(items) do
    items
    |> Enum.filter(& &1.visible)
    |> Enum.map(fn item ->
      if item.type == :category and Map.has_key?(item, :children) do
        visible_children = Enum.filter(item.children, & &1.visible)
        %{item | children: visible_children}
      else
        item
      end
    end)
    |> Enum.filter(fn item ->
      if item.type == :category do
        length(Map.get(item, :children, [])) > 0
      else
        true
      end
    end)
  end

  # 根据当前页面获取初始展开的分类
  def get_initial_expanded_categories(current_page) do
    expanded = MapSet.new()

    # 根据当前页面自动展开对应的分类
    result =
      case current_page do
        page when page in ["users", "subordinates"] ->
          MapSet.put(expanded, "user_management")

        page when page in ["stocks", "bet_records"] ->
          MapSet.put(expanded, "game_data")

        _ ->
          expanded
      end

    # 添加调试日志
    require Logger
    Logger.info("🔍 导航调试 - 页面: #{current_page}, 展开分类: #{inspect(MapSet.to_list(result))}")

    result
  end

  # 检查分类是否展开
  defp category_expanded?(expanded_categories, category_id) do
    MapSet.member?(expanded_categories, category_id)
  end

  # 获取页面标题
  defp get_page_title(item) do
    case item do
      "profile" -> "个人信息管理"
      "users" -> "用户管理"
      "subordinates" -> "下线管理"
      "stocks" -> "股票持仓管理"
      "bet_records" -> "下注记录"
      _ -> "管理后台"
    end
  end
end
