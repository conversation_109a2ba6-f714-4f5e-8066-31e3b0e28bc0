defmodule Mix.Tasks.Cypridina.Races do
  @moduledoc """
  赛马数据管理 Mix 任务

  ## 可用命令:

  ### 清除所有比赛记录
      mix cypridina.races clear_all

  ### 清除指定状态的比赛记录
      mix cypridina.races clear_by_status <status>

  ### 清除指定日期之前的比赛记录
      mix cypridina.races clear_before_date <date>

  ### 统计比赛记录数量
      mix cypridina.races count

  ### 列出最近的比赛记录
      mix cypridina.races list [--limit=10]

  ## 状态说明:
  - 0: 等待开始
  - 1: 进行中
  - 2: 已结束

  ## 示例:

      # 清除所有比赛记录
      mix cypridina.races clear_all

      # 清除已结束的比赛记录
      mix cypridina.races clear_by_status 2

      # 清除2024年1月1日之前的比赛记录
      mix cypridina.races clear_before_date 2024-01-01

      # 统计比赛记录数量
      mix cypridina.races count

      # 列出最近20条比赛记录
      mix cypridina.races list --limit=20
  """

  use Mix.Task

  alias RacingGame.Race
  require Ash.Query

  @shortdoc "赛马数据管理命令行工具"

  def run(args) do
    Mix.Task.run("app.start")

    case args do
      ["clear_all"] -> clear_all_races()
      ["clear_by_status", status] -> clear_races_by_status(status)
      ["clear_before_date", date] -> clear_races_before_date(date)
      ["count"] -> count_races()
      ["list" | rest] -> list_races(rest)
      _ -> show_help()
    end
  end

  defp clear_all_races do
    Mix.shell().info("⚠️  即将清除数据库中的所有比赛记录...")

    if Mix.shell().yes?("确定要继续吗？此操作不可撤销！") do
      # 获取所有比赛记录
      races = Race |> Ash.read!()
      total_count = length(races)

      if total_count == 0 do
        Mix.shell().info("📭 数据库中没有比赛记录")
      else
        Mix.shell().info("🗑️  正在删除 #{total_count} 条比赛记录...")

        # 批量删除所有比赛记录
        Enum.each(races, fn race ->
          race |> Ash.destroy!()
        end)

        Mix.shell().info("✅ 成功清除了 #{total_count} 条比赛记录")
      end
    else
      Mix.shell().info("❌ 操作已取消")
    end
  end

  defp clear_races_by_status(status_str) do
    case Integer.parse(status_str) do
      {status, ""} when status in [0, 1, 2] ->
        status_name =
          case status do
            0 -> "等待开始"
            1 -> "进行中"
            2 -> "已结束"
          end

        Mix.shell().info("⚠️  即将清除状态为 #{status}(#{status_name}) 的比赛记录...")

        if Mix.shell().yes?("确定要继续吗？") do
          # 查询指定状态的比赛记录
          races =
            Race
            |> Ash.Query.filter(status == ^status)
            |> Ash.read!()

          total_count = length(races)

          if total_count == 0 do
            Mix.shell().info("📭 没有找到状态为 #{status}(#{status_name}) 的比赛记录")
          else
            Mix.shell().info("🗑️  正在删除 #{total_count} 条比赛记录...")

            Enum.each(races, fn race ->
              race |> Ash.destroy!()
            end)

            Mix.shell().info("✅ 成功清除了 #{total_count} 条状态为 #{status}(#{status_name}) 的比赛记录")
          end
        else
          Mix.shell().info("❌ 操作已取消")
        end

      _ ->
        Mix.shell().error("❌ 无效的状态值。状态必须是 0(等待开始)、1(进行中) 或 2(已结束)")
    end
  end

  defp clear_races_before_date(date_str) do
    case Date.from_iso8601(date_str) do
      {:ok, date} ->
        datetime = DateTime.new!(date, ~T[00:00:00], "Etc/UTC")

        Mix.shell().info("⚠️  即将清除 #{date_str} 之前的比赛记录...")

        if Mix.shell().yes?("确定要继续吗？") do
          # 查询指定日期之前的比赛记录
          races =
            Race
            |> Ash.Query.filter(start_time < ^datetime)
            |> Ash.read!()

          total_count = length(races)

          if total_count == 0 do
            Mix.shell().info("📭 没有找到 #{date_str} 之前的比赛记录")
          else
            Mix.shell().info("🗑️  正在删除 #{total_count} 条比赛记录...")

            Enum.each(races, fn race ->
              race |> Ash.destroy!()
            end)

            Mix.shell().info("✅ 成功清除了 #{total_count} 条 #{date_str} 之前的比赛记录")
          end
        else
          Mix.shell().info("❌ 操作已取消")
        end

      {:error, _} ->
        Mix.shell().error("❌ 无效的日期格式。请使用 YYYY-MM-DD 格式，例如: 2024-01-01")
    end
  end

  defp count_races do
    races = Race |> Ash.read!()
    total_count = length(races)

    # 按状态统计
    status_counts =
      races
      |> Enum.group_by(& &1.status)
      |> Enum.map(fn {status, races_list} ->
        status_name =
          case status do
            0 -> "等待开始"
            1 -> "进行中"
            2 -> "已结束"
            _ -> "未知状态"
          end

        {status_name, length(races_list)}
      end)
      |> Enum.sort()

    Mix.shell().info("📊 比赛记录统计:")
    Mix.shell().info("   总计: #{total_count} 条记录")

    if total_count > 0 do
      Mix.shell().info("   按状态分布:")

      Enum.each(status_counts, fn {status_name, count} ->
        Mix.shell().info("     #{status_name}: #{count} 条")
      end)
    end
  end

  defp list_races(args) do
    limit = parse_limit_option(args)

    races =
      Race
      |> Ash.Query.sort(start_time: :desc)
      |> Ash.Query.limit(limit)
      |> Ash.read!()

    if length(races) == 0 do
      Mix.shell().info("📭 没有找到比赛记录")
    else
      Mix.shell().info("📋 最近的 #{length(races)} 条比赛记录:")
      Mix.shell().info("")

      Enum.each(races, fn race ->
        status_name =
          case race.status do
            0 -> "等待开始"
            1 -> "进行中"
            2 -> "已结束"
            _ -> "未知"
          end

        start_time =
          if race.start_time do
            race.start_time |> DateTime.to_string()
          else
            "未设置"
          end

        positions =
          if race.positions && length(race.positions) > 0 do
            Enum.join(race.positions, ", ")
          else
            "未设置"
          end

        Mix.shell().info("  期号: #{race.issue}")
        Mix.shell().info("    状态: #{status_name}")
        Mix.shell().info("    开始时间: #{start_time}")
        Mix.shell().info("    排名: #{positions}")
        Mix.shell().info("")
      end)
    end
  end

  defp parse_limit_option(args) do
    case Enum.find(args, &String.starts_with?(&1, "--limit=")) do
      nil ->
        10

      limit_arg ->
        case String.split(limit_arg, "=", parts: 2) do
          [_, limit_str] ->
            case Integer.parse(limit_str) do
              {limit, ""} when limit > 0 -> limit
              _ -> 10
            end

          _ ->
            10
        end
    end
  end

  defp show_help do
    Mix.shell().info(@moduledoc)
  end
end
