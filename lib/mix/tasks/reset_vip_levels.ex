defmodule Mix.Tasks.ResetVipLevels do
  @moduledoc """
  重置VIP等级数据的Mix任务
  
  使用方法:
    mix reset_vip_levels
  
  这个任务会：
  1. 删除所有现有的VIP等级
  2. 重新创建完整的VIP等级列表（VIP0-VIP9）
  3. 验证创建结果
  """
  
  use Mix.Task
  
  alias Teen.GameManagement.VipLevel
  
  @shortdoc "重置VIP等级数据"
  
  def run(_args) do
    # 启动应用
    Mix.Task.run("app.start")
    
    IO.puts("🔄 开始重置VIP等级数据...")
    
    # 删除所有现有的VIP等级
    delete_existing_levels()
    
    # 创建新的VIP等级
    create_new_levels()
    
    # 验证结果
    verify_levels()
    
    IO.puts("\n🎉 VIP等级重置完成！")
  end
  
  defp delete_existing_levels do
    IO.puts("🗑️  删除现有VIP等级...")
    
    case Ash.bulk_destroy(VipLevel, :destroy, %{}, return_errors?: true) do
      %{records: deleted_records} ->
        IO.puts("✅ 已删除 #{length(deleted_records)} 个现有VIP等级")
      {:error, error} ->
        IO.puts("⚠️  删除现有VIP等级时出现错误: #{inspect(error)}")
    end
  end
  
  defp create_new_levels do
    IO.puts("📝 创建新的VIP等级...")
    Teen.GameManagement.seed_default_vip_levels()
  end
  
  defp verify_levels do
    IO.puts("🔍 验证创建结果...")
    
    case VipLevel.list_active_levels() do
      {:ok, vip_levels} ->
        IO.puts("📊 当前VIP等级列表:")
        
        vip_levels
        |> Enum.sort_by(& &1.level)
        |> Enum.each(fn vip_level ->
          recharge_amount = format_amount(vip_level.recharge_requirement)
          daily_bonus = format_amount(vip_level.daily_bonus)
          
          IO.puts("  VIP#{vip_level.level} - #{vip_level.level_name}")
          IO.puts("    充值要求: #{recharge_amount}")
          IO.puts("    每日奖励: #{daily_bonus}")
          IO.puts("    兑换加成: #{vip_level.exchange_rate_bonus}%")
          IO.puts("    充值奖励: #{vip_level.recharge_bonus}%")
          IO.puts("")
        end)
        
        IO.puts("✨ 总计: #{length(vip_levels)} 个VIP等级")
        
      {:error, error} ->
        IO.puts("❌ 验证VIP等级时出现错误: #{inspect(error)}")
    end
  end
  
  defp format_amount(decimal_amount) do
    amount = Decimal.to_integer(decimal_amount)
    cond do
      amount >= 10_000_000 -> "#{div(amount, 10_000_000)}千万分"
      amount >= 1_000_000 -> "#{div(amount, 1_000_000)}百万分"
      amount >= 10_000 -> "#{div(amount, 10_000)}万分"
      amount >= 1_000 -> "#{div(amount, 1_000)}千分"
      true -> "#{amount}分"
    end
  end
end
