defmodule Teen.PaymentSystem do
  @moduledoc """
  支付系统域

  包含支付配置、兑换配置、银行信息、支付渠道、支付网关等功能
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  alias Teen.PaymentSystem.{PaymentConfig, ExchangeConfig, PaymentGateway, PaymentOrder, PaymentService, BankConfig, UserBankCard}

  admin do
    show? true
  end

  resources do
    resource Teen.PaymentSystem.PaymentConfig
    resource Teen.PaymentSystem.ExchangeConfig
    resource Teen.PaymentSystem.PaymentGateway
    resource Teen.PaymentSystem.PaymentOrder
    resource Teen.PaymentSystem.PaymentService
    resource Teen.PaymentSystem.BankConfig
    resource Teen.PaymentSystem.UserBankCard
  end

  # ==================== 业务逻辑函数 ====================

  @doc """
  获取用户可用的支付方式
  """
  def get_available_payment_methods(user_id, amount) do
    with {:ok, configs} <- PaymentConfig.list_active_configs(),
         filtered_configs <- filter_configs_by_amount(configs, amount),
         available_configs <- filter_configs_by_user_eligibility(filtered_configs, user_id) do
      # 按排序和费率排序
      sorted_configs = Enum.sort_by(available_configs, &{&1.sort_order, &1.fee_rate})

      {:ok, sorted_configs}
    end
  end

  @doc """
  选择最优支付网关
  """
  def select_optimal_gateway(payment_type, amount) do
    with {:ok, configs} <- PaymentConfig.list_by_payment_type(payment_type),
         available_configs <- filter_available_configs(configs, amount) do
      # 选择费率最低且状态正常的网关
      optimal_config =
        Enum.min_by(available_configs, fn config ->
          {config.fee_rate, config.sort_order}
        end)

      case optimal_config do
        nil -> {:error, "No available gateway"}
        config -> {:ok, config}
      end
    end
  end

  @doc """
  计算支付费用
  """
  def calculate_payment_fees(amount, payment_config) do
    fee_amount = Decimal.mult(amount, Decimal.div(payment_config.fee_rate, 100))
    deduction_amount = Decimal.mult(amount, Decimal.div(payment_config.deduction_rate, 100))

    %{
      original_amount: amount,
      fee_rate: payment_config.fee_rate,
      fee_amount: fee_amount,
      deduction_rate: payment_config.deduction_rate,
      deduction_amount: deduction_amount,
      total_cost: Decimal.add(amount, fee_amount),
      net_amount: Decimal.sub(amount, deduction_amount)
    }
  end

  @doc """
  获取兑换配置
  """
  def get_exchange_config(exchange_type, user_vip_level \\ 0) do
    config_name =
      case exchange_type do
        1 -> "game_exchange"
        2 -> "promotion_exchange"
        _ -> "default_exchange"
      end

    case ExchangeConfig.get_by_name(config_name) do
      {:ok, [config]} ->
        # 根据VIP等级调整配置
        adjusted_config = adjust_config_for_vip(config, user_vip_level)
        {:ok, adjusted_config}

      {:ok, []} ->
        {:error, "Exchange config not found"}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  验证兑换资格
  """
  def validate_exchange_eligibility(user_id, amount, exchange_type) do
    with {:ok, config} <- get_exchange_config(exchange_type),
         :ok <- validate_amount_limits(amount, config),
         :ok <- validate_daily_limits(user_id, amount, config),
         :ok <- validate_user_requirements(user_id, config) do
      {:ok, config}
    end
  end

  @doc """
  计算兑换金额
  """
  def calculate_exchange_amount(coins, exchange_config) do
    # 基础兑换比例
    base_amount = Decimal.div(coins, exchange_config.exchange_rate)

    # 扣除手续费
    fee_amount = Decimal.mult(base_amount, Decimal.div(exchange_config.fee_rate, 100))

    # 扣除税费
    tax_amount = Decimal.mult(base_amount, Decimal.div(exchange_config.tax_rate, 100))

    # 最终金额
    final_amount = Decimal.sub(Decimal.sub(base_amount, fee_amount), tax_amount)

    %{
      coins: coins,
      exchange_rate: exchange_config.exchange_rate,
      base_amount: base_amount,
      fee_rate: exchange_config.fee_rate,
      fee_amount: fee_amount,
      tax_rate: exchange_config.tax_rate,
      tax_amount: tax_amount,
      final_amount: final_amount
    }
  end

  @doc """
  更新支付网关状态（单个或批量）
  """
  def update_gateway_status(gateway_id_or_ids, status)

  def update_gateway_status(gateway_id, status) when is_binary(gateway_id) do
    case Ash.get(PaymentGateway, gateway_id) do
      {:ok, gateway} ->
        case status do
          :enable -> PaymentGateway.enable(gateway)
          :disable -> PaymentGateway.disable(gateway)
          _ -> {:error, "Invalid status"}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  def update_gateway_status(gateway_ids, status) when is_list(gateway_ids) do
    results =
      Enum.map(gateway_ids, fn gateway_id ->
        case Ash.get(PaymentGateway, gateway_id) do
          {:ok, gateway} ->
            case status do
              :enable -> PaymentGateway.enable(gateway)
              :disable -> PaymentGateway.disable(gateway)
              _ -> {:error, "Invalid status"}
            end

          {:error, reason} ->
            {:error, {gateway_id, reason}}
        end
      end)

    {successes, failures} =
      Enum.split_with(results, fn
        {:ok, _} -> true
        _ -> false
      end)

    %{
      success_count: length(successes),
      failure_count: length(failures),
      failures: failures
    }
  end

  @doc """
  测试支付网关连接
  """
  def test_gateway_connection(gateway_id) do
    case Ash.get(PaymentGateway, gateway_id) do
      {:ok, gateway} ->
        # 这里应该实现实际的网关连接测试
        test_result = simulate_gateway_test(gateway)

        # 更新最后测试时间
        PaymentGateway.test_connection(gateway)

        {:ok, test_result}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  批量更新支付配置状态
  """
  def batch_update_config_status(config_ids, status) when is_list(config_ids) do
    results =
      Enum.map(config_ids, fn config_id ->
        case PaymentConfig.read(config_id) do
          {:ok, config} ->
            case status do
              :enable -> PaymentConfig.enable(config)
              :disable -> PaymentConfig.disable(config)
              _ -> {:error, "Invalid status"}
            end

          {:error, reason} ->
            {:error, {config_id, reason}}
        end
      end)

    {successes, failures} =
      Enum.split_with(results, fn
        {:ok, _} -> true
        _ -> false
      end)

    %{
      success_count: length(successes),
      failure_count: length(failures),
      failures: failures
    }
  end

  # ==================== 辅助函数 ====================

  @doc """
  获取默认的充值档位（客户端兼容格式）
  """
  defp get_default_recharge_sections do
    # 默认充值档位（分为单位），转换为客户端期望的字符串格式
    [50000, 100000, 500000, 1000000, 2000000, 5000000, ********, ********]
    |> Enum.join("|")
  end

  @doc """
  获取默认的奖励档位（客户端兼容格式）
  """
  defp get_default_bonus_sections do
    # 默认奖励档位（对应充值档位的奖励金额），转换为客户端期望的字符串格式
    [0, 0, 0, 0, 0, 0, 0, 0]
    |> Enum.join("|")
  end

  # ==================== 私有函数 ====================

  defp filter_configs_by_amount(configs, amount) do
    Enum.filter(configs, fn config ->
      Decimal.compare(amount, config.min_amount) != :lt and
        Decimal.compare(amount, config.max_amount) != :gt
    end)
  end

  defp filter_configs_by_user_eligibility(configs, _user_id) do
    # 这里可以根据用户的VIP等级、黑名单状态等进行过滤
    configs
  end

  defp filter_available_configs(configs, amount) do
    configs
    |> Enum.filter(&(&1.status == 1))
    |> filter_configs_by_amount(amount)
  end

  defp adjust_config_for_vip(config, vip_level) do
    # 根据VIP等级调整配置
    vip_bonus =
      case vip_level do
        # 20%优惠
        level when level >= 5 -> Decimal.new("0.2")
        # 10%优惠
        level when level >= 3 -> Decimal.new("0.1")
        # 5%优惠
        level when level >= 1 -> Decimal.new("0.05")
        _ -> Decimal.new("0")
      end

    adjusted_fee_rate = Decimal.mult(config.fee_rate, Decimal.sub(Decimal.new("1"), vip_bonus))

    %{config | fee_rate: adjusted_fee_rate}
  end

  defp validate_amount_limits(amount, config) do
    cond do
      Decimal.compare(amount, config.min_amount) == :lt ->
        {:error, "Amount below minimum limit"}

      Decimal.compare(amount, config.max_amount) == :gt ->
        {:error, "Amount exceeds maximum limit"}

      true ->
        :ok
    end
  end

  defp validate_daily_limits(_user_id, _amount, _config), do: :ok
  defp validate_user_requirements(_user_id, _config), do: :ok

  defp simulate_gateway_test(_gateway) do
    case :rand.uniform(10) do
      n when n <= 8 ->
        %{
          status: :success,
          response_time: :rand.uniform(1000),
          message: "Gateway connection successful"
        }

      _ ->
        %{
          status: :failed,
          response_time: nil,
          message: "Gateway connection failed"
        }
    end
  end

  @doc """
  获取用户银行配置信息
  包含银行列表和用户绑定的银行卡信息
  """
  def get_user_bank_config(user_id) do
    with {:ok, banks} <- BankConfig.list_active_banks(),
         {:ok, user_cards} <- get_user_cards(user_id) do

      # 构建银行配置映射
      bank_config =
        banks
        |> Enum.with_index()
        |> Enum.map(fn {bank, index} ->
          # 查找用户绑定的银行卡信息
          user_card = Enum.find(user_cards, fn card -> card.bank_id == bank.id end)

          bank_info = %{
            "id" => bank.id,
            "Name" => bank.name,
            "BankCode" => bank.bank_code,
            "MinAmount" => Decimal.to_string(bank.min_amount),
            "MaxAmount" => Decimal.to_string(bank.max_amount),
            "FeeRate" => Decimal.to_string(bank.fee_rate),
            "IconUrl" => bank.icon_url,
            "Status" => bank.status
          }

          # 如果用户绑定了这个银行的卡，添加账户信息
          bank_info = if user_card do
            Map.merge(bank_info, %{
              "BankAccountName" => user_card.account_name,
              "BankAccountNum" => user_card.account_number,
              "IsDefault" => user_card.is_default
            })
          else
            bank_info
          end

          {index, bank_info}
        end)
        |> Enum.into(%{})

      # 构建绑定银行卡配置
      bind_bank_config =
        user_cards
        |> Enum.with_index()
        |> Enum.map(fn {card, index} ->
          {index, %{
            "BankID" => card.bank_id,
            "BankAccountName" => card.account_name,
            "BankAccountNum" => card.account_number,
            "IsDefault" => card.is_default,
            "Status" => card.status
          }}
        end)
        |> Enum.into(%{})

      {:ok, %{
        "BankConfig" => bank_config,
        "BindBankConfig" => bind_bank_config
      }}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  获取客户端充值配置（兼容客户端RechargeInfo格式）
  """
  def get_client_recharge_config do
    case PaymentConfig.list_active_configs() do
      {:ok, payment_configs} ->
        # 构建客户端期望的充值配置格式
        recharge_list = payment_configs |> Enum.with_index() |> Enum.map(fn {config, index} ->
          # 获取默认的充值档位配置
          sections = get_default_recharge_sections()
          send_bonus = get_default_bonus_sections()

          %{
            "ChargeID" => config.id,
            "ChargeType" => 1,
            "GatewayId" => config.gateway_id,
            "GatewayName" => config.gateway_name,
            "PayTypeCode" => config.payment_type,  # 这是客户端期望的关键字段
            "PayTypeId" => index,
            "TypeName" => config.payment_type_name,
            "ShowName" => config.payment_type_name,
            "MinPrice" => Decimal.to_integer(config.min_amount),
            "MaxPrice" => Decimal.to_integer(config.max_amount),
            "Rate" => Decimal.to_float(config.fee_rate),
            "SubRate" => 0,
            "Sort" => config.sort_order || index,
            "TypeSort" => config.sort_order || index,
            "Section" => sections,
            "SendBonus" => send_bonus
          }
        end)

        {:ok, recharge_list}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取系统配置信息
  包含支付配置、银行配置等
  """
  def get_system_config(user_id \\ nil) do
    # 获取支付配置
    payment_configs = case PaymentConfig.list_active_configs() do
      {:ok, configs} -> configs
      _ -> []
    end

    # 获取银行配置
    bank_config = case get_user_bank_config(user_id) do
      {:ok, config} -> config
      _ -> %{"BankConfig" => %{}, "BindBankConfig" => %{}}
    end

    # 构建充值配置 (RechargeConfig)
    recharge_config = payment_configs |> Enum.with_index() |> Enum.map(fn {config, index} ->
      {index, %{
        "ChargeID" => index,
        "ChargeType" => 1,  # 默认充值类型
        "GatewayId" => index,
        "GatewayName" => config.gateway_name,
        "MaxPrice" => Decimal.to_integer(config.max_amount),
        "MinPrice" => Decimal.to_integer(config.min_amount),
        "PayTypeCode" => index,
        "PayTypeId" => index,
        "Rate" => Decimal.to_float(config.fee_rate),
        "Section" => generate_recharge_sections(config.min_amount, config.max_amount),
        "SendBonus" => generate_bonus_sections(config.min_amount, config.max_amount),
        "Sort" => index,
        "SubRate" => Decimal.to_float(config.deduction_rate || Decimal.new("0")),
        "TypeName" => config.payment_type,
        "ShowName" => config.payment_type_name,
        "TypeSort" => index
      }}
    end) |> Enum.into(%{})

    # 构建完整的系统配置
    config = Map.merge(bank_config, %{
      "PaymentConfig" => payment_configs |> Enum.with_index() |> Enum.map(fn {config, index} ->
        {index, %{
          "ChargeID" => config.id,
          "ChargeType" => 1,  # 默认充值类型
          "GatewayId" => config.gateway_id,
          "GatewayName" => config.gateway_name,
          "PayTypeCode" => config.payment_type,  # 客户端期望的字段名
          "PayTypeId" => index,
          "TypeName" => config.payment_type_name,
          "ShowName" => config.payment_type_name,
          "MinPrice" => Decimal.to_integer(config.min_amount),
          "MaxPrice" => Decimal.to_integer(config.max_amount),
          "Rate" => Decimal.to_float(config.fee_rate),
          "SubRate" => 0,
          "Sort" => config.sort_order || index,
          "TypeSort" => config.sort_order || index,
          "Section" => get_default_recharge_sections(),
          "SendBonus" => get_default_bonus_sections(),
          "status" => config.status,
          # 保持向后兼容的字段
          "id" => config.id,
          "gateway_name" => config.gateway_name,
          "payment_type" => config.payment_type,
          "payment_type_name" => config.payment_type_name,
          "min_amount" => Decimal.to_string(config.min_amount),
          "max_amount" => Decimal.to_string(config.max_amount),
          "fee_rate" => Decimal.to_string(config.fee_rate)
        }}
      end) |> Enum.into(%{}),
      "RechargeConfig" => recharge_config
    })

    {:ok, config}
  end

  # 辅助函数：获取用户银行卡
  defp get_user_cards(nil), do: {:ok, []}
  defp get_user_cards(user_id), do: UserBankCard.list_by_user(%{user_id: user_id})

  # 辅助函数：生成充值区间（客户端兼容格式）
  defp generate_recharge_sections(min_amount, max_amount) do
    min = Decimal.to_integer(min_amount)
    max = Decimal.to_integer(max_amount)

    # 生成常见的充值金额区间
    base_sections = [50000, 100000, 500000, 1000000, 2000000, 5000000, ********, ********]

    # 过滤出在最小和最大金额范围内的区间
    sections = base_sections
    |> Enum.filter(fn amount -> amount >= min and amount <= max end)
    |> case do
      [] -> [min, max]  # 如果没有合适的区间，使用最小和最大值
      sections -> sections
    end

    # 转换为客户端期望的字符串格式
    sections |> Enum.join("|")
  end

  # 辅助函数：生成奖励区间（客户端兼容格式）
  defp generate_bonus_sections(min_amount, max_amount) do
    min = Decimal.to_integer(min_amount)
    max = Decimal.to_integer(max_amount)

    # 生成常见的充值金额区间（用于计算奖励）
    base_sections = [50000, 100000, 500000, 1000000, 2000000, 5000000, ********, ********]

    # 过滤出在最小和最大金额范围内的区间
    sections = base_sections
    |> Enum.filter(fn amount -> amount >= min and amount <= max end)
    |> case do
      [] -> [min, max]  # 如果没有合适的区间，使用最小和最大值
      sections -> sections
    end

    # 生成对应的奖励金额（这里使用5%作为示例）并转换为字符串格式
    sections
    |> Enum.map(fn amount -> trunc(amount * 0.05) end)
    |> Enum.join("|")
  end
end
