defmodule Teen.Filters.VipLevelSelect do
  @moduledoc """
  VIP等级选择器
  
  用于在管理界面中选择VIP等级，动态从数据库获取可用的VIP等级
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "VIP等级"

  @impl Backpex.Filters.Select
  def prompt, do: "选择VIP等级..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    # 动态获取所有启用的VIP等级
    case Teen.GameManagement.VipLevel.list_active_levels() do
      {:ok, vip_levels} ->
        # 添加"全部"选项
        all_option = [{"全部", nil}]
        
        # 将VIP等级转换为选项格式，按等级排序
        vip_options = 
          vip_levels
          |> Enum.sort_by(& &1.level)
          |> Enum.map(fn vip_level ->
            {"VIP#{vip_level.level} - #{vip_level.level_name}", vip_level.level}
          end)
        
        all_option ++ vip_options
        
      {:error, _} ->
        # 如果获取失败，返回默认选项
        [
          {"全部", nil},
          {"VIP0 - 普通用户", 0},
          {"VIP1 - 青铜VIP", 1},
          {"VIP2 - 白银VIP", 2},
          {"VIP3 - 黄金VIP", 3},
          {"VIP4 - 铂金VIP", 4},
          {"VIP5 - 钻石VIP", 5}
        ]
    end
  end
end
