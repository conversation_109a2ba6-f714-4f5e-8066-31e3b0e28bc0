defmodule Teen.Filters.RewardTypeFilter do
  @moduledoc """
  奖励类型过滤器
  """

  use Backpex.Filter
  import Phoenix.Component

  @impl Backpex.Filter
  def label, do: "奖励类型"

  @impl Backpex.Filter
  def type, do: :select

  @impl Backpex.Filter
  def options do
    [
      {"金币", :coins},
      {"积分", :points},
      {"现金", :cash},
      {"转盘次数", :wheel_spins},
      {"道具", :items}
    ]
  end

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <select name="value" class="form-select">
      <option value="">选择奖励类型...</option>
      <%= for {label, value} <- options() do %>
        <option value={value}><%= label %></option>
      <% end %>
    </select>
    """
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource, _socket) when is_nil(value) or value == "" do
    query
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource, _socket) do
    import Ash.Expr
    Ash.Query.filter(query, expr(reward_type == ^value))
  end

  @impl Backpex.Filter
  def can?(_assigns), do: true
end
