defmodule Teen.Filters.TaskTypeFilter do
  @moduledoc """
  任务类型过滤器
  
  用于在邀请奖励配置管理页面中按任务类型进行过滤
  """
  
  use Backpex.Filter
  import Phoenix.Component

  @impl Backpex.Filter
  def label, do: "任务类型"

  @impl Backpex.Filter
  def type, do: :select

  @impl Backpex.Filter
  def options do
    [
      {"邀请注册", :invite_register},
      {"邀请充值", :invite_recharge},
      {"邀请游戏", :invite_play},
      {"邀请留存", :invite_retention}
    ]
  end

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <div class="space-y-2">
      <label class="block text-sm font-medium text-gray-700">
        <%= @filter.label %>
      </label>
      <select
        name={@name}
        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
      >
        <option value="">全部</option>
        <%= for {label, value} <- options() do %>
          <option value={value} selected={@value == to_string(value)}>
            <%= label %>
          </option>
        <% end %>
      </select>
    </div>
    """
  end

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filter
  def query(query, value, _live_resource, _socket) when is_nil(value) or value == "" do
    query
  end

  def query(query, value, _live_resource, _socket) do
    import Ash.Expr
    task_type = String.to_existing_atom(value)
    Ash.Query.filter(query, expr(task_type == ^task_type))
  end

  # 辅助函数：获取任务类型的显示标签
  defp get_task_type_label("invite_register"), do: "邀请注册"
  defp get_task_type_label("invite_recharge"), do: "邀请充值"
  defp get_task_type_label("invite_play"), do: "邀请游戏"
  defp get_task_type_label("invite_retention"), do: "邀请留存"
  defp get_task_type_label(_), do: "未知类型"
end
