defmodule Teen.Filters.ActivitySelect do
  @moduledoc """
  活动选择过滤器
  """

  use Backpex.Filter
  import Phoenix.Component

  @impl Backpex.Filter
  def label, do: "活动"

  @impl Backpex.Filter
  def type, do: :select

  @impl Backpex.Filter
  def options do
    [
      {"所有活动", ""},
      {"测试活动1", "test-1"},
      {"测试活动2", "test-2"}
    ]
  end

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <select name="value" class="form-select">
      <%= for {label, value} <- options() do %>
        <option value={value}><%= label %></option>
      <% end %>
    </select>
    """
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource, _socket) when is_nil(value) or value == "" do
    query
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource, _socket) do
    import Ash.Expr
    Ash.Query.filter(query, expr(activity_id == ^value))
  end

  @impl Backpex.Filter
  def can?(_assigns), do: true
end
