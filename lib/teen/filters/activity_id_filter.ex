defmodule Teen.Filters.ActivityIdFilter do
  @moduledoc """
  活动ID过滤器
  
  用于在邀请奖励配置管理页面中按活动进行过滤
  """
  
  use Backpex.Filter
  import Phoenix.Component

  @impl Backpex.Filter
  def label, do: "活动"

  @impl Backpex.Filter
  def type, do: :select

  @impl Backpex.Filter
  def options do
    # 动态获取所有活动
    case Teen.ActivitySystem.InviteCashActivity.read() do
      {:ok, activities} ->
        activities
        |> Enum.map(fn activity -> {activity.title, activity.id} end)
        |> Enum.sort_by(fn {title, _id} -> title end)
      
      {:error, _} ->
        []
    end
  end

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <div class="space-y-2">
      <label class="block text-sm font-medium text-gray-700">
        <%= @filter.label %>
      </label>
      <select
        name={@name}
        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
      >
        <option value="">全部活动</option>
        <%= for {title, id} <- options() do %>
          <option value={id} selected={@value == id}>
            <%= title %>
          </option>
        <% end %>
      </select>
    </div>
    """
  end

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filter
  def query(query, value, _live_resource, _socket) when is_nil(value) or value == "" do
    query
  end

  def query(query, value, _live_resource, _socket) do
    import Ash.Expr
    Ash.Query.filter(query, expr(activity_id == ^value))
  end
end
