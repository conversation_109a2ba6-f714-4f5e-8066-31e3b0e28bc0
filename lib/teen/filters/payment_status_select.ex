defmodule Teen.Filters.PaymentStatusSelect do
  @moduledoc """
  支付状态筛选器

  用于筛选不同状态的支付订单
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "订单状态"

  @impl Backpex.Filters.Select
  def prompt, do: "选择订单状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"待支付", "pending"},
      {"支付中", "processing"},
      {"支付成功", "success"},
      {"支付失败", "failed"},
      {"已取消", "cancelled"},
      {"已退款", "refunded"}
    ]
  end
end
