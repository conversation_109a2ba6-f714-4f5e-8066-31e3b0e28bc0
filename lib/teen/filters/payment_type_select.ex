defmodule Teen.Filters.PaymentTypeSelect do
  @moduledoc """
  支付类型筛选器

  用于筛选不同类型的支付配置
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "支付类型"

  @impl Backpex.Filters.Select
  def prompt, do: "选择支付类型..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"支付宝", "alipay"},
      {"支付宝H5", "alipay_h5"},
      {"支付宝扫码", "alipay_scan"},
      {"微信支付", "wechat"},
      {"微信H5", "wechat_h5"},
      {"微信扫码", "wechat_scan"},
      {"银行卡", "bank_card"},
      {"快捷支付", "quick_pay"},
      {"网银支付", "online_banking"},
      {"QQ钱包", "qq_pay"},
      {"京东支付", "jd_pay"},
      {"云闪付", "union_pay"},
      {"USDT", "usdt"},
      {"比特币", "btc"},
      {"以太坊", "eth"}
    ]
  end
end
