defmodule Cypridina.Teen.GameSystem.Games.Slot777.Slot777ConfigCLI do
  @moduledoc """
  Slot777配置管理命令行工具
  
  提供简单的命令行接口来管理游戏配置，方便运营人员快速调整参数
  
  使用方法：
  ```
  # 在iex中使用
  iex> alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777ConfigCLI, as: CLI
  iex> CLI.show_current_config()
  iex> CLI.apply_preset(:low_win_rate)
  iex> CLI.show_presets()
  ```
  """

  require Logger
  alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777Config
  alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777ConfigManager

  @doc """
  显示当前配置概览
  """
  def show_current_config do
    config = Slot777Config.get_current_config()
    
    IO.puts("\n" <> IO.ANSI.cyan() <> "=== Slot777 当前配置概览 ===" <> IO.ANSI.reset())
    IO.puts("")
    
    # 中奖率配置
    IO.puts(IO.ANSI.yellow() <> "🎯 中奖率配置:" <> IO.ANSI.reset())
    IO.puts("  RTP (返还率): #{config.win_rate.rtp}%")
    IO.puts("  图标权重总和: #{config.win_rate.icon_weights |> Map.values() |> Enum.sum()}")
    
    # 下注配置
    IO.puts(IO.ANSI.yellow() <> "💰 下注配置:" <> IO.ANSI.reset())
    IO.puts("  底分: #{config.betting.difen}")
    IO.puts("  下注范围: #{config.betting.min_bet} - #{config.betting.max_bet}")
    IO.puts("  固定倍率: #{config.betting.bet_rate_num}")
    
    # Jackpot配置
    IO.puts(IO.ANSI.yellow() <> "🎰 Jackpot配置:" <> IO.ANSI.reset())
    IO.puts("  种子金额: #{config.jackpot.seed_amount}")
    IO.puts("  贡献率: #{config.jackpot.contribution_rate * 100}%")
    
    # 免费游戏配置
    IO.puts(IO.ANSI.yellow() <> "🎁 免费游戏配置:" <> IO.ANSI.reset())
    IO.puts("  触发次数: #{inspect(config.free_game.trigger_table)}")
    IO.puts("  倍率: #{config.free_game.multiplier}")
    
    IO.puts("")
  end

  @doc """
  显示可用的配置预设
  """
  def show_presets do
    presets = Slot777ConfigManager.get_config_presets()
    
    IO.puts("\n" <> IO.ANSI.cyan() <> "=== 可用配置预设 ===" <> IO.ANSI.reset())
    IO.puts("")
    
    Enum.each(presets, fn {key, preset} ->
      IO.puts(IO.ANSI.green() <> "#{key}" <> IO.ANSI.reset() <> " - #{preset.name}")
      IO.puts("  #{preset.description}")
      IO.puts("  RTP: #{preset.win_rate.rtp}%")
      IO.puts("")
    end)
    
    IO.puts(IO.ANSI.yellow() <> "使用方法: apply_preset(:preset_name)" <> IO.ANSI.reset())
    IO.puts("")
  end

  @doc """
  应用配置预设
  """
  def apply_preset(preset_name) when is_atom(preset_name) do
    IO.puts("\n" <> IO.ANSI.cyan() <> "正在应用配置预设: #{preset_name}" <> IO.ANSI.reset())
    
    case Slot777ConfigManager.apply_preset(preset_name) do
      {:ok, _config} ->
        IO.puts(IO.ANSI.green() <> "✅ 配置预设应用成功!" <> IO.ANSI.reset())
        show_current_config()
        
      {:error, reason} ->
        IO.puts(IO.ANSI.red() <> "❌ 配置预设应用失败: #{reason}" <> IO.ANSI.reset())
    end
  end

  @doc """
  显示图标权重详情
  """
  def show_icon_weights do
    config = Slot777Config.get_current_config()
    weights = config.win_rate.icon_weights
    total_weight = weights |> Map.values() |> Enum.sum()
    
    IO.puts("\n" <> IO.ANSI.cyan() <> "=== 图标权重配置 ===" <> IO.ANSI.reset())
    IO.puts("")
    
    icon_names = %{
      0 => "WILD字",
      1 => "香蕉",
      2 => "西瓜", 
      3 => "草莓",
      4 => "葡萄",
      5 => "芒果",
      6 => "榴莲",
      7 => "山竹",
      8 => "BAR",
      9 => "苹果(FREE)",
      10 => "7"
    }
    
    Enum.each(0..10, fn icon_id ->
      weight = Map.get(weights, icon_id, 0)
      percentage = Float.round(weight / total_weight * 100, 2)
      name = Map.get(icon_names, icon_id, "未知")
      
      bar_length = trunc(percentage / 2)  # 缩放到50字符宽度
      bar = String.duplicate("█", bar_length) <> String.duplicate("░", 50 - bar_length)
      
      IO.puts("#{icon_id |> to_string() |> String.pad_leading(2)} #{name |> String.pad_trailing(12)} #{weight |> to_string() |> String.pad_leading(3)} (#{percentage |> to_string() |> String.pad_leading(5)}%) #{bar}")
    end)
    
    IO.puts("")
    IO.puts("总权重: #{total_weight}")
    IO.puts("")
  end

  @doc """
  显示赔率表
  """
  def show_payout_table do
    config = Slot777Config.get_current_config()
    payout_table = config.payout.payout_table
    
    IO.puts("\n" <> IO.ANSI.cyan() <> "=== 赔率表配置 ===" <> IO.ANSI.reset())
    IO.puts("")
    
    icon_names = %{
      0 => "WILD字",
      1 => "香蕉",
      2 => "西瓜", 
      3 => "草莓",
      4 => "葡萄",
      5 => "芒果",
      6 => "榴莲",
      7 => "山竹",
      8 => "BAR",
      9 => "苹果(FREE)",
      10 => "7"
    }
    
    IO.puts("图标        2连    3连    4连    5连")
    IO.puts("─────────────────────────────────────")
    
    Enum.each(0..10, fn icon_id ->
      name = Map.get(icon_names, icon_id, "未知")
      payouts = Map.get(payout_table, icon_id, %{})
      
      payout_2 = Map.get(payouts, 2, "-") |> to_string() |> String.pad_leading(6)
      payout_3 = Map.get(payouts, 3, "-") |> to_string() |> String.pad_leading(6)
      payout_4 = Map.get(payouts, 4, "-") |> to_string() |> String.pad_leading(6)
      payout_5 = Map.get(payouts, 5, "-") |> to_string() |> String.pad_leading(6)
      
      IO.puts("#{name |> String.pad_trailing(12)}#{payout_2}#{payout_3}#{payout_4}#{payout_5}")
    end)
    
    IO.puts("")
  end

  @doc """
  快速调整中奖率
  """
  def quick_adjust_win_rate(new_rtp) when is_number(new_rtp) do
    cond do
      new_rtp < 70 or new_rtp > 98 ->
        IO.puts(IO.ANSI.red() <> "❌ RTP必须在70%-98%之间" <> IO.ANSI.reset())
        
      true ->
        IO.puts("\n" <> IO.ANSI.cyan() <> "正在调整RTP为: #{new_rtp}%" <> IO.ANSI.reset())
        
        # 这里可以实现RTP调整逻辑
        # 暂时只显示提示
        IO.puts(IO.ANSI.yellow() <> "⚠️  此功能需要实现配置更新逻辑" <> IO.ANSI.reset())
        IO.puts("建议使用预设配置或手动修改配置文件")
    end
  end

  @doc """
  显示配置调整建议
  """
  def show_tuning_suggestions do
    # 模拟统计数据
    mock_stats = %{
      win_rate: 88.5,
      jackpot_frequency: 0.002,
      free_game_frequency: 0.015,
      avg_bet: 500,
      total_games: 10000
    }
    
    suggestions = Slot777ConfigManager.get_tuning_suggestions(mock_stats)
    
    IO.puts("\n" <> IO.ANSI.cyan() <> "=== 配置调整建议 ===" <> IO.ANSI.reset())
    IO.puts("")
    
    IO.puts(IO.ANSI.yellow() <> "当前统计:" <> IO.ANSI.reset())
    IO.puts("  中奖率: #{mock_stats.win_rate}%")
    IO.puts("  Jackpot频率: #{mock_stats.jackpot_frequency * 100}%")
    IO.puts("  免费游戏频率: #{mock_stats.free_game_frequency * 100}%")
    IO.puts("")
    
    IO.puts(IO.ANSI.yellow() <> "建议:" <> IO.ANSI.reset())
    if length(suggestions.suggestions) > 0 do
      Enum.each(suggestions.suggestions, fn suggestion ->
        IO.puts("  • #{suggestion}")
      end)
    else
      IO.puts("  • 当前配置良好，无需调整")
    end
    
    IO.puts("")
    IO.puts(IO.ANSI.yellow() <> "推荐预设: #{suggestions.recommended_preset}" <> IO.ANSI.reset())
    IO.puts(IO.ANSI.yellow() <> "风险等级: #{suggestions.risk_level}" <> IO.ANSI.reset())
    IO.puts("")
  end

  @doc """
  显示帮助信息
  """
  def help do
    IO.puts("\n" <> IO.ANSI.cyan() <> "=== Slot777 配置管理工具 ===" <> IO.ANSI.reset())
    IO.puts("")
    IO.puts("可用命令:")
    IO.puts("")
    IO.puts(IO.ANSI.green() <> "show_current_config()" <> IO.ANSI.reset() <> "     - 显示当前配置概览")
    IO.puts(IO.ANSI.green() <> "show_presets()" <> IO.ANSI.reset() <> "            - 显示可用配置预设")
    IO.puts(IO.ANSI.green() <> "apply_preset(:name)" <> IO.ANSI.reset() <> "       - 应用配置预设")
    IO.puts(IO.ANSI.green() <> "show_icon_weights()" <> IO.ANSI.reset() <> "       - 显示图标权重详情")
    IO.puts(IO.ANSI.green() <> "show_payout_table()" <> IO.ANSI.reset() <> "       - 显示赔率表")
    IO.puts(IO.ANSI.green() <> "quick_adjust_win_rate(85)" <> IO.ANSI.reset() <> "  - 快速调整中奖率")
    IO.puts(IO.ANSI.green() <> "show_tuning_suggestions()" <> IO.ANSI.reset() <> " - 显示调整建议")
    IO.puts(IO.ANSI.green() <> "help()" <> IO.ANSI.reset() <> "                   - 显示此帮助信息")
    IO.puts("")
    IO.puts("示例:")
    IO.puts("  CLI.apply_preset(:low_win_rate)   # 应用低中奖率模式")
    IO.puts("  CLI.show_icon_weights()           # 查看图标权重分布")
    IO.puts("")
  end
end
