defmodule Cypridina.Teen.GameSystem.Games.Slot777.Slot777Config do
  @moduledoc """
  Slot777游戏配置管理模块 - 简化版本
  现在只提供默认配置，实际的配置合并由 RoomManager 统一处理
  """

  require Logger


  def get_default_config do
    %{
      # ===== 基础游戏配置 =====
      game_basic: %{
        rows: 3,
        cols: 5,
        max_lines: 9,
        # 游戏名称
        name: "Slot777老虎机",
        # 游戏版本
        version: "2.0.0"
      },

      # ===== 下注配置 =====
      betting: %{
        # 底分 (基础下注单位)
        difen: 100,
        # 固定倍率 (对应前端的 BET_RATE_NUM)
        bet_rate_num: 9,
        # 金币比例 (对应前端的 SCORE_RATE)
        score_rate: 1,
        # 下注倍率选项
        odds_config: %{
          "1" => 0.2,
          "2" => 1,
          "3" => 2,
          "4" => 10,
          "5" => 20,
          "6" => 100,
          "7" => 200
        },
        # 最小下注
        min_bet: 1,
        # 最大下注
        max_bet: 1000
      },

      # ===== 中奖率控制配置 =====
      win_rate: %{
        # 整体返还率 (RTP - Return to Player)
        rtp: 85.0,
        # 图标权重配置 (数字越大出现概率越高)
        icon_weights: %{
          0 => 1,    # WILD字 - 极低频 (降低)
          1 => 30,   # 香蕉 - 高频 (最低赔率) (增加)
          2 => 25,   # 西瓜 - 高频 (增加)
          3 => 20,   # 草莓 - 中频 (增加)
          4 => 15,   # 葡萄 - 中频
          5 => 12,   # 芒果 - 中频
          6 => 10,   # 榴莲 - 中频
          7 => 8,    # 山竹 - 低频
          8 => 3,    # BAR - 低频 (高赔率) (降低)
          9 => 4,    # 苹果 - 低频 (免费游戏触发) (降低)
          10 => 2    # 7 - 极低频 (Jackpot级别) (降低)
        },
        # 大奖触发概率控制
        big_win_control: %{
          # Jackpot触发概率 (万分之一)
          jackpot_probability: 0.01,
          # 免费游戏触发概率控制
          free_game_probability: 0.05,
          # 连续大奖间隔控制 (防止连续大奖)
          big_win_cooldown: 100
        }
      },

      # ===== 赔率表配置 =====
      payout: %{
        # 赔率表 (图标类型 => {连击数 => 赔率}) - 按照游戏规则图片更新
        payout_table: %{
          0 => %{},                                        # WILD字 (替代其他图标，无独立赔率)
          1 => %{2 => 1, 3 => 3, 4 => 10, 5 => 75},       # 香蕉 (按图片: 2连1倍, 3连3倍, 4连10倍, 5连75倍)
          2 => %{3 => 3, 4 => 10, 5 => 85},               # 西瓜 (按图片: 3连3倍, 4连10倍, 5连85倍)
          3 => %{3 => 15, 4 => 40, 5 => 250},             # 草莓 (按图片: 3连15倍, 4连40倍, 5连250倍)
          4 => %{3 => 25, 4 => 50, 5 => 400},             # 葡萄 (按图片: 3连25倍, 4连50倍, 5连400倍)
          5 => %{3 => 30, 4 => 70, 5 => 550},             # 芒果 (按图片: 3连30倍, 4连70倍, 5连550倍)
          6 => %{3 => 35, 4 => 80, 5 => 650},             # 榴莲 (按图片: 3连35倍, 4连80倍, 5连650倍)
          7 => %{3 => 45, 4 => 100, 5 => 800},            # 山竹 (按图片: 3连45倍, 4连100倍, 5连800倍)
          8 => %{3 => 75, 4 => 175, 5 => 1250},           # BAR (按图片: 3连75倍, 4连175倍, 5连1250倍)
          9 => %{3 => 25, 4 => 40, 5 => 400},             # 苹果 (FREE，按图片: 3连25倍, 4连40倍, 5连400倍)
          10 => %{3 => 100, 4 => 200, 5 => 1750}          # 7 (JACKPOT，按图片: 3连100倍, 4连200倍, 5连1750倍)
        }
      },

      # ===== Jackpot配置 =====
      jackpot: %{
        # Jackpot种子金额
        seed_amount: 10000,
        # 贡献率 (每次下注的百分比贡献给Jackpot)
        contribution_rate: 0.005,  # 降低到0.5%
        # 最小触发金额
        min_trigger_amount: 50000,
        # Jackpot比例配置 (根据7的数量和单线下注金额)
        percentage_table: %{
          # 单线下注₹200
          200 => %{5 => 30, 4 => 25, 3 => 20},  # 降低比例
          # 单线下注₹100
          100 => %{5 => 20, 4 => 15, 3 => 12},  # 降低比例
          # 单线下注₹20
          20 => %{5 => 12, 4 => 6, 3 => 4},     # 降低比例
          # 单线下注₹10
          10 => %{5 => 8, 4 => 4, 3 => 2}       # 降低比例
        }
      },

      # ===== 免费游戏配置 =====
      free_game: %{
        # 触发免费游戏需要的苹果数量和对应次数
        trigger_table: %{
          5 => 12,  # 5个苹果 = 12次免费 (降低)
          4 => 8,   # 4个苹果 = 8次免费 (降低)
          3 => 4    # 3个苹果 = 4次免费 (降低)
        },
        # 免费游戏倍率
        multiplier: 1.5,  # 降低倍率
        # 免费游戏中的特殊权重 (可选)
        special_weights: nil
      },

      # ===== 测试功能配置 =====
      testing: %{
        # 是否启用免费游戏测试
        enable_free_game_test: false,
        # 免费游戏测试触发条件 (连续旋转次数)
        free_game_test_spins: 2,  # 增加到50次
        # 是否启用Jackpot测试
        enable_jackpot_test: false,
        # 测试模式下的特殊配置
        test_mode_config: %{
          # 测试时使用的特殊权重
          test_icon_weights: %{
            0 => 5,    # WILD字 - 测试时增加
            1 => 20,   # 香蕉
            2 => 20,   # 西瓜
            3 => 15,   # 草莓
            4 => 15,   # 葡萄
            5 => 10,   # 芒果
            6 => 10,   # 榴莲
            7 => 8,    # 山竹
            8 => 5,    # BAR
            9 => 10,   # 苹果 - 测试时增加
            10 => 2    # 7
          }
        }
      },

      # ===== 房间配置 =====
      room: %{
        # 最大玩家数
        max_players: 1,
        # 最小玩家数
        min_players: 1,
        # 自动开始延迟
        auto_start_delay: 1000,
        # 是否启用机器人
        enable_robots: false,
        # 机器人数量
        robot_count: 0
      }
    }
  end

  def get_current_config() do
    Logger.info("🎰 [SLOT777_CONFIG] 返回默认配置")
    get_default_config()
  end

  @doc """
  获取合并后的配置
  接收 RoomManager 传入的合并后配置，进行最终处理
  """
  def get_merged_config(room_config) when is_map(room_config) do
    Logger.info("🎰 [SLOT777_CONFIG] 使用合并后的房间配置")

    # 获取默认配置作为基础
    default_config = get_default_config()

    # 与房间配置进行最终合并，确保配置完整性
    final_config = deep_merge_configs(default_config, room_config)

    Logger.info("🎰 [SLOT777_CONFIG] 配置合并完成，配置项数: #{map_size(final_config)}")
    final_config
  end

    # 合并默认配置和后台配置（参考 Slot777 的高级合并逻辑）
  defp deep_merge_configs(default_config, backend_config) when is_map(default_config) and is_map(backend_config) do
    if map_size(backend_config) == 0 do
      Logger.info("🏠 [ROOM_MANAGER] 使用默认配置 - 后台配置为空")
      default_config
    else
      Logger.info("🏠 [ROOM_MANAGER] 合并配置 - 后台配置项数: #{map_size(backend_config)}")

      # 标准化后台配置的键类型，使其与默认配置匹配
      normalized_backend_config = normalize_keys_to_match_default(backend_config, default_config)

      # 深度合并配置
      deep_merge(default_config, normalized_backend_config)
    end
  end

  defp deep_merge_configs(default_config, _backend_config) do
    Logger.warning("🏠 [ROOM_MANAGER] 配置格式错误，使用默认配置")
    default_config || %{}
  end

  # 深度合并Map - 支持列表、原子等特殊数据类型（参考 Slot777Config）
  defp deep_merge(left, right) when is_map(left) and is_map(right) do
    Map.merge(left, right, &merge_values/3)
  end

  defp deep_merge(_left, right), do: right

  # 合并值的策略函数
  defp merge_values(key, left_val, right_val) do
    cond do
      # 两个都是Map：递归深度合并
      is_map(left_val) and is_map(right_val) ->
        deep_merge(left_val, right_val)

      # 两个都是列表：用右边的列表完全替换左边的列表
      is_list(left_val) and is_list(right_val) ->
        Logger.debug("🏠 [ROOM_MANAGER] 列表合并 #{key}: 使用后台配置")
        right_val

      # 其他类型（包括原子、字符串、数字等）：直接用右边的值覆盖左边
      true ->
        if String.contains?(to_string(key), "odds") or key in [:odds_config, "odds_config"] do
          Logger.info("🏠 [ROOM_MANAGER] 重要配置项 #{key}: #{inspect(left_val)} -> #{inspect(right_val)}")
        end
        right_val
    end
  end

  # 标准化键类型，使后台配置的键类型与默认配置匹配（参考 Slot777Config）
  defp normalize_keys_to_match_default(custom_config, default_config) when is_map(custom_config) and is_map(default_config) do
    # 获取默认配置的键类型模式
    default_keys = Map.keys(default_config)

    custom_config
    |> Enum.map(fn {key, value} ->
      # 找到对应的默认配置键
      matching_default_key = find_matching_key(key, default_keys)

      # 递归处理嵌套的Map
      normalized_value = if is_map(value) and matching_default_key do
        default_nested = Map.get(default_config, matching_default_key, %{})
        if is_map(default_nested) do
          normalize_keys_to_match_default(value, default_nested)
        else
          value
        end
      else
        value
      end

      # 使用匹配的键或原键
      final_key = matching_default_key || key
      {final_key, normalized_value}
    end)
    |> Enum.into(%{})
  end

  defp normalize_keys_to_match_default(value, _default), do: value

  # 查找匹配的键（处理字符串键 <-> 原子键的转换）
  defp find_matching_key(key, default_keys) do
    Enum.find(default_keys, fn default_key ->
      cond do
        # 完全匹配
        key == default_key -> true

        # 字符串键 -> 原子键
        is_binary(key) and is_atom(default_key) ->
          to_string(default_key) == key

        # 原子键 -> 字符串键
        is_atom(key) and is_binary(default_key) ->
          to_string(key) == default_key

        true -> false
      end
    end)
  end
  def validate_config(config) when is_map(config) do
    required_keys = [:game_basic, :betting, :win_rate, :payout, :symbol_weights, :jackpot, :free_game, :testing]
    missing_keys = Enum.filter(required_keys, fn key -> not Map.has_key?(config, key) end)

    case missing_keys do
      [] -> {:ok, config}
      keys -> {:error, "缺少必需的配置项: #{inspect(keys)}"}
    end
  end

  def validate_config(_), do: {:error, "配置必须是Map类型"}

  def reload_config, do: :ok
  def update_config(_new_config), do: :ok
end
