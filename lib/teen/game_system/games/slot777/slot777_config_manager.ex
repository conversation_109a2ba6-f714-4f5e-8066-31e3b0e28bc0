defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.Slot777.Slot777ConfigManager do
  @moduledoc """
  Slot777配置管理工具
  
  提供运营人员友好的配置管理接口，包括：
  - 配置预设模板
  - 配置验证
  - 配置热更新
  - 配置导入导出
  - 配置历史记录
  """

  require Logger
  alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777Config

  @doc """
  获取配置预设模板
  """
  def get_config_presets do
    %{
      # 高中奖率模式 (适合活动期间)
      high_win_rate: %{
        name: "高中奖率模式",
        description: "适合活动期间，提高玩家体验",
        win_rate: %{
          rtp: 92.0,
          icon_weights: %{
            0 => 3,    # WILD字 - 增加
            1 => 25,   # 香蕉
            2 => 22,   # 西瓜
            3 => 18,   # 草莓
            4 => 15,   # 葡萄
            5 => 12,   # 芒果
            6 => 10,   # 榴莲
            7 => 8,    # 山竹
            8 => 5,    # BAR
            9 => 8,    # 苹果 - 增加免费游戏
            10 => 4    # 7 - 增加Jackpot
          }
        },
        free_game: %{
          trigger_table: %{5 => 15, 4 => 12, 3 => 8},  # 增加免费次数
          multiplier: 2.0
        }
      },

      # 标准模式 (默认配置)
      standard: %{
        name: "标准模式",
        description: "平衡的游戏体验",
        win_rate: %{
          rtp: 85.0,
          icon_weights: %{
            0 => 1,    # WILD字
            1 => 30,   # 香蕉
            2 => 25,   # 西瓜
            3 => 20,   # 草莓
            4 => 15,   # 葡萄
            5 => 12,   # 芒果
            6 => 10,   # 榴莲
            7 => 8,    # 山竹
            8 => 3,    # BAR
            9 => 4,    # 苹果
            10 => 2    # 7
          }
        },
        free_game: %{
          trigger_table: %{5 => 12, 4 => 8, 3 => 4},
          multiplier: 1.5
        }
      },

      # 低中奖率模式 (提高收益)
      low_win_rate: %{
        name: "低中奖率模式",
        description: "提高平台收益",
        win_rate: %{
          rtp: 78.0,
          icon_weights: %{
            0 => 1,    # WILD字 - 保持低频
            1 => 35,   # 香蕉 - 增加低赔率图标
            2 => 30,   # 西瓜 - 增加低赔率图标
            3 => 20,   # 草莓
            4 => 15,   # 葡萄
            5 => 10,   # 芒果
            6 => 8,    # 榴莲
            7 => 6,    # 山竹
            8 => 2,    # BAR - 降低高赔率图标
            9 => 2,    # 苹果 - 降低免费游戏
            10 => 1    # 7 - 降低Jackpot
          }
        },
        free_game: %{
          trigger_table: %{5 => 8, 4 => 5, 3 => 3},  # 降低免费次数
          multiplier: 1.2
        }
      },

      # 测试模式 (方便调试)
      testing: %{
        name: "测试模式",
        description: "方便开发和测试",
        win_rate: %{
          rtp: 95.0,
          icon_weights: %{
            0 => 10,   # WILD字 - 大幅增加
            1 => 20,   # 香蕉
            2 => 20,   # 西瓜
            3 => 15,   # 草莓
            4 => 15,   # 葡萄
            5 => 10,   # 芒果
            6 => 10,   # 榴莲
            7 => 8,    # 山竹
            8 => 8,    # BAR - 增加
            9 => 15,   # 苹果 - 大幅增加免费游戏
            10 => 10   # 7 - 增加Jackpot
          }
        },
        free_game: %{
          trigger_table: %{5 => 20, 4 => 15, 3 => 10},  # 大幅增加
          multiplier: 3.0
        }
      }
    }
  end

  @doc """
  应用配置预设
  """
  def apply_preset(preset_name) do
    presets = get_config_presets()
    
    case Map.get(presets, preset_name) do
      nil ->
        {:error, "预设不存在: #{preset_name}"}
      
      preset ->
        Logger.info("🔧 [SLOT777_CONFIG] 应用配置预设: #{preset.name}")
        
        # 获取当前配置
        current_config = Slot777Config.get_current_config()
        
        # 合并预设配置
        updated_config = deep_merge(current_config, preset)
        
        # 验证配置
        case validate_config(updated_config) do
          :ok ->
            # 应用配置 (这里可以实现配置持久化)
            Logger.info("✅ [SLOT777_CONFIG] 配置预设应用成功: #{preset.name}")
            {:ok, updated_config}
          
          {:error, reason} ->
            Logger.error("❌ [SLOT777_CONFIG] 配置验证失败: #{reason}")
            {:error, reason}
        end
    end
  end

  @doc """
  验证配置有效性
  """
  def validate_config(config) do
    with :ok <- validate_win_rate_config(config.win_rate),
         :ok <- validate_payout_config(config.payout),
         :ok <- validate_betting_config(config.betting),
         :ok <- validate_jackpot_config(config.jackpot),
         :ok <- validate_free_game_config(config.free_game) do
      :ok
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  获取配置调整建议
  """
  def get_tuning_suggestions(current_stats) do
    suggestions = []
    
    # 根据统计数据给出调整建议
    suggestions = if current_stats.win_rate > 90 do
      ["中奖率过高，建议降低图标权重或赔率" | suggestions]
    else
      suggestions
    end
    
    suggestions = if current_stats.jackpot_frequency < 0.001 do
      ["Jackpot触发频率过低，建议增加7的权重" | suggestions]
    else
      suggestions
    end
    
    suggestions = if current_stats.free_game_frequency < 0.01 do
      ["免费游戏触发频率过低，建议增加苹果的权重" | suggestions]
    else
      suggestions
    end
    
    %{
      suggestions: suggestions,
      recommended_preset: recommend_preset(current_stats),
      risk_level: calculate_risk_level(current_stats)
    }
  end

  @doc """
  导出配置为JSON格式
  """
  def export_config_to_json do
    config = Slot777Config.get_current_config()
    
    case Jason.encode(config, pretty: true) do
      {:ok, json} ->
        timestamp = DateTime.utc_now() |> DateTime.to_iso8601()
        filename = "slot777_config_#{timestamp}.json"
        {:ok, json, filename}
      
      {:error, reason} ->
        {:error, "导出失败: #{reason}"}
    end
  end

  @doc """
  从JSON导入配置
  """
  def import_config_from_json(json_string) do
    case Jason.decode(json_string) do
      {:ok, config_map} ->
        # 转换为原子键的配置
        config = convert_keys_to_atoms(config_map)
        
        case validate_config(config) do
          :ok ->
            Logger.info("✅ [SLOT777_CONFIG] 配置导入成功")
            {:ok, config}
          
          {:error, reason} ->
            {:error, "配置验证失败: #{reason}"}
        end
      
      {:error, reason} ->
        {:error, "JSON解析失败: #{reason}"}
    end
  end

  @doc """
  获取配置修改历史
  """
  def get_config_history(limit \\ 10) do
    # 这里可以从数据库或日志中获取配置修改历史
    # 暂时返回模拟数据
    [
      %{
        timestamp: DateTime.utc_now(),
        action: "应用预设",
        preset: "standard",
        operator: "admin",
        changes: ["中奖率调整为85%", "免费游戏次数调整"]
      }
    ]
  end

  # ===== 私有函数 =====

  # 验证中奖率配置
  defp validate_win_rate_config(win_rate_config) do
    cond do
      win_rate_config.rtp < 70 or win_rate_config.rtp > 98 ->
        {:error, "RTP必须在70%-98%之间"}
      
      not is_map(win_rate_config.icon_weights) ->
        {:error, "图标权重必须是Map格式"}
      
      Enum.any?(win_rate_config.icon_weights, fn {_icon, weight} -> weight < 0 end) ->
        {:error, "图标权重不能为负数"}
      
      true ->
        :ok
    end
  end

  # 验证赔率配置
  defp validate_payout_config(payout_config) do
    if is_map(payout_config.payout_table) do
      :ok
    else
      {:error, "赔率表必须是Map格式"}
    end
  end

  # 验证下注配置
  defp validate_betting_config(betting_config) do
    cond do
      betting_config.min_bet <= 0 ->
        {:error, "最小下注必须大于0"}
      
      betting_config.max_bet <= betting_config.min_bet ->
        {:error, "最大下注必须大于最小下注"}
      
      true ->
        :ok
    end
  end

  # 验证Jackpot配置
  defp validate_jackpot_config(jackpot_config) do
    cond do
      jackpot_config.contribution_rate < 0 or jackpot_config.contribution_rate > 0.1 ->
        {:error, "Jackpot贡献率必须在0%-10%之间"}
      
      true ->
        :ok
    end
  end

  # 验证免费游戏配置
  defp validate_free_game_config(free_game_config) do
    cond do
      free_game_config.multiplier < 1.0 ->
        {:error, "免费游戏倍率不能小于1.0"}
      
      true ->
        :ok
    end
  end

  # 推荐预设
  defp recommend_preset(stats) do
    cond do
      stats.win_rate > 90 -> :low_win_rate
      stats.win_rate < 80 -> :high_win_rate
      true -> :standard
    end
  end

  # 计算风险等级
  defp calculate_risk_level(stats) do
    cond do
      stats.win_rate > 95 -> :high
      stats.win_rate > 90 -> :medium
      stats.win_rate < 75 -> :medium
      true -> :low
    end
  end

  # 深度合并Map
  defp deep_merge(left, right) when is_map(left) and is_map(right) do
    Map.merge(left, right, fn _key, left_val, right_val ->
      deep_merge(left_val, right_val)
    end)
  end

  defp deep_merge(_left, right), do: right

  # 转换键为原子
  defp convert_keys_to_atoms(map) when is_map(map) do
    Map.new(map, fn {key, value} ->
      atom_key = if is_binary(key), do: String.to_atom(key), else: key
      {atom_key, convert_keys_to_atoms(value)}
    end)
  end

  defp convert_keys_to_atoms(value), do: value
end
