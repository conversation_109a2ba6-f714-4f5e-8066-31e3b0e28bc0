defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.Slot777.Slot777GameLogic do
  @moduledoc """
  Slot777游戏逻辑引擎
  实现老虎机的核心算法，包括：
  - 图标生成
  - 中奖线计算
  - 赔率计算
  - Jackpot计算
  - 免费游戏触发
  """

  require Logger

  # 引入配置模块
  alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777Config

  # 游戏常量
  @rows 3
  @cols 5
  @max_lines 9

  # 图标类型 (对应前端 EM_SLOT777_ICONTYPE) - 值范围0-10
  @icon_types %{
    wild: 0,        # WILD字 (NUM_WILD)
    banana: 1,      # 香蕉 (NUM_1)
    watermelon: 2,  # 西瓜 (NUM_2)
    strawberry: 3,  # 草莓 (NUM_3)
    grape: 4,       # 葡萄 (NUM_4)
    mango: 5,       # 芒果 (NUM_5)
    durian: 6,      # 榴莲 (NUM_6)
    mangosteen: 7,  # 山竹 (NUM_7)
    bar: 8,         # BAR (NUM_8)
    apple: 9,       # 苹果 (NUM_apple)
    seven: 10       # 7 (NUM_777)
  }

  # 中奖线定义 (3行5列，按位置索引1-15) - 与前端LINE_POS_EX完全一致
  # 矩阵布局: 1  2  3  4  5
  #          6  7  8  9  10
  #          11 12 13 14 15
  #
  # ⚠️ 重要：必须与前端 SLOT777Define.ts 中的 LINE_POS_EX 完全一致！
  @win_lines [
    [6, 7, 8, 9, 10],             # 线1: 中行 (与前端LINE_POS_EX[0]一致)
    [1, 2, 3, 4, 5],              # 线2: 上行 (与前端LINE_POS_EX[1]一致)
    [11, 12, 13, 14, 15],         # 线3: 下行 (与前端LINE_POS_EX[2]一致)
    [1, 7, 13, 9, 5],             # 线4: V形 (与前端LINE_POS_EX[3]一致)
    [11, 7, 3, 9, 15],            # 线5: 倒V形 (与前端LINE_POS_EX[4]一致)
    [1, 2, 8, 14, 15],            # 线6: 上升形 (与前端LINE_POS_EX[5]一致)
    [11, 12, 8, 4, 5],            # 线7: 下降形 (与前端LINE_POS_EX[6]一致)
    [6, 12, 8, 4, 10],            # 线8: M形 (与前端LINE_POS_EX[7]一致)
    [6, 2, 8, 14, 10]             # 线9: W形 (与前端LINE_POS_EX[8]一致)
  ]

  @doc """
  生成游戏结果

  ## 参数
  - bet_odds: 下注倍率
  - player_money: 玩家金币数量
  - base_bet: 基础下注金额 (difen)
  - game_config: 游戏配置 (包含 bet_rate_num 和 score_rate)
  """
  def generate_game_result(bet_odds, player_money, base_bet \\ nil, game_config \\ nil) do
    # 获取配置
    config = Slot777Config.get_current_config()
    betting_config = config.betting

    # 使用配置中的值或传入的值
    actual_base_bet = base_bet || betting_config.difen
    bet_rate_num = if game_config, do: game_config.bet_rate_num, else: betting_config.bet_rate_num
    score_rate = if game_config, do: game_config.score_rate, else: betting_config.score_rate

    # 生成3x5的图标矩阵
    icon_matrix = generate_icon_matrix(config)

    # 转换为前端期望的一维数组格式 (1-15)
    icon_result = matrix_to_array(icon_matrix)

    # 计算中奖线
    {lineresult_obj, win_lines_info, total_multiplier} = calculate_win_lines(icon_matrix, config)

    # 计算实际下注金额 - 使用配置中的值
    # 前端计算: odds × BET_RATE_NUM × difen × SCORE_RATE
    bet_amount = actual_base_bet * bet_odds  * score_rate * bet_rate_num

    # 计算奖金 - 得奖不需要乘以bet_rate_num，所以使用不含bet_rate_num的金额计算
    win_bet_amount = actual_base_bet * bet_odds * score_rate
    win_money = if total_multiplier > 0 do
      trunc(win_bet_amount * total_multiplier)
    else
      0
    end

    # 计算玩家输赢 (赢金 - 下注)
    change_money = win_money - bet_amount

    # 检查是否触发Jackpot - 得奖不需要乘以bet_rate_num，所以使用不含bet_rate_num的金额计算
    {jackpot_cash, seven_count} = calculate_jackpot(icon_matrix, win_bet_amount, config)

    # 检查是否触发免费游戏
    free_times = calculate_free_times(icon_matrix, config)

    # 构建结果 - 使用前端期望的格式
    %{
      "freetimes" => free_times,
      "sevennum" => seven_count,
      "iconresult" => icon_result,
      "linecount" => map_size(lineresult_obj),
      "lineresult" => lineresult_obj,
      "totalmult" => total_multiplier,
      "winmoney" => win_money,
      "changemoney" => change_money,
      "jackpotcash" => jackpot_cash,
      "luckyjackpot" => 0,  # 转盘彩金，暂时为0
      "winLinesInfo" => win_lines_info,  # 前端需要的中奖线信息
      "multgoldnum" => if(total_multiplier > 0, do: total_multiplier, else: 0)  # 倍率金币数
    }
  end

  @doc """
  生成3x5的图标矩阵
  """
  defp generate_icon_matrix(config) do
    for _row <- 1..@rows do
      for _col <- 1..@cols do
        generate_weighted_icon(config)
      end
    end
  end

  @doc """
  根据权重生成图标
  """
  defp generate_weighted_icon(config) do
    # 获取图标权重配置
    icon_weights = config.win_rate.icon_weights
    total_weight = icon_weights |> Map.values() |> Enum.sum()
    random_value = :rand.uniform(total_weight)

    find_icon_by_weight(random_value, icon_weights, 0)
  end

  defp find_icon_by_weight(target, weights, current_sum) do
    Enum.find_value(weights, fn {icon, weight} ->
      new_sum = current_sum + weight
      if target <= new_sum do
        icon
      else
        find_icon_by_weight(target, Map.delete(weights, icon), new_sum)
      end
    end) || 0  # 默认返回樱桃（现在是值0）
  end

  @doc """
  将3x5矩阵转换为1-15的一维数组
  """
  defp matrix_to_array(matrix) do
    matrix
    |> List.flatten()
    |> Enum.with_index(1)
    |> Enum.into(%{}, fn {icon, index} -> {index, icon} end)
  end

  @doc """
  计算中奖线
  """
  defp calculate_win_lines(matrix, config) do
    flat_matrix = List.flatten(matrix)

    # 计算完整的中奖线信息（包含倍率）
    full_win_lines =
      @win_lines
      |> Enum.with_index(1)
      |> Enum.reduce([], fn {line_positions, line_index}, acc ->
        case check_line_win(flat_matrix, line_positions, config) do
          {true, icon_type, count, multiplier} ->
            win_info = %{
              "line" => line_index,
              "icon" => icon_type,
              "num" => count,
              "mult" => multiplier
            }
            [win_info | acc]

          {false, _, _, _} ->
            acc
        end
      end)
      |> Enum.reverse()

    # 生成前端期望的格式：lineresult 为对象，winLinesInfo 为数组
    {lineresult_obj, win_lines_info} = if length(full_win_lines) > 0 do
      # lineresult: 对象格式 {"1": {line: x, num: y}, "2": {...}}
      lineresult_obj =
        full_win_lines
        |> Enum.with_index(1)
        |> Enum.into(%{}, fn {win_info, index} ->
          {to_string(index), %{
            "line" => win_info["line"],
            "num" => win_info["num"]
          }}
        end)

      # winLinesInfo: 数组格式，包含中奖位置信息
      win_lines_info =
        full_win_lines
        |> Enum.map(fn win_info ->
          line_num = win_info["line"]
          count = win_info["num"]

          # 获取该线的位置定义
          line_positions = Enum.at(@win_lines, line_num - 1, [])

          # 取前count个位置作为中奖位置
          prize_icon_list = Enum.take(line_positions, count)

          %{
            "nLine" => line_num,
            "nCount" => count,
            "prizeIconList" => prize_icon_list
          }
        end)

      {lineresult_obj, win_lines_info}
    else
      {%{}, []}
    end

    # 计算总倍率
    total_multiplier =
      full_win_lines
      |> Enum.map(& &1["mult"])
      |> Enum.sum()

    {lineresult_obj, win_lines_info, total_multiplier}
  end

  @doc """
  检查单条线是否中奖
  """
  defp check_line_win(flat_matrix, line_positions, config) do
    # 获取线上的图标
    line_icons = Enum.map(line_positions, fn pos ->
      Enum.at(flat_matrix, pos - 1)
    end)

    # 检查连续相同图标 (从左到右，至少2个连续才有奖)
    case find_consecutive_match(line_icons) do
      {icon_type, count} when count >= 2 ->
        multiplier = get_payout_multiplier(icon_type, count, config)
        if multiplier > 0 do
          {true, icon_type, count, multiplier}
        else
          {false, nil, 0, 0}
        end

      _ ->
        {false, nil, 0, 0}
    end
  end

  @doc """
  查找连续匹配的图标 (支持万能牌替代)
  """
  defp find_consecutive_match([first | rest]) do
    # 如果第一个是万能牌，找到第一个非万能牌作为基准
    base_icon = if first == 0, do: find_first_non_wild(rest), else: first

    if base_icon do
      count = 1 + count_consecutive_with_wild(base_icon, rest)
      {base_icon, count}
    else
      # 全是万能牌的情况，按最高价值图标计算 (7)
      {10, length([first | rest])}
    end
  end

  # 处理空列表的情况
  defp find_consecutive_match([]), do: {nil, 0}

  # 找到第一个非万能牌图标
  defp find_first_non_wild([]), do: nil
  defp find_first_non_wild([0 | rest]), do: find_first_non_wild(rest)  # WILD = 0
  defp find_first_non_wild([icon | _]), do: icon

  # 支持万能牌的连续计数
  defp count_consecutive_with_wild(_target, []), do: 0
  defp count_consecutive_with_wild(target, [0 | rest]), do: 1 + count_consecutive_with_wild(target, rest)  # WILD = 0
  defp count_consecutive_with_wild(target, [target | rest]), do: 1 + count_consecutive_with_wild(target, rest)
  defp count_consecutive_with_wild(_target, _), do: 0

  @doc """
  获取赔率倍数
  """
  defp get_payout_multiplier(icon_type, count, config) do
    payout_table = config.payout.payout_table
    payout_table
    |> Map.get(icon_type, %{})
    |> Map.get(count, 0)
  end

  @doc """
  计算Jackpot - 根据游戏规则图片
  单线下注达到₹10，相同符号(7)就是jackpot，获得Jackpot池的一定比例
  """
  defp calculate_jackpot(matrix, bet_amount, config) do
    flat_matrix = List.flatten(matrix)
    seven_count = Enum.count(flat_matrix, & &1 == 10)  # 7现在是值10

    # 计算单线下注金额 (总下注除以线数)
    single_line_bet = bet_amount / @max_lines

    # 获取当前Jackpot池金额
    current_jackpot_pool = get_current_jackpot()

    # 根据7的数量和单线下注金额计算Jackpot比例
    jackpot_percentage = get_jackpot_percentage(seven_count, single_line_bet, config)

    jackpot_cash = if jackpot_percentage > 0 do
      trunc(current_jackpot_pool * jackpot_percentage / 100)
    else
      0
    end

    {jackpot_cash, seven_count}
  end

  @doc """
  根据7的数量和单线下注金额获取Jackpot比例
  """
  defp get_jackpot_percentage(seven_count, single_line_bet, config) when seven_count >= 3 do
    percentage_table = config.jackpot.percentage_table

    # 找到匹配的下注级别
    bet_level = cond do
      single_line_bet >= 200 -> 200
      single_line_bet >= 100 -> 100
      single_line_bet >= 20 -> 20
      single_line_bet >= 10 -> 10
      true -> nil
    end

    if bet_level do
      level_config = Map.get(percentage_table, bet_level, %{})
      Map.get(level_config, seven_count, 0)
    else
      0
    end
  end

  defp get_jackpot_percentage(_, _, _), do: 0

  @doc """
  计算免费游戏次数 - 根据游戏规则图片
  苹果(FREE)触发免费游戏：3个=5次，4个=10次，5个=15次
  """
  defp calculate_free_times(matrix, config) do
    flat_matrix = List.flatten(matrix)
    apple_count = Enum.count(flat_matrix, & &1 == 9)  # 苹果(FREE)现在是值9

    trigger_table = config.free_game.trigger_table
    Map.get(trigger_table, apple_count, 0)
  end

  @doc """
  获取当前Jackpot池金额
  """
  def get_current_jackpot() do
    # 这里可以从数据库或缓存中获取实际的Jackpot金额
    # 暂时返回一个模拟值
    base_jackpot = 100000
    random_addition = :rand.uniform(50000)
    base_jackpot + random_addition
  end

  @doc """
  更新Jackpot池
  """
  def update_jackpot_pool(bet_amount) do
    # 获取配置中的贡献率
    config = Slot777Config.get_current_config()
    contribution_rate = config.jackpot.contribution_rate

    # 每次下注的一定比例进入Jackpot池
    contribution = trunc(bet_amount * contribution_rate)

    # 这里应该更新数据库中的Jackpot金额
    Logger.info("🎰 [SLOT777] Jackpot池增加: #{contribution} (贡献率: #{contribution_rate * 100}%)")

    contribution
  end

  @doc """
  测试特定图标矩阵的中奖情况 - 用于调试
  """
  def test_specific_matrix(icon_matrix) do
    Logger.info("🎰 [SLOT777] 测试图标矩阵: #{inspect(icon_matrix)}")

    # 获取配置
    config = Slot777Config.get_current_config()

    # 转换为前端期望的一维数组格式 (1-15)
    icon_result = matrix_to_array(icon_matrix)
    Logger.info("🎰 [SLOT777] 一维数组: #{inspect(icon_result)}")

    # 计算中奖线
    {lineresult_obj, win_lines_info, total_multiplier} = calculate_win_lines(icon_matrix, config)
    Logger.info("🎰 [SLOT777] 中奖线: #{inspect(lineresult_obj)}")
    Logger.info("🎰 [SLOT777] winLinesInfo: #{inspect(win_lines_info)}")
    Logger.info("🎰 [SLOT777] 总赔率: #{total_multiplier}")

    %{
      "iconresult" => icon_result,
      "linecount" => map_size(lineresult_obj),
      "lineresult" => lineresult_obj,
      "winLinesInfo" => win_lines_info,
      "totalmult" => total_multiplier
    }
  end

  @doc """
  测试用户图片中的具体中奖情况
  """
  def test_user_image_case() do
    # 根据用户图片的矩阵 (使用新的图标映射):
    # 第1行: WILD(0) + 香蕉(1) + 香蕉(1) + BAR(8) + 草莓(3)
    # 第2行: 榴莲(6) + 葡萄(4) + 榴莲(6) + 西瓜(2) + WILD(0)
    # 第3行: WILD(0) + WILD(0) + WILD(0) + 葡萄(4) + 芒果(5)
    test_matrix = [
      [0, 1, 1, 8, 3],   # 第1行: WILD, 香蕉, 香蕉, BAR, 草莓
      [6, 4, 6, 2, 0],   # 第2行: 榴莲, 葡萄, 榴莲, 西瓜, WILD
      [0, 0, 0, 4, 5]    # 第3行: WILD, WILD, WILD, 葡萄, 芒果
    ]

    Logger.info("🎰 [SLOT777] 测试用户图片中奖情况")
    result = test_specific_matrix(test_matrix)

    # 详细分析每条中奖线
    flat_matrix = List.flatten(test_matrix)

    Logger.info("🎰 [SLOT777] 详细分析用户图片的中奖线:")

    # 获取配置
    config = Slot777Config.get_current_config()

    # 线2 [1,2,3,4,5]: 第1行 - WILD(0) + 香蕉(1) + 香蕉(1) + BAR(8) + 草莓(3)
    line2_icons = [0, 1, 1, 8, 3]
    Logger.info("🎰 [SLOT777] 线2 (第1行): #{inspect(line2_icons)}")
    case find_consecutive_match(line2_icons) do
      {icon_type, count} ->
        multiplier = get_payout_multiplier(icon_type, count, config)
        Logger.info("🎰 [SLOT777] 线2结果: #{icon_type}图标 #{count}连 = #{multiplier}倍")
      _ ->
        Logger.info("🎰 [SLOT777] 线2结果: 无中奖")
    end

    # 线3 [11,12,13,14,15]: 第3行 - WILD(0) + WILD(0) + WILD(0) + 葡萄(4) + 芒果(5)
    line3_icons = [0, 0, 0, 4, 5]
    Logger.info("🎰 [SLOT777] 线3 (第3行): #{inspect(line3_icons)}")
    case find_consecutive_match(line3_icons) do
      {icon_type, count} ->
        multiplier = get_payout_multiplier(icon_type, count, config)
        Logger.info("🎰 [SLOT777] 线3结果: #{icon_type}图标 #{count}连 = #{multiplier}倍")
      _ ->
        Logger.info("🎰 [SLOT777] 线3结果: 无中奖")
    end

    result
  end

  @doc """
  测试图片中的香蕉中奖情况 - 使用修正后的中奖线
  """
  def test_banana_case() do
    # 根据图片分析的矩阵:
    # 位置:  1    2    3    4    5
    #       葡萄  WILD  草莓  WILD  香蕉    (第1行) - [3, 10, 2, 10, 0]
    #       香蕉  草莓  葡萄  葡萄  玉米    (第2行) - [0, 2, 3, 3, 5]
    #       西瓜  香蕉  草莓  香蕉  香蕉    (第3行) - [1, 0, 2, 0, 0]
    test_matrix = [
      [3, 10, 2, 10, 0],  # 第1行: 葡萄, WILD, 草莓, WILD, 香蕉
      [0, 2, 3, 3, 5],    # 第2行: 香蕉, 草莓, 葡萄, 葡萄, 玉米
      [1, 0, 2, 0, 0]     # 第3行: 西瓜, 香蕉, 草莓, 香蕉, 香蕉
    ]

    Logger.info("🎰 [SLOT777] 测试香蕉中奖情况 (使用修正后的中奖线)")
    result = test_specific_matrix(test_matrix)

    # 现在使用修正后的中奖线重新分析
    flat_matrix = List.flatten(test_matrix)

    # 新的线1 [6,7,8,9,10]: 香蕉(0)-草莓(2)-葡萄(3)-葡萄(3)-玉米(5)
    line1_icons = [0, 2, 3, 3, 5]  # 位置6,7,8,9,10对应的图标
    Logger.info("🎰 [SLOT777] 新线1图标: #{inspect(line1_icons)} - 只有1个香蕉，不中奖")

    # 新的线9 [6,2,8,14,10]: 香蕉(0)-WILD(10)-葡萄(3)-香蕉(0)-玉米(5)
    line9_icons = [0, 10, 3, 0, 5]  # 位置6,2,8,14,10对应的图标
    Logger.info("🎰 [SLOT777] 新线9图标: #{inspect(line9_icons)} - 香蕉+WILD=2连，应该中奖!")

    result
  end
end
