defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Slot777.Slot777GlobalManager do
  @moduledoc """
  Slot777 全局管理器

  负责管理：
  1. 全局共享的 Jackpot 池
  2. 所有 slot777 房间的注册和管理
  3. 全局中奖通知广播
  4. 房间间的数据同步
  """

  use GenServer
  require Logger

  alias <PERSON>pridina.Teen.GameSystem.Games.Slot777.Slot777Jackpot

  # 状态结构
  defstruct [
    # 全局共享的 Jackpot 状态
    global_jackpot_state: nil,
    # 当前 Jackpot 金额
    current_jackpot_amount: 0,
    # 注册的 slot777 房间列表 %{room_id => room_info}
    registered_rooms: %{},
    # 最近的中奖记录
    recent_winners: [],
    # 统计信息
    stats: %{
      total_rooms: 0,
      total_players: 0,
      total_contributions: 0,
      total_jackpots_won: 0
    }
  ]

  # 客户端 API

  @doc """
  启动全局管理器
  """
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @doc """
  注册 slot777 房间
  """
  def register_room(room_id, room_pid) do
    GenServer.call(__MODULE__, {:register_room, room_id, room_pid})
  end

  @doc """
  注销 slot777 房间
  """
  def unregister_room(room_id) do
    GenServer.call(__MODULE__, {:unregister_room, room_id})
  end

  @doc """
  贡献到全局 Jackpot（广域累积：所有输赢都进入奖池）
  """
  def contribute_to_jackpot(room_id, net_contribution) do
    GenServer.call(__MODULE__, {:contribute_jackpot, room_id, net_contribution})
  end

  @doc """
  生成游戏结果（由全局管理器决定是否中奖）
  """
  def generate_game_result(room_id, user_id, player_name, odds, current_points, game_config) do
    GenServer.call(__MODULE__, {:generate_game_result, room_id, user_id, player_name, odds, current_points, game_config})
  end

  @doc """
  检查是否触发Jackpot
  """
  def check_jackpot_trigger(room_id, user_id, player_name, bet_amount, seven_count, game_result) do
    GenServer.call(__MODULE__, {:check_jackpot_trigger, room_id, user_id, player_name, bet_amount, seven_count, game_result})
  end

  @doc """
  触发 Jackpot 中奖
  """
  def trigger_jackpot(room_id, user_id, player_name, bet_amount, seven_count) do
    GenServer.call(__MODULE__, {:trigger_jackpot, room_id, user_id, player_name, bet_amount, seven_count})
  end

  @doc """
  获取当前 Jackpot 金额
  """
  def get_current_jackpot_amount() do
    GenServer.call(__MODULE__, :get_current_jackpot_amount)
  end

  @doc """
  获取全局统计信息
  """
  def get_global_stats() do
    GenServer.call(__MODULE__, :get_global_stats)
  end

  @doc """
  获取最近中奖记录
  """
  def get_recent_winners(limit \\ 10) do
    GenServer.call(__MODULE__, {:get_recent_winners, limit})
  end

  # 服务器回调

  @impl true
  def init(_opts) do
    Logger.info("🎰 [SLOT777_GLOBAL] 启动 Slot777 全局管理器")

    # 初始化全局 Jackpot 状态
    initial_jackpot_state = Slot777Jackpot.init_state()
    initial_amount = Slot777Jackpot.get_current_amount(initial_jackpot_state)

    state = %__MODULE__{
      global_jackpot_state: initial_jackpot_state,
      current_jackpot_amount: initial_amount,
      registered_rooms: %{},
      recent_winners: [],
      stats: %{
        total_rooms: 0,
        total_players: 0,
        total_contributions: 0,
        total_jackpots_won: 0
      }
    }

    Logger.info("🎰 [SLOT777_GLOBAL] 全局管理器启动完成 - 初始Jackpot: #{initial_amount}")
    {:ok, state}
  end

  @impl true
  def handle_call({:register_room, room_id, room_pid}, _from, state) do
    Logger.info("🏠 [ROOM_REGISTER] 注册 slot777 房间 - 房间ID: #{room_id}")

    # 监控房间进程
    Process.monitor(room_pid)

    # 添加房间信息
    room_info = %{
      pid: room_pid,
      registered_at: DateTime.utc_now(),
      player_count: 0
    }

    new_registered_rooms = Map.put(state.registered_rooms, room_id, room_info)
    new_stats = %{state.stats | total_rooms: map_size(new_registered_rooms)}

    new_state = %{state |
      registered_rooms: new_registered_rooms,
      stats: new_stats
    }

    # 向新房间发送当前 Jackpot 金额
    send_jackpot_update_to_room(room_id, room_pid, state.current_jackpot_amount)

    Logger.info("✅ [ROOM_REGISTER] 房间注册成功 - 总房间数: #{new_stats.total_rooms}")
    {:reply, {:ok, state.current_jackpot_amount}, new_state}
  end

  @impl true
  def handle_call({:unregister_room, room_id}, _from, state) do
    Logger.info("🏠 [ROOM_UNREGISTER] 注销 slot777 房间 - 房间ID: #{room_id}")

    new_registered_rooms = Map.delete(state.registered_rooms, room_id)
    new_stats = %{state.stats | total_rooms: map_size(new_registered_rooms)}

    new_state = %{state |
      registered_rooms: new_registered_rooms,
      stats: new_stats
    }

    Logger.info("✅ [ROOM_UNREGISTER] 房间注销成功 - 剩余房间数: #{new_stats.total_rooms}")
    {:reply, :ok, new_state}
  end

  @impl true
  def handle_call({:contribute_jackpot, room_id, net_contribution}, _from, state) do
    Logger.debug("🌐 [GLOBAL_JACKPOT] 房间 #{room_id} 广域累积贡献: #{net_contribution}")

    # 🌐 广域累积：净贡献可能为正（玩家输钱）或负（玩家赢钱）
    old_amount = state.current_jackpot_amount
    new_amount = max(state.global_jackpot_state.base_amount, old_amount + net_contribution)

    # 更新全局 Jackpot 状态
    new_jackpot_state = %{state.global_jackpot_state |
      current_amount: new_amount,
      total_contributed: state.global_jackpot_state.total_contributed + max(0, net_contribution)
    }

    # 更新统计（只统计正贡献）
    actual_contribution = max(0, net_contribution)
    new_stats = %{state.stats | total_contributions: state.stats.total_contributions + actual_contribution}

    new_state = %{state |
      global_jackpot_state: new_jackpot_state,
      current_jackpot_amount: new_amount,
      stats: new_stats
    }

    # 如果金额有变化，广播给所有房间
    if new_amount != old_amount do
      broadcast_jackpot_update_to_all_rooms(new_state, new_amount)
      Logger.info("📢 [GLOBAL_JACKPOT] 奖池更新广播 - 旧金额: #{old_amount}, 新金额: #{new_amount}, 净贡献: #{net_contribution}")
    end

    {:reply, {net_contribution, new_amount}, new_state}
  end

  @impl true
  def handle_call({:trigger_jackpot, room_id, user_id, player_name, bet_amount, seven_count}, _from, state) do
    Logger.info("🎯 [JACKPOT_WIN] Jackpot 中奖 - 房间: #{room_id}, 玩家: #{player_name} (#{user_id}), 7的数量: #{seven_count}")

    # 触发全局 Jackpot
    case Slot777Jackpot.trigger_jackpot(state.global_jackpot_state, user_id, player_name, bet_amount, seven_count) do
      {:ok, jackpot_amount, new_jackpot_state} ->
        new_current_amount = Slot777Jackpot.get_current_amount(new_jackpot_state)

        # 记录中奖信息
        winner_record = %{
          user_id: user_id,
          player_name: player_name,
          room_id: room_id,
          jackpot_amount: jackpot_amount,
          seven_count: seven_count,
          won_at: DateTime.utc_now()
        }

        # 更新最近中奖记录（保留最近20条）
        new_recent_winners = [winner_record | state.recent_winners] |> Enum.take(20)

        # 更新统计
        new_stats = %{state.stats | total_jackpots_won: state.stats.total_jackpots_won + 1}

        new_state = %{state |
          global_jackpot_state: new_jackpot_state,
          current_jackpot_amount: new_current_amount,
          recent_winners: new_recent_winners,
          stats: new_stats
        }

        # 广播 Jackpot 金额更新给所有房间
        broadcast_jackpot_update_to_all_rooms(new_state, new_current_amount)

        # 广播中奖通知给所有房间
        broadcast_jackpot_winner_notification(new_state, winner_record)

        Logger.info("🎉 [JACKPOT_WIN_SUCCESS] Jackpot 中奖处理完成 - 奖金: #{jackpot_amount}, 新Jackpot: #{new_current_amount}")
        {:reply, {:ok, jackpot_amount}, new_state}

      {:error, reason} ->
        Logger.error("❌ [JACKPOT_WIN_ERROR] Jackpot 中奖失败 - 原因: #{reason}")
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_call(:get_current_jackpot_amount, _from, state) do
    {:reply, state.current_jackpot_amount, state}
  end

  @impl true
  def handle_call(:get_global_stats, _from, state) do
    # 计算当前在线玩家数
    total_players = state.registered_rooms
    |> Enum.map(fn {_room_id, room_info} -> room_info.player_count end)
    |> Enum.sum()

    stats = %{state.stats | total_players: total_players}
    {:reply, stats, %{state | stats: stats}}
  end

  @impl true
  def handle_call({:get_recent_winners, limit}, _from, state) do
    winners = Enum.take(state.recent_winners, limit)
    {:reply, winners, state}
  end

  @impl true
  def handle_call({:generate_game_result, room_id, user_id, player_name, odds, current_points, game_config}, _from, state) do
    Logger.debug("🌐 [GENERATE_RESULT] 房间 #{room_id} 请求生成游戏结果 - 玩家: #{player_name}, 倍率: #{odds}")

    # 使用原有的游戏逻辑生成结果
    alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777GameLogic
    game_result = Slot777GameLogic.generate_game_result(odds, current_points, game_config.difen, game_config)

    # 🌐 由全局管理器决定是否触发Jackpot
    seven_count = Map.get(game_result, "sevennum", 0)
    should_trigger_jackpot = should_trigger_global_jackpot?(state, seven_count, odds)

    final_result = if should_trigger_jackpot do
      Logger.info("🎯 [GLOBAL_JACKPOT] 全局管理器决定触发Jackpot - 玩家: #{player_name}, 7的数量: #{seven_count}")
      # 计算Jackpot奖金
      jackpot_amount = calculate_jackpot_amount(state.current_jackpot_amount, seven_count)
      Map.put(game_result, "jackpotcash", jackpot_amount)
    else
      game_result
    end

    {:reply, final_result, state}
  end

  @impl true
  def handle_call({:check_jackpot_trigger, room_id, user_id, player_name, bet_amount, seven_count, game_result}, _from, state) do
    jackpot_amount = Map.get(game_result, "jackpotcash", 0)

    if jackpot_amount > 0 do
      Logger.info("🎯 [JACKPOT_TRIGGER] 触发Jackpot - 房间: #{room_id}, 玩家: #{player_name}, 金额: #{jackpot_amount}")

      # 触发全局 Jackpot
      case Slot777Jackpot.trigger_jackpot(state.global_jackpot_state, user_id, player_name, bet_amount, seven_count) do
        {:ok, actual_jackpot_amount, new_jackpot_state} ->
          new_current_amount = Slot777Jackpot.get_current_amount(new_jackpot_state)

          # 记录中奖信息
          winner_record = %{
            user_id: user_id,
            player_name: player_name,
            room_id: room_id,
            jackpot_amount: actual_jackpot_amount,
            seven_count: seven_count,
            won_at: DateTime.utc_now()
          }

          # 更新最近中奖记录（保留最近20条）
          new_recent_winners = [winner_record | state.recent_winners] |> Enum.take(20)

          # 更新统计
          new_stats = %{state.stats | total_jackpots_won: state.stats.total_jackpots_won + 1}

          new_state = %{state |
            global_jackpot_state: new_jackpot_state,
            current_jackpot_amount: new_current_amount,
            recent_winners: new_recent_winners,
            stats: new_stats
          }

          # 广播 Jackpot 金额更新给所有房间
          broadcast_jackpot_update_to_all_rooms(new_state, new_current_amount)

          # 广播中奖通知给所有房间
          broadcast_jackpot_winner_notification(new_state, winner_record)

          Logger.info("🎉 [JACKPOT_WIN_SUCCESS] Jackpot 中奖处理完成 - 奖金: #{actual_jackpot_amount}, 新Jackpot: #{new_current_amount}")
          {:reply, {:trigger, actual_jackpot_amount}, new_state}

        {:error, reason} ->
          Logger.error("❌ [JACKPOT_WIN_ERROR] Jackpot 中奖失败 - 原因: #{reason}")
          {:reply, {:error, reason}, state}
      end
    else
      {:reply, {:no_trigger}, state}
    end
  end

  @impl true
  def handle_info({:DOWN, _ref, :process, pid, reason}, state) do
    # 处理房间进程退出
    case find_room_by_pid(state.registered_rooms, pid) do
      {room_id, _room_info} ->
        Logger.info("🏠 [ROOM_DOWN] 房间进程退出 - 房间ID: #{room_id}, 原因: #{inspect(reason)}")
        new_registered_rooms = Map.delete(state.registered_rooms, room_id)
        new_stats = %{state.stats | total_rooms: map_size(new_registered_rooms)}

        new_state = %{state |
          registered_rooms: new_registered_rooms,
          stats: new_stats
        }

        {:noreply, new_state}

      nil ->
        {:noreply, state}
    end
  end

  # 🌐 广域累积Jackpot辅助函数

  @doc """
  判断是否应该触发全局Jackpot
  """
  defp should_trigger_global_jackpot?(state, seven_count, odds) do
    # 🎯 全局Jackpot触发条件：
    # 1. 至少有3个7
    # 2. 奖池金额足够大
    # 3. 随机概率（基于奖池大小和下注倍率）

    cond do
      seven_count >= 5 ->
        # 5个7：高概率触发
        trigger_probability = min(0.8, state.current_jackpot_amount / 1_000_000 * odds / 100)
        :rand.uniform() < trigger_probability

      seven_count >= 4 ->
        # 4个7：中等概率触发
        trigger_probability = min(0.3, state.current_jackpot_amount / 2_000_000 * odds / 100)
        :rand.uniform() < trigger_probability

      seven_count >= 3 ->
        # 3个7：低概率触发
        trigger_probability = min(0.1, state.current_jackpot_amount / 5_000_000 * odds / 100)
        :rand.uniform() < trigger_probability

      true ->
        false
    end
  end

  @doc """
  计算Jackpot奖金金额
  """
  defp calculate_jackpot_amount(current_amount, seven_count) do
    # 根据7的数量计算奖金比例
    percentage = case seven_count do
      5 -> 1.0    # 5个7：100%奖池
      4 -> 0.5    # 4个7：50%奖池
      3 -> 0.2    # 3个7：20%奖池
      _ -> 0.1    # 其他：10%奖池
    end

    trunc(current_amount * percentage)
  end

  # 私有函数

  # 广播 Jackpot 更新给所有房间
  defp broadcast_jackpot_update_to_all_rooms(state, new_amount) do
    jackpot_message = %{
      "mainId" => 5,
      "subId" => 1005,  # SC_SLOT777_JACKPOT_P
      "data" => %{
        "jackpot" => new_amount
      }
    }

    Enum.each(state.registered_rooms, fn {room_id, room_info} ->
      send_message_to_room(room_id, room_info.pid, jackpot_message)
    end)
  end

  # 广播中奖通知给所有房间
  defp broadcast_jackpot_winner_notification(state, winner_record) do
    winner_message = %{
      "mainId" => 5,
      "subId" => 1007,  # SC_SLOT777_JACKPOT_WINNER_NOTIFICATION
      "data" => %{
        "winner_name" => winner_record.player_name,
        "jackpot_amount" => winner_record.jackpot_amount,
        "seven_count" => winner_record.seven_count,
        "room_id" => winner_record.room_id,
        "won_at" => DateTime.to_unix(winner_record.won_at)
      }
    }

    Enum.each(state.registered_rooms, fn {room_id, room_info} ->
      send_message_to_room(room_id, room_info.pid, winner_message)
    end)

    Logger.info("📢 [WINNER_BROADCAST] 中奖通知已广播给 #{map_size(state.registered_rooms)} 个房间")
  end

  # 发送 Jackpot 更新给特定房间
  defp send_jackpot_update_to_room(room_id, room_pid, amount) do
    message = %{
      "mainId" => 5,
      "subId" => 1005,
      "data" => %{
        "jackpot" => amount
      }
    }

    send_message_to_room(room_id, room_pid, message)
  end

  # 发送消息给房间
  defp send_message_to_room(room_id, room_pid, message) do
    GenServer.cast(room_pid, {:broadcast_message, message})
  end

  # 根据 PID 查找房间
  defp find_room_by_pid(registered_rooms, pid) do
    Enum.find(registered_rooms, fn {_room_id, room_info} ->
      room_info.pid == pid
    end)
  end
end
