defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Slot777.Slot777Game do
  @moduledoc """
  Slot777老虎机游戏定义模块

  实现游戏工厂行为，定义Slot777游戏的基本信息和配置
  同时负责启动和管理Slot777全局管理器
  """

  @behaviour Cypridina.RoomSystem.GameFactory

  use GenServer
  require Logger

  alias <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Slot777.Slot777GlobalManager
  alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777Config

  @impl true
  def game_type, do: :slot777
    @impl true
  def game_class_type, do: 1

  @impl true
  def game_name, do: "Slot777老虎机"

  @impl true
  def room_module, do: Cypridina.Teen.GameSystem.Games.Slot777.Slot777Room

  @impl true
  def default_config do
    # 获取统一配置
    config = Slot777Config.get_current_config()

    %{
      # 房间配置
      max_players: config.room.max_players,
      min_players: config.room.min_players,
      auto_start_delay: config.room.auto_start_delay,
      enable_robots: config.room.enable_robots,
      robot_count: config.room.robot_count,

      # 下注配置
      min_bet: config.betting.min_bet,
      max_bet: config.betting.max_bet,
      odds_config: config.betting.odds_config,

      # 游戏配置 (保持兼容性)
      game_config: %{
        # 基础配置
        reels: config.game_basic.cols,
        rows: config.game_basic.rows,
        paylines: config.game_basic.max_lines,

        # 下注配置
        min_bet: config.betting.min_bet,
        max_bet: config.betting.max_bet,
        bet_multipliers: Map.values(config.betting.odds_config),

        # 中奖率配置
        rtp: config.win_rate.rtp,

        # Jackpot配置
        jackpot_config: %{
          seed_amount: config.jackpot.seed_amount,
          contribution_rate: config.jackpot.contribution_rate,
          min_trigger_amount: config.jackpot.min_trigger_amount
        },

        # 免费游戏配置
        free_game_config: %{
          scatter_count: 3,  # 苹果触发
          free_spins: config.free_game.trigger_table[3] || 4,
          multiplier: config.free_game.multiplier
        },

        # 图标配置
        symbol_config: %{
          normal_symbols: [1, 2, 3, 4, 5, 6, 7, 8, 9],
          wild_symbol: 0,    # WILD
          scatter_symbol: 9  # 苹果(FREE)
        },

        # 支付表 (使用配置中的赔率表)
        paytable: config.payout.payout_table
      }
    }
  end

  @impl true
  def is_lobby_game?, do: false

  @impl true
  def supported_game_ids do
    [
      # Slot777 (统一使用的ID)
      40
    ]
  end

  @doc """
  获取游戏统计信息
  """
  def get_game_stats do
    if global_manager_running?() do
      try do
        # 🌐 从全局管理器获取实时统计
        stats = Slot777GlobalManager.get_global_stats()
        jackpot_amount = Slot777GlobalManager.get_current_jackpot_amount()

        %{
          total_rooms: stats.total_rooms,
          active_players: stats.total_players,
          total_jackpot: jackpot_amount,
          total_contributions: stats.total_contributions,
          total_jackpots_won: stats.total_jackpots_won,
          global_manager_status: :running
        }
      rescue
        error ->
          Logger.error("❌ [SLOT777_GAME] 获取统计信息失败: #{inspect(error)}")
          get_default_stats()
      end
    else
      get_default_stats()
    end
  end

  @doc """
  获取当前活跃房间数
  """
  def get_total_rooms do
    if global_manager_running?() do
      try do
        stats = Slot777GlobalManager.get_global_stats()
        stats.total_rooms
      rescue
        _ -> 0
      end
    else
      0
    end
  end

  @doc """
  获取当前活跃玩家数
  """
  def get_active_players do
    if global_manager_running?() do
      try do
        stats = Slot777GlobalManager.get_global_stats()
        stats.total_players
      rescue
        _ -> 0
      end
    else
      0
    end
  end

  # 获取默认统计信息（当全局管理器不可用时）
  defp get_default_stats do
    config = default_config()
    %{
      total_rooms: 0,
      active_players: 0,
      total_jackpot: config.game_config.jackpot_config.seed_amount,
      total_contributions: 0,
      total_jackpots_won: 0,
      global_manager_status: :stopped
    }
  end

  @doc """
  获取当前总Jackpot金额
  """
  def get_total_jackpot do
    # 🌐 从全局管理器获取实时Jackpot金额
    case Slot777GlobalManager.get_current_jackpot_amount() do
      amount when is_number(amount) -> amount
      _ ->
        # 如果全局管理器不可用，返回默认值
        config = default_config()
        config.game_config.jackpot_config.seed_amount
    end
  end

  # 🌐 全局管理器相关功能

  @doc """
  启动Slot777全局管理器
  """
  def start_global_manager do
    case Slot777GlobalManager.start_link([]) do
      {:ok, pid} ->
        Logger.info("🎰 [SLOT777_GAME] 全局管理器启动成功")
        {:ok, pid}

      {:error, {:already_started, pid}} ->
        Logger.info("🎰 [SLOT777_GAME] 全局管理器已存在")
        {:ok, pid}

      {:error, reason} ->
        Logger.error("❌ [SLOT777_GAME] 全局管理器启动失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  停止Slot777全局管理器
  """
  def stop_global_manager do
    case GenServer.whereis(Slot777GlobalManager) do
      nil ->
        Logger.info("🎰 [SLOT777_GAME] 全局管理器未运行")
        :ok

      pid ->
        Logger.info("🎰 [SLOT777_GAME] 停止全局管理器")
        GenServer.stop(pid)
    end
  end

  @doc """
  检查全局管理器是否运行
  """
  def global_manager_running? do
    case GenServer.whereis(Slot777GlobalManager) do
      nil -> false
      _pid -> true
    end
  end

  @doc """
  获取全局管理器状态
  """
  def get_global_manager_status do
    if global_manager_running?() do
      try do
        stats = Slot777GlobalManager.get_global_stats()
        jackpot_amount = Slot777GlobalManager.get_current_jackpot_amount()

        %{
          status: :running,
          jackpot_amount: jackpot_amount,
          stats: stats
        }
      rescue
        _ -> %{status: :error, message: "无法获取状态"}
      end
    else
      %{status: :stopped}
    end
  end

  @doc """
  验证游戏配置
  """
  def validate_config(config) do
    required_keys = [:max_players, :min_players, :game_config]

    case Enum.all?(required_keys, &Map.has_key?(config, &1)) do
      true -> {:ok, config}
      false -> {:error, "Missing required configuration keys"}
    end
  end

  @doc """
  获取游戏版本信息
  """
  def version_info do
    %{
      version: "2.0.0",
      build_date: "2024-12-16",
      features: [
        "基础老虎机游戏",
        "广域累积Jackpot系统",
        "全局管理器",
        "免费游戏",
        "Wild和Scatter图标",
        "多倍率下注",
        "实时统计监控"
      ]
    }
  end

  # 🚀 模块启动时自动启动全局管理器

  @doc """
  初始化Slot777游戏模块
  当游戏被注册到GameFactory时调用
  """
  def init_game_module do
    Logger.info("🎰 [SLOT777_GAME] 初始化Slot777游戏模块")

    # 自动启动全局管理器
    case start_global_manager() do
      {:ok, _pid} ->
        Logger.info("🎰 [SLOT777_GAME] 游戏模块初始化完成，全局管理器已启动")
        :ok

      {:error, reason} ->
        Logger.error("❌ [SLOT777_GAME] 游戏模块初始化失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  清理Slot777游戏模块
  当游戏被从GameFactory注销时调用
  """
  def cleanup_game_module do
    Logger.info("🎰 [SLOT777_GAME] 清理Slot777游戏模块")

    # 停止全局管理器
    stop_global_manager()

    Logger.info("🎰 [SLOT777_GAME] 游戏模块清理完成")
    :ok
  end

  # 🔧 健康检查和维护功能

  @doc """
  执行健康检查
  """
  def health_check do
    %{
      game_module: :healthy,
      global_manager: if(global_manager_running?(), do: :healthy, else: :stopped),
      timestamp: DateTime.utc_now()
    }
  end

  @doc """
  重启全局管理器
  """
  def restart_global_manager do
    Logger.info("🔄 [SLOT777_GAME] 重启全局管理器")

    # 先停止
    stop_global_manager()

    # 等待一秒
    Process.sleep(1000)

    # 再启动
    case start_global_manager() do
      {:ok, _pid} ->
        Logger.info("✅ [SLOT777_GAME] 全局管理器重启成功")
        :ok

      {:error, reason} ->
        Logger.error("❌ [SLOT777_GAME] 全局管理器重启失败: #{inspect(reason)}")
        {:error, reason}
    end
  end
end
