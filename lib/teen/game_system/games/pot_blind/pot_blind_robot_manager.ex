defmodule Cypridina.Teen.GameSystem.Games.PotBlind.PotBlindRobotManager do
  @moduledoc """
  PotBlind游戏机器人管理器

  ## 功能特点
  - 高质量的机器人AI，符合人类思维模式
  - 多种机器人性格类型
  - 智能的下注和比牌策略
  - 动态的风险评估和决策调整
  - 分层的代码架构，易于维护和扩展
  """

  require Logger
  alias Cypridina.Teen.GameSystem.Games.PotBlind.PotBlindGameLogic
  alias Cypridina.Teen.GameSystem.Games.PotBlind.PotBlindAI
  alias Cypridina.Teen.GameSystem.PlayerDataBuilder

  # ==================== 机器人配置 ====================

  # 机器人性格类型及其特征
  @robot_personalities %{
    aggressive: %{
      name: "激进型",
      description: "喜欢加注和比牌，风险承受高",
      aggression: 0.8,              # 攻击性：0.8 = 80%，高攻击性，频繁加注和比牌
      risk_tolerance: 0.8,          # 风险承受度：0.8 = 80%，愿意承担高风险获取高回报
      bluff_frequency: 0.3,         # 诈唬频率：0.3 = 30%，经常诈唬，即使牌不好也可能下注
      fold_threshold: 0.2,          # 弃牌阈值：0.2 = 20%，只有极差的牌才弃牌，坚持到底
      bet_sizing: :large,           # 下注大小：大额下注，施加压力
      competition_bonus: 0.2        # 🔧 比牌概率加成：+20%，更愿意主动比牌
    },
    conservative: %{
      name: "保守型",
      description: "谨慎下注，只在强牌时行动",
      aggression: 0.3,              # 攻击性：0.3 = 30%，低攻击性，很少主动加注
      risk_tolerance: 0.4,          # 风险承受度：0.4 = 40%，中低风险承受，避免冒险
      bluff_frequency: 0.1,         # 诈唬频率：0.1 = 10%，很少诈唬，主要靠真实牌力
      fold_threshold: 0.6,          # 弃牌阈值：0.6 = 60%，牌力不足60%就考虑弃牌
      bet_sizing: :small,           # 下注大小：小额下注，稳健策略
      competition_bonus: 0.1        # 🔧 比牌概率加成：+10%，比较谨慎的比牌倾向
    },
    balanced: %{
      name: "平衡型",
      description: "根据牌力合理决策，适中风险",
      aggression: 0.5,              # 攻击性：0.5 = 50%，中等攻击性，根据情况调整
      risk_tolerance: 0.5,          # 风险承受度：0.5 = 50%，中等风险承受，平衡收益与风险
      bluff_frequency: 0.2,         # 诈唬频率：0.2 = 20%，适度诈唬，增加不可预测性
      fold_threshold: 0.4,          # 弃牌阈值：0.4 = 40%，牌力低于40%考虑弃牌，较为理性
      bet_sizing: :medium,          # 下注大小：中等下注，平衡策略
      competition_bonus: 0.15       # 🔧 比牌概率加成：+15%，适中的比牌倾向
    },
    tight: %{
      name: "紧型",
      description: "只玩强牌，弃牌率高",
      aggression: 0.2,              # 攻击性：0.2 = 20%，较低攻击性，倾向于保守下注
      risk_tolerance: 0.3,          # 风险承受度：0.3 = 30%，风险厌恶，避免高风险决策
      bluff_frequency: 0.05,        # 诈唬频率：0.05 = 5%，很少诈唬，只在极强牌时行动
      fold_threshold: 0.7,          # 弃牌阈值：0.7 = 70%，牌力低于70%就弃牌，非常谨慎
      bet_sizing: :small,           # 下注大小：小额下注，控制风险
      competition_bonus: 0.05
    },
    loose: %{
      name: "松型",
      description: "参与率高，各种牌都可能跟注",
      aggression: 0.6,              # 攻击性：0.6 = 60%，较高攻击性，经常参与行动
      risk_tolerance: 0.7,          # 风险承受度：0.7 = 70%，高风险承受，愿意冒险
      bluff_frequency: 0.25,        # 诈唬频率：0.25 = 25%，经常诈唬，增加游戏变数
      fold_threshold: 0.25,         # 弃牌阈值：0.25 = 25%，很少弃牌，参与率极高
      bet_sizing: :medium,          # 下注大小：中等下注，保持参与度
      competition_bonus: 0.25       # 🔧 比牌概率加成：+25%，最积极的比牌倾向
    },
    tricky: %{
      name: "狡猾型",
      description: "根据游戏阶段调整策略",
      aggression: 0.55,             # 攻击性：0.55 = 55%，中高攻击性，策略性调整
      risk_tolerance: 0.6,          # 风险承受度：0.6 = 60%，中高风险承受，计算风险收益
      bluff_frequency: 0.2,         # 诈唬频率：0.2 = 20%，适度诈唬，时机选择精准
      fold_threshold: 0.35,         # 弃牌阈值：0.35 = 35%，根据局势灵活调整弃牌标准
      bet_sizing: :variable,        # 下注大小：可变下注，根据牌力和局势调整
      competition_bonus: 0.12       # 🔧 比牌概率加成：+12%，策略性比牌，选择时机
    }
  }

  # 机器人配置结构体
  defstruct [
    # 机器人数量配置
    robot_count: 3,                    # 默认机器人数量
    min_robot_count: 1,                # 最小机器人数量
    max_robot_count: 4,                # 最大机器人数量

    # 机器人行为配置 - 🔧 优化比牌相关配置
    action_probability: 0.95,          # 机器人行动概率 (95%)
    base_competition_rate: 0.4,        # 基础比牌概率 (40%) - 各性格在此基础上调整
    competition_hand_threshold: 0.6,   # 比牌最低牌力要求
    bluff_competition_rate: 0.1,       # 诈唬比牌概率 (10%)

    # 积分配置
    initial_points_min: 80000,         # 机器人初始积分最小值
    initial_points_max: 150000,        # 机器人初始积分最大值
    default_robot_points: 100000,      # 默认积分
    min_robot_points: 10000,           # 机器人最低积分

    # 思考时间配置
    min_think_time: 1500,              # 最小思考时间 (1.5秒)
    max_think_time: 8000,              # 最大思考时间 (8秒)
    competition_think_bonus: 1000,     # 比牌额外思考时间

    # 轮换配置
    rotation_probability: 0.05,        # 轮换概率 (5%)
    max_robot_age: 1800000,           # 机器人最大存活时间 (30分钟)
    rotation_interval: 300000,         # 轮换检查间隔 (5分钟)

    # 性格分布配置
    personality_weights: %{            # 各性格出现权重
      aggressive: 20,
      loose: 25,      # 🔧 增加松型权重，提高比牌频率
      balanced: 25,
      conservative: 15,
      tight: 10,
      tricky: 15
    }
  ]

  @type robot_config :: %__MODULE__{
    robot_count: integer(),
    min_robot_count: integer(),
    max_robot_count: integer(),
    action_probability: float(),
    base_competition_rate: float(),
    competition_hand_threshold: float(),
    bluff_competition_rate: float(),
    initial_points_min: integer(),
    initial_points_max: integer(),
    default_robot_points: integer(),
    min_robot_points: integer(),
    min_think_time: integer(),
    max_think_time: integer(),
    competition_think_bonus: integer(),
    rotation_probability: float(),
    max_robot_age: integer(),
    rotation_interval: integer(),
    personality_weights: map()
  }

  # ==================== 公共API ====================

  @doc """
  获取当前配置
  """
  def get_current_config do
    # 可以从配置文件或数据库加载，这里使用默认配置
    %__MODULE__{}
  end

  @doc """
  获取机器人性格配置
  """
  def get_personality_config(personality) when personality in [:aggressive, :conservative, :balanced, :tight, :loose, :tricky] do
    Map.get(@robot_personalities, personality)
  end
  def get_personality_config(_), do: Map.get(@robot_personalities, :balanced)

  @doc """
  获取所有可用的机器人性格
  """
  def get_available_personalities, do: Map.keys(@robot_personalities)

  @doc """
  根据权重随机选择机器人性格
  """
  def choose_random_personality(config \\ %__MODULE__{}) do
    config.personality_weights
    |> Enum.flat_map(fn {personality, weight} ->
         List.duplicate(personality, weight)
       end)
    |> Enum.random()
  end

  @doc """
  判断是否为机器人
  """
  def is_robot?(%{is_robot: is_robot}) when is_boolean(is_robot), do: is_robot
  def is_robot?(_), do: false

  @doc """
  生成机器人昵称 - 使用管道操作符优化
  """
  def generate_robot_nickname do
    %{
      prefixes: ["智能", "高手", "大师", "专家", "玩家", "老手", "新手", "幸运"],
      suffixes: ["小王", "小李", "小张", "小刘", "小陈", "小赵", "小孙", "小周"]
    }
    |> then(fn %{prefixes: prefixes, suffixes: suffixes} ->
         "#{Enum.random(prefixes)}#{Enum.random(suffixes)}#{:rand.uniform(999)}"
       end)
  end

  @doc """
  生成机器人头像
  """
  def generate_robot_avatar do
    1..20
    |> Enum.random()
    |> then(&"local_avatar_#{&1}")
  end

  @doc """
  生成机器人AI数据 - 🔧 基于性格配置生成完整AI数据
  """
  def generate_robot_ai_data(personality, config \\ %__MODULE__{}) do
    base_config = get_personality_config(personality)

    %{
      personality: personality,
      aggression: add_random_variance(base_config.aggression, 0.1),
      risk_tolerance: add_random_variance(base_config.risk_tolerance, 0.1),
      bluff_frequency: add_random_variance(base_config.bluff_frequency, 0.05),
      fold_threshold: add_random_variance(base_config.fold_threshold, 0.1),
      bet_sizing: base_config.bet_sizing,
      competition_bonus: base_config.competition_bonus,

      # 比牌相关配置 - 🔧 从管理器配置获取
      base_competition_rate: config.base_competition_rate,
      competition_hand_threshold: config.competition_hand_threshold,
      bluff_competition_rate: config.bluff_competition_rate,

      # 思考时间配置
      min_think_time: config.min_think_time,
      max_think_time: config.max_think_time,
      competition_think_bonus: config.competition_think_bonus,

      # 统计数据
      games_played: 0,
      total_wins: 0,
      total_losses: 0,
      total_profit: 0,
      consecutive_losses: 0,
      hot_streak: 0,
      hand_reading_skill: add_random_variance(0.7, 0.2),
      position_awareness: add_random_variance(0.6, 0.2),
      created_at: DateTime.utc_now(),
      last_action_time: nil
    }
  end

  # 为数值添加随机变化 - 增加机器人个性差异
  defp add_random_variance(base_value, variance) do
    min_val = max(0.0, base_value - variance)
    max_val = min(1.0, base_value + variance)
    min_val + :rand.uniform() * (max_val - min_val)
  end

  # ==================== 机器人创建和管理 ====================

  @doc """
  创建机器人玩家 - 🔧 使用管道操作符和函数式编程
  """
  def create_robot_players(state, count \\ 2) do
    Logger.info("🤖 [POT_BLIND_ROBOT] 创建 #{count} 个机器人玩家")

    config = get_current_config()

    robots =
      1..count
      |> Enum.map(&create_single_robot(state, &1, config))
      |> Enum.filter(&(&1 != nil))

    Logger.info("🤖 [POT_BLIND_ROBOT] 成功创建 #{length(robots)} 个机器人")
    robots
  end

  @doc """
  当真实玩家加入时，可能添加机器人 - 🔧 使用with语句优化
  """
  def maybe_add_robots_for_real_player(state, real_player) do
    with false <- is_robot?(real_player),
         current_count <- count_robots(state),
         target_count <- calculate_target_robot_count(state),
         needed_count when needed_count > 0 <- max(0, target_count - current_count) do

      Logger.info("🤖 [POT_BLIND_ROBOT] 真实玩家加入: 当前机器人=#{current_count}, 目标=#{target_count}, 需要添加=#{needed_count}")
      add_robots_to_room(state, needed_count)
    else
      true ->
        Logger.info("🤖 [POT_BLIND_ROBOT] 机器人加入，不触发机器人添加逻辑")
        {state, nil}
      0 ->
        Logger.info("🤖 [POT_BLIND_ROBOT] 机器人数量已足够")
        {state, nil}
      _ ->
        {state, nil}
    end
  end

  @doc """
  添加机器人到房间 - 🔧 使用模式匹配优化
  """
  def add_robots_to_room(state, count) when is_integer(count) and count > 0 do
    state
    |> create_robot_players(count)
    |> then(&{state, {:add_robots_with_seats, &1}})
  end

  def add_robots_to_room(state, _count), do: {state, nil}

  @doc """
  管理机器人（清理、轮换、补充） - 🔧 使用管道操作符链式处理
  """
  def manage_robots(state) do
    state
    |> cleanup_broke_robots()
    |> maybe_rotate_robots()
    |> ensure_robot_count()
  end

  @doc """
  触发机器人行动（当轮到机器人操作时调用） - 🔧 使用with语句优化
  """
  def trigger_robot_action(state, robot_id) do
    with {:ok, robot_player} <- fetch_robot_player(state, robot_id),
         true <- is_robot?(robot_player) do

      Logger.info("🤖 [POT_BLIND_ROBOT] 触发机器人 #{robot_id} 行动")

      # 生成机器人决策 - 🔧 传入配置参数
      {decision, think_time} =
        robot_player
        |> ensure_robot_has_ai_config()
        |> then(&PotBlindAI.make_robot_decision(state, &1))

      # 延迟执行机器人行动
      schedule_robot_action(robot_id, decision, think_time)
      state
    else
      {:error, :not_found} ->
        Logger.warning("🤖 [POT_BLIND_ROBOT] 机器人 #{robot_id} 不存在")
        state
      false ->
        Logger.warning("🤖 [POT_BLIND_ROBOT] 玩家 #{robot_id} 不是机器人")
        state
    end
  end

  @doc """
  检查当前玩家是否为机器人，如果是则触发行动 - 🔧 使用模式匹配优化
  """
  def maybe_trigger_robot_turn(state) do
    with current_player_id when not is_nil(current_player_id) <- Map.get(state, :current_player_id),
         {:ok, current_player} <- fetch_robot_player(state, current_player_id),
         true <- is_robot?(current_player) do

      Logger.info("🤖 [POT_BLIND_ROBOT] 当前玩家 #{current_player_id} 是机器人，触发行动")
      trigger_robot_action(state, current_player_id)
    else
      nil ->
        Logger.debug("🤖 [POT_BLIND_ROBOT] 当前没有玩家")
        state
      {:error, :not_found} ->
        Logger.warning("🤖 [POT_BLIND_ROBOT] 当前玩家不存在")
        state
      false ->
        Logger.debug("🤖 [POT_BLIND_ROBOT] 当前玩家不是机器人")
        state
    end
  end

  # ==================== 私有辅助函数 ====================

  # 获取机器人玩家 - 🔧 统一的错误处理
  defp fetch_robot_player(state, player_id) do
    case Map.get(state.players, player_id) do
      nil -> {:error, :not_found}
      player -> {:ok, player}
    end
  end

  # 确保机器人有AI配置 - 🔧 动态补充AI配置
  defp ensure_robot_has_ai_config(robot_player) do
    case robot_player do
      %{robot_ai_data: ai_data} when is_map(ai_data) and map_size(ai_data) > 0 ->
        robot_player
      %{user: %{robot_ai: ai_data}} when is_map(ai_data) and map_size(ai_data) > 0 ->
        robot_player
      _ ->
        # 动态生成AI配置
        personality = choose_random_personality()
        ai_data = generate_robot_ai_data(personality)

        Logger.info("🤖 [POT_BLIND_ROBOT] 为机器人 #{robot_player.numeric_id} 生成AI配置: #{personality}")

        # 更新用户数据中的robot_ai字段
        updated_user = Map.put(robot_player.user, :robot_ai, ai_data)

        robot_player
        |> Map.put(:robot_ai_data, ai_data)
        |> Map.put(:user, updated_user)
    end
  end



  # 创建单个机器人 - 🔧 使用新的配置系统和管道操作符
  defp create_single_robot(state, index, config \\ %__MODULE__{}) do
    config = config || get_current_config()

    # 生成唯一的机器人ID - 🔧 使用管道操作符优化
    robot_id =
      state.id
      |> generate_unique_robot_id(index)

    # 选择机器人性格
    personality = choose_random_personality(config)

    # 生成机器人基础数据 - 🔧 使用函数式编程风格
    robot_base_data = %{
      numeric_id: robot_id,
      id: robot_id,
      nickname: generate_robot_nickname(),
      avatar: generate_robot_avatar(),
      level: :rand.uniform(20),
      points: generate_robot_initial_points(config),
      created_at: DateTime.utc_now()
    }

    # 生成AI数据
    robot_ai_data = generate_robot_ai_data(personality, config)

    # 使用PlayerDataBuilder创建机器人玩家数据 - 🔧 链式调用
    robot_player =
      robot_base_data
      |> PlayerDataBuilder.create_robot_player_data([
           is_ready: true,
           is_robot: true
         ])
      |> add_robot_ai_data(robot_ai_data)

    Logger.info("🤖 [POT_BLIND_ROBOT] 创建机器人成功 - ID: #{robot_id}, 性格: #{personality}, 比牌加成: #{robot_ai_data.competition_bonus}")
    robot_player
  end

  # 生成唯一的机器人ID - 🔧 独立函数，便于测试
  defp generate_unique_robot_id(room_id, index) do
    room_hash = :erlang.phash2(room_id, 1000)
    time_hash = :erlang.phash2(DateTime.utc_now(), 1000)
    base_id = 3000000 + room_hash * 1000 + time_hash + index
    abs(base_id)
  end

  # 为机器人添加AI数据 - 🔧 纯函数，便于测试
  defp add_robot_ai_data(robot_player, robot_ai_data) do
    # 更新用户数据中的robot_ai字段
    updated_user = Map.put(robot_player.user, :robot_ai, robot_ai_data)

    robot_player
    |> Map.put(:robot_ai_data, robot_ai_data)
    |> Map.put(:user, updated_user)
  end

  # ==================== 机器人配置辅助函数 ====================

  # 生成机器人初始积分 - 🔧 使用配置参数
  defp generate_robot_initial_points(config \\ %__MODULE__{}) do
    config = config || get_current_config()

    config.initial_points_min
    |> then(&(&1 + :rand.uniform(config.initial_points_max - &1)))
  end



  # ==================== 机器人管理功能 ====================

  # 清理破产的机器人
  defp cleanup_broke_robots(state) do
    config = get_current_config()

    broke_robots = state.players
    |> Enum.filter(fn {_id, player} ->
      is_robot?(player) and Map.get(player.user, :points, 0) < config.min_robot_points
    end)
    |> Enum.map(fn {id, _player} -> id end)

    if length(broke_robots) > 0 do
      Logger.info("🤖 [POT_BLIND_ROBOT] 清理 #{length(broke_robots)} 个破产机器人")

      Enum.reduce(broke_robots, state, fn robot_id, acc_state ->
        remove_robot_from_room(acc_state, robot_id)
      end)
    else
      state
    end
  end

  # 可能轮换机器人
  defp maybe_rotate_robots(state) do
    config = get_current_config()

    if :rand.uniform() < config.rotation_probability do
      rotate_robots(state)
    else
      state
    end
  end

  # 轮换机器人
  defp rotate_robots(state) do
    robots_to_rotate = find_robots_to_rotate(state)

    if length(robots_to_rotate) > 0 do
      Logger.info("🤖 [POT_BLIND_ROBOT] 轮换 #{length(robots_to_rotate)} 个机器人")

      Enum.reduce(robots_to_rotate, state, fn robot_id, acc_state ->
        remove_robot_from_room(acc_state, robot_id)
      end)
      |> add_replacement_robots(length(robots_to_rotate))
    else
      state
    end
  end

  # 找到需要轮换的机器人
  defp find_robots_to_rotate(state) do
    config = get_current_config()
    current_time = DateTime.utc_now()

    state.players
    |> Enum.filter(fn {_id, player} ->
      if is_robot?(player) do
        created_at = Map.get(player.user, :created_at, current_time)
        age_ms = DateTime.diff(current_time, created_at, :millisecond)
        age_ms > config.max_robot_age
      else
        false
      end
    end)
    |> Enum.map(fn {id, _player} -> id end)
  end

  # 从房间移除机器人
  defp remove_robot_from_room(state, robot_id) do
    Logger.info("🤖 [POT_BLIND_ROBOT] 移除机器人: #{robot_id}")

    updated_players = Map.delete(state.players, robot_id)
    %{state | players: updated_players}
  end

  # 添加替换机器人
  defp add_replacement_robots(state, count) do
    if count > 0 do
      Logger.info("🤖 [POT_BLIND_ROBOT] 添加 #{count} 个替换机器人")

      new_robots = create_robot_players(state, count)

      updated_players = Enum.reduce(new_robots, state.players, fn robot, acc_players ->
        Map.put(acc_players, robot.numeric_id, robot)
      end)

      %{state | players: updated_players}
    else
      state
    end
  end

  # 确保机器人数量
  defp ensure_robot_count(state) do
    config = get_current_config()
    current_robot_count = count_robots(state)
    target_count = calculate_target_robot_count(state)

    cond do
      current_robot_count < target_count ->
        needed = target_count - current_robot_count
        Logger.info("🤖 [POT_BLIND_ROBOT] 需要添加 #{needed} 个机器人")
        add_replacement_robots(state, needed)

      current_robot_count > config.max_robot_count ->
        excess = current_robot_count - config.max_robot_count
        Logger.info("🤖 [POT_BLIND_ROBOT] 需要移除 #{excess} 个机器人")
        remove_excess_robots(state, excess)

      true ->
        state
    end
  end

  # 计算目标机器人数量
  defp calculate_target_robot_count(state) do
    config = get_current_config()
    human_player_count = count_human_players(state)

    # 根据真实玩家数量调整机器人数量
    case human_player_count do
      0 -> config.robot_count  # 没有真实玩家时保持基础数量
      1 -> max(config.robot_count - 1, config.min_robot_count)  # 有1个真实玩家
      _ -> max(config.robot_count - human_player_count + 1, config.min_robot_count)  # 多个真实玩家
    end
  end

  # 移除多余的机器人
  defp remove_excess_robots(state, count) do
    robots = state.players
    |> Enum.filter(fn {_id, player} -> is_robot?(player) end)
    |> Enum.take(count)
    |> Enum.map(fn {id, _player} -> id end)

    Enum.reduce(robots, state, fn robot_id, acc_state ->
      remove_robot_from_room(acc_state, robot_id)
    end)
  end

  # ==================== 辅助函数 ====================

  # 统计机器人数量
  defp count_robots(state) do
    state.players
    |> Enum.count(fn {_id, player} -> is_robot?(player) end)
  end

  # 计算真实玩家数量
  defp count_human_players(state) do
    state.players
    |> Enum.count(fn {_id, player} -> not is_robot?(player) end)
  end

  # ==================== 机器人行动调度 ====================

  # 调度机器人行动
  defp schedule_robot_action(robot_id, decision, think_time) do
    Logger.info("🤖 [POT_BLIND_ROBOT] 调度机器人 #{robot_id} 行动: #{inspect(decision)}, 延迟: #{think_time}ms")

    # 发送延迟消息给房间进程
    Process.send_after(self(), {:robot_action, robot_id, decision}, think_time)
  end

  @doc """
  执行机器人行动（在房间进程的handle_info中调用）
  """
  def execute_robot_action(state, robot_id, decision) do
    Logger.info("🤖 [POT_BLIND_ROBOT] 执行机器人 #{robot_id} 行动: #{inspect(decision)}")

    case Map.get(state.players, robot_id) do
      nil ->
        Logger.warning("🤖 [POT_BLIND_ROBOT] 机器人 #{robot_id} 不存在，无法执行行动")
        state

      robot_player ->
        if is_robot?(robot_player) and Map.get(state, :current_player_id) == robot_id do
          perform_robot_action(state, robot_player, decision)
        else
          Logger.warning("🤖 [POT_BLIND_ROBOT] 机器人 #{robot_id} 不是当前玩家或已不是机器人")
          state
        end
    end
  end

  # 执行具体的机器人行动
  defp perform_robot_action(state, robot_player, decision) do
    case decision do
      {:bet, fill} ->
        Logger.info("🤖 [POT_BLIND_ROBOT] 机器人 #{robot_player.numeric_id} 下注: fill=#{fill}")
        # 发送机器人下注消息给房间进程
        send(self(), {:robot_bet, robot_player.numeric_id, fill})
        state

      {:fold, _} ->
        Logger.info("🤖 [POT_BLIND_ROBOT] 机器人 #{robot_player.numeric_id} 弃牌")
        # 发送机器人弃牌消息给房间进程
        send(self(), {:robot_fold, robot_player.numeric_id})
        state

      {:competition, target_id} ->
        Logger.info("🤖 [POT_BLIND_ROBOT] 机器人 #{robot_player.numeric_id} 比牌，目标: #{target_id}")
        # 发送机器人比牌消息给房间进程
        send(self(), {:robot_competition, robot_player.numeric_id, target_id})
        state

      _ ->
        Logger.warning("🤖 [POT_BLIND_ROBOT] 未知的机器人行动: #{inspect(decision)}")
        # 默认弃牌
        send(self(), {:robot_fold, robot_player.numeric_id})
        state
    end
  end
end
