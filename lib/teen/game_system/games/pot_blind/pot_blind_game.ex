defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.PotBlind.PotBlindGame do
  @moduledoc """
  PotBlind游戏工厂模块 - 基于前端 TPPBDefine.ts 重新定义

  ## 协议定义 (参考前端 TPPBDefine.ts Protos)
  - SC_TEENPATTI_START_P = 1000        # 开始
  - SC_TEENPATTI_SENDCARD_P = 1001     # 发牌
  - CS_TEENPATTI_BET_P = 1002          # 下注
  - SC_TEENPATTI_BET_P = 1003          # 下注
  - CS_TEENPATTI_COMPETITION_P = 1004  # 比牌
  - SC_TEENPATTI_COMPETITION_P = 1005  # 比牌
  - CS_TEENPATTI_FOLD_P = 1006         # 弃牌
  - SC_TEENPATTI_FOLD_P = 1007         # 弃牌
  - SC_TEENPATTI_WAITOPT_P = 1010      # 等待操作
  - SC_TEENPATTI_JIESHUAN_P = 1011     # 结算
  """

  @behaviour Cypridina.RoomSystem.GameFactory

  @impl true
  def game_type, do: :pot_blind

  @impl true
  def game_name, do: "Teen Patti Pot Blind"

  @impl true
  def room_module, do: Cypridina.Teen.GameSystem.Games.PotBlind.PotBlindRoom

  @impl true
  def default_config do
    %{
      # 基础配置 (参考前端 Constant)
      max_players: 5,        # PLAYER_COUNT
      min_players: 2,
      auto_start_delay: 5000, # 5秒自动开始

      # 游戏配置 (参考 C++ 和前端)
      difen: 100,                    # 底注
      pot_limit: 61440,              # 奖池限制
      blind_limit: 3840,             # 盲注限制
      blind_round_limit: 1,          # 盲注轮数限制
      comp_min_turn_num: 3,          # 最小比牌轮数
      oper_wait_time: 13000,         # 操作等待时间(毫秒)

      # 加注倍率 (参考前端 DEFAULT_MULTIS)
      add_bei_lvs: [1, 2, 4, 8]
    }
  end

  @impl true
  def is_lobby_game?, do: false

  @impl true
  def supported_game_ids do
    [3]  # PotBlind 游戏ID
  end

end
