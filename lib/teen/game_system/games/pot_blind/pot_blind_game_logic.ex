defmodule <PERSON>pridina.Teen.GameSystem.Games.PotBlind.PotBlindGameLogic do
  @moduledoc """
  PotBlind游戏逻辑模块 - 专门处理牌相关的逻辑

  ## 功能模块
  - 牌型定义和比较
  - 发牌和洗牌逻辑
  - 牌型识别和评估
  - 比牌结果计算
  - 牌数据格式化
  """

  require Logger

  # ==================== 牌型定义 ====================

  # 牌型类型值（数值越大牌型越大）
  @card_type_values %{
    trail: 6,        # 豹子（三条）
    pure_sequence: 5, # 同花顺
    sequence: 4,     # 顺子
    color: 3,        # 同花
    pair: 2,         # 对子
    high_card: 1     # 高牌
  }

  # 牌型数值（前端期望格式）
  @card_type_numbers %{
    trail: 6,        # EM_TEENPATTI_CARDTYPE_BAOZI 豹子
    pure_sequence: 5, # EM_TEENPATTI_CARDTYPE_TONGHUASHUN 同花顺
    sequence: 4,     # EM_TEENPATTI_CARDTYPE_SHUNZI 顺子
    color: 3,        # EM_TEENPATTI_CARDTYPE_TONGHUA 金花
    pair: 2,         # EM_TEENPATTI_CARDTYPE_DUIZI 对子
    high_card: 1,    # EM_TEENPATTI_CARDTYPE_DAN 单牌
    none: 0          # EM_TEENPATTI_CARDTYPE_NONE 无
  }

  # ==================== 公共API ====================

  @doc """
  创建并洗牌
  """
  def create_and_shuffle_deck() do
    # 创建标准52张牌
    suits = [1, 2, 3, 4]  # 1=黑桃, 2=红桃, 3=梅花, 4=方块
    values = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]  # 11=J, 12=Q, 13=K, 14=A

    deck = for suit <- suits, value <- values do
      %{suit: suit, value: value}
    end

    # 洗牌
    Enum.shuffle(deck)
  end

  @doc """
  比较两手牌的大小
  """
  def compare_cards(cards1, cards2) do
    type1 = get_card_type_value(cards1)
    type2 = get_card_type_value(cards2)

    cond do
      type1 > type2 -> :player1_wins
      type2 > type1 -> :player2_wins
      true -> compare_same_type_cards(cards1, cards2, type1)
    end
  end

  @doc """
  获取牌型
  """
  def get_card_type(cards) when length(cards) == 3 do
    sorted_values = cards |> Enum.map(& &1.value) |> Enum.sort(:desc)
    suits = cards |> Enum.map(& &1.suit) |> Enum.uniq()

    cond do
      # 豹子（三条）
      is_trail?(sorted_values) -> :trail

      # 同花顺
      length(suits) == 1 and is_sequence?(sorted_values) -> :pure_sequence

      # 顺子
      is_sequence?(sorted_values) -> :sequence

      # 同花
      length(suits) == 1 -> :color

      # 对子
      is_pair?(sorted_values) -> :pair

      # 高牌
      true -> :high_card
    end
  end

  def get_card_type(_cards), do: :none

  @doc """
  获取牌型数值（前端期望格式）
  """
  def get_card_type_number(cards) do
    card_type = get_card_type(cards)
    Map.get(@card_type_numbers, card_type, 0)
  end

  @doc """
  格式化牌数据为客户端格式
  """
  def format_cards_for_client(cards) do
    Enum.map(cards, fn card ->
      %{
        "suit" => card.suit,
        "value" => card.value
      }
    end)
  end

  @doc """
  格式化牌数据为 PotBlind 前端期望格式
  前端期望 color 值从3开始：3、4、5、6
  """
  def format_cards_for_potblind_client(cards) do
    cards
    |> Enum.map(fn card ->
      %{
        "color" => convert_suit_to_color(Map.get(card, :suit, 1)),    # suit -> color (3,4,5,6)
        "number" => Map.get(card, :value, 0)   # value -> number
      }
    end)
  end

  # 将内部 suit 值 (1,2,3,4) 转换为前端期望的 color 值 (3,4,5,6)
  defp convert_suit_to_color(suit) do
    case suit do
      1 -> 3  # 黑桃 -> 3
      2 -> 4  # 红桃 -> 4
      3 -> 5  # 梅花 -> 5
      4 -> 6  # 方块 -> 6
      _ -> 3  # 默认值
    end
  end

  @doc """
  比较两个玩家的牌，返回获胜者
  """
  def compare_player_cards(player1, player2) do
    cards1 = Map.get(player1, :cards, [])
    cards2 = Map.get(player2, :cards, [])

    case compare_cards(cards1, cards2) do
      :player1_wins -> player1
      :player2_wins -> player2
      :tie -> player1  # 平局时第一个玩家获胜
    end
  end

  @doc """
  验证牌的有效性
  """
  def validate_cards(cards) do
    cond do
      length(cards) != 3 ->
        {:error, "牌数必须为3张"}

      not Enum.all?(cards, &valid_card?/1) ->
        {:error, "包含无效的牌"}

      has_duplicate_cards?(cards) ->
        {:error, "包含重复的牌"}

      true ->
        {:ok, cards}
    end
  end

  # ==================== 私有函数 ====================

  # 获取牌型类型值（数值越大牌型越大）
  defp get_card_type_value(cards) do
    card_type = get_card_type(cards)
    Map.get(@card_type_values, card_type, 0)
  end

  # 检查是否为豹子（三条）
  defp is_trail?([a, a, a]), do: true
  defp is_trail?(_), do: false

  # 检查是否为对子
  defp is_pair?([a, a, _]), do: true
  defp is_pair?([_, a, a]), do: true
  defp is_pair?(_), do: false

  # 检查是否为顺子
  defp is_sequence?([14, 3, 2]), do: true  # A-3-2 特殊顺子
  defp is_sequence?([a, b, c]) when a == b + 1 and b == c + 1, do: true
  defp is_sequence?(_), do: false

  # 比较相同牌型的牌
  defp compare_same_type_cards(cards1, cards2, type) do
    case type do
      6 -> compare_trail_cards(cards1, cards2)      # 豹子
      5 -> compare_sequence_cards(cards1, cards2)   # 同花顺
      4 -> compare_sequence_cards(cards1, cards2)   # 顺子
      3 -> compare_high_cards(cards1, cards2)       # 同花
      2 -> compare_pair_cards(cards1, cards2)       # 对子
      1 -> compare_high_cards(cards1, cards2)       # 高牌
      _ -> :tie
    end
  end

  # 比较豹子
  defp compare_trail_cards(cards1, cards2) do
    value1 = cards1 |> Enum.map(& &1.value) |> Enum.at(0)
    value2 = cards2 |> Enum.map(& &1.value) |> Enum.at(0)

    cond do
      value1 > value2 -> :player1_wins
      value2 > value1 -> :player2_wins
      true -> :tie
    end
  end

  # 比较对子
  defp compare_pair_cards(cards1, cards2) do
    {pair1, kicker1} = get_pair_and_kicker(cards1)
    {pair2, kicker2} = get_pair_and_kicker(cards2)

    cond do
      pair1 > pair2 -> :player1_wins
      pair2 > pair1 -> :player2_wins
      kicker1 > kicker2 -> :player1_wins
      kicker2 > kicker1 -> :player2_wins
      true -> :tie
    end
  end

  # 比较顺子
  defp compare_sequence_cards(cards1, cards2) do
    high1 = get_sequence_high_card(cards1)
    high2 = get_sequence_high_card(cards2)

    cond do
      high1 > high2 -> :player1_wins
      high2 > high1 -> :player2_wins
      true -> :tie
    end
  end

  # 比较高牌
  defp compare_high_cards(cards1, cards2) do
    sorted1 = cards1 |> Enum.map(& &1.value) |> Enum.sort(:desc)
    sorted2 = cards2 |> Enum.map(& &1.value) |> Enum.sort(:desc)

    compare_card_lists(sorted1, sorted2)
  end

  # 递归比较牌列表
  defp compare_card_lists([], []), do: :tie
  defp compare_card_lists([h1 | t1], [h2 | t2]) do
    cond do
      h1 > h2 -> :player1_wins
      h2 > h1 -> :player2_wins
      true -> compare_card_lists(t1, t2)
    end
  end

  # 获取对子和踢脚牌
  defp get_pair_and_kicker(cards) do
    values = cards |> Enum.map(& &1.value) |> Enum.sort()

    case values do
      [a, a, b] -> {a, b}
      [a, b, b] -> {b, a}
      _ -> {0, 0}
    end
  end

  # 获取顺子的最高牌
  defp get_sequence_high_card(cards) do
    values = cards |> Enum.map(& &1.value) |> Enum.sort(:desc)

    case values do
      [14, 3, 2] -> 3  # A-3-2 顺子，3为最高
      [h | _] -> h
    end
  end

  # 验证单张牌的有效性
  defp valid_card?(%{suit: suit, value: value}) do
    suit in [1, 2, 3, 4] and value in [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]
  end

  defp valid_card?(_), do: false

  # 检查是否有重复的牌
  defp has_duplicate_cards?(cards) do
    unique_cards = Enum.uniq_by(cards, fn card -> {card.suit, card.value} end)
    length(unique_cards) != length(cards)
  end
end
