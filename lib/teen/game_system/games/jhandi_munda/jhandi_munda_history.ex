defmodule Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaHistory do
  @moduledoc """
  Jhandi Munda 游戏历史记录管理模块

  功能：
  - 管理游戏历史记录
  - 生成走势图数据
  - 统计分析功能
  - 玩家排行榜
  """

  require Logger
  alias Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaConstants

  defstruct [
    :records,           # 历史记录列表
    :max_records,       # 最大记录数
    :statistics,        # 统计信息
    :player_rankings    # 玩家排行榜
  ]

  @doc """
  初始化历史记录状态
  """
  def init_state(opts \\ []) do
    max_records = Keyword.get(opts, :max_records, JhandiMundaConstants.max_history_records())

    %__MODULE__{
      records: [],
      max_records: max_records,
      statistics: init_statistics(),
      player_rankings: %{
        win_count: [],      # 胜利次数排行
        bet_amount: [],     # 下注金额排行
        win_amount: []      # 中奖金额排行
      }
    }
  end

  @doc """
  添加新的游戏记录
  """
  def add_record(history_state, game_record) do
    new_records = [game_record | history_state.records]
                 |> Enum.take(history_state.max_records)

    updated_statistics = update_statistics(history_state.statistics, game_record)

    %{history_state |
      records: new_records,
      statistics: updated_statistics
    }
  end

  @doc """
  获取最近的历史记录
  """
  def get_recent_records(history_state, count \\ 10) do
    history_state.records
    |> Enum.take(count)
  end

  @doc """
  获取走势图数据
  """
  def get_trend_data(history_state) do
    records = history_state.records

    %{
      recent_results: get_recent_symbol_results(records, 20),
      symbol_frequencies: get_symbol_frequencies(records),
      hot_symbols: get_hot_symbols(records, 10),
      cold_symbols: get_cold_symbols(records, 10),
      winning_patterns: get_winning_patterns(records)
    }
  end

  @doc """
  更新玩家排行榜
  """
  def update_player_rankings(history_state, player_results) do
    updated_rankings = player_results
                      |> Enum.reduce(history_state.player_rankings, fn {player_id, result}, rankings ->
                        update_player_ranking(rankings, player_id, result)
                      end)

    %{history_state | player_rankings: updated_rankings}
  end

  @doc """
  获取玩家排行榜
  """
  def get_player_rankings(history_state, ranking_type \\ :win_amount, limit \\ 10) do
    case ranking_type do
      :win_count ->
        history_state.player_rankings.win_count |> Enum.take(limit)
      :bet_amount ->
        history_state.player_rankings.bet_amount |> Enum.take(limit)
      :win_amount ->
        history_state.player_rankings.win_amount |> Enum.take(limit)
      _ ->
        []
    end
  end

  @doc """
  获取统计信息
  """
  def get_statistics(history_state) do
    history_state.statistics
  end

  @doc """
  格式化历史记录供前端使用
  """
  def format_for_client(history_state, opts \\ []) do
    count = Keyword.get(opts, :count, 20)
    include_trend = Keyword.get(opts, :include_trend, true)
    include_rankings = Keyword.get(opts, :include_rankings, false)

    result = %{
      data: get_recent_records(history_state, count)
           |> Enum.map(&format_record_for_client/1)
    }

    result = if include_trend do
      Map.put(result, :trend, get_trend_data(history_state))
    else
      result
    end

    result = if include_rankings do
      Map.put(result, :rankings, %{
        win_amount: get_player_rankings(history_state, :win_amount, 10),
        bet_amount: get_player_rankings(history_state, :bet_amount, 10),
        win_count: get_player_rankings(history_state, :win_count, 10)
      })
    else
      result
    end

    result
  end

  @doc """
  清理过期记录
  """
  def cleanup_old_records(history_state) do
    # 只保留最大记录数的记录
    new_records = history_state.records
                 |> Enum.take(history_state.max_records)

    %{history_state | records: new_records}
  end

  # 私有函数

  defp init_statistics do
    symbols = JhandiMundaConstants.get_all_symbols()

    %{
      total_rounds: 0,
      symbol_counts: symbols |> Enum.map(fn s -> {s, 0} end) |> Enum.into(%{}),
      symbol_frequencies: symbols |> Enum.map(fn s -> {s, 0.0} end) |> Enum.into(%{}),
      average_winning_symbols: 0.0,
      max_same_symbol_count: 0,
      last_updated: DateTime.utc_now()
    }
  end

  defp update_statistics(statistics, game_record) do
    new_total_rounds = statistics.total_rounds + 1

    # 更新符号计数
    new_symbol_counts = game_record.symbol_counts
                       |> Enum.reduce(statistics.symbol_counts, fn {symbol, count}, acc ->
                         Map.update(acc, symbol, count, &(&1 + count))
                       end)

    # 计算符号频率
    total_dice_rolls = new_total_rounds * JhandiMundaConstants.dice_count()
    new_symbol_frequencies = new_symbol_counts
                            |> Enum.map(fn {symbol, count} ->
                              frequency = if total_dice_rolls > 0, do: count / total_dice_rolls, else: 0.0
                              {symbol, frequency}
                            end)
                            |> Enum.into(%{})

    # 计算平均中奖符号数
    total_winning_symbols = statistics.average_winning_symbols * statistics.total_rounds +
                           length(game_record.winning_symbols)
    new_average_winning_symbols = total_winning_symbols / new_total_rounds

    # 更新最大相同符号数
    max_count_in_round = game_record.symbol_counts
                        |> Map.values()
                        |> Enum.max(fn -> 0 end)
    new_max_same_symbol_count = max(statistics.max_same_symbol_count, max_count_in_round)

    %{statistics |
      total_rounds: new_total_rounds,
      symbol_counts: new_symbol_counts,
      symbol_frequencies: new_symbol_frequencies,
      average_winning_symbols: new_average_winning_symbols,
      max_same_symbol_count: new_max_same_symbol_count,
      last_updated: DateTime.utc_now()
    }
  end

  defp get_recent_symbol_results(records, count) do
    records
    |> Enum.take(count)
    |> Enum.map(fn record ->
      %{
        round_id: record.round_id,
        dice_results: record.dice_results,
        winning_symbols: record.winning_symbols,
        timestamp: record.timestamp
      }
    end)
  end

  defp get_symbol_frequencies(records) do
    if length(records) == 0 do
      JhandiMundaConstants.get_all_symbols()
      |> Enum.map(fn symbol -> {symbol, 0.0} end)
      |> Enum.into(%{})
    else
      total_dice = length(records) * JhandiMundaConstants.dice_count()

      records
      |> Enum.reduce(%{}, fn record, acc ->
        record.symbol_counts
        |> Enum.reduce(acc, fn {symbol, count}, inner_acc ->
          Map.update(inner_acc, symbol, count, &(&1 + count))
        end)
      end)
      |> Enum.map(fn {symbol, count} ->
        frequency = count / total_dice
        {symbol, frequency}
      end)
      |> Enum.into(%{})
    end
  end

  defp get_hot_symbols(records, recent_count) do
    records
    |> Enum.take(recent_count)
    |> get_symbol_frequencies()
    |> Enum.sort_by(fn {_symbol, frequency} -> frequency end, :desc)
    |> Enum.take(3)
  end

  defp get_cold_symbols(records, recent_count) do
    records
    |> Enum.take(recent_count)
    |> get_symbol_frequencies()
    |> Enum.sort_by(fn {_symbol, frequency} -> frequency end, :asc)
    |> Enum.take(3)
  end

  defp get_winning_patterns(records) do
    records
    |> Enum.take(20)
    |> Enum.map(fn record ->
      max_count = record.symbol_counts |> Map.values() |> Enum.max(fn -> 0 end)
      %{
        round_id: record.round_id,
        max_same_count: max_count,
        winning_symbol_count: length(record.winning_symbols)
      }
    end)
  end

  defp update_player_ranking(rankings, player_id, result) do
    # 这里简化实现，实际应该维护更复杂的排行榜逻辑
    # 可以根据需要添加更详细的玩家统计信息
    rankings
  end

  defp format_record_for_client(record) do
    %{
      roundid: record.round_id,
      posnums: record.symbol_counts,
      timestamp: DateTime.to_unix(record.timestamp),
      # 添加大赢家信息
      big_winners: format_big_winners_for_client(record)
    }
  end

  # 格式化大赢家信息供前端使用
  defp format_big_winners_for_client(record) do
    # 从记录中获取大赢家信息，如果没有则返回空列表
    big_winners = Map.get(record, :big_winners, [])

    big_winners
    |> Enum.map(fn winner ->
      %{
        "time" => format_time_for_display(record.timestamp),
        "rewards" => winner.win_amount,
        "bet" => winner.bet_amount,
        "type" => get_winner_type_display(winner.winning_symbols),
        "player_name" => winner.player_name,
        "player_id" => winner.player_id,
        "head_id" => Map.get(winner, :head_id, 1)
      }
    end)
  end

  # 格式化时间显示
  defp format_time_for_display(timestamp) do
    timestamp
    |> DateTime.to_time()
    |> Time.to_string()
    |> String.slice(0, 5)  # 只显示 HH:MM
  end

  # 获取中奖类型显示文本
  defp get_winner_type_display(winning_symbols) do
    case length(winning_symbols) do
      1 -> "单一符号"
      2 -> "双符号"
      3 -> "三符号"
      4 -> "四符号"
      5 -> "五符号"
      6 -> "满符号"
      _ -> "未知"
    end
  end
end
