defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.SlotNiu.SlotNiuConfigManager do
  @moduledoc """
  SlotNiu配置管理工具
  
  提供运营人员友好的配置管理接口，包括：
  - 配置预设模板
  - 配置验证
  - 配置热更新
  - 配置导入导出
  - 配置历史记录
  """

  require Logger
  alias <PERSON>pridina.Teen.GameSystem.Games.SlotNiu.SlotNiuConfig

  @doc """
  获取配置预设模板
  """
  def get_config_presets do
    %{
      # 高中奖率模式 (适合活动期间)
      high_win_rate: %{
        name: "高中奖率模式",
        description: "适合活动期间，提高玩家体验",
        rtp_config: %{
          target_rtp: 98.0
        },
        icon_weights: %{
          0 => 80,    # WILD - 增加
          1 => 140,   # 10
          2 => 140,   # J
          3 => 130,   # Q
          4 => 120,   # K
          5 => 100,   # A
          6 => 90,    # 狼
          7 => 70,    # 虎
          8 => 60,    # 鹰
          9 => 100,   # 鹿 - 增加
          10 => 70    # 牛 - 增加
        },
        win_probability: %{
          base_win_rates: %{
            1000 => 35,  # 增加10%
            500 => 34,   # 增加10%
            100 => 33,   # 增加10%
            50 => 32,    # 增加10%
            10 => 35,    # 增加10%
            1 => 30      # 增加10%
          }
        },
        free_game: %{
          free_spins_table: %{
            3 => 8,      # 增加免费次数
            4 => 15,     # 增加免费次数
            5 => 20      # 增加免费次数
          },
          multiplier: 2.5  # 增加倍率
        },
        turntable_rewards: [
          %{type: "free_spins", value: 8, weight: 320, luckyspinid: 1, name: "Free 8次"},
          %{type: "free_spins", value: 15, weight: 280, luckyspinid: 2, name: "Free 15次"},
          %{type: "free_spins", value: 20, weight: 180, luckyspinid: 3, name: "Free 20次"},
          %{type: "wild_single", value: 1, weight: 120, luckyspinid: 4, name: "Expanding Wild-1"},
          %{type: "wild_double", value: 2, weight: 80, luckyspinid: 5, name: "Expanding Wild-2"},
          %{type: "jackpot", value: 1, weight: 180, luckyspinid: 6, name: "Jackpot 1倍"},
          %{type: "jackpot", value: 2, weight: 60, luckyspinid: 7, name: "Jackpot 2倍"},
          %{type: "jackpot", value: 3, weight: 20, luckyspinid: 8, name: "Jackpot 3倍"}
        ]
      },

      # 标准模式 (默认配置)
      standard: %{
        name: "标准模式",
        description: "平衡的游戏体验",
        rtp_config: %{
          target_rtp: 96.5
        },
        icon_weights: %{
          0 => 60,    # WILD
          1 => 150,   # 10
          2 => 150,   # J
          3 => 140,   # Q
          4 => 130,   # K
          5 => 90,    # A
          6 => 80,    # 狼
          7 => 50,    # 虎
          8 => 40,    # 鹰
          9 => 80,    # 鹿
          10 => 50    # 牛
        },
        win_probability: %{
          base_win_rates: %{
            1000 => 25,
            500 => 24,
            100 => 23,
            50 => 22,
            10 => 25,
            1 => 20
          }
        },
        free_game: %{
          free_spins_table: %{
            3 => 5,
            4 => 10,
            5 => 15
          },
          multiplier: 2
        }
      },

      # 低中奖率模式 (提高收益)
      low_win_rate: %{
        name: "低中奖率模式",
        description: "提高平台收益",
        rtp_config: %{
          target_rtp: 94.0
        },
        icon_weights: %{
          0 => 40,    # WILD - 降低
          1 => 160,   # 10 - 增加低赔率图标
          2 => 160,   # J - 增加低赔率图标
          3 => 150,   # Q
          4 => 140,   # K
          5 => 80,    # A
          6 => 70,    # 狼
          7 => 40,    # 虎 - 降低
          8 => 30,    # 鹰 - 降低
          9 => 60,    # 鹿 - 降低
          10 => 30    # 牛 - 降低
        },
        win_probability: %{
          base_win_rates: %{
            1000 => 18,  # 降低7%
            500 => 17,   # 降低7%
            100 => 16,   # 降低7%
            50 => 15,    # 降低7%
            10 => 18,    # 降低7%
            1 => 13      # 降低7%
          }
        },
        free_game: %{
          free_spins_table: %{
            3 => 3,      # 降低免费次数
            4 => 6,      # 降低免费次数
            5 => 10      # 降低免费次数
          },
          multiplier: 1.5  # 降低倍率
        },
        turntable_rewards: [
          %{type: "free_spins", value: 3, weight: 280, luckyspinid: 1, name: "Free 3次"},
          %{type: "free_spins", value: 6, weight: 220, luckyspinid: 2, name: "Free 6次"},
          %{type: "free_spins", value: 10, weight: 120, luckyspinid: 3, name: "Free 10次"},
          %{type: "wild_single", value: 1, weight: 80, luckyspinid: 4, name: "Expanding Wild-1"},
          %{type: "wild_double", value: 2, weight: 30, luckyspinid: 5, name: "Expanding Wild-2"},
          %{type: "jackpot", value: 1, weight: 120, luckyspinid: 6, name: "Jackpot 1倍"},
          %{type: "jackpot", value: 2, weight: 25, luckyspinid: 7, name: "Jackpot 2倍"},
          %{type: "jackpot", value: 3, weight: 5, luckyspinid: 8, name: "Jackpot 3倍"}
        ]
      },

      # 测试模式 (方便调试)
      testing: %{
        name: "测试模式",
        description: "方便开发和测试",
        rtp_config: %{
          target_rtp: 99.0
        },
        icon_weights: %{
          0 => 120,   # WILD - 大幅增加
          1 => 120,   # 10
          2 => 120,   # J
          3 => 110,   # Q
          4 => 110,   # K
          5 => 100,   # A
          6 => 100,   # 狼
          7 => 90,    # 虎
          8 => 80,    # 鹰
          9 => 120,   # 鹿 - 大幅增加
          10 => 100   # 牛 - 大幅增加
        },
        win_probability: %{
          base_win_rates: %{
            1000 => 50,  # 大幅增加
            500 => 50,   # 大幅增加
            100 => 50,   # 大幅增加
            50 => 50,    # 大幅增加
            10 => 50,    # 大幅增加
            1 => 50      # 大幅增加
          }
        },
        free_game: %{
          free_spins_table: %{
            3 => 15,     # 大幅增加
            4 => 25,     # 大幅增加
            5 => 35      # 大幅增加
          },
          multiplier: 3.0  # 大幅增加
        },
        turntable_rewards: [
          %{type: "free_spins", value: 15, weight: 250, luckyspinid: 1, name: "Free 15次"},
          %{type: "free_spins", value: 25, weight: 200, luckyspinid: 2, name: "Free 25次"},
          %{type: "free_spins", value: 35, weight: 150, luckyspinid: 3, name: "Free 35次"},
          %{type: "wild_single", value: 1, weight: 150, luckyspinid: 4, name: "Expanding Wild-1"},
          %{type: "wild_double", value: 2, weight: 100, luckyspinid: 5, name: "Expanding Wild-2"},
          %{type: "jackpot", value: 1, weight: 200, luckyspinid: 6, name: "Jackpot 1倍"},
          %{type: "jackpot", value: 2, weight: 80, luckyspinid: 7, name: "Jackpot 2倍"},
          %{type: "jackpot", value: 3, weight: 30, luckyspinid: 8, name: "Jackpot 3倍"}
        ]
      },

      # Jackpot专注模式
      jackpot_focus: %{
        name: "Jackpot专注模式",
        description: "增加Jackpot触发概率",
        rtp_config: %{
          target_rtp: 96.0
        },
        jackpot: %{
          win_probability: 0.005,    # 增加5倍Jackpot概率
          contribution_rate: 0.015   # 增加贡献率
        },
        turntable_rewards: [
          %{type: "free_spins", value: 5, weight: 200, luckyspinid: 1, name: "Free 5次"},
          %{type: "free_spins", value: 10, weight: 150, luckyspinid: 2, name: "Free 10次"},
          %{type: "free_spins", value: 15, weight: 100, luckyspinid: 3, name: "Free 15次"},
          %{type: "wild_single", value: 1, weight: 80, luckyspinid: 4, name: "Expanding Wild-1"},
          %{type: "wild_double", value: 2, weight: 40, luckyspinid: 5, name: "Expanding Wild-2"},
          %{type: "jackpot", value: 1, weight: 250, luckyspinid: 6, name: "Jackpot 1倍"},  # 大幅增加
          %{type: "jackpot", value: 2, weight: 120, luckyspinid: 7, name: "Jackpot 2倍"}, # 大幅增加
          %{type: "jackpot", value: 3, weight: 60, luckyspinid: 8, name: "Jackpot 3倍"}   # 大幅增加
        ]
      }
    }
  end

  @doc """
  应用配置预设
  """
  def apply_preset(preset_name) do
    presets = get_config_presets()
    
    case Map.get(presets, preset_name) do
      nil ->
        {:error, "预设不存在: #{preset_name}"}
      
      preset ->
        Logger.info("🔧 [SLOTNIU_CONFIG] 应用配置预设: #{preset.name}")
        
        # 获取当前配置
        current_config = SlotNiuConfig.get_current_config()
        
        # 合并预设配置
        updated_config = deep_merge(current_config, preset)
        
        # 验证配置
        case validate_config(updated_config) do
          :ok ->
            # 应用配置 (这里可以实现配置持久化)
            Logger.info("✅ [SLOTNIU_CONFIG] 配置预设应用成功: #{preset.name}")
            {:ok, updated_config}
          
          {:error, reason} ->
            Logger.error("❌ [SLOTNIU_CONFIG] 配置验证失败: #{reason}")
            {:error, reason}
        end
    end
  end

  @doc """
  验证配置有效性
  """
  def validate_config(config) do
    with :ok <- validate_icon_weights(config.icon_weights),
         :ok <- validate_payout_table(config.payout_table),
         :ok <- validate_betting_config(config.betting),
         :ok <- validate_rtp_config(config.rtp_config),
         :ok <- validate_free_game_config(config.free_game),
         :ok <- validate_jackpot_config(config.jackpot),
         :ok <- validate_turntable_rewards(config.turntable_rewards) do
      :ok
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  获取配置调整建议
  """
  def get_tuning_suggestions(current_stats) do
    suggestions = []
    
    # 根据统计数据给出调整建议
    suggestions = if current_stats.win_rate > 95 do
      ["中奖率过高，建议降低图标权重或中奖概率" | suggestions]
    else
      suggestions
    end
    
    suggestions = if current_stats.jackpot_frequency < 0.0005 do
      ["Jackpot触发频率过低，建议增加牛头权重或转盘Jackpot权重" | suggestions]
    else
      suggestions
    end
    
    suggestions = if current_stats.free_game_frequency < 0.01 do
      ["免费游戏触发频率过低，建议增加牛头权重" | suggestions]
    else
      suggestions
    end
    
    suggestions = if current_stats.rtp < 94 do
      ["RTP过低，建议增加中奖概率或赔率" | suggestions]
    else
      suggestions
    end
    
    %{
      suggestions: suggestions,
      recommended_preset: recommend_preset(current_stats),
      risk_level: calculate_risk_level(current_stats)
    }
  end

  @doc """
  导出配置为JSON格式
  """
  def export_config_to_json do
    config = SlotNiuConfig.get_current_config()
    
    case Jason.encode(config, pretty: true) do
      {:ok, json} ->
        timestamp = DateTime.utc_now() |> DateTime.to_iso8601()
        filename = "slotniu_config_#{timestamp}.json"
        {:ok, json, filename}
      
      {:error, reason} ->
        {:error, "导出失败: #{reason}"}
    end
  end

  @doc """
  从JSON导入配置
  """
  def import_config_from_json(json_string) do
    case Jason.decode(json_string) do
      {:ok, config_map} ->
        # 转换为原子键的配置
        config = convert_keys_to_atoms(config_map)
        
        case validate_config(config) do
          :ok ->
            Logger.info("✅ [SLOTNIU_CONFIG] 配置导入成功")
            {:ok, config}
          
          {:error, reason} ->
            {:error, "配置验证失败: #{reason}"}
        end
      
      {:error, reason} ->
        {:error, "JSON解析失败: #{reason}"}
    end
  end

  # ===== 私有函数 =====

  # 验证图标权重
  defp validate_icon_weights(icon_weights) do
    total_weight = Enum.reduce(icon_weights, 0, fn {_icon, weight}, acc -> acc + weight end)
    
    cond do
      total_weight != 1000 ->
        {:error, "图标权重总和必须为1000，当前为#{total_weight}"}
      
      Enum.any?(icon_weights, fn {_icon, weight} -> weight < 0 end) ->
        {:error, "图标权重不能为负数"}
      
      true ->
        :ok
    end
  end

  # 验证赔率表
  defp validate_payout_table(payout_table) do
    if is_map(payout_table) do
      :ok
    else
      {:error, "赔率表必须是Map格式"}
    end
  end

  # 验证下注配置
  defp validate_betting_config(betting_config) do
    cond do
      betting_config.min_bet <= 0 ->
        {:error, "最小下注必须大于0"}
      
      betting_config.max_bet <= betting_config.min_bet ->
        {:error, "最大下注必须大于最小下注"}
      
      true ->
        :ok
    end
  end

  # 验证RTP配置
  defp validate_rtp_config(rtp_config) do
    cond do
      rtp_config.target_rtp < 70 or rtp_config.target_rtp > 99 ->
        {:error, "目标RTP必须在70%-99%之间"}
      
      true ->
        :ok
    end
  end

  # 验证免费游戏配置
  defp validate_free_game_config(free_game_config) do
    cond do
      free_game_config.multiplier < 1.0 ->
        {:error, "免费游戏倍率不能小于1.0"}
      
      true ->
        :ok
    end
  end

  # 验证Jackpot配置
  defp validate_jackpot_config(jackpot_config) do
    cond do
      jackpot_config.contribution_rate < 0 or jackpot_config.contribution_rate > 0.1 ->
        {:error, "Jackpot贡献率必须在0%-10%之间"}
      
      true ->
        :ok
    end
  end

  # 验证转盘奖励
  defp validate_turntable_rewards(turntable_rewards) do
    total_weight = Enum.reduce(turntable_rewards, 0, fn reward, acc -> acc + reward.weight end)
    
    if total_weight > 0 do
      :ok
    else
      {:error, "转盘奖励权重总和必须大于0"}
    end
  end

  # 推荐预设
  defp recommend_preset(stats) do
    cond do
      stats.win_rate > 95 -> :low_win_rate
      stats.win_rate < 85 -> :high_win_rate
      stats.jackpot_frequency < 0.0005 -> :jackpot_focus
      true -> :standard
    end
  end

  # 计算风险等级
  defp calculate_risk_level(stats) do
    cond do
      stats.win_rate > 98 -> :high
      stats.win_rate > 95 -> :medium
      stats.win_rate < 80 -> :medium
      true -> :low
    end
  end

  # 深度合并Map
  defp deep_merge(left, right) when is_map(left) and is_map(right) do
    Map.merge(left, right, fn _key, left_val, right_val ->
      deep_merge(left_val, right_val)
    end)
  end

  defp deep_merge(_left, right), do: right

  # 转换键为原子
  defp convert_keys_to_atoms(map) when is_map(map) do
    Map.new(map, fn {key, value} ->
      atom_key = if is_binary(key), do: String.to_atom(key), else: key
      {atom_key, convert_keys_to_atoms(value)}
    end)
  end

  defp convert_keys_to_atoms(value), do: value
end
