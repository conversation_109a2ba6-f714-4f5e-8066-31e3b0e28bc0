defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.SlotNiu.SlotNiuConfigAPI do
  @moduledoc """
  SlotNiu配置管理API
  
  提供运营人员友好的配置管理接口，包括：
  - 配置预设应用
  - 配置参数调整
  - 配置验证和导入导出
  - 配置统计和建议
  """

  require Logger
  alias <PERSON>pridina.Teen.GameSystem.Games.SlotNiu.{SlotNiuConfig, SlotNiuConfigManager}

  @doc """
  获取当前配置概览
  """
  def get_config_overview do
    config = SlotNiuConfig.get_current_config()
    
    %{
      game_info: config.game_info,
      rtp: config.rtp_config.target_rtp,
      icon_weights_summary: summarize_icon_weights(config.icon_weights),
      betting_range: "#{config.betting.min_bet} - #{config.betting.max_bet}",
      free_game_enabled: config.free_game.trigger_count > 0,
      jackpot_enabled: config.jackpot.win_probability > 0,
      turntable_rewards_count: length(config.turntable_rewards)
    }
  end

  @doc """
  获取可用的配置预设列表
  """
  def get_available_presets do
    presets = SlotNiuConfigManager.get_config_presets()
    
    Enum.map(presets, fn {key, preset} ->
      %{
        key: key,
        name: preset.name,
        description: preset.description,
        rtp: get_in(preset, [:rtp_config, :target_rtp]) || "未设置"
      }
    end)
  end

  @doc """
  应用配置预设
  """
  def apply_preset(preset_name, operator \\ "system") do
    Logger.info("🔧 [SLOTNIU_CONFIG_API] 用户 #{operator} 应用配置预设: #{preset_name}")
    
    case SlotNiuConfigManager.apply_preset(preset_name) do
      {:ok, updated_config} ->
        # 记录配置变更
        log_config_change(preset_name, "应用预设", operator)
        
        {:ok, %{
          message: "配置预设应用成功",
          preset: preset_name,
          new_rtp: updated_config.rtp_config.target_rtp,
          applied_at: DateTime.utc_now()
        }}
      
      {:error, reason} ->
        Logger.error("❌ [SLOTNIU_CONFIG_API] 应用预设失败: #{reason}")
        {:error, reason}
    end
  end

  @doc """
  调整单个配置参数
  """
  def update_config_parameter(path, value, operator \\ "system") do
    Logger.info("🔧 [SLOTNIU_CONFIG_API] 用户 #{operator} 更新配置参数: #{inspect(path)} = #{inspect(value)}")
    
    current_config = SlotNiuConfig.get_current_config()
    
    # 更新配置
    updated_config = put_in(current_config, path, value)
    
    # 验证配置
    case SlotNiuConfigManager.validate_config(updated_config) do
      :ok ->
        # 记录配置变更
        log_config_change(path, "参数调整", operator, %{old_value: get_in(current_config, path), new_value: value})
        
        {:ok, %{
          message: "配置参数更新成功",
          path: path,
          old_value: get_in(current_config, path),
          new_value: value,
          updated_at: DateTime.utc_now()
        }}
      
      {:error, reason} ->
        Logger.error("❌ [SLOTNIU_CONFIG_API] 配置验证失败: #{reason}")
        {:error, reason}
    end
  end

  @doc """
  批量调整图标权重
  """
  def update_icon_weights(new_weights, operator \\ "system") do
    Logger.info("🔧 [SLOTNIU_CONFIG_API] 用户 #{operator} 批量更新图标权重")
    
    # 验证权重总和
    total_weight = Enum.reduce(new_weights, 0, fn {_icon, weight}, acc -> acc + weight end)
    
    if total_weight != 1000 do
      {:error, "图标权重总和必须为1000，当前为#{total_weight}"}
    else
      update_config_parameter([:icon_weights], new_weights, operator)
    end
  end

  @doc """
  调整RTP设置
  """
  def update_rtp(new_rtp, operator \\ "system") do
    Logger.info("🔧 [SLOTNIU_CONFIG_API] 用户 #{operator} 更新RTP: #{new_rtp}%")
    
    cond do
      new_rtp < 70 or new_rtp > 99 ->
        {:error, "RTP必须在70%-99%之间"}
      
      true ->
        update_config_parameter([:rtp_config, :target_rtp], new_rtp, operator)
    end
  end

  @doc """
  调整转盘奖励权重
  """
  def update_turntable_rewards(new_rewards, operator \\ "system") do
    Logger.info("🔧 [SLOTNIU_CONFIG_API] 用户 #{operator} 更新转盘奖励配置")
    
    # 验证奖励配置
    total_weight = Enum.reduce(new_rewards, 0, fn reward, acc -> acc + reward.weight end)
    
    if total_weight <= 0 do
      {:error, "转盘奖励权重总和必须大于0"}
    else
      update_config_parameter([:turntable_rewards], new_rewards, operator)
    end
  end

  @doc """
  获取配置调整建议
  """
  def get_tuning_suggestions(game_stats \\ %{}) do
    # 模拟游戏统计数据
    default_stats = %{
      win_rate: 85.0,
      jackpot_frequency: 0.001,
      free_game_frequency: 0.02,
      rtp: 96.5
    }
    
    stats = Map.merge(default_stats, game_stats)
    SlotNiuConfigManager.get_tuning_suggestions(stats)
  end

  @doc """
  导出当前配置
  """
  def export_config(operator \\ "system") do
    Logger.info("📤 [SLOTNIU_CONFIG_API] 用户 #{operator} 导出配置")
    
    case SlotNiuConfigManager.export_config_to_json() do
      {:ok, json, filename} ->
        log_config_change("export", "配置导出", operator)
        {:ok, %{json: json, filename: filename, exported_at: DateTime.utc_now()}}
      
      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  导入配置
  """
  def import_config(json_string, operator \\ "system") do
    Logger.info("📥 [SLOTNIU_CONFIG_API] 用户 #{operator} 导入配置")
    
    case SlotNiuConfigManager.import_config_from_json(json_string) do
      {:ok, config} ->
        log_config_change("import", "配置导入", operator)
        {:ok, %{
          message: "配置导入成功",
          rtp: config.rtp_config.target_rtp,
          imported_at: DateTime.utc_now()
        }}
      
      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取配置历史记录
  """
  def get_config_history(limit \\ 10) do
    # 这里可以从数据库或日志中获取实际的配置历史
    # 暂时返回模拟数据
    [
      %{
        timestamp: DateTime.utc_now(),
        action: "应用预设",
        details: "standard",
        operator: "admin",
        changes: ["RTP调整为96.5%", "图标权重标准化"]
      },
      %{
        timestamp: DateTime.add(DateTime.utc_now(), -3600),
        action: "参数调整",
        details: "RTP: 95.0% -> 96.5%",
        operator: "operator1",
        changes: ["提高返还率"]
      }
    ]
    |> Enum.take(limit)
  end

  @doc """
  重置为默认配置
  """
  def reset_to_default(operator \\ "system") do
    Logger.info("🔄 [SLOTNIU_CONFIG_API] 用户 #{operator} 重置为默认配置")
    
    apply_preset(:standard, operator)
  end

  @doc """
  获取配置验证结果
  """
  def validate_current_config do
    config = SlotNiuConfig.get_current_config()
    
    case SlotNiuConfigManager.validate_config(config) do
      :ok ->
        {:ok, "当前配置有效"}
      
      {:error, reason} ->
        {:error, reason}
    end
  end

  # ===== 私有函数 =====

  # 汇总图标权重信息
  defp summarize_icon_weights(icon_weights) do
    total_weight = Enum.reduce(icon_weights, 0, fn {_icon, weight}, acc -> acc + weight end)
    high_weight_icons = Enum.filter(icon_weights, fn {_icon, weight} -> weight > 100 end)
    
    %{
      total_weight: total_weight,
      high_weight_count: length(high_weight_icons),
      wild_weight: Map.get(icon_weights, 0, 0),
      scatter_weight: Map.get(icon_weights, 10, 0)
    }
  end

  # 记录配置变更
  defp log_config_change(target, action, operator, details \\ %{}) do
    Logger.info("📝 [SLOTNIU_CONFIG_LOG] 配置变更记录:")
    Logger.info("📝 [SLOTNIU_CONFIG_LOG] - 操作员: #{operator}")
    Logger.info("📝 [SLOTNIU_CONFIG_LOG] - 动作: #{action}")
    Logger.info("📝 [SLOTNIU_CONFIG_LOG] - 目标: #{inspect(target)}")
    Logger.info("📝 [SLOTNIU_CONFIG_LOG] - 详情: #{inspect(details)}")
    Logger.info("📝 [SLOTNIU_CONFIG_LOG] - 时间: #{DateTime.utc_now()}")
    
    # 这里可以将日志写入数据库或文件
    :ok
  end
end
