defmodule Cyprid<PERSON>.Teen.GameSystem.Games.SlotNiu.SlotNiuConfig do
  @moduledoc """
  SlotNiu游戏配置模块

  提供SlotNiu游戏的所有配置参数，包括：
  - 图标权重和概率
  - 赔率表配置
  - 免费游戏配置
  - Jackpot配置
  - 转盘奖励配置
  - 下注配置
  - RTP配置
  """

  require Logger

  @doc """
  获取默认配置
  """
  def get_default_config do
    %{
      # ===== 游戏基础配置 =====
      game_info: %{
        name: "SlotN<PERSON>",
        version: "1.0.0",
        description: "SlotNiu老虎机游戏"
      },

      # ===== 老虎机配置 =====
      slot_config: %{
        rows: 3,                    # 行数
        cols: 5,                    # 列数
        lines: 9,                   # 支付线数
        max_lines: 9                # 最大线数
      },

      # ===== 图标配置 =====
      symbol_config: %{
        wild_symbol: 0,             # WILD图标
        scatter_symbol: 10,         # 牛头图标(Scatter)
        deer_symbol: 9,             # 鹿头图标(特殊)
        normal_symbols: [1, 2, 3, 4, 5, 6, 7, 8], # 普通图标
        total_symbols: 11           # 总图标数
      },

      # ===== 图标权重配置 =====
      icon_weights: %{
        # 图标ID => 权重 (总权重1000)
        0 => 60,    # WILD - 6%
        1 => 150,   # 10 - 15%
        2 => 150,   # J - 15%
        3 => 140,   # Q - 14%
        4 => 130,   # K - 13%
        5 => 90,    # A - 9%
        6 => 80,    # 狼 - 8%
        7 => 50,    # 虎 - 5%
        8 => 40,    # 鹰 - 4%
        9 => 80,    # 鹿 - 8%
        10 => 50    # 牛 - 5%
      },

      # ===== 赔率表配置 =====
      payout_table: %{
        # 图标ID => [3连, 4连, 5连]的倍率
        0 => [100, 200, 1750],      # WILD (最高倍率)
        1 => [3, 10, 75],           # 10
        2 => [3, 10, 90],           # J
        3 => [15, 40, 150],         # Q
        4 => [25, 50, 200],         # K
        5 => [30, 70, 250],         # A
        6 => [35, 80, 300],         # 狼头
        7 => [45, 100, 350],        # 虎头
        8 => [70, 170, 400],        # 鹰头
        9 => [0, 0, 0],             # 鹿头 (特殊图标)
        10 => [25, 40, 400]         # 牛头 (Scatter)
      },

      # ===== 下注配置 =====
      betting: %{
        min_bet: 1.8,               # 最小下注
        max_bet: 1800,              # 最大下注
        bet_amounts: [1.8, 9, 18, 90, 180, 900, 1800], # 下注金额选项
        bet_multipliers: [1, 5, 10, 50, 100, 500, 1000] # 下注倍率
      },

      # ===== RTP配置 =====
      rtp_config: %{
        target_rtp: 96.5,           # 目标返还率
        min_rtp: 94.0,              # 最小返还率
        max_rtp: 98.0,              # 最大返还率
        adjustment_factor: 0.1      # RTP调整因子
      },

      # ===== 中奖概率配置 =====
      win_probability: %{
        # 基础中奖概率 (按下注倍率)
        base_win_rates: %{
          1000 => 25,               # 高倍率：25%中奖概率
          500 => 24,                # 中高倍率：24%中奖概率
          100 => 23,                # 中倍率：23%中奖概率
          50 => 22,                 # 中低倍率：22%中奖概率
          10 => 25,                 # 低倍率：25%中奖概率
          1 => 20                   # 最低倍率：20%中奖概率
        },

        # 中奖倍数分布
        win_multiplier_distribution: %{
          small_win: %{probability: 50, range: {1, 3}},      # 50%概率：1-3倍 (小奖)
          medium_win: %{probability: 30, range: {3, 8}},     # 30%概率：3-8倍 (中奖)
          big_win: %{probability: 15, range: {8, 18}},       # 15%概率：8-18倍 (大奖)
          super_win: %{probability: 5, range: {20, 40}}      # 5%概率：20-40倍 (超级大奖)
        }
      },

      # ===== 免费游戏配置 =====
      free_game: %{
        trigger_count: 3,           # 触发免费游戏需要的牛头数量
        free_spins_table: %{        # 牛头数量 => 免费次数
          3 => 5,                   # 3个牛头 => 5次免费
          4 => 10,                  # 4个牛头 => 10次免费
          5 => 15                   # 5个牛头 => 15次免费
        },
        multiplier: 2,              # 免费游戏倍率
        max_free_spins: 50,         # 最大免费次数
        retrigger_enabled: true     # 是否允许重新触发
      },

      # ===== 牛头出现概率配置 =====
      bull_probability: %{
        # 中奖时牛头出现概率
        win_distribution: %{
          0 => 70,                  # 70%概率：0个牛头
          1 => 15,                  # 15%概率：1个牛头
          2 => 10,                  # 10%概率：2个牛头 (触发转盘)
          3 => 3,                   # 3%概率：3个牛头 (触发免费游戏)
          4 => 1,                   # 1%概率：4个牛头
          5 => 1                    # 1%概率：5个牛头
        },
        # 不中奖时牛头出现概率
        lose_distribution: %{
          0 => 90,                  # 90%概率：0个牛头
          1 => 7,                   # 7%概率：1个牛头
          2 => 3                    # 3%概率：2个牛头 (触发转盘)
        }
      },

      # ===== 转盘奖励配置 =====
      turntable_rewards: [
        %{type: "free_spins", value: 5, weight: 300, luckyspinid: 1, name: "Free 5次"},
        %{type: "free_spins", value: 10, weight: 250, luckyspinid: 2, name: "Free 10次"},
        %{type: "free_spins", value: 15, weight: 150, luckyspinid: 3, name: "Free 15次"},
        %{type: "wild_single", value: 1, weight: 100, luckyspinid: 4, name: "Expanding Wild-1"},
        %{type: "wild_double", value: 2, weight: 50, luckyspinid: 5, name: "Expanding Wild-2"},
        %{type: "jackpot", value: 1, weight: 150, luckyspinid: 6, name: "Jackpot 1倍"},
        %{type: "jackpot", value: 2, weight: 40, luckyspinid: 7, name: "Jackpot 2倍"},
        %{type: "jackpot", value: 3, weight: 10, luckyspinid: 8, name: "Jackpot 3倍"}
      ],

      # ===== Jackpot配置 =====
      jackpot: %{
        initial_amount: 150000,     # 初始Jackpot金额 (实际金额乘以100)
        min_amount: 150000,         # 最小Jackpot金额
        max_amount: 157500,         # 最大Jackpot金额
        contribution_rate: 0.01,    # Jackpot贡献率 (1%)
        win_probability: 0.001,     # Jackpot中奖概率 (0.1%)
        broadcast_interval: 2000,   # 广播间隔时间(毫秒)
        random_range: %{            # 随机范围
          min: -300,
          max: 300
        },
        # Jackpot倍率表 - 根据下注金额和牛头数量计算倍率
        multipliers: %{
          180 => %{3 => 2, 4 => 3, 5 => 4, 6 => 5, 7 => 6, 8 => 7, 9 => 8},
          900 => %{3 => 10, 4 => 15, 5 => 20, 6 => 25, 7 => 30, 8 => 35, 9 => 40},
          1800 => %{3 => 20, 4 => 30, 5 => 40, 6 => 50, 7 => 60, 8 => 70, 9 => 80},
          9000 => %{3 => 100, 4 => 150, 5 => 200, 6 => 250, 7 => 300, 8 => 350, 9 => 400},
          18000 => %{3 => 200, 4 => 300, 5 => 400, 6 => 500, 7 => 600, 8 => 700, 9 => 800},
          90000 => %{3 => 1000, 4 => 1500, 5 => 2000, 6 => 2500, 7 => 3000, 8 => 3500, 9 => 4000},
          180000 => %{3 => 2000, 4 => 3000, 5 => 4000, 6 => 5000, 7 => 6000, 8 => 7000, 9 => 8000}
        }
      },

      # ===== EXPANDING WILD配置 =====
      expanding_wild: %{
        enabled: true,              # 是否启用
        max_columns: 5,             # 最大列数
        animation_duration: 500,    # 动画持续时间(毫秒)
        settlement_delay: 1000      # 结算延迟(毫秒)
      },

      # ===== 机器人配置 =====
      robot: %{
        enabled: true,              # 是否启用机器人
        bet_time_span: %{           # 机器人下注时间间隔
          min: 4000,                # 最小间隔(毫秒)
          max: 6000                 # 最大间隔(毫秒)
        },
        bet_choice_weights: [100, 200, 150, 100, 80, 50, 20] # 机器人下注选择权重
      },

      # ===== 风控配置 =====
      risk_control: %{
        dark_revenue_rate: 100,     # 暗税比例(千分比)
        center_line_up_rate: 5000,  # 上限比例(千分比)
        up_fix_safe_score: 300000,  # 上安全线固定线
        center_line_down_rate: 300, # 下限比例(千分比)
        down_fix_safe_score: 100000, # 下安全线固定线
        safe_up_score_max: 350000,  # 上线最大值
        safe_bottom_score_max: 150000, # 下线最大值
        pre_rate: 700,              # 上下前置线比例(千分比)
        put_score_prob: 800,        # 放分概率(千分比)
        get_score_prob: 800         # 收分概率(千分比)
      },

      # ===== 房间配置 =====
      room: %{
        max_players: 1,             # 最大玩家数
        min_players: 1,             # 最小玩家数
        auto_start_delay: 1000,     # 自动开始延迟
        enable_robots: false,       # 是否启用机器人
        robot_count: 0              # 机器人数量
      }
    }
  end

  @doc """
  获取当前生效的配置
  可以从数据库、配置文件或环境变量中读取自定义配置
  """
  def get_current_config do
    # 获取默认配置
    default_config = get_default_config()

    # 尝试从环境变量或配置文件中读取自定义配置
    custom_config = load_custom_config()

    # 合并配置 (自定义配置覆盖默认配置)
    merge_configs(default_config, custom_config)
  end

  @doc """
  获取图标权重配置
  """
  def get_icon_weights do
    config = get_current_config()
    config.icon_weights
  end

  @doc """
  获取赔率表配置
  """
  def get_payout_table do
    config = get_current_config()
    config.payout_table
  end

  @doc """
  获取下注配置
  """
  def get_betting_config do
    config = get_current_config()
    config.betting
  end

  @doc """
  获取免费游戏配置
  """
  def get_free_game_config do
    config = get_current_config()
    config.free_game
  end

  @doc """
  获取转盘奖励配置
  """
  def get_turntable_rewards do
    config = get_current_config()
    config.turntable_rewards
  end

  @doc """
  获取Jackpot配置
  """
  def get_jackpot_config do
    config = get_current_config()
    config.jackpot
  end

  @doc """
  获取Jackpot倍率表配置
  """
  def get_jackpot_multipliers do
    config = get_current_config()
    config.jackpot.multipliers
  end

  @doc """
  获取中奖概率配置
  """
  def get_win_probability_config do
    config = get_current_config()
    config.win_probability
  end

  @doc """
  获取牛头概率配置
  """
  def get_bull_probability_config do
    config = get_current_config()
    config.bull_probability
  end

  @doc """
  获取RTP配置
  """
  def get_rtp_config do
    config = get_current_config()
    config.rtp_config
  end

  @doc """
  获取风控配置
  """
  def get_risk_control_config do
    config = get_current_config()
    config.risk_control
  end

  # ===== 私有函数 =====

  # 加载自定义配置
  defp load_custom_config do
    # 这里可以从数据库、配置文件或环境变量中读取自定义配置
    # 暂时返回空配置
    %{}
  end

  # 合并配置
  defp merge_configs(default_config, custom_config) do
    deep_merge(default_config, custom_config)
  end

  # 深度合并Map
  defp deep_merge(left, right) when is_map(left) and is_map(right) do
    Map.merge(left, right, fn _key, left_val, right_val ->
      deep_merge(left_val, right_val)
    end)
  end

  defp deep_merge(_left, right), do: right
end
