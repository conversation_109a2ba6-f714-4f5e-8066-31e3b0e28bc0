defmodule Cyprid<PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiConfig do
  @moduledoc """
  Teen Patti配置管理模块

  负责管理所有可配置参数，包括：
  - 库存控制配置
  - 幸运值概率配置
  - 机器人行为配置
  - 游戏规则配置
  - 特殊事件配置
  """

  require Logger

  # 库存控制配置
  @stock_config %{
    # 库存上下限（按分值场分别设置）
    stock_limits: %{
      10 => %{upper: 100000, lower: -50000, safe_zone: 10000},   # 10分场
      50 => %{upper: 500000, lower: -250000, safe_zone: 50000},  # 50分场
      100 => %{upper: 1000000, lower: -500000, safe_zone: 100000}, # 100分场
      500 => %{upper: 5000000, lower: -2500000, safe_zone: 500000} # 500分场
    },

    # 暗税配置（玩家赢钱时抽取的税）
    hidden_tax_rate: 0.05,  # 5%暗税，可由后台配置

    # 收分/放分模式触发概率
    collect_mode_probability: 0.2,  # 收分模式触发概率20%
    release_mode_probability: 0.2,  # 放分模式触发概率20%

    # 冤家牌触发概率
    rival_cards_probability: 0.05,  # 冤家牌触发概率5%，默认0

    # 连续好牌概率及衰减机制
    consecutive_good_cards: %{
      base_probability: 0.01,  # 基础概率1%，默认0
      round_1_multiplier: 1.2, # 第1轮概率倍数
      round_2_multiplier: 0.8, # 第2轮概率倍数
      round_3_multiplier: 0.5, # 第3轮概率倍数
      round_4_multiplier: 0.3, # 第4轮概率倍数
      round_5_multiplier: 0.1  # 第5轮概率倍数
    }
  }

  # 幸运值对应的牌型概率权重配置 - 按客户要求
  @luck_card_probabilities %{
    # 好运爆棚 (850-1000)
    850..1000 => %{
      high_card: 400,      # 高牌
      pair: 250,           # 对子
      color: 150,          # 同花
      sequence: 150,       # 顺子
      pure_sequence: 30,   # 同花顺
      trail: 20            # 三条
    },

    # 小有手气 (650-849)
    650..849 => %{
      high_card: 500,
      pair: 230,
      color: 130,
      sequence: 120,
      pure_sequence: 15,
      trail: 5
    },

    # 手气平稳 (400-649)
    400..649 => %{
      high_card: 600,
      pair: 210,
      color: 120,
      sequence: 80,
      pure_sequence: 5,
      trail: 5
    },

    # 有点背 (200-399)
    200..399 => %{
      high_card: 720,
      pair: 150,
      color: 80,
      sequence: 45,
      pure_sequence: 3,
      trail: 0
    },

    # 霉运缠身 (1-199)
    1..199 => %{
      high_card: 820,
      pair: 100,
      color: 50,
      sequence: 25,
      pure_sequence: 0,
      trail: 0
    }
  }

  # 新玩家第一局牌型概率配置
  @new_player_first_game_probabilities %{
    player: %{
      high_card: 3,
      pair: 5,
      color: 1,
      sequence: 85,
      pure_sequence: 1,
      trail: 5
    },
    robot: %{
      high_card: 60,
      pair: 20,
      color: 12,
      sequence: 7,
      pure_sequence: 0.5,
      trail: 0.5
    }
  }

  # 新玩家金币从来没有达到过80的概率配置
  @new_player_never_80_probabilities %{
    player: %{
      high_card: 54,
      pair: 15,
      color: 3,
      sequence: 25,
      pure_sequence: 1,
      trail: 1
    },
    # player: %{
    #   high_card: 1,
    #   pair: 1,
    #   color: 1,
    #   sequence: 1,
    #   pure_sequence: 99,
    #   trail: 1
    # },
    robot: %{
      high_card: 60,
      pair: 20,
      color: 12,
      sequence: 7,
      pure_sequence: 0.5,
      trail: 0.5
    }
  }

  # 待充值玩家：指玩家金币第一次超过80
  @waiting_recharge_probabilities %{
    player: %{
      high_card: 65,
      pair: 24,
      color: 8,
      sequence: 3,
      pure_sequence: 0,
      trail: 0
    },
    # player: %{
    #   high_card: 1,
    #   pair: 1,
    #   color: 80,
    #   sequence: 80,
    #   pure_sequence: 0,
    #   trail: 0
    # },
    robot: %{
      high_card: 60,
      pair: 20,
      color: 12,
      sequence: 7,
      pure_sequence: 0.5,
      trail: 0.5
    }
  }

  # 充值玩家概率配置
  @charged_player_probabilities %{
    player: %{
      high_card: 50,
      pair: 25,
      color: 15,
      sequence: 8,
      pure_sequence: 1.5,
      trail: 0.5
    },
    robot: %{
      high_card: 55,
      pair: 22,
      color: 13,
      sequence: 8,
      pure_sequence: 1.5,
      trail: 0.5
    }
  }

  # 免费玩家概率配置
  @free_player_probabilities %{
    player: %{
      high_card: 60,
      pair: 25,
      color: 10,
      sequence: 4,
      pure_sequence: 0.8,
      trail: 0.2
    },
    robot: %{
      high_card: 65,
      pair: 20,
      color: 10,
      sequence: 4,
      pure_sequence: 0.8,
      trail: 0.2
    }
  }

  # 机器人概率配置
  @robot_probabilities %{
    player: %{
      high_card: 60,
      pair: 20,
      color: 12,
      sequence: 7,
      pure_sequence: 0.5,
      trail: 0.5
    },
    robot: %{
      high_card: 60,
      pair: 20,
      color: 12,
      sequence: 7,
      pure_sequence: 0.5,
      trail: 0.5
    }
  }

  # 冤家牌事件配置 冤家牌是待充值玩家才会触发的
  @rival_cards_config %{
    # 冤家牌触发概率（当玩家拿到同花或顺子时）
    trigger_probability: 90,
    # 未充值玩家赢金额阈值
    win_amount_threshold: 30,
    # 未充值玩家换牌概率
    exchange_probability: 50,
    # 充值玩家输赢概率配置
    recharged_player_win_rates: %{
      # 赢金额 < 充值金额：100%让玩家赢
      under_recharge_amount: 100,
      # 充值金额 ≤ 赢金额 < 2倍充值金额：60%让玩家赢
      one_to_two_times: 60,
      # 赢金额 ≥ 2倍充值金额：30%让玩家赢
      over_two_times: 30
    }
  }

  # 玩家类型换牌策略概率配置
  @player_exchange_probabilities %{
    # 新玩家首局换牌概率
    new_player_first_game: %{
      # 新玩家第一局遇到强牌时的换牌概率
      strong_card_exchange: 100,  # 100%换牌
      # 顺子遇到更大顺子时的换牌概率
      sequence_vs_bigger_sequence: 80  # 80%换牌
    },
    # 新玩家金币从来没有达到过80的换牌概率
    new_player_never_80: %{
      # 强牌但非最强时的换牌概率
      strong_but_not_strongest: 70  # 70%换牌
    },
    # 待充值玩家换牌概率
    waiting_recharge: %{
      # 最强牌时的换牌概率
      strongest_card_exchange: 70  # 70%换牌
    }
  }

  # 机器人牌调整策略概率配置（当玩家已看牌时使用）
  @robot_adjustment_probabilities %{
    # 新玩家首局让玩家赢的概率
    new_player_first_game: 85,
    # 新玩家金币从来没有达到过80让玩家赢的概率
    new_player_never_80: 70,
    # 待充值玩家让玩家赢的概率（让玩家输的概率更高）
    waiting_recharge: 30,
    # 回头玩家让玩家赢的概率（按新玩家处理）
    returning_player: 85
  }

  # 机器人行为配置 - 增强版
  @robot_behavior_config %{
    # 机器人加入延迟（2-6秒）
    join_delay_min: 2000,
    join_delay_max: 6000,

    # 机器人操作延迟 - 按类型区分
    operation_delay: %{
      aggressive: %{min_think_time: 500, max_think_time: 3000},    # 激进型反应快
      conservative: %{min_think_time: 2000, max_think_time: 8000}, # 保守型思考久
      tricky: %{min_think_time: 1000, max_think_time: 6000},      # 迷惑型不定
      supportive: %{min_think_time: 1500, max_think_time: 5000},  # 陪玩型适中
      balanced: %{min_think_time: 1000, max_think_time: 6000}     # 平衡型正常
    },

    # 机器人金币配置 - 按类型区分
    initial_money: %{
      aggressive: %{min: 15000, max: 50000},   # 激进型金币多
      conservative: %{min: 20000, max: 80000}, # 保守型金币最多
      tricky: %{min: 8000, max: 30000},       # 迷惑型金币少
      supportive: %{min: 12000, max: 40000},  # 陪玩型金币中等
      balanced: %{min: 10000, max: 60000}     # 平衡型金币适中
    },

    # 机器人性格分布概率
    type_distribution: %{
      aggressive: 0.15,     # 15%激进型
      conservative: 0.25,   # 25%保守型
      tricky: 0.20,        # 20%迷惑型
      supportive: 0.20,    # 20%陪玩型
      balanced: 0.20       # 20%平衡型
    },

    # 机器人状态转换概率
    state_transition: %{
      normal_to_bluff: 0.05,           # 正常转诈唬概率
      normal_to_collect: 0.1,          # 正常转收分概率
      normal_to_send_money: 0.08,      # 正常转送分概率
      consecutive_wins_threshold: 3,    # 连胜触发收分阈值
      consecutive_losses_threshold: 5   # 连败触发送分阈值
    },

    # 机器人充值等待触发概率
    recharge_wait_probability: 0.01,  # 1%概率触发机器人充值等待

    # 机器人智能退出金币阈值
    exit_money_threshold: 1000,

    # 机器人比牌策略配置
    competition_strategy: %{
      min_hand_strength: 350,          # 最低比牌手牌强度
      aggressive_competition_rate: 0.4, # 激进型比牌概率
      conservative_competition_rate: 0.1, # 保守型比牌概率
      late_game_bonus: 0.2             # 后期比牌概率加成
    }
  }

  # 幸运值调整配置
  @luck_adjustment_config %{
    # 输赢倍数对应的幸运值调整范围
    multiplier_adjustments: %{
      {1, 5} => %{win: {1, 2}, lose: {1, 2}},
      {6, 10} => %{win: {3, 5}, lose: {4, 6}},
      {11, 20} => %{win: {6, 10}, lose: {8, 12}},
      {21, 40} => %{win: {10, 20}, lose: {15, 25}},
      {41, 70} => %{win: {15, 30}, lose: {25, 35}},
      {71, 100} => %{win: {30, 50}, lose: {40, 55}},
      {101, 200} => %{win: {40, 60}, lose: {55, 70}},
      {201, :infinity} => %{win: {50, 80}, lose: {75, 100}}
    },

    # 特殊事件调整
    special_events: %{
      consecutive_lose_5: {3, 5},      # 连续输5局
      consecutive_win_3: {5, 10},      # 连续赢3局
      extreme_lose_300: 900,           # 极端爆炸局输超过300倍
      extreme_win_300: 150,            # 极端爆炸局赢超过300倍
      get_trail: {5, 10},              # 拿到三条
      after_recharge: 750              # 充值后
    }
  }

  @doc """
  获取库存配置
  """
  def get_stock_config, do: @stock_config

  @doc """
  获取指定分值场的库存限制
  """
  def get_stock_limits(base_bet) do
    Map.get(@stock_config.stock_limits, base_bet, @stock_config.stock_limits[10])
  end

  @doc """
  获取暗税率
  """
  def get_hidden_tax_rate, do: @stock_config.hidden_tax_rate

  @doc """
  获取收分模式触发概率
  """
  def get_collect_mode_probability, do: @stock_config.collect_mode_probability

  @doc """
  获取放分模式触发概率
  """
  def get_release_mode_probability, do: @stock_config.release_mode_probability

  @doc """
  获取冤家牌触发概率
  """
  def get_rival_cards_probability, do: @stock_config.rival_cards_probability

  @doc """
  获取连续好牌配置
  """
  def get_consecutive_good_cards_config, do: @stock_config.consecutive_good_cards

  @doc """
  根据幸运值获取牌型概率权重
  """
  def get_card_probabilities_by_luck(luck_value) do
    Enum.find_value(@luck_card_probabilities, @luck_card_probabilities[400..649], fn {range, probabilities} ->
      if luck_value in range, do: probabilities
    end)
  end

  @doc """
  获取新玩家第一局概率配置
  """
  def get_new_player_first_game_probabilities, do: @new_player_first_game_probabilities

  @doc """
  获取新玩家从未达到80金币的概率配置
  """
  def get_new_player_never_80_probabilities, do: @new_player_never_80_probabilities

  @doc """
  获取待充值玩家概率配置
  """
  def get_waiting_recharge_probabilities, do: @waiting_recharge_probabilities

  @doc """
  获取充值玩家概率配置
  """
  def get_charged_player_probabilities, do: @charged_player_probabilities

  @doc """
  获取免费玩家概率配置
  """
  def get_free_player_probabilities, do: @free_player_probabilities

  @doc """
  获取机器人概率配置
  """
  def get_robot_probabilities, do: @robot_probabilities

  @doc """
  获取冤家牌事件配置
  """
  def get_rival_cards_config, do: @rival_cards_config

  @doc """
  获取冤家牌触发概率
  """
  def get_rival_cards_trigger_probability, do: @rival_cards_config.trigger_probability

  @doc """
  获取未充值玩家赢金额阈值
  """
  def get_win_amount_threshold, do: @rival_cards_config.win_amount_threshold

  @doc """
  获取未充值玩家换牌概率
  """
  def get_exchange_probability, do: @rival_cards_config.exchange_probability

  @doc """
  获取充值玩家输赢概率配置
  """
  def get_recharged_player_win_rates, do: @rival_cards_config.recharged_player_win_rates

  @doc """
  获取玩家类型换牌策略概率配置
  """
  def get_player_exchange_probabilities, do: @player_exchange_probabilities

  @doc """
  获取新玩家首局换牌概率配置
  """
  def get_new_player_first_game_probabilities, do: @player_exchange_probabilities.new_player_first_game

  @doc """
  获取新玩家从未达到80换牌概率配置
  """
  def get_new_player_never_80_probabilities, do: @player_exchange_probabilities.new_player_never_80

  @doc """
  获取待充值玩家换牌概率配置
  """
  def get_waiting_recharge_probabilities_exchange, do: @player_exchange_probabilities.waiting_recharge

  @doc """
  获取机器人牌调整策略概率配置
  """
  def get_robot_adjustment_probabilities, do: @robot_adjustment_probabilities

  @doc """
  获取指定玩家类型的机器人调整概率
  """
  def get_robot_adjustment_probability(player_type) do
    Map.get(@robot_adjustment_probabilities, player_type)
  end

  @doc """
  获取机器人行为配置
  """
  def get_robot_behavior_config, do: @robot_behavior_config

  @doc """
  获取幸运值调整配置
  """
  def get_luck_adjustment_config, do: @luck_adjustment_config

  @doc """
  根据输赢倍数获取幸运值调整范围
  """
  def get_luck_adjustment_by_multiplier(multiplier, is_win) do
    adjustment_key = if is_win, do: :win, else: :lose

    Enum.find_value(@luck_adjustment_config.multiplier_adjustments, {1, 2}, fn {{min_mult, max_mult}, adjustments} ->
      if (max_mult == :infinity and multiplier >= min_mult) or (multiplier >= min_mult and multiplier <= max_mult) do
        Map.get(adjustments, adjustment_key, {1, 2})
      end
    end)
  end

  @doc """
  检查是否应该触发收分模式
  """
  def should_trigger_collect_mode?(current_stock, stock_limits) do
    upper_limit = Map.get(stock_limits, :upper_limit, Map.get(stock_limits, :upper, 20000))
    current_stock > upper_limit and :rand.uniform() < @stock_config.collect_mode_probability
  end

  @doc """
  检查是否应该触发放分模式
  """
  def should_trigger_release_mode?(current_stock, stock_limits) do
    lower_limit = Map.get(stock_limits, :lower_limit, Map.get(stock_limits, :lower, 5000))
    current_stock < lower_limit and :rand.uniform() < @stock_config.release_mode_probability
  end

  @doc """
  检查是否在安全区间（不触发收分/放分）
  """
  def in_safe_zone?(current_stock, stock_limits) do
    abs(current_stock) <= stock_limits.safe_zone
  end

  @doc """
  检查是否应该触发冤家牌
  """
  def should_trigger_rival_cards? do
    :rand.uniform() < @stock_config.rival_cards_probability
  end

  @doc """
  检查是否应该触发连续好牌
  """
  def should_trigger_consecutive_good_cards? do
    :rand.uniform() < @stock_config.consecutive_good_cards.base_probability
  end

  @doc """
  获取机器人类型分布配置
  """
  def get_robot_type_distribution do
    @robot_behavior_config.type_distribution
  end

  @doc """
  根据机器人类型获取操作延迟配置
  """
  def get_robot_operation_delay(robot_type) do
    Map.get(@robot_behavior_config.operation_delay, robot_type, @robot_behavior_config.operation_delay.balanced)
  end

  @doc """
  根据机器人类型获取初始金币配置
  """
  def get_robot_initial_money(robot_type) do
    Map.get(@robot_behavior_config.initial_money, robot_type, @robot_behavior_config.initial_money.balanced)
  end

  @doc """
  获取机器人状态转换配置
  """
  def get_robot_state_transition_config do
    @robot_behavior_config.state_transition
  end

  @doc """
  获取机器人比牌策略配置
  """
  def get_robot_competition_strategy do
    @robot_behavior_config.competition_strategy
  end

  @doc """
  根据玩家类型和充值状态计算幸运值
  """
  def calculate_player_luck_value(player_data) do
    base_luck = 500  # 默认幸运值

    # 根据充值状态调整
    luck_value = case Map.get(player_data, :recharge_status, :free) do
      :charged_recently -> 750                                 # 刚充值
      :charged_old -> base_luck + 100                          # 老充值用户
      :free -> base_luck - 100                                 # 免费用户
      :waiting_recharge -> base_luck - 200                     # 待充值用户
      _ -> base_luck
    end

    # 根据连胜连败调整
    consecutive_wins = Map.get(player_data, :consecutive_wins, 0)
    consecutive_losses = Map.get(player_data, :consecutive_losses, 0)

    luck_value = cond do
      consecutive_losses >= 5 -> luck_value + 50   # 连败补偿
      consecutive_wins >= 3 -> luck_value - 30     # 连胜削弱
      true -> luck_value
    end

    # 限制在合理范围内
    max(1, min(1000, luck_value))
  end

  @doc """
  获取发牌模式触发概率配置
  """
  def get_dealing_mode_probabilities do
    %{
      normal: 0.7,        # 70%正常发牌
      rival_cards: 0.05,  # 5%冤家牌
      collect: 0.125,     # 12.5%收分模式
      release: 0.125      # 12.5%放分模式
    }
  end

  @doc """
  检查是否应该触发特殊发牌模式
  """
  def should_trigger_special_dealing_mode?(current_stock, stock_limits, player_luck_values) do
    # 检查库存状态
    cond do
      should_trigger_collect_mode?(current_stock, stock_limits) -> :collect
      should_trigger_release_mode?(current_stock, stock_limits) -> :release
      should_trigger_rival_cards_by_luck?(player_luck_values) -> :rival_cards
      true -> :normal
    end
  end

  @doc """
  根据玩家幸运值检查是否触发冤家牌
  """
  defp should_trigger_rival_cards_by_luck?(player_luck_values) do
    # 如果有玩家幸运值很高，增加冤家牌概率
    max_luck = Enum.max(player_luck_values, fn -> 0 end)

    trigger_probability = cond do
      max_luck >= 800 -> 0.15  # 高幸运值15%概率
      max_luck >= 600 -> 0.08  # 中等幸运值8%概率
      true -> 0.02             # 低幸运值2%概率
    end

    :rand.uniform() < trigger_probability
  end

  @doc """
  获取连续好牌轮次概率倍数
  """
  def get_consecutive_good_cards_multiplier(round_number) do
    case round_number do
      1 -> @stock_config.consecutive_good_cards.round_1_multiplier
      2 -> @stock_config.consecutive_good_cards.round_2_multiplier
      3 -> @stock_config.consecutive_good_cards.round_3_multiplier
      4 -> @stock_config.consecutive_good_cards.round_4_multiplier
      5 -> @stock_config.consecutive_good_cards.round_5_multiplier
      _ -> 0.1  # 超过5轮后概率极低
    end
  end

  # 换牌策略配置
  @card_exchange_config %{
    # 新玩家第一局换牌策略
    new_player_first_game: %{
      # 如果玩家拿到的牌是非顺子，并且本局有其他玩家拿到的是顺子、同花顺或三条，那就将本局最大的牌换给玩家
      non_sequence_vs_strong: :exchange_strongest,
      # 如果玩家拿到的是非顺子，并且本局有其他玩家没有拿到顺子、同花顺或三条，那就不换牌
      non_sequence_vs_weak: :no_exchange,
      # 如果玩家拿到的是顺子，并且本局有其他玩家拿到的是同花顺或三条，那就将最大的牌换个玩家
      sequence_vs_stronger: :exchange_strongest,
      # 如果玩家拿到的是顺子，并且本局有其他玩家拿到更大的顺子，但是没有人拿到同花顺或三条，那就80%的概率将最大的顺子牌换给玩家
      sequence_vs_bigger_sequence: {:exchange_probability, 80},
      # 玩家如果拿到同花以上的牌，选择弃牌后，并且出现最后两个机器人比牌的情况，如果玩家不是最大的牌，80%的概率将两个机器人的牌换的比玩家小再进行比牌
      fold_adjustment: {:exchange_probability, 80}
    },
    # 新玩家金币从来没有达到过80
    new_player_never_80: %{
      # 如果玩家拿到的是顺子及以上的牌，但是不是场上最的大牌，70%的概率将场上最大的牌换给玩家
      strong_but_not_strongest: {:exchange_probability, 70},
      # 其他情况不换牌
      default: :no_exchange
    },
    # 待充值玩家：指玩家金币第一次超过80
    waiting_recharge: %{
      # 如果玩家拿到的是全场最大的牌，70%的概率将牌与全场第二大的牌进行调换
      strongest_card: {:exchange_probability, 70},
      # 其他情况不换牌
      default: :no_exchange
    }
  }

  @doc """
  获取换牌策略配置
  """
  def get_card_exchange_config(player_type) do
    Map.get(@card_exchange_config, player_type, @card_exchange_config.new_player_first_game)
  end

  @doc """
  获取新玩家第一局换牌策略
  """
  def get_new_player_first_game_exchange_config() do
    @card_exchange_config.new_player_first_game
  end

  @doc """
  获取新玩家从未达到80换牌策略
  """
  def get_new_player_never_80_exchange_config() do
    @card_exchange_config.new_player_never_80
  end

  @doc """
  获取待充值玩家换牌策略
  """
  def get_waiting_recharge_exchange_config() do
    @card_exchange_config.waiting_recharge
  end
end
