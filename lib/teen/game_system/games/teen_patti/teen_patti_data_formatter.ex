defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiDataFormatter do
  @moduledoc """
  Teen Patti游戏数据格式化模块

  处理游戏数据的格式化和转换，确保与前端数据格式兼容
  基于前端TPDefine.ts和C++服务端的数据结构
  """
  require Logger
  alias <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.TeenPatti.{TeenPattiGame, TeenPattiLogic}

  @doc """
  将服务端牌数据格式化为客户端格式

  ## 服务端格式
  %{suit: :spades, value: 14}

  ## 客户端格式
  %{"color" => 6, "number" => 14}
  """
  def format_cards_for_client(cards) when is_list(cards) do
    Enum.map(cards, &format_single_card_for_client/1)
  end

  def format_cards_for_client(_), do: []

  @doc """
  格式化单张牌为客户端格式
  """
  def format_single_card_for_client(%{suit: suit, value: value}) do
    client_color = suit_to_client_color(suit)
    %{
      "color" => client_color,
      "number" => value
    }
  end

  def format_single_card_for_client(invalid_card) do

    Logger.warning("🃏 [CARD_FORMAT] ⚠️ 无效卡牌数据: #{inspect(invalid_card)}")
    %{"color" => 0, "number" => 0}
  end

  @doc """
  将服务端花色转换为客户端花色值

  🎯 修复：根据前端TPCardLayer.ts中的showPokerData函数和错误信息：
  前端期望的花色映射（基于资源路径 res/textures/card/num_black_6）：
  - 3: 方块 (diamonds) -> 红色数字 (num_red_)
  - 4: 梅花 (clubs) -> 黑色数字 (num_black_)
  - 5: 红桃 (hearts) -> 红色数字 (num_red_)
  - 6: 黑桃 (spades) -> 黑色数字 (num_black_)
  """
  defp suit_to_client_color(suit) do
    case suit do
      :spades -> 6      # 黑桃 -> 6 (黑色)
      :hearts -> 5      # 红桃 -> 5 (红色)
      :diamonds -> 3    # 方块 -> 3 (红色)
      :clubs -> 4       # 梅花 -> 4 (黑色)
      _ -> 0
    end
  end

  @doc """
  将客户端花色值转换为服务端花色
  🎯 修复：与上面的映射保持一致
  """
  def client_color_to_suit(color) do
    case color do
      6 -> :spades      # 黑桃
      5 -> :hearts      # 红桃
      3 -> :diamonds    # 方块
      4 -> :clubs       # 梅花
      _ -> :unknown
    end
  end

  @doc """
  将客户端牌数据转换为服务端格式
  """
  def format_cards_from_client(cards) when is_list(cards) do
    Enum.map(cards, &format_single_card_from_client/1)
  end

  def format_cards_from_client(_), do: []

  @doc """
  格式化单张牌从客户端格式
  """
  def format_single_card_from_client(%{"color" => color, "number" => number}) do
    %{
      suit: client_color_to_suit(color),
      value: number
    }
  end

  def format_single_card_from_client(_), do: %{suit: :unknown, value: 0}

  @doc """
  获取牌型的客户端数值

  对应前端CardType枚举：
  - 0: EM_TEENPATTI_CARDTYPE_NONE - 无
  - 1: EM_TEENPATTI_CARDTYPE_DAN - 单牌/高牌
  - 2: EM_TEENPATTI_CARDTYPE_DUIZI - 对子
  - 3: EM_TEENPATTI_CARDTYPE_TONGHUA - 同花
  - 4: EM_TEENPATTI_CARDTYPE_SHUNZI - 顺子
  - 5: EM_TEENPATTI_CARDTYPE_TONGHUASHUN - 同花顺
  - 6: EM_TEENPATTI_CARDTYPE_BAOZI - 豹子
  """
  def get_card_type_value(cards) do
    card_type = TeenPattiLogic.calculate_card_type(cards)

    case card_type do
      :none -> 0
      :high_card -> 1
      :pair -> 2
      :color -> 3
      :sequence -> 4
      :pure_sequence -> 5
      :trail -> 6
      _ -> 0
    end
  end

  @doc """
  将客户端牌型值转换为服务端牌型
  """
  def client_card_type_to_server(type_value) do
    case type_value do
      0 -> :none
      1 -> :high_card
      2 -> :pair
      3 -> :color
      4 -> :sequence
      5 -> :pure_sequence
      6 -> :trail
      _ -> :none
    end
  end

  @doc """
  格式化玩家状态为客户端格式

  对应前端EM_TEENPATTI_PLAYERSTATE：
  - 0: EM_TEENPATTI_PLAYER_NONE - 无效状态
  - 1: EM_TEENPATTI_PLAYER_PLAY - 游戏状态
  - 2: EM_TEENPATTI_PLAYER_FOLD - 弃牌状态
  - 3: EM_TEENPATTI_PLAYER_LOSE - 输牌状态
  """
  def format_player_state(state) do
    case state do
      :none -> 0
      :play -> 1
      :fold -> 2
      :lose -> 3
      _ -> 0
    end
  end

  @doc """
  将客户端玩家状态转换为服务端状态
  """
  def client_player_state_to_server(state_value) do
    case state_value do
      0 -> :none
      1 -> :play
      2 -> :fold
      3 -> :lose
      _ -> :none
    end
  end

  @doc """
  格式化游戏状态为客户端格式

  对应前端GameState：
  - 0: EM_TEENPATTI_GAMESTATE_START - 游戏开始
  - 1: EM_TEENPATTI_GAMESTATE_SENDCARD - 发牌状态
  - 2: EM_TEENPATTI_GAMESTATE_BET - 下注状态
  - 3: EM_TEENPATTI_GAMESTATE_COMPETITION - 比牌状态
  - 4: EM_TEENPATTI_GAMESTATE_END - 结束状态
  - 5: EM_TEENPATTI_GAMESTATE_WAITRECHARGE - 等待充值
  """
  def format_game_state(state) do
    case state do
      :start -> 0
      :send_card -> 1
      :bet -> 2
      :competition -> 3
      :end -> 4
      :wait_recharge -> 5
      _ -> 0
    end
  end

  @doc """
  格式化下注类型为客户端格式

  对应前端BetType：
  - 0: BOTTOM - 底注
  - 1: FILL - 下注
  - 2: COMPETITION - 比牌
  - 3: FOLD - 弃牌
  """
  def format_bet_type(bet_type) do
    case bet_type do
      :bottom -> 0
      :fill -> 1
      :competition -> 2
      :fold -> 3
      _ -> 0
    end
  end

  @doc """
  将客户端下注类型转换为服务端类型
  """
  def client_bet_type_to_server(bet_type_value) do
    case bet_type_value do
      0 -> :bottom
      1 -> :fill
      2 -> :competition
      3 -> :fold
      _ -> :bottom
    end
  end

  @doc """
  格式化玩家信息为客户端格式
  """
  def format_player_info(player, game_state \\ %{}) do
    player_id = player.numeric_id

    %{
      "playerid" => player_id,
      "seat" => Map.get(player, :seat, 0),
      "nickname" => Map.get(player, :nickname, ""),
      "avatar" => Map.get(player, :avatar, ""),
      "money" => Map.get(player, :points, 0),
      "level" => Map.get(player, :level, 1),
      "isgaming" => Map.get(game_state, :player_states, %{}) |> Map.get(player_id, :none) != :none,
      "state" => format_player_state(Map.get(game_state, :player_states, %{}) |> Map.get(player_id, :none)),
      "bet" => Map.get(game_state, :player_bets, %{}) |> Map.get(player_id, 0),
      "totalbet" => Map.get(game_state, :player_total_bets, %{}) |> Map.get(player_id, 0),
      "seen" => Map.get(game_state, :player_seen_status, %{}) |> Map.get(player_id, false)
    }
  end

  @doc """
  格式化房间信息为客户端格式
  """
  def format_room_info(state) do
    %{
      "roomid" => state.id,
      "gameid" => state.game_id,
      "difen" => state.game_data.config.base_bet,
      "inmoney" => state.game_data.config.base_bet * 10,  # 入场金币要求
      "minmoney" => state.game_data.config.base_bet * 5,  # 最小金币要求
      "maxplayers" => state.max_players,
      "currentplayers" => map_size(state.players),
      "roomstate" => if(state.room_state == :playing, do: 1, else: 0),
      "gamestate" => format_game_state(state.game_data.state),
      "round" => state.game_data.round,
      "pot_total" => state.game_data.pot_total,
      "current_bet" => state.game_data.current_bet
    }
  end

  @doc """
  格式化结算数据为客户端格式
  """
  def format_settlement_data(settlement_result, state) do
    %{
      "winnerid" => settlement_result.winner,
      "pot_total" => state.game_data.pot_total,
      "player_changes" => settlement_result.player_changes,
      "player_final_money" => format_player_final_money(state),
      "winning_cards" => format_winning_cards(settlement_result, state),
      "all_player_cards" => format_all_player_cards_for_settlement(state)
    }
  end

  @doc """
  格式化机器人数据为客户端格式
  """
  def format_robot_data(robot_data) do
    %{
      "playerid" => robot_data.numeric_id,
      "nickname" => robot_data.nickname,
      "avatar" => robot_data.avatar,
      "money" => robot_data.points,
      "level" => robot_data.level || 1,
      "is_robot" => true,
      "ai_style" => robot_data.ai_style || :moderate,
      "created_at" => robot_data.created_at
    }
  end

  @doc """
  格式化统计数据为客户端格式
  """
  def format_statistics_data(statistics) do
    %{
      "total_rounds" => statistics.total_rounds,
      "total_pot" => statistics.total_pot,
      "player_wins" => statistics.player_wins,
      "card_type_distribution" => statistics.card_type_stats,
      "average_pot_size" => if(statistics.total_rounds > 0,
                               do: div(statistics.total_pot, statistics.total_rounds),
                               else: 0)
    }
  end

  # ==================== 私有辅助函数 ====================

  defp format_player_final_money(state) do
    state.players
    |> Enum.map(fn {player_id, player} ->
      {player_id, player.points || 0}
    end)
    |> Enum.into(%{})
  end

  defp format_winning_cards(settlement_result, state) do
    if settlement_result.winner do
      winner_cards = Map.get(state.game_data.player_cards, settlement_result.winner, [])
      format_cards_for_client(winner_cards)
    else
      []
    end
  end

  defp format_all_player_cards_for_settlement(state) do
    state.game_data.player_cards
    |> Enum.map(fn {player_id, cards} ->
      {player_id, %{
        "cards" => format_cards_for_client(cards),
        "cardtype" => get_card_type_value(cards),
        "cardtype_name" => TeenPattiLogic.get_card_type_name_en(TeenPattiLogic.calculate_card_type(cards))
      }}
    end)
    |> Enum.into(%{})
  end

  @doc """
  验证客户端数据格式
  """
  def validate_client_data(data, expected_fields) do
    Enum.all?(expected_fields, fn field ->
      Map.has_key?(data, field) and not is_nil(data[field])
    end)
  end

  @doc """
  安全获取客户端数据字段
  """
  def safe_get_client_field(data, field, default \\ nil) do
    Map.get(data, field, default)
  end

  @doc """
  格式化错误信息为客户端格式
  """
  def format_error_response(error_code, error_message) do
    %{
      "success" => false,
      "error_code" => error_code,
      "error_message" => error_message,
      "timestamp" => DateTime.utc_now() |> DateTime.to_unix(:millisecond)
    }
  end

  @doc """
  格式化成功响应为客户端格式
  """
  def format_success_response(data \\ %{}) do
    base_response = %{
      "success" => true,
      "timestamp" => DateTime.utc_now() |> DateTime.to_unix(:millisecond)
    }

    Map.merge(base_response, data)
  end
end
