defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiMessageBuilder do
  @moduledoc """
  Teen Patti游戏消息构建器

  统一处理所有客户端协议消息的构建，减少重复代码
  基于前端协议定义和C++服务端实现
  """

  require Logger
  alias Cypridina.Teen.GameSystem.Games.TeenPatti.{TeenPattiGame, TeenPattiDataFormatter}

  # 主协议ID
  @main_id 5

  @doc """
  构建基础协议消息结构
  """
  def build_message(sub_id, data, opts \\ []) do
    base_message = %{
      "mainId" => @main_id,
      "subId" => sub_id,
      "data" => data
    }

    if opts[:with_timestamp] do
      Map.put(base_message, "timestamp", DateTime.utc_now() |> DateTime.to_unix(:millisecond))
    else
      base_message
    end
  end
  def build_hall_message(main_id,sub_id, data, opts \\ []) do
    base_message = %{
      "mainId" => main_id,
      "subId" => sub_id,
      "data" => data
    }

    if opts[:with_timestamp] do
      Map.put(base_message, "timestamp", DateTime.utc_now() |> DateTime.to_unix(:millisecond))
    else
      base_message
    end
  end
  @doc """
  构建游戏配置消息 (SC_TEENPATTI_START_P - 1000)
  参考C++服务端的数据格式
  """
  def build_game_config(state) do
    config = state.game_data.config

    # 获取庄家信息 (对照旧项目日志: banker: 5, banker_userid: ********)
    banker_player = get_banker_player(state)
    banker_seat = if banker_player, do: get_player_seat_in_room(state, banker_player.numeric_id), else: 5
    banker_userid = if banker_player, do: banker_player.numeric_id, else: 0

    # 对照旧项目日志优化数据结构
    # 旧项目日志: {difen: 30, banker: 5, banker_userid: ********, state: 0, potlimit: 60000, ...}
    data = %{
      "difen" => config.base_bet,                                 # 对照旧项目: difen: 30
      "banker" => banker_seat,                                    # 对照旧项目: banker: 5
      "banker_userid" => banker_userid,                           # 对照旧项目: banker_userid: ********
      "state" => TeenPattiDataFormatter.format_game_state(:start), # 对照旧项目: state: 0
      "potlimit" => config.pot_limit,                             # 对照旧项目: potlimit: 60000
      # 添加其他Teen Patti配置
      "chaallimit" => config.chaal_limit,
      "blindroundlimit" => config.blind_round_limit,
      "compminturnnum" => config.comp_min_turn_num,
      "rejectcompnum" => config.reject_comp_num,
      # 添加游戏轮次信息
      "round" => state.game_data.round || 1,
      "curtimes" => state.game_data.turn_count || 1
    }

    protocols = TeenPattiGame.protocols()
    build_message(protocols.sc_teenpatti_start, data)
  end

  @doc """
  构建发牌消息 (SC_TEENPATTI_SENDCARD_P - 1001)
  参考C++服务端和前端期望的数据结构
  """
  def build_dealing_message(state, player) do
    # 获取正确的座位号 - 使用房间中的座位分配逻辑
    seat_id = get_player_seat_in_room(state, player.numeric_id)

    # 获取正确的玩家金币
    player_money = get_player_money_safe(state, player)

    # 参考前端TPGameCore.ts onSendCard函数期望的数据结构
    data = %{
      "state" => TeenPattiDataFormatter.format_game_state(state.game_data.state),  # 修复：使用格式化函数转换为数字
      "playerid" => player.numeric_id,                   # 玩家ID
      "seatid" => seat_id,                               # 座位号（修复）
      "bettype" => 0,                                    # 下注类型：初始状态
      "curtimes" => state.game_data.turn_count || 1,     # 当前轮次
      "allbet" => state.game_data.pot_total || 0,        # 总下注金额
      "pmoney" => player_money                           # 玩家金币（修复）
    }

    # 如果是机器人，添加额外信息（参考C++服务端）
    final_data = if Map.get(player, :is_robot, false) do
      Map.merge(data, %{
        "story_mode" => 0,    # 故事模式
        "storyid" => 0,       # 故事ID
        "cardtype" => 1,      # 临时牌型，实际发牌时更新
        "maxpoint" => 1       # 临时最大点数，实际发牌时更新
      })
    else
      data
    end

    protocols = TeenPattiGame.protocols()
    build_message(protocols.sc_teenpatti_sendcard, final_data)
  end

  @doc """
  构建下注开始消息 (SC_TEENPATTI_WAITOPT_P - 1014)
  参考C++服务端的数据结构
  """
  def build_betting_start(state) do
    # 获取当前操作玩家的座位号
    current_player = Map.get(state.players, state.game_data.current_player)
    current_seat = if current_player, do: get_player_seat(current_player), else: 1

    data = %{
      "state" => TeenPattiDataFormatter.format_game_state(state.game_data.state),  # 修复：使用格式化函数转换为数字
      "optseatid" => current_seat,  # 使用座位号而不是玩家ID
      "curtimes" => Map.get(state.game_data, :current_times, 1),  # 🎯 修复：使用current_times
      "allbet" => Map.get(state.game_data, :total_bet, 0),
      "waittime" => state.game_data.config.operation_wait_time,
      "turnnum" => Map.get(state.game_data, :turn_count, 1),
      "optsumtime" => state.game_data.config.operation_wait_time
    }

    protocols = TeenPattiGame.protocols()
    build_message(protocols.sc_teenpatti_waitopt, data)
  end

  @doc """
  构建下注响应消息 (SC_TEENPATTI_BET_P - 1003)
  参考前端TPGameCore.ts onBetData函数期望的数据结构
  """
  def build_bet_response(state, player_id, bet_amount, bet_type, fill \\ 1) do
    # 获取玩家的总下注金额（用于mybetall字段）
    player_total_bet = Map.get(state.game_data.player_total_bets, player_id, 0)

    # 获取玩家座位号
    player_seat = get_player_seat_in_room(state, player_id)

    # 🎯 完全匹配前端期望的字段：playerid, seatid, bettype, bet, allbet, curtimes, mybetall, fill
    data = %{
      "playerid" => player_id,                                # 前端: Common.toInt(info["playerid"])
      "seatid" => player_seat,                                # 🎯 新增：座位号（旧项目有此字段）
      "bettype" => bet_type,                                  # 前端: Common.toInt(info["bettype"])
      "bet" => bet_amount,                                    # 前端: Common.toInt(info["bet"])
      "allbet" => state.game_data.pot_total,                  # 前端: this.allbet = Common.toInt(info["allbet"])
      "curtimes" => state.game_data.turn_count,               # 前端: this.curtimes = Common.toInt(info["curtimes"])
      "mybetall" => player_total_bet + bet_amount,            # 前端: this.mybetall = Common.toInt(info["mybetall"])
      "fill" => fill,                                         # 🎯 关键：下注倍数，1=跟注，2=加注
      "state" => TeenPattiDataFormatter.format_game_state(:bet)  # 游戏状态
    }

    protocols = TeenPattiGame.protocols()
    build_message(protocols.sc_teenpatti_bet, data)
  end

  @doc """
  构建看牌响应消息 (SC_TEENPATTI_LOOK_P - 1005)
  """
  def build_look_response(state, player_id) do
    player_cards = Map.get(state.game_data.player_cards, player_id, [])
    formatted_cards = TeenPattiDataFormatter.format_cards_for_client(player_cards)

    data = %{
      "playerid" => player_id,
      "cards" => formatted_cards,
      "cardtype" => TeenPattiDataFormatter.get_card_type_value(player_cards)
    }

    protocols = TeenPattiGame.protocols()
    build_message(protocols.sc_teenpatti_look, data)
  end

  @doc """
  构建比牌请求消息 (SC_TEENPATTI_COMPETITION_P - 1007)
  参考前端TPGameCore.ts onCompetition函数期望的数据结构
  """
  def build_competition_request(state, requester_id, target_id) do
    # 获取玩家座位号
    requester_seat = get_player_seat_in_room(state, requester_id)
    target_seat = get_player_seat_in_room(state, target_id)

    # 前端期望的字段：state, optseatid, compseatid
    data = %{
      "state" => TeenPattiDataFormatter.format_game_state(:competition),  # 修复：使用格式化函数转换为数字
      "optseatid" => requester_seat,                        # ✅ 修复：添加操作者座位号
      "compseatid" => target_seat,                          # ✅ 修复：添加比牌对象座位号
      "waittime" => state.game_data.config.competition_time
    }

    protocols = TeenPattiGame.protocols()
    build_message(protocols.sc_teenpatti_competition, data)
  end

  @doc """
  构建比牌确认响应消息 (SC_TEENPATTI_COMPCONFIRM_P - 1009)
  🎯 修复：构建前端期望的比牌结果协议数据格式
  参考旧项目C++逻辑和前端 onCompConfirm 函数期望的数据结构
  """
  def build_competition_confirm_response(state, requester_id, target_id, confirmed, winner_id \\ nil) do
    # 如果只是确认阶段（没有winner_id），发送简单确认协议
    if is_nil(winner_id) do
      data = %{
        "playerid" => target_id,
        "seatid" => get_player_seat_in_room(state, target_id),
        "confirm" => confirmed
      }

      protocols = TeenPattiGame.protocols()
      build_message(protocols.sc_teenpatti_compconfirm, data)
    else
      # 比牌结果阶段，发送完整的比牌结果协议
      requester_seat = get_player_seat_in_room(state, requester_id)
      target_seat = get_player_seat_in_room(state, target_id)

      # 获取手牌信息
      requester_cards = Map.get(state.game_data.player_cards, requester_id, [])
      target_cards = Map.get(state.game_data.player_cards, target_id, [])

      # 🎯 调试：检查手牌数据
      Logger.info("🃏 [TEEN_PATTI_MSG_BUILD] ========== 构建比牌结果协议 ==========")
      Logger.info("🃏 [TEEN_PATTI_MSG_BUILD] 发起者#{requester_id}手牌: #{inspect(requester_cards)}")
      Logger.info("🃏 [TEEN_PATTI_MSG_BUILD] 目标#{target_id}手牌: #{inspect(target_cards)}")
      Logger.info("🃏 [TEEN_PATTI_MSG_BUILD] 胜者: #{winner_id}")

      # 🎯 关键：前端期望的座位信息格式，参考旧项目字段含义
      # 旧项目中：CompSeat是被比牌玩家，OptSeat是发起比牌玩家
      data = %{
        "playerid" => target_id,  # 被比牌的玩家ID
        "seatid" => target_seat,  # 被比牌的玩家座位
        "confirm" => true,        # 比牌确认结果（总是true，因为已经执行了比牌）
        "compseat" => %{
          "seatid" => target_seat,    # 被比牌的玩家座位（CompPlayer）
          "iswin" => if(winner_id == target_id, do: 1, else: 0),
          "cards" => TeenPattiDataFormatter.format_cards_for_client(target_cards),
          "cardtype" => TeenPattiDataFormatter.get_card_type_value(target_cards)
        },
        "optseat" => %{
          "seatid" => requester_seat, # 发起比牌的玩家座位（OptPlayer）
          "iswin" => if(winner_id == requester_id, do: 1, else: 0),
          "cards" => TeenPattiDataFormatter.format_cards_for_client(requester_cards),
          "cardtype" => TeenPattiDataFormatter.get_card_type_value(requester_cards)
        }
      }

      # 🎯 调试：检查构建的数据
      Logger.info("🃏 [TEEN_PATTI_MSG_BUILD] 🎯 构建的比牌结果数据: #{inspect(data)}")
      Logger.info("🃏 [TEEN_PATTI_MSG_BUILD] 🃏 optseat cards: #{inspect(data["optseat"]["cards"])}")
      Logger.info("🃏 [TEEN_PATTI_MSG_BUILD] 🃏 compseat cards: #{inspect(data["compseat"]["cards"])}")

      protocols = TeenPattiGame.protocols()
      message = build_message(protocols.sc_teenpatti_compconfirm, data)

      Logger.info("🃏 [TEEN_PATTI_MSG_BUILD] 🎯 最终协议消息: #{inspect(message)}")
      message
    end
  end

  @doc """
  构建弃牌消息 (SC_TEENPATTI_FOLD_P - 1011)
  参考前端TPGameCore.ts onCardFold函数期望的数据结构
  """
  def build_fold_message(state, player_id) do
    # 获取玩家座位号
    player_seat = get_player_seat_in_room(state, player_id)

    # 前端期望的字段：playerid, seatid
    data = %{
      "playerid" => player_id,
      "seatid" => player_seat,                              # ✅ 修复：添加座位号
      "state" => TeenPattiDataFormatter.format_player_state(:fold)  # 修复：使用格式化函数转换为数字
    }

    protocols = TeenPattiGame.protocols()
    build_message(protocols.sc_teenpatti_fold, data)
  end

  @doc """
  构建等待充值响应消息 (SC_TEENPATTI_WAITRECHARGE_P - 1013)
  参考前端TPGameCore.ts onWaitRecharge函数期望的数据结构
  """
  def build_wait_recharge_response(state, player_id, confirmed) do
    # 获取玩家座位号
    player_seat = get_player_seat_in_room(state, player_id)

    # 前端期望的字段：state, seatid, optsumtime
    data = %{
      "state" => TeenPattiDataFormatter.format_game_state(state.game_data.state),  # 修复：使用格式化函数转换为数字
      "seatid" => player_seat,                              # ✅ 修复：添加座位号
      "optsumtime" => state.game_data.config.recharge_wait_time  # ✅ 修复：使用optsumtime而不是waittime
    }

    protocols = TeenPattiGame.protocols()
    build_message(protocols.sc_teenpatti_waitrecharge, data)
  end

  @doc """
  构建结算消息 (SC_TEENPATTI_JIESHUAN_P - 1015)
  参考前端TPGameCore.ts onJieShuan函数期望的数据结构
  """
  def build_settlement(state, settlement_result) do
    # 🎯 修复：只包含参与当前局游戏的玩家（有手牌的玩家）
    # 游戏开始后加入的玩家不应该出现在结算中
    player_list =
      state.players
      |> Map.values()
      |> Enum.filter(fn player ->
        # 只包含有手牌的玩家（参与了当前局游戏）
        player_cards = Map.get(state.game_data.player_cards, player.numeric_id, [])
        length(player_cards) > 0
      end)
      |> Enum.into(%{}, fn player ->
        player_id = player.numeric_id
        player_seat = get_player_seat_in_room(state, player_id)
        player_cards = Map.get(state.game_data.player_cards, player_id, [])
        change_money = Map.get(settlement_result.player_changes, player_id, 0)

        player_info = %{
          "playerid" => player_id,
          "seatid" => player_seat,                          # ✅ 修复：添加座位号
          "changemoney" => change_money,                    # ✅ 修复：金币变化
          "cards" => TeenPattiDataFormatter.format_cards_for_client(player_cards),  # ✅ 修复：玩家手牌
          "cardtype" => TeenPattiDataFormatter.get_card_type_value(player_cards)    # ✅ 修复：牌型
        }

        {to_string(player_id), player_info}
      end)

    # 前端期望的字段：sysopencard, winner, playerlist
    data = %{
      "sysopencard" => 1,                                   # ✅ 修复：系统开牌标志
      "winner" => settlement_result.winner,                 # ✅ 修复：使用winner而不是winnerid
      "playerlist" => player_list                           # ✅ 修复：玩家列表格式
    }

    protocols = TeenPattiGame.protocols()
    build_message(protocols.sc_teenpatti_jieshuan, data)
  end

  @doc """
  构建当前游戏状态消息（用于重连）
  重连时应该发送与游戏开始协议相同的数据结构，而不是当前状态数据
  """
  def build_current_state(state, player) do
    # 重连时发送游戏配置数据，与build_game_config保持一致
    config = state.game_data.config

    # 获取庄家信息
    banker_player = get_banker_player(state)
    banker_seat = if banker_player, do: get_player_seat(banker_player), else: 1
    banker_userid = if banker_player, do: banker_player.numeric_id, else: 0

    # 发送与游戏开始协议相同的数据结构，前端期望这些字段
    data = %{
      "difen" => config.base_bet,
      "banker" => banker_seat,
      "banker_userid" => banker_userid,
      "state" => TeenPattiDataFormatter.format_game_state(state.game_data.state),  # 修复：使用格式化函数转换为数字
      "potlimit" => config.pot_limit,
      "chaallimit" => config.chaal_limit,
      "blindroundlimit" => config.blind_round_limit,
      "compminturnnum" => config.comp_min_turn_num,
      "rejectcompnum" => config.reject_comp_num,
      # 添加当前游戏进度信息
      "round" => state.game_data.round,
      "pot_total" => state.game_data.pot_total,
      "current_bet" => state.game_data.current_bet,
      "turn_count" => state.game_data.turn_count
    }

    protocols = TeenPattiGame.protocols()
    build_message(protocols.sc_teenpatti_start, data)
  end

  @doc """
  构建玩家状态更新消息
  """
  def build_player_state_update(state, player_id, new_state) do
    data = %{
      "playerid" => player_id,
      "state" => new_state,
      "playermoney" => get_player_points(state, player_id)
    }

    protocols = TeenPattiGame.protocols()
    build_message(protocols.sc_teenpatti_waitopt, data)
  end

  @doc """
  构建错误响应消息
  """
  def build_error_response(error_code, error_message \\ "") do
    data = %{
      "error_code" => error_code,
      "error_message" => error_message
    }

    build_message(9999, data)  # 通用错误协议
  end

  # ==================== 辅助函数 ====================

  defp get_player_points(state, player_id) do
    case Map.get(state.players, player_id) do
      nil -> 0
      player -> player.points || 0
    end
  end

  defp format_all_player_cards(state) do
    state.game_data.player_cards
    |> Enum.map(fn {player_id, cards} ->
      {player_id, TeenPattiDataFormatter.format_cards_for_client(cards)}
    end)
    |> Enum.into(%{})
  end

  defp get_all_player_card_types(state) do
    state.game_data.player_cards
    |> Enum.map(fn {player_id, cards} ->
      {player_id, TeenPattiDataFormatter.get_card_type_value(cards)}
    end)
    |> Enum.into(%{})
  end

  @doc """
  构建机器人操作通知消息
  """
  def build_robot_action_notice(state, robot_id, action, action_data \\ %{}) do
    base_data = %{
      "playerid" => robot_id,
      "action" => action,
      "timestamp" => DateTime.utc_now() |> DateTime.to_unix(:millisecond)
    }

    data = Map.merge(base_data, action_data)

    # 根据动作类型选择协议
    protocols = TeenPattiGame.protocols()

    sub_id = case action do
      "bet" -> protocols.sc_teenpatti_bet
      "look" -> protocols.sc_teenpatti_look
      "fold" -> protocols.sc_teenpatti_fold
      "competition" -> protocols.sc_teenpatti_competition
      _ -> protocols.sc_teenpatti_waitopt
    end

    build_message(sub_id, data)
  end

  @doc """
  构建玩家进入房间消息 (SC_ROOM_PLAYER_ENTER_P - mainId=4, subId=12)
  使用标准的房间协议，与前端完全匹配
  """
  def build_player_enter_message(player, seat_number \\ 0) do
    data = %{
      "playerid" => player.numeric_id,
      "nickname" => get_player_nickname(player),  # 使用专门函数获取昵称
      "money" => get_player_money(player),
      "iscustomhead" => 0,
      "headid" => get_player_headid(player),  # 使用专门函数获取头像ID
      "wxheadurl" => get_player_wxheadurl(player),  # 使用专门函数获取微信头像URL
      "seat" => seat_number,  # 使用传入的座位号
      "playerstate" => get_player_state(player),
      "isgaming" => get_player_gaming_status(player),  # 关键字段：是否在游戏中
      "level" => Map.get(player.user, :level, 1)  # 玩家等级
    }

    # 使用标准房间协议 mainId=4, subId=12 (注意小写)
    %{
      "mainId" => 4,
      "subId" => 12,
      "data" => data  # 使用小写的data，匹配前端期望
    }
  end

  # 辅助函数：获取玩家金钱 - 参考SlotNiu的实现
  defp get_player_money(player) do
    # 对于机器人，直接返回其金钱
    if Map.get(player, :is_robot, false) do
      Map.get(player.user, :points, 0)
    else
      # 对于真实玩家，需要通过room_base获取积分
      # 这里返回0，让调用方通过get_player_points获取
      0
    end
  end

  # 辅助函数：安全获取玩家金币（修复版本）
  defp get_player_money_safe(state, player) do
    cond do
      Map.get(player, :is_robot, false) ->
        # 机器人玩家 - 从机器人数据中获取
        robot_data = Map.get(state.game_data.robots, player.numeric_id)
        Map.get(robot_data, :points, 0)

      true ->
        # 真实玩家 - 使用房间基础函数获取积分
        try do
          Cypridina.Teen.GameSystem.RoomBase.get_player_points(state, player.numeric_id)
        rescue
          _ -> 0
        end
    end
  end

  # 🎯 关键修复：获取房间中玩家的固定座位号 (对照旧项目：座位从1开始)
  # 前端期望座位号稳定，不会因为其他玩家加入而改变
  defp get_player_seat_in_room(state, player_id) do
    # 🎯 关键修复：直接从玩家数据中获取固定座位号
    # 不再按ID排序重新分配，确保座位稳定
    case Map.get(state.players, player_id) do
      %{seat: seat} when is_integer(seat) and seat > 0 ->
        Logger.info("🎯 [SEAT_ASSIGNMENT] 玩家固定座位 - 玩家ID: #{player_id}, 座位号: #{seat}")
        seat
      _ ->
        Logger.warning("🃏 [TEEN_PATTI_SEAT] 玩家没有座位信息 - 玩家: #{player_id}, 返回默认座位1")
        1
    end
  end

  # 辅助函数：获取玩家座位
  defp get_player_seat(player) do
    Map.get(player, :seat, 0)
  end

  # 辅助函数：获取玩家状态
  defp get_player_state(player) do
    Map.get(player, :state, 0)
  end

  # 辅助函数：获取玩家游戏状态 (前端关键字段)
  defp get_player_gaming_status(player) do
    # 机器人默认在游戏中
    if Map.get(player, :is_robot, false) do
      1
    else
      # 真实玩家根据游戏状态判断
      case Map.get(player, :game_state, :waiting) do
        :playing -> 1
        :waiting -> 0
        _ -> 0
      end
    end
  end

  # 辅助函数：获取庄家玩家
  defp get_banker_player(state) do
    # 简单选择第一个玩家作为庄家
    state.players
    |> Map.values()
    |> List.first()
  end



  # 辅助函数：获取玩家昵称（机器人和真实玩家）
  defp get_player_nickname(player) do
    if Map.get(player, :is_robot, false) do
      # 🎯 修复：机器人使用真实的印度人名，不暴露机器人身份
      Map.get(player, :nickname, "Player#{player.numeric_id}")
    else
      # 🎯 修复：真实玩家从 user 对象中获取用户名作为昵称
      case player do
        %{user: %{username: username}} when is_binary(username) and username != "" ->
          username
        %{user: %{nickname: nickname}} when is_binary(nickname) and nickname != "" ->
          nickname
        %{nickname: nickname} when is_binary(nickname) and nickname != "" ->
          nickname
        %{username: username} when is_binary(username) and username != "" ->
          username
        _ ->
          "玩家#{player.numeric_id}"
      end
    end
  end

  # 辅助函数：获取玩家头像ID（机器人和真实玩家）
  defp get_player_headid(player) do
    if Map.get(player, :is_robot, false) do
      # 机器人使用数字头像ID (1-10)，前端期望数字而不是字符串
      robot_avatar_id = Map.get(player, :avatar_id, nil)
      if is_binary(robot_avatar_id) do
        # 如果是字符串，转换为数字ID
        1 + rem(abs(player.numeric_id), 10)
      else
        # 如果已经是数字，直接使用
        robot_avatar_id || (1 + rem(abs(player.numeric_id), 10))
      end
    else
      # 真实玩家使用用户头像ID
      Map.get(player.user, :avatar_id, 1)
    end
  end

  # 辅助函数：获取玩家微信头像URL（机器人和真实玩家）
  defp get_player_wxheadurl(player) do
    if Map.get(player, :is_robot, false) do
      # 机器人没有微信头像URL
      ""
    else
      # 真实玩家从 user 对象中获取微信头像URL
      case player do
        %{user: %{avatar_url: url}} when is_binary(url) and url != "" ->
          url
        _ ->
          ""
      end
    end
  end

  @doc """
  构建房间信息更新消息
  """
  def build_room_info_update(state) do
    active_players =
      state.players
      |> Map.values()
      |> Enum.filter(fn player ->
        Map.get(state.game_data.player_states, player.numeric_id, :none) != :fold
      end)

    data = %{
      "room_id" => state.id,
      "player_count" => length(active_players),
      "game_state" => state.game_data.state,
      "round" => state.game_data.round,
      "pot_total" => state.game_data.pot_total
    }

    build_message(9998, data)  # 房间信息更新协议
  end

  @doc """
  构建房间信息消息 (使用标准Game协议)
  参考SlotNiu的成功实现，使用对象格式的playerlist
  """
  def build_room_info(state, get_player_points_fn \\ nil) do
    alias Cypridina.Teen.GameSystem.PlayerData

    # 🎯 关键修复：构建玩家列表 - 使用玩家的固定座位号
    # 前端期望座位号稳定，不会因为其他玩家加入而改变
    # 对照旧项目：座位从1开始编号，使用玩家的固定seat字段
    player_list =
      state.players
      |> Map.values()
      |> Enum.into(%{}, fn player ->
        # 🎯 关键修复：使用玩家的固定座位号，而不是重新分配
        player_seat = Map.get(player, :seat, 1)  # 获取玩家的固定座位号

        # 使用传入的函数或默认的PlayerData.get_points方法安全获取玩家积分
        player_money = if get_player_points_fn do
          get_player_points_fn.(player.numeric_id)
        else
          PlayerData.get_points(player)
        end

        player_info = %{
          "playerid" => player.numeric_id,
          "nickname" => get_player_nickname(player),  # 使用专门函数获取昵称
          "money" => player_money,
          "seat" => player_seat,  # 🎯 关键修复：使用玩家的固定座位号
          "headid" => get_player_headid(player),  # 使用专门函数获取头像ID
          "wxheadurl" => get_player_wxheadurl(player),  # 使用专门函数获取微信头像URL
          "level" => Map.get(player.user, :level, 1),
          "isgaming" => get_player_gaming_status(player),  # 关键字段：是否在游戏中
          "iscustomhead" => 0,  # 是否自定义头像
          "playerstate" => 0  # 玩家状态
        }

        # 🎯 关键修复：使用玩家的固定座位号作为key，确保前端座位稳定
        {to_string(player_seat), player_info}
      end)

    # 对照旧项目日志优化房间配置数据格式
    # 旧项目日志: {cointype: 1, difen: 30, dingfen: 0, inmoney: 500, minmoney: 100, ...}
    config = state.game_data.config

    data = %{
      "roomid" => state.id,
      "playerlist" => player_list,  # 对象格式：{"1": {...}, "2": {...}}
      "playercount" => map_size(player_list),
      "roomstate" => 0,  # 房间状态
      # 对照旧项目日志添加的关键字段
      "cointype" => 1,                    # 金币类型
      "difen" => config.base_bet,         # 底分 (对照旧项目: difen: 30)
      "dingfen" => 0,                     # 定分 (对照旧项目: dingfen: 0)
      "inmoney" => config.base_bet * 10,  # 入场金额 (对照旧项目: inmoney: 500)
      "minmoney" => config.base_bet * 5,  # 最小金额 (对照旧项目: minmoney: 100)
      # 添加其他Teen Patti特有配置
      "potlimit" => config.pot_limit,     # 总注限制
      "chaallimit" => config.chaal_limit, # 单注封顶
      "blindroundlimit" => config.blind_round_limit, # 暗注最大回合
      "maxplayers" => state.max_players   # 最大玩家数
    }

    # 使用标准Game协议: MainProto.Game (4) + Game.SC_ROOM_INFO_P (2)
    # 注意：使用小写的mainId和subId，匹配前端期望
    %{
      "mainId" => 4,
      "subId" => 2,
      "data" => data
    }
  end

  @doc """
  构建暂离状态消息
  🎯 修复：使用前端期望的协议ID
  """
  def build_zanli_message(player_id, is_zanli) do
    if is_zanli do
      # 暂离成功消息 - SC_ROOM_ZANLI_SUCCESS_P
      data = %{
        "playerid" => player_id
      }
      build_message(4, 9, data)  # MainID=4, SubID=9 (SC_ROOM_ZANLI_SUCCESS_P)
    else
      # 暂离回来成功消息 - SC_ROOM_ZANLI_COMBACK_SUCCESS_P
      data = %{
        "playerid" => player_id
      }
      build_message(4, 11, data)  # MainID=4, SubID=11 (SC_ROOM_ZANLI_COMBACK_SUCCESS_P)
    end
  end

  @doc """
  构建玩家退出消息 (SC_ROOM_PLAYER_QUIT_P - mainId=4, subId=14)
  参考C++服务端的玩家退出协议
  """
  def build_player_quit_message(player_id, reason \\ 0) do
    data = %{
      "playerid" => player_id,
      "reason" => reason  # 0=正常退出, 1=破产退出, 2=暂离退出
    }

    # 使用标准房间协议 mainId=4, subId=14
    %{
      "mainId" => 4,
      "subId" => 14,
      "data" => data
    }
  end

  @doc """
  构建看牌协议 (SC_TEENPATTI_LOOK_P - mainId=5, subId=1005)
  对照旧项目日志格式: {playerid: 96515263, seatid: 4, _roomid: 1, _playerid: 82606679}
  """
  def build_look_card_message(state, look_player_id, current_player_id) do
    look_player_seat = get_player_seat_in_room(state, look_player_id)

    # 对照旧项目日志格式
    data = %{
      "playerid" => look_player_id,                    # 对照旧项目: playerid: 96515263
      "seatid" => look_player_seat,                    # 对照旧项目: seatid: 4
      "_roomid" => state.id,                           # 对照旧项目: _roomid: 1
      "_playerid" => current_player_id                 # 对照旧项目: _playerid: 82606679
    }

    protocols = TeenPattiGame.protocols()
    build_message(protocols.sc_teenpatti_look, data)
  end
end
