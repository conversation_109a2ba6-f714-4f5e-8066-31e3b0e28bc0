defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiRoom do
  @moduledoc """
  Teen Patti游戏房间实现

  ## 游戏流程
  1. **初始化阶段** - 房间创建和配置
  2. **玩家管理** - 玩家加入、离开、重连
  3. **发牌阶段** - 给每个玩家发3张牌
  4. **下注阶段** - 玩家进行下注、看牌、比牌、弃牌操作
  5. **结算阶段** - 计算输赢、分发奖励
  6. **等待阶段** - 准备下一轮游戏

  ## 游戏规则
  - Teen Patti是一种3张牌的扑克游戏
  - 支持2-5名玩家
  - 牌型：豹子 > 同花顺 > 顺子 > 同花 > 对子 > 高牌
  - 支持盲注(Blind)和明注(Seen)机制
  - 支持机器人和真实玩家混合游戏
  """

  use Cypridina.Teen.GameSystem.RoomBase, game_type: :teen_patti
  import Bitwise

  alias Cypridina.Teen.GameSystem.Games.TeenPatti.{TeenPattiGame, TeenPattiLogic}
  alias Cy<PERSON><PERSON>ina.Teen.GameSystem.Games.TeenPatti.{TeenPattiMessageBuilder, TeenPattiDataFormatter}
  alias Cy<PERSON>rid<PERSON>.Teen.GameSystem.Games.TeenPatti.{TeenPattiPlayer, TeenPattiDealer, TeenPattiConfig, TeenPattiRobot}
  alias Cypridina.Teen.GameSystem.{RobotService, PlayerDataBuilder}
  alias Cypridina.Accounts

  require Logger

  # ==================== 状态管理 ====================

  @impl true
  def init_game_logic(state) do
    Logger.debug("🃏 [TEEN_PATTI] 初始化Teen Patti游戏逻辑 - 房间: #{state.id}")

    # 获取游戏配置
    config = TeenPattiGame.default_config()
    game_config = TeenPattiGame.full_config()

    # 构建游戏数据
    game_data = build_game_data(config, game_config)

    new_state = %{state | game_data: game_data}

    Logger.debug("🃏 [TEEN_PATTI] Teen Patti游戏逻辑初始化完成")
    new_state
  end

  # 构建游戏数据
  defp build_game_data(config, game_config) do
    %{
      # 游戏状态
      state: TeenPattiGame.game_states().start,
      round: 1,

      # 牌局数据
      deck: [],
      player_cards: %{},

      # 下注数据
      current_bet: config.base_bet,
      pot_total: 0,
      player_bets: %{},
      player_total_bets: %{},

      # 玩家状态
      player_states: %{},
      player_seen_status: %{},  # 玩家是否已看牌

      # 操作控制
      current_player: nil,
      operation_timer: nil,
      turn_count: 0,  # 轮次计数（用于比牌条件判断）
      current_times: 1,  # 🎯 关键修复：当前下注倍数（对应旧项目的m_nCurTimes）
      turn_start_time: nil,  # 🎯 修复：添加缺失的 turn_start_time 字段

      # 庄家信息
      banker_id: nil,
      banker_seat: nil,

      # 比牌相关
      competition_player: nil,
      competition_target: nil,

      # 游戏配置
      config: game_config,

      # 统计数据
      statistics: build_initial_statistics(),

      # 机器人管理
      robots: %{}
    }
  end

  # 构建初始统计数据
  defp build_initial_statistics do
    %{
      total_rounds: 0,
      total_pot: 0,
      player_wins: %{},
      card_type_stats: %{}
    }
  end

  @impl true
  def on_player_joined(state, player) do
    player_type = if Map.get(player, :is_robot, false), do: "机器人", else: "真实玩家"

    # 🤖 如果是机器人，打印性格信息
    if Map.get(player, :is_robot, false) do
      robot_type = Map.get(player, :robot_type, :balanced)
      robot_state = Map.get(player, :robot_state, :normal)
      Logger.info("🤖 [TEEN_PATTI_ROBOT_PERSONALITY] 机器人加入 - ID: #{player.numeric_id}, 性格: #{robot_type}, 状态: #{robot_state}")
    else
      Logger.info("👤 [TEEN_PATTI_PLAYER_JOIN] 真实玩家加入 - ID: #{player.numeric_id}, 昵称: #{Map.get(player, :nickname, "未知")}")
    end

    # 🎯 关键修复：为玩家分配固定座位号
    # 前端期望座位号稳定，不会因为其他玩家加入而改变
    Logger.debug("🃏 [TEEN_PATTI_SEAT] 为玩家分配座位 - 玩家: #{player.numeric_id}")

    assigned_seat = assign_player_seat(state, player.numeric_id)
    Logger.debug("🃏 [TEEN_PATTI_SEAT] ✅ 座位分配完成 - 玩家: #{player.numeric_id}, 座位: #{assigned_seat}")

    # 🎯 关键修复：为真实玩家创建 Teen Patti 特有的游戏数据
    updated_player = if not Map.get(player, :is_robot, false) do
      # 真实玩家：创建 Teen Patti 游戏数据
      player_id = to_string(player.numeric_id)

      # 🎯 修复：使用get_player_points获取实际积分，避免Ash.NotLoaded错误
      current_points = get_player_points(state, player.numeric_id)
      Logger.debug("🃏 玩家积分: #{current_points}")

      teen_patti_data = TeenPattiPlayer.create_player_data(player_id, %{
        numeric_id: player.numeric_id,
        total_coins: current_points
      })

      # 合并 Teen Patti 数据到玩家的 user 字段中
      updated_user = Map.merge(player.user, teen_patti_data)

      # 更新玩家数据，添加座位信息和游戏数据
      player
      |> Map.put(:seat, assigned_seat)
      |> Map.put(:user, updated_user)
    else
      # 机器人：只添加座位信息
      Map.put(player, :seat, assigned_seat)
    end

    # 初始化玩家游戏状态
    game_data = %{state.game_data |
      player_states: Map.put(state.game_data.player_states, player.numeric_id, :none),
      player_seen_status: Map.put(state.game_data.player_seen_status, player.numeric_id, false),
      player_bets: Map.put(state.game_data.player_bets, player.numeric_id, 0),
      player_total_bets: Map.put(state.game_data.player_total_bets, player.numeric_id, 0)
    }

    # 更新房间状态，使用带座位信息的玩家数据
    new_state = %{state |
      players: Map.put(state.players, player.numeric_id, updated_player),
      game_data: game_data
    }

    # 发送游戏配置给新玩家
    send_game_config(new_state, updated_player)

    # 发送房间玩家列表给新玩家
    send_player_list(new_state, updated_player)

    # 如果游戏正在进行，发送当前状态
    if new_state.game_data.state != 0 do  # 🎯 修复：使用数字0而不是:start
      send_current_game_state(new_state, updated_player)
    end

    # 🎯 修复：移除重复的 broadcast_player_enter 调用
    # 父类 RoomBase 已经在第225行调用过 broadcast_player_enter，不需要重复调用
    # 广播玩家进入消息 (对照旧项目日志: [4][12]玩家信息) - 已由父类处理
    state_after_broadcast = new_state

    # 🎯 修复：玩家进入游戏后不自动准备，等待满足最低人数后再准备
    # 根据用户要求：不是一进入房间就准备，而是满足最低人数后自动在三秒后准备
    Logger.info("🃏 [TEEN_PATTI_READY] 玩家进入游戏，等待满足最低人数后自动准备 - 玩家: #{updated_player.numeric_id}")

    # 发送玩家状态协议 (对照旧项目日志: [4][4]玩家状态) - 设置为等待状态
    state_after_status = broadcast_player_status_change(state_after_broadcast, updated_player, 0) # 0=等待状态

    # 🎯 修复：发送房间状态协议 - 根据当前游戏状态决定
    # 如果游戏正在进行中，发送游戏中状态；如果等待开始，发送等待状态
    current_room_state = case state_after_status.game_data.state do
      0 -> 0  # 等待开始
      _ -> 1  # 游戏中
    end

    Logger.info("🃏 [TEEN_PATTI_JOIN] 玩家加入时房间状态: 游戏状态=#{state_after_status.game_data.state}, 发送房间状态=#{current_room_state}")

    # 🎯 关键修复：中途加入的玩家不应该收到自动准备倒计时
    # 只有在游戏真正等待开始且满足条件时才发送自动准备时间
    state_after_room_status = if current_room_state == 0 do
      # 游戏等待开始，但不立即发送自动准备时间给新加入的玩家
      # 自动准备逻辑应该在满足最小玩家数量时统一触发
      broadcast_room_status_change_without_auto_ready(state_after_status, 0)
    else
      # 游戏进行中，发送游戏中状态
      broadcast_room_status_change(state_after_status, 1)
    end

    # 对照旧项目：真实玩家加入后，延迟2-6秒开始逐个添加机器人
    # 这样模拟真实玩家陆续加入的效果，而不是一次性添加所有机器人
    real_players = count_real_players(state_after_room_status)
    current_robots = count_robot_players(state_after_room_status)
    target_robots = state_after_room_status.game_data.config.robot_count

    if real_players > 0 and current_robots < target_robots do
      # 对照旧项目：延迟2-6秒后开始添加第一个机器人
      delay_ms = 2000 + :rand.uniform(4000)  # 2-6秒随机延迟
      Logger.info("🤖 [TEEN_PATTI_ROBOT] 真实玩家加入，将在 #{delay_ms}ms 后开始添加机器人")
      Process.send_after(self(), :add_single_robot, delay_ms)
    end

    # 对照旧项目：启动机器人积分检查定时器 (只在第一个真实玩家加入时启动)
    real_players = count_real_players(state_after_room_status)
    if real_players == 1 do
      Logger.info("🤖 [TEEN_PATTI_ROBOT] 第一个真实玩家加入，启动机器人积分检查定时器")
      Process.send_after(self(), :check_robot_money, 30_000)  # 30秒后开始检查
    end

    # 🎯 修改：每次有玩家进入都检查是否可以开始游戏
    Logger.info("🃏 [TEEN_PATTI_PLAYER_JOIN] 🎯 检查游戏开始条件前 - 当前游戏状态: #{state_after_room_status.game_data.state}")
    final_state = check_and_start_game(state_after_room_status)

    Logger.info("🃏 [PLAYER_JOIN] 玩家加入完成 - 总玩家:#{map_size(final_state.players)} 游戏状态:#{final_state.game_data.state}")

    final_state
  end

  @impl true
  def on_player_rejoined(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info("🃏 [REJOIN] 玩家重连 - ID:#{numeric_id} 游戏状态:#{state.game_data.state}")

    # 注意：RoomBase已经处理了玩家重连逻辑，我们只需要发送游戏状态信息
    # 检查玩家积分（用于日志）
    current_points = get_player_points(state, numeric_id)
    Logger.info("🃏 [TEEN_PATTI_REJOIN] 重连玩家当前积分: #{current_points}")

    # 发送房间信息给重连玩家
    Logger.info("🃏 [TEEN_PATTI_REJOIN] 发送房间信息给重连玩家...")
    send_player_list(state, player)

    # 🎯 关键修复：如果游戏正在进行中，发送当前游戏状态给重连玩家
    # 这样可以确保玩家重连后能收到发牌协议等游戏状态
    case state.game_data.state do
      1 ->  # 🎯 修复：send_card状态
        Logger.info("🃏 [TEEN_PATTI_REJOIN] 游戏正在发牌阶段，发送发牌协议给重连玩家")
        send_dealing_message_to_player(state, player)

      2 ->  # 🎯 修复：bet状态
        Logger.info("🃏 [TEEN_PATTI_REJOIN] 游戏正在下注阶段，发送下注状态给重连玩家")
        send_betting_state_to_player(state, player)

      :competition ->
        Logger.info("🃏 [TEEN_PATTI_REJOIN] 游戏正在比牌阶段，发送比牌状态给重连玩家")
        send_competition_state_to_player(state, player)

      _ ->
        Logger.info("🃏 [TEEN_PATTI_REJOIN] 游戏未开始或已结束，无需发送额外状态")
    end

    # 发送当前游戏状态给重连玩家
    Logger.info("🃏 [TEEN_PATTI_REJOIN] 发送游戏状态给重连玩家...")
    # 根据游戏状态发送不同的协议
    case state.game_data.state do
      0 ->  # 🎯 修复：使用数字0而不是:start
        # 游戏未开始，发送游戏配置
        send_game_config(state, player)
      _ ->
        # 游戏进行中，发送当前状态
        send_current_game_state(state, player)
    end

    Logger.info("🃏 [REJOIN] 重连处理完成")
    state
  end

  @impl true
  def on_player_left(state, player) do
    Logger.info("🃏 [TEEN_PATTI] 玩家离开游戏 - 玩家: #{player.numeric_id}")

    # 如果是当前操作玩家离开，需要跳过其回合
    new_state = if state.game_data.current_player == player.numeric_id do
      handle_player_timeout(state, player.numeric_id)
    else
      state
    end

    # 清理玩家数据
    game_data = %{new_state.game_data |
      player_states: Map.delete(new_state.game_data.player_states, player.numeric_id),
      player_seen_status: Map.delete(new_state.game_data.player_seen_status, player.numeric_id),
      player_bets: Map.delete(new_state.game_data.player_bets, player.numeric_id),
      player_total_bets: Map.delete(new_state.game_data.player_total_bets, player.numeric_id),
      player_cards: Map.delete(new_state.game_data.player_cards, player.numeric_id)
    }

    game_cleaned_state = %{new_state | game_data: game_data}

    # 检查游戏是否需要结束
    game_end_checked_state = check_game_end_condition(game_cleaned_state)

    # 调用默认的玩家移除逻辑（广播消息、从玩家列表移除等）
    default_on_player_left(game_end_checked_state, player)
  end

  @impl true
  def on_game_start(state) do
    Logger.info("🃏 [TEEN_PATTI] 开始新一轮游戏 - 房间: #{state.id}")

    # 参考C++服务端：检查玩家数量是否足够开始游戏
    active_players = get_active_players(state)
    if length(active_players) < 2 do
      Logger.info("🃏 [TEEN_PATTI] 玩家数量不足，无法开始游戏 (当前: #{length(active_players)})")
      end_game_business(state)
    else
      Logger.info("🃏 [TEEN_PATTI] 玩家数量足够，开始游戏 (当前: #{length(active_players)})")

      # 1. 重置所有玩家状态 (参考C++服务端ResetAllPlayerState)
      state = reset_all_player_state(state)

      # 2. 广播玩家状态变更为准备 (对照旧项目日志: playerstate: 1)
      state = broadcast_all_players_status_change(state, 1)  # 1=准备状态

      # 3. 广播房间状态变更为游戏中 (对照旧项目日志: roomstate: 1)
      state = broadcast_room_status_change(state, 1)  # 1=游戏中

      # 4. 广播玩家状态变更为游戏中 (对照旧项目日志: playerstate: 2)
      state = broadcast_all_players_status_change(state, 2)  # 2=游戏中

      # 5. 广播游戏开始协议 (SC_TEENPATTI_START_P - 1000)
      state_with_banker = broadcast_game_start(state)

      # 6. 收取盲注 (对照旧项目日志: [4][8]金币更新 + [5][1003]下注协议)
      state_after_blinds = collect_blind_bets(state_with_banker)

      # 7. 开始发牌
      start_dealing_phase(state_after_blinds)
    end
  end

  # ==================== 游戏流程控制 ====================

  # 收取盲注 (对照旧项目日志: [4][8]金币更新 + [5][1003]下注协议)
  defp collect_blind_bets(state) do
    Logger.info("🃏 [TEEN_PATTI] 开始收取盲注阶段")

    # 获取参与游戏的玩家，按座位顺序排序
    active_players = get_active_players(state)
    |> Enum.sort_by(fn player -> get_player_seat_by_id(state, player.numeric_id) end)

    base_bet = state.game_data.config.base_bet

    # 按座位顺序收取盲注
    {updated_state, _} = Enum.reduce(active_players, {state, 0}, fn player, {acc_state, index} ->
      player_id = player.numeric_id
      seat_id = get_player_seat_by_id(acc_state, player_id)

      Logger.info("🃏 [TEEN_PATTI] 收取玩家盲注 - 玩家: #{player_id}, 座位: #{seat_id}, 金额: #{base_bet}")

      # 1. 扣除玩家金币并发送[4][8]金币更新协议
      state_after_deduct = deduct_player_money_and_broadcast(acc_state, player_id, base_bet)

      # 2. 记录下注并发送[5][1003]下注协议
      state_after_bet = record_blind_bet_and_broadcast(state_after_deduct, player_id, seat_id, base_bet, index)

      {state_after_bet, index + 1}
    end)

    Logger.info("🃏 [TEEN_PATTI] 盲注收取完成 - 总底池: #{updated_state.game_data.pot_total}")
    updated_state
  end

  # 扣除玩家金币并广播[4][8]金币更新协议
  defp deduct_player_money_and_broadcast(state, player_id, amount) do
    # 扣除玩家积分
    subtract_player_points(state, player_id, amount)

    # 获取扣除后的金币数量
    current_money = get_player_points(state, player_id)

    # 构建金币更新协议 (对照旧项目日志: [4][8])
    message = %{
      "mainId" => 4,
      "subId" => 8,
      "data" => %{
        "playerid" => player_id,
        "coin" => current_money,
        "gamemode" => 0,
        "roomid" => state.id
      }
    }

    Logger.info("🃏 [MONEY_UPDATE] 玩家#{player_id} 金币更新:#{current_money} [4][8]")

    broadcast_to_room(state, message)
    state
  end

  # 记录盲注并广播[5][1003]下注协议
  defp record_blind_bet_and_broadcast(state, player_id, seat_id, bet_amount, turn_index) do
    # 更新游戏数据中的下注记录
    game_data = %{state.game_data |
      player_bets: Map.put(state.game_data.player_bets, player_id, bet_amount),
      player_total_bets: Map.put(state.game_data.player_total_bets, player_id, bet_amount),
      pot_total: state.game_data.pot_total + bet_amount,
      turn_count: turn_index + 1
    }

    updated_state = %{state | game_data: game_data}

    # 构建下注协议 (对照旧项目日志: [5][1003])
    message = %{
      "mainId" => 5,
      "subId" => 1003,
      "data" => %{
        "playerid" => player_id,
        "seatid" => seat_id,
        "bet" => bet_amount,
        "bettype" => 0,  # 0=盲注
        "curtimes" => game_data.current_times,  # 🎯 修复：使用current_times
        "allbet" => game_data.pot_total,
        "state" => TeenPattiDataFormatter.format_game_state(:start)  # 游戏开始状态
      }
    }

    Logger.info("🃏 [BET] 玩家#{player_id}(座位#{seat_id}) 下注:#{bet_amount} [5][1003]")

    broadcast_to_room(updated_state, message)
    updated_state
  end

  # 参考C++服务端ResetAllPlayerState函数
  defp reset_all_player_state(state) do
    Logger.info("🃏 [TEEN_PATTI] 重置所有玩家状态")

    # 🎯 简化：重置机器人no_fold字段
    state = reset_robots_no_fold(state)

    # 🎯 关键修复：只有在游戏开始前就在房间的玩家才能参与当前小局
    # 游戏中途加入的机器人保持waiting_next_round状态，等待下一局
    updated_players =
      state.players
      |> Enum.map(fn {player_id, player} ->
        # 🎯 修复：根据玩家当前状态决定是否参与游戏
        new_game_state = case Map.get(player, :game_state, :waiting) do
          :waiting -> :playing  # 等待中的玩家参与游戏
          :waiting_next_round -> :waiting_next_round  # 等待下一局的玩家保持状态
          _ -> :playing  # 其他状态的玩家参与游戏
        end

        Logger.info("🃏 [TEEN_PATTI] 玩家状态更新 - 玩家: #{player_id}, 原状态: #{Map.get(player, :game_state, :waiting)}, 新状态: #{new_game_state}")

        updated_player = Map.put(player, :game_state, new_game_state)
        {player_id, updated_player}
      end)
      |> Enum.into(%{})

    # 🎯 修复：只为参与游戏的玩家设置游戏状态
    # 只有game_state为:playing的玩家才参与当前小局
    participating_players = updated_players
    |> Enum.filter(fn {_player_id, player} ->
      Map.get(player, :game_state) == :playing
    end)
    |> Enum.map(fn {player_id, _player} -> player_id end)

    player_states =
      participating_players
      |> Enum.map(fn player_id -> {player_id, :play} end)
      |> Enum.into(%{})

    # 清空游戏数据
    game_data = %{state.game_data |
      player_states: player_states,
      player_seen_status: %{},
      player_bets: %{},
      player_total_bets: %{},
      player_cards: %{},
      current_player: nil,
      turn_count: 0,
      current_times: 1,  # 🎯 修复：重置下注倍数
      pot_total: 0
    }

    Logger.info("🃏 [TEEN_PATTI] ✅ 参与游戏的玩家: #{inspect(participating_players)}")
    Logger.info("🃏 [TEEN_PATTI] ✅ 等待下一局的玩家: #{inspect(Map.keys(updated_players) -- participating_players)}")
    %{state | players: updated_players, game_data: game_data}
  end

  # 参考C++服务端EndGameBussiness函数
  defp end_game_business(state) do
    Logger.info("🃏 [TEEN_PATTI] 结束游戏业务")

    # 清理定时器
    clear_timers(state)

    # 重置游戏状态为等待
    game_data = %{state.game_data | state: :waiting}
    %{state | game_data: game_data}
  end

  # 清理所有定时器
  defp clear_timers(state) do
    if state.game_data.operation_timer do
      Process.cancel_timer(state.game_data.operation_timer)
    end

    # 🎯 修复：清理操作定时器
    state = cancel_operation_timer(state)

    # 🎯 修复：清理比牌动画定时器
    state = cancel_competition_animation_timer(state)

    # 🎯 修复：清理充值定时器
    state = cancel_recharge_timer(state)

    # 🎯 简化：重置机器人不弃牌字段
    state = reset_robots_no_fold(state)

    # 🎯 修复：清理准备定时器
    cancel_ready_timer(state)
  end

  defp maybe_start_game(state) do
    active_players = get_active_players(state)
    total_players = length(active_players)
    real_players = count_real_players(state)
    min_players = state.game_data.config.min_players

    Logger.debug("🃏 [TEEN_PATTI] 检查开始条件 - 总玩家: #{total_players}, 真实玩家: #{real_players}, 最低要求: #{min_players}, 游戏状态: #{state.game_data.state}")

    # 满足开始条件：
    # 1. 游戏处于等待状态
    # 2. 总玩家数达到最低要求
    # 3. 至少有一个真实玩家
    Logger.debug(total_players >= min_players)
    Logger.debug(real_players >= 1)
    Logger.debug(state.game_data.state == 0)  # 🎯 修复：使用数字0而不是:start
    if total_players >= min_players and real_players >= 1 and state.game_data.state == 0 do
      Logger.info("🃏 [TEEN_PATTI] 满足开始条件，立即启动游戏 - 总玩家: #{total_players}, 真实玩家: #{real_players}")

      # 🎯 修改：不延迟启动，玩家进入游戏就自动进入准备状态
      on_game_start(state)
    else
      state
    end
  end

  defp start_dealing_phase(state) do
    Logger.info("🃏 [TEEN_PATTI] 开始发牌阶段")

    # 获取参与游戏的玩家
    active_players = get_active_players(state)

    # 构建游戏上下文
    game_context = build_dealing_context(state, active_players)

    # 使用新的发牌模块进行智能发牌
    player_cards = TeenPattiDealer.deal_cards(active_players, game_context)

    # 设置玩家状态为游戏中
    player_states =
      active_players
      |> Enum.map(fn player -> {player.numeric_id, :play} end)
      |> Enum.into(%{})

    # 更新游戏数据
    game_data = %{state.game_data |
      state: 1,  # 🎯 修复：send_card状态
      player_cards: player_cards,
      player_states: player_states,
      turn_count: 0,
      current_times: 1  # 🎯 修复：重置下注倍数
    }

    new_state = %{state | game_data: game_data}

    # 广播发牌消息
    broadcast_dealing(new_state)

    # 延迟后开始下注阶段
    Process.send_after(self(), :start_betting_phase,
                      new_state.game_data.config.send_card_time * 1000)

    new_state
  end

  # 注意：start_betting_phase函数已在下面重新定义，这里删除重复定义

  # ==================== 消息处理 ====================

  @impl true
  def handle_game_message(state, player, %{"mainId" => 5, "subId" => sub_id} = message) do
    # 🎯 简化：移除额外的暂离定时器逻辑，直接在操作超时时判断暂离
    protocols = TeenPattiGame.protocols()

    case sub_id do
      sub_id when sub_id == protocols.cs_teenpatti_bet ->
        handle_bet_request(state, player, message)

      sub_id when sub_id == protocols.cs_teenpatti_look ->
        handle_look_request(state, player, message)

      sub_id when sub_id == protocols.cs_teenpatti_competition ->
        handle_competition_request(state, player, message)

      sub_id when sub_id == protocols.cs_teenpatti_compconfirm ->
        handle_competition_confirm(state, player, message)

      sub_id when sub_id == protocols.cs_teenpatti_fold ->
        handle_fold_request(state, player, message)

      sub_id when sub_id == protocols.cs_teenpatti_waitrecharge ->
        handle_wait_recharge_request(state, player, message)

      _ ->
        Logger.warning("🃏 [TEEN_PATTI] 未知协议: #{sub_id}")
        state
    end
  end

  # 🎯 简化：暂离回来的消息处理
  # 处理房间相关协议 (MainID=4)
  @impl true
  def handle_game_message(state, player, %{"mainId" => 4, "subId" => 10} = _message) do
    # CS_ROOM_ZANLI_COMBACK_P - 暂离回来协议 (MainID=4, SubID=10)
    Logger.info("🃏 [ZANLI] 玩家#{player.numeric_id} 暂离回来请求 [4][10]")

    # 检查玩家是否在暂离状态
    player_data = Map.get(state.players, player.numeric_id)
    is_zanli = player_data && Map.get(player_data, :zanli_mode, false)

    Logger.info("🃏 [TEEN_PATTI_ZANLI] 玩家当前暂离状态: #{is_zanli}")

    if is_zanli do
      Logger.info("🃏 [TEEN_PATTI_ZANLI] ✅ 玩家退出暂离模式 - 玩家: #{player.numeric_id}")
      exit_zanli_mode(state, player.numeric_id)
    else
      Logger.info("🃏 [TEEN_PATTI_ZANLI] ⚠️ 玩家不在暂离状态，忽略暂离回来请求 - 玩家: #{player.numeric_id}")
      state
    end
  end

  def handle_game_message(state, _player, message) do
    Logger.warning("🃏 [TEEN_PATTI] 无效消息格式: #{inspect(message)}")
    state
  end

  @impl true
  def handle_game_tick(state) do
    # Teen Patti游戏的定时处理逻辑
    # 可以用于处理游戏状态检查、超时处理等
    state
  end

  # 处理添加单个机器人消息 (对照旧项目：机器人逐个加入，模拟真实玩家行为)
  def handle_info(:add_single_robot, state) do
    Logger.info("🤖 [TEEN_PATTI_ROBOT] ========== 处理添加单个机器人消息 ==========")

    # 检查是否还需要添加机器人 (对照旧项目：只有真实玩家存在时才添加机器人)
    current_robots = count_robot_players(state)
    target_robots = state.game_data.config.robot_count
    real_players = count_real_players(state)
    total_players = map_size(state.players)

    Logger.info("🤖 [TEEN_PATTI_ROBOT] 机器人状态检查 - 当前机器人: #{current_robots}, 目标: #{target_robots}, 真实玩家: #{real_players}, 总玩家: #{total_players}")

    new_state = if current_robots < target_robots and real_players > 0 and total_players < state.max_players do
      # 添加一个机器人，模拟真实玩家进入流程
      robot_state = add_single_robot(state)

      # 获取新加入的机器人 (对照旧项目：机器人逐个加入，每个都要广播进入消息)
      # 🎯 修复：获取最新添加的机器人 - 通过比较添加前后的机器人列表
      old_robot_ids = Map.values(state.players)
      |> Enum.filter(fn player -> Map.get(player, :is_robot, false) end)
      |> Enum.map(fn player -> player.numeric_id end)
      |> MapSet.new()

      new_robot_players = Map.values(robot_state.players)
      |> Enum.filter(fn player ->
        Map.get(player, :is_robot, false) and
        not MapSet.member?(old_robot_ids, player.numeric_id)
      end)

      if length(new_robot_players) > 0 do
        new_robot = hd(new_robot_players)
        robot_nickname = get_player_name(new_robot)
        # 🎯 修复：机器人积分存储在user.points中，不是直接的points字段
        robot_points = Map.get(new_robot.user, :points, 0)
        Logger.info("🤖 [TEEN_PATTI_ROBOT] 新机器人加入 - ID: #{new_robot.numeric_id}, 昵称: #{robot_nickname}, 积分: #{robot_points}")

        # 对照旧项目：机器人进入房间的完整流程
        # 🎯 修复：机器人座位已在 add_single_robot 中分配，直接获取
        updated_robot = Map.get(robot_state.players, new_robot.numeric_id, new_robot)
        assigned_seat = Map.get(updated_robot, :seat, 1)
        Logger.info("🤖 [TEEN_PATTI_ROBOT_SEAT] ✅ 机器人座位信息 - 机器人: #{new_robot.numeric_id}, 座位: #{assigned_seat}")

        # 使用已经包含座位信息的状态
        robot_state_with_seat = robot_state

        # 1. 广播机器人进入消息 (像真实玩家一样，使用带座位信息的机器人数据)
        Logger.debug("🤖 [TEEN_PATTI_ROBOT] 广播机器人进入 - ID: #{new_robot.numeric_id}, 座位: #{assigned_seat}")
        broadcast_player_enter(robot_state_with_seat, updated_robot)

        # 2. 机器人进入等待状态，等待满足最低人数后统一准备
        Logger.debug("🤖 [TEEN_PATTI_ROBOT] 机器人进入等待状态 - 机器人: #{new_robot.numeric_id}")
        broadcast_player_status_change(robot_state_with_seat, updated_robot, 0)  # 0=等待状态

        # 3. 检查机器人积分是否充足 (对照旧项目：积分不足的机器人需要重新充值)
        min_money = TeenPattiGame.robot_behavior().min_money
        if robot_points < min_money do
          Logger.info("🤖 [TEEN_PATTI_ROBOT] 机器人积分不足，自动充值 - 机器人: #{new_robot.numeric_id}, 当前: #{robot_points}, 最低: #{min_money}")
          # 机器人自动充值到安全水平
          recharge_amount = min_money * 2 - robot_points
          recharge_robot(robot_state_with_seat, new_robot.numeric_id, recharge_amount)
        end

        # 使用更新后的状态继续处理
        robot_state = robot_state_with_seat
      end

      # 对照旧项目：每次添加机器人后都检查是否可以开始游戏
      robot_state_after_check = check_and_start_game(robot_state)

      # 对照旧项目：继续添加机器人，直到达到目标数量
      updated_robots = count_robot_players(robot_state_after_check)
      if updated_robots < target_robots and map_size(robot_state_after_check.players) < state.max_players do
        # 对照旧项目：机器人间隔2-6秒加入，模拟真实玩家行为
        delay_ms = 2000 + :rand.uniform(4000)  # 2-6秒随机延迟
        Logger.info("🤖 [TEEN_PATTI_ROBOT] 将在 #{delay_ms}ms 后添加下一个机器人 (当前: #{updated_robots}/#{target_robots})")
        Process.send_after(self(), :add_single_robot, delay_ms)
      else
        Logger.info("🤖 [TEEN_PATTI_ROBOT] 机器人添加完成 (#{updated_robots}/#{target_robots})")
      end

      robot_state_after_check
    else
      Logger.info("🤖 [TEEN_PATTI_ROBOT] 无需添加更多机器人 - 当前: #{current_robots}/#{target_robots}, 真实玩家: #{real_players}, 房间容量: #{total_players}/#{state.max_players}")
      state
    end

    {:noreply, new_state}
  end

  # 处理游戏开始消息
  def handle_info(:start_game, state) do
    Logger.info("🃏 [TEEN_PATTI] 处理游戏开始消息")
    new_state = on_game_start(state)
    {:noreply, new_state}
  end

  # 🎯 修改：处理准备定时器到期消息
  def handle_info(:ready_timer_expired, state) do
    Logger.info("⏰ [TEEN_PATTI_READY] ========== 准备定时器到期 ==========")
    Logger.info("⏰ [TEEN_PATTI_READY] 3秒准备时间到，所有玩家自动准备并开始游戏")

    # 清除定时器引用
    updated_state = Map.put(state, :ready_timer_ref, nil)

    # 再次检查人数是否足够
    active_players = get_active_players(updated_state)
    total_players = length(active_players)
    real_players = count_real_players(updated_state)
    min_players = updated_state.game_data.config.min_players

    if total_players >= min_players and real_players >= 1 and updated_state.game_data.state == 0 do
      Logger.info("⏰ [TEEN_PATTI_READY] 定时器到期，满足条件，所有玩家自动准备并开始游戏")

      # 1. 所有玩家自动设置为准备状态
      ready_state = set_all_players_ready(updated_state)

      # 2. 开始游戏
      game_state = on_game_start(ready_state)
      {:noreply, game_state}
    else
      Logger.info("⏰ [TEEN_PATTI_READY] 定时器到期但条件不满足，取消自动开始 - 玩家数: #{total_players}, 真实玩家: #{real_players}")
      {:noreply, updated_state}
    end
  end

  # 处理开始下注阶段消息
  def handle_info(:start_betting_phase, state) do
    Logger.info("🃏 [TEEN_PATTI] 处理开始下注阶段消息")
    new_state = start_betting_phase(state)
    {:noreply, new_state}
  end

  # 🎯 新增：处理比牌动画定时器到期
  def handle_info({:competition_animation_expired, winner_id, requester_id, target_id}, state) do
    Logger.info("🎬 [TEEN_PATTI] 比牌动画定时器到期，继续游戏流程，胜者: #{winner_id}")

    # 清除比牌动画定时器引用
    updated_state = cancel_competition_animation_timer(state)

    # 检查游戏结束条件
    remaining_players = get_active_players(updated_state)
    Logger.info("🃏 [TEEN_PATTI_COMP] 比牌动画结束后剩余玩家数: #{length(remaining_players)}")

    if length(remaining_players) <= 1 do
      Logger.info("🃏 [TEEN_PATTI_COMP] 只剩一个玩家，游戏结束")
      final_state = settle_game(updated_state)
      {:noreply, final_state}
    else
      Logger.info("🃏 [TEEN_PATTI_COMP] 还有多个玩家，继续下注阶段")
      # 胜者继续操作
      final_state = start_player_turn(updated_state, winner_id)
      {:noreply, final_state}
    end
  end

  # 处理操作超时消息
  def handle_info({:operation_timeout, player_id}, state) do
    Logger.info("🃏 [TEEN_PATTI] 处理操作超时: 玩家 #{player_id}")
    new_state = handle_player_operation_timeout(state, player_id)
    {:noreply, new_state}
  end

  # 处理充值超时消息
  def handle_info({:recharge_timeout, player_id}, state) do
    Logger.info("🃏 [TEEN_PATTI] 处理充值超时: 玩家 #{player_id}")
    new_state = handle_recharge_timeout(state, player_id)
    {:noreply, new_state}
  end

  # 处理设置机器人不能弃牌
  def handle_info({:set_robot_no_fold, robot_id, no_fold_value}, state) do
    Logger.info("🃏 [TEEN_PATTI] 设置机器人#{robot_id}的no_fold=#{no_fold_value}")
    new_state = set_robot_no_fold(state, robot_id, no_fold_value)
    {:noreply, new_state}
  end

  # 🎯 修复：处理机器人动作消息
  def handle_info({:robot_action, robot_id}, state) do
    Logger.info("🤖 [TEEN_PATTI] 处理机器人动作: 机器人 #{robot_id}")

    # 检查是否轮到该机器人操作
    if state.game_data.current_player == robot_id do
      Logger.info("🤖 [TEEN_PATTI] 执行机器人动作 - 机器人: #{robot_id}")
      new_state = execute_robot_action(state, robot_id)
      {:noreply, new_state}
    else
      Logger.warning("🤖 [TEEN_PATTI] 机器人动作时机不对 - 机器人: #{robot_id}, 当前操作玩家: #{state.game_data.current_player}")
      {:noreply, state}
    end
  end

  # 处理机器人看牌消息 (对照旧项目日志: 机器人自动看牌)
  def handle_info({:robot_look_card, robot_id}, state) do
    Logger.info("🤖 [TEEN_PATTI] 处理机器人看牌: 机器人 #{robot_id}")
    new_state = handle_robot_look_card(state, robot_id)
    {:noreply, new_state}
  end

  # 处理机器人自动确认比牌消息
  def handle_info({:robot_auto_confirm_competition, robot_id}, state) do
    Logger.info("🤖 [TEEN_PATTI] 处理机器人自动确认比牌: 机器人 #{robot_id}")
    new_state = handle_robot_auto_confirm_competition(state, robot_id)
    {:noreply, new_state}
  end

  # 🎯 修复：处理机器人下注消息（使用fill参数）
  def handle_info({:robot_bet, robot_id, fill}, state) do
    Logger.info("🤖 [TEEN_PATTI] 处理机器人下注: 机器人 #{robot_id}, fill: #{fill} (#{if fill == 1, do: "跟注", else: "加注"})")
    new_state = handle_robot_bet_with_fill(state, robot_id, fill)
    {:noreply, new_state}
  end

  # 处理机器人比牌消息
  def handle_info({:robot_competition, robot_id, target_id}, state) do
    Logger.info("🤖 [TEEN_PATTI] 处理机器人比牌: 机器人 #{robot_id}, 目标: #{target_id}")
    new_state = handle_robot_competition(state, robot_id, target_id)
    {:noreply, new_state}
  end

  # 处理机器人弃牌消息
  def handle_info({:robot_fold, robot_id}, state) do
    Logger.info("🤖 [TEEN_PATTI] 处理机器人弃牌: 机器人 #{robot_id}")
    new_state = handle_fold_action(state, robot_id)
    {:noreply, new_state}
  end

  # 处理开始下一轮消息
  def handle_info(:start_next_round, state) do
    Logger.info("🃏 [TEEN_PATTI] 处理开始下一轮消息")
    new_state = start_next_round(state)
    {:noreply, new_state}
  end

  # 处理关闭房间消息（暂离玩家退出后房间解散）
  def handle_info(:close_room, state) do
    Logger.info("🃏 [TEEN_PATTI_ZANLI] 收到关闭房间消息 - 房间: #{state.id}")

    # 通知房间管理器关闭房间
    try do
      Cypridina.RoomSystem.RoomManager.close_room(state.id)
      Logger.info("🃏 [TEEN_PATTI_ZANLI] ✅ 房间关闭请求已发送")
    rescue
      error ->
        Logger.error("🃏 [TEEN_PATTI_ZANLI] ❌ 房间关闭失败: #{inspect(error)}")
    end

    {:stop, :normal, state}
  end

  # 处理延迟添加机器人消息
  def handle_info(:add_robots_delayed, state) do
    Logger.info("🤖 [TEEN_PATTI] 处理延迟添加机器人消息")
    new_state = add_robots_if_needed(state)
    {:noreply, new_state}
  end

  # 处理添加第二个机器人消息
  def handle_info(:add_second_robot, state) do
    Logger.info("🤖 [ROBOT_JOIN] 添加第二个机器人")
    new_state = if map_size(state.players) < state.max_players do
      add_single_robot(state)
    else
      state
    end
    {:noreply, new_state}
  end

  # 处理延迟机器人进房消息
  def handle_info(:add_delayed_robot, state) do
    Logger.info("🤖 [ROBOT_JOIN] 延迟机器人进房")
    new_state = if map_size(state.players) < state.max_players do
      add_single_robot(state)
    else
      state
    end
    {:noreply, new_state}
  end

  # 处理机器人决策执行消息
  def handle_info({:execute_robot_decision, robot_id, decision}, state) do
    Logger.info("🤖 [TEEN_PATTI_ROBOT] 执行机器人决策 - 机器人: #{robot_id}, 决策: #{inspect(decision)}")

    # 检查是否轮到该机器人操作
    if state.game_data.current_player == robot_id do
      new_state = case decision do
        {:bet, fill} ->
          # 机器人下注
          handle_robot_bet(state, robot_id, fill)

        {:fold, _} ->
          # 机器人弃牌
          handle_robot_fold(state, robot_id)

        {:look, _} ->
          # 机器人看牌
          handle_robot_look(state, robot_id)

        {:competition, target_id} ->
          # 机器人比牌
          handle_robot_competition(state, robot_id, target_id)

        _ ->
          Logger.warning("🤖 [TEEN_PATTI_ROBOT] 未知的机器人决策: #{inspect(decision)}")
          state
      end

      {:noreply, new_state}
    else
      Logger.warning("🤖 [TEEN_PATTI_ROBOT] 机器人决策时机不对 - 机器人: #{robot_id}, 当前操作玩家: #{state.game_data.current_player}")
      {:noreply, state}
    end
  end

  # 处理玩家弃牌消息
  def handle_info({:player_fold, player_id}, state) do
    Logger.info("🃏 [TEEN_PATTI] 处理玩家弃牌消息 - 玩家: #{player_id}")

    # 检查是否轮到该玩家操作
    if state.game_data.current_player == player_id do
      new_state = handle_fold_action(state, player_id)
      {:noreply, new_state}
    else
      Logger.warning("🃏 [TEEN_PATTI] 弃牌时机不对 - 玩家: #{player_id}, 当前操作玩家: #{state.game_data.current_player}")
      {:noreply, state}
    end
  end

  # 处理玩家下注消息
  def handle_info({:player_bet, player_id, fill}, state) do
    Logger.info("🃏 [TEEN_PATTI] 处理玩家下注消息 - 玩家: #{player_id}, 倍数: #{fill}")

    # 检查是否轮到该玩家操作
    if state.game_data.current_player == player_id do
      # 计算下注金额
      bet_amount = calculate_bet_amount(state, player_id, fill)
      new_state = execute_bet_action(state, player_id, bet_amount, fill)
      {:noreply, new_state}
    else
      Logger.warning("🃏 [TEEN_PATTI] 下注时机不对 - 玩家: #{player_id}, 当前操作玩家: #{state.game_data.current_player}")
      {:noreply, state}
    end
  end

  # 🎯 新增：处理机器人积分不足退出消息 (对照旧项目：机器人积分不足时自动退出并重新充值)
  def handle_info(:check_robot_money, state) do
    Logger.info("🤖 [TEEN_PATTI_ROBOT] ========== 检查机器人积分状态 ==========")

    min_money = TeenPattiGame.robot_behavior().min_money
    robot_players = Map.values(state.players)
    |> Enum.filter(fn player -> Map.get(player, :is_robot, false) end)

    # 检查每个机器人的积分状态
    {robots_to_remove, robots_to_recharge} =
      Enum.reduce(robot_players, {[], []}, fn robot, {remove_list, recharge_list} ->
        # 🎯 修复：机器人积分存储在user.points中
        current_money = Map.get(robot.user, :points, 0)

        cond do
          current_money <= 0 ->
            # 对照旧项目：积分为0的机器人直接退出
            Logger.info("🤖 [TEEN_PATTI_ROBOT] 机器人积分为0，准备退出 - 机器人: #{robot.numeric_id}")
            {[robot.numeric_id | remove_list], recharge_list}

          current_money < min_money ->
            # 对照旧项目：积分不足的机器人自动充值
            Logger.info("🤖 [TEEN_PATTI_ROBOT] 机器人积分不足，准备充值 - 机器人: #{robot.numeric_id}, 当前: #{current_money}, 最低: #{min_money}")
            {remove_list, [robot.numeric_id | recharge_list]}

          true ->
            {remove_list, recharge_list}
        end
      end)

    # 处理需要退出的机器人
    new_state = Enum.reduce(robots_to_remove, state, fn robot_id, acc_state ->
      handle_robot_exit(acc_state, robot_id, :low_money)
    end)

    # 处理需要充值的机器人
    final_state = Enum.reduce(robots_to_recharge, new_state, fn robot_id, acc_state ->
      handle_robot_recharge(acc_state, robot_id)
    end)

    # 对照旧项目：定期检查机器人积分状态 (每30秒检查一次)
    Process.send_after(self(), :check_robot_money, 30_000)

    {:noreply, final_state}
  end

  # 🎯 新增：处理机器人退出 (对照旧项目：机器人退出时要广播退出消息)
  defp handle_robot_exit(state, robot_id, reason) do
    robot = Map.get(state.players, robot_id)

    if robot && Map.get(robot, :is_robot, false) do
      robot_nickname = get_player_name(robot)
      Logger.info("🤖 [TEEN_PATTI_ROBOT] 机器人退出房间 - ID: #{robot_id}, 昵称: #{robot_nickname}, 原因: #{reason}")

      # 对照旧项目：广播机器人退出消息 (使用玩家退出协议)
      # 协议：mainId=4, subId=18 (SC_ROOM_DEL_PLAYER_P)
      # 🎯 修复：机器人积分存储在user.points中
      robot_points = Map.get(robot.user, :points, 0)
      exit_message = %{
        "mainId" => 4,
        "subId" => 18,
        "data" => %{
          "userid" => robot_id,
          "money" => robot_points,
          "bchange" => 0,  # 机器人退出不扣费
          "totalmoney" => robot_points
        }
      }

      Logger.info("🤖 [TEEN_PATTI_ROBOT] 广播机器人退出协议 - 机器人: #{robot_id}")
      broadcast_to_room(state, exit_message)

      # 从房间中移除机器人
      new_players = Map.delete(state.players, robot_id)
      updated_state = %{state | players: new_players}

      # 对照旧项目：机器人退出后，如果机器人数量不足，延迟添加新机器人
      current_robots = count_robot_players(updated_state)
      target_robots = state.game_data.config.robot_count
      real_players = count_real_players(updated_state)

      if current_robots < target_robots and real_players > 0 do
        # 延迟5-10秒后添加新机器人，模拟真实玩家行为
        delay_ms = 5000 + :rand.uniform(5000)
        Logger.info("🤖 [TEEN_PATTI_ROBOT] 机器人退出后将在 #{delay_ms}ms 后添加新机器人")
        Process.send_after(self(), :add_single_robot, delay_ms)
      end

      updated_state
    else
      Logger.warning("🤖 [TEEN_PATTI_ROBOT] 尝试退出无效的机器人: #{robot_id}")
      state
    end
  end

  # 🎯 新增：处理机器人充值 (对照旧项目：机器人自动充值到安全水平)
  defp handle_robot_recharge(state, robot_id) do
    robot = Map.get(state.players, robot_id)

    if robot && Map.get(robot, :is_robot, false) do
      min_money = TeenPattiGame.robot_behavior().min_money
      safe_money = min_money * 3  # 充值到最低要求的3倍，确保安全
      # 🎯 修复：机器人积分存储在user.points中
      current_points = Map.get(robot.user, :points, 0)
      recharge_amount = safe_money - current_points

      Logger.info("🤖 [TEEN_PATTI_ROBOT] 机器人自动充值 - ID: #{robot_id}, 充值金额: #{recharge_amount}, 充值后: #{safe_money}")

      # 更新机器人积分 - 需要更新user.points字段
      updated_user = Map.put(robot.user, :points, safe_money)
      updated_robot = %{robot | user: updated_user}
      new_players = Map.put(state.players, robot_id, updated_robot)

      %{state | players: new_players}
    else
      Logger.warning("🤖 [TEEN_PATTI_ROBOT] 尝试为无效机器人充值: #{robot_id}")
      state
    end
  end



  # 处理其他未知消息
  def handle_info(message, state) do
    Logger.debug("🃏 [TEEN_PATTI] 未处理的消息: #{inspect(message)}")
    {:noreply, state}
  end

  # ==================== 辅助函数 ====================

  # 🎯 修复：获取参与当前小局的活跃玩家
  # 只有在游戏开始前就在房间的玩家才能参与当前小局
  # 游戏中途加入的机器人需要等待下一局
  defp get_active_players(state) do
    state.players
    |> Map.values()
    |> Enum.filter(fn player ->
      # 检查玩家是否已弃牌
      not_folded = Map.get(state.game_data.player_states, player.numeric_id, :none) != :fold

      # 🎯 关键修复：检查玩家的游戏状态
      # 只有game_state为:playing的玩家才能参与当前小局
      # waiting_next_round状态的玩家需要等待下一局
      is_participating = case Map.get(player, :game_state, :waiting) do
        :playing -> true
        :waiting -> state.game_data.state == 0  # 只有在等待状态时，waiting玩家才能参与
        :waiting_next_round -> false  # 等待下一局的玩家不参与当前小局
        _ -> false
      end

      not_folded and is_participating
    end)
  end

  @doc """
  构建发牌上下文
  """
  defp build_dealing_context(state, active_players) do
    # 获取库存状态（临时实现，后续需要从实际库存系统获取）
    base_bet = state.game_data.config.base_bet
    stock_limits = TeenPattiConfig.get_stock_limits(base_bet)
    current_stock = get_current_stock(state)  # 临时实现

    %{
      # 库存相关
      current_stock: current_stock,
      stock_limits: stock_limits,
      stock_status: determine_stock_status(current_stock, stock_limits),

      # 玩家相关
      player_cards: Map.get(state.game_data, :player_cards, %{}),
      active_players: length(active_players),
      active_player_ids: Enum.map(active_players, & &1.numeric_id),

      # 游戏状态
      round: Map.get(state.game_data, :round, 1),
      turn_count: Map.get(state.game_data, :turn_count, 0),
      pot_total: Map.get(state.game_data, :pot_total, 0),

      # 特殊状态检查
      player_money_low: check_player_money_low(active_players),
      has_new_player: check_has_new_player(active_players),
      has_waiting_recharge_player: check_has_waiting_recharge_player(active_players)
    }
  end

  @doc """
  获取当前库存（临时实现）
  """
  defp get_current_stock(_state) do
    # 临时实现：返回0，后续需要从实际库存系统获取
    0
  end

  @doc """
  确定库存状态
  """
  defp determine_stock_status(current_stock, stock_limits) do
    cond do
      current_stock > stock_limits.upper -> :high
      current_stock < stock_limits.lower -> :low
      abs(current_stock) <= stock_limits.safe_zone -> :safe
      true -> :normal
    end
  end

  @doc """
  检查是否有玩家金币较低
  """
  defp check_player_money_low(active_players) do
    Enum.any?(active_players, fn player ->
      if not Map.get(player, :is_robot, false) do
        player_points = Map.get(player, :points, 0)
        player_points < 1000  # 临时阈值
      else
        false
      end
    end)
  end

  @doc """
  检查是否有新玩家
  """
  defp check_has_new_player(active_players) do
    Enum.any?(active_players, fn player ->
      if not Map.get(player, :is_robot, false) do
        # 临时实现：检查玩家等级或游戏次数
        Map.get(player, :level, 1) == 1
      else
        false
      end
    end)
  end

  @doc """
  检查是否有待充值玩家
  """
  defp check_has_waiting_recharge_player(active_players) do
    Enum.any?(active_players, fn player ->
      if not Map.get(player, :is_robot, false) do
        # 临时实现：检查玩家金币是否超过80
        player_points = Map.get(player, :points, 0)
        player_points > 80 and player_points < 200  # 临时判断逻辑
      else
        false
      end
    end)
  end

  defp choose_first_player(players) do
    # 简单实现：选择第一个玩家
    # 可以扩展为随机选择或按庄家顺序
    hd(players)
  end

  defp is_robot_player?(state, player_id) do
    # 🎯 修复：使用is_robot字段判断机器人
    case Map.get(state.players, player_id) do
      nil -> false
      player -> Map.get(player, :is_robot, false)
    end
  end

  # 可能添加机器人 - 使用新的机器人系统
  defp maybe_add_robots(state) do
    if state.game_data.config.enable_robots do
      add_robots_if_needed(state)
    else
      state
    end
  end

  @doc """
  根据需要添加机器人
  """
  defp add_robots_if_needed(state) do
    real_players = count_real_players(state)
    current_robots = count_robot_players(state)
    total_players = map_size(state.players)

    # 只有当有真实玩家且机器人数量不足时才添加
    if real_players > 0 and current_robots < 1 and total_players < state.max_players do
      # 🎯 优化：模拟真人进房行为，随机决定进房方式
      case :rand.uniform(3) do
        1 ->
          # 单个机器人进房
          Logger.info("🤖 [ROBOT_JOIN] 单个机器人进房")
          add_single_robot(state)
        2 ->
          # 两个机器人几乎同时进房
          Logger.info("🤖 [ROBOT_JOIN] 两个机器人同时进房")
          state_with_first = add_single_robot(state)
          if map_size(state_with_first.players) < state.max_players do
            # 延迟1-3秒后添加第二个机器人
            Process.send_after(self(), :add_second_robot, Enum.random(1000..3000))
          end
          state_with_first
        3 ->
          # 延迟进房（模拟真人思考时间）
          Logger.info("🤖 [ROBOT_JOIN] 机器人延迟进房")
          delay = Enum.random(2000..8000)  # 2-8秒延迟
          Process.send_after(self(), :add_delayed_robot, delay)
          state
      end
    else
      state
    end
  end

  @doc """
  添加单个机器人
  """
  defp add_single_robot(state) do
    robot_id = generate_robot_id()
    robot_data = TeenPattiRobot.create_robot(robot_id)

    # 使用 PlayerDataBuilder 创建正确的 PlayerData 结构
    robot_player = PlayerDataBuilder.create_robot_player_data(robot_data)

    # 先添加机器人到房间玩家列表（不带座位）
    temp_players = Map.put(state.players, robot_id, robot_player)
    temp_state = %{state | players: temp_players}

    # 为机器人分配座位
    assigned_seat = assign_player_seat(temp_state, robot_id)

    # 更新机器人数据，添加座位信息
    robot_player_with_seat = Map.put(robot_player, :seat, assigned_seat)
    updated_players = Map.put(state.players, robot_id, robot_player_with_seat)

    # 同时添加机器人到游戏数据的机器人列表中
    updated_robots = Map.put(state.game_data.robots, robot_id, robot_data)
    updated_game_data = %{state.game_data | robots: updated_robots}

    updated_state = %{state |
      players: updated_players,
      game_data: updated_game_data
    }

    Logger.info("🤖 [TEEN_PATTI_ROBOT] 添加机器人成功 - 机器人: #{robot_id}, 座位: #{assigned_seat}")

    updated_state
  end

  @doc """
  生成机器人ID
  """
  defp generate_robot_id do
    # 🎯 修复：使用正数ID，通过is_robot字段判断机器人
    :rand.uniform(999999) + 100000
  end

  @doc """
  获取玩家幸运值
  """
  defp get_player_luck_value(state, player_id) do
    case Map.get(state.players, player_id) do
      nil -> 500  # 默认幸运值
      player ->
        if Map.get(player, :is_robot, false) do
          500  # 机器人默认幸运值
        else
          # 🎯 修复：直接从玩家的 user 数据中获取幸运值，不重新创建
          TeenPattiPlayer.get_player_field(player, :luck_value, 500)
        end
    end
  end

  @doc """
  获取玩家类型名称（用于日志显示）
  """
  defp get_player_type_name(state, player_id) do
    case Map.get(state.players, player_id) do
      nil -> "未知"
      player ->
        if Map.get(player, :is_robot, false) do
          "机器人"
        else
          # 🎯 简化：直接使用玩家数据计算类型（total_coins已在加入时设置）
          player_type = TeenPattiPlayer.calculate_player_type_dynamically(player)

          case player_type do
            :free -> "免费玩家"
            :new -> "新玩家"
            :new_never_80 -> "新手玩家"
            :waiting_recharge -> "待充值玩家"
            :returning -> "回头玩家"
            :charged_1 -> "1充玩家"
            :charged_2 -> "2充玩家"
            :charged_3 -> "3充玩家"
            :charged_4_plus -> "大R玩家"
            _ -> "普通玩家"
          end
        end
    end
  end

  @doc """
  机器人充值
  """
  defp recharge_robot(state, robot_id, amount) do
    case Map.get(state.players, robot_id) do
      nil ->
        Logger.warning("🤖 [TEEN_PATTI_ROBOT] 机器人不存在: #{robot_id}")
        state

      robot ->
        current_points = Map.get(robot, :points, 0)
        updated_robot = %{robot | points: current_points + amount}
        updated_players = Map.put(state.players, robot_id, updated_robot)

        Logger.info("🤖 [TEEN_PATTI_ROBOT] 机器人充值成功 - 机器人: #{robot_id}, 充值: #{amount}, 新余额: #{current_points + amount}")

        %{state | players: updated_players}
    end
  end

  @doc """
  执行机器人动作
  """
  defp execute_robot_action(state, robot_id) do
    case Map.get(state.players, robot_id) do
      nil ->
        Logger.warning("🤖 [TEEN_PATTI_ROBOT] 机器人不存在: #{robot_id}")
        state

      robot ->
        # 获取机器人手牌
        robot_cards = Map.get(state.game_data.player_cards, robot_id, [])

        # 构建游戏上下文
        game_context = build_robot_game_context(state, robot_id)

        # 让机器人做决策
        decision = TeenPattiRobot.make_decision(robot, robot_cards, game_context)

        # 计算操作延迟
        delay = TeenPattiRobot.calculate_action_delay(robot, elem(decision, 0))

        # 延迟执行决策
        Process.send_after(self(), {:execute_robot_decision, robot_id, decision}, delay)

        state
    end
  end

  @doc """
  安排机器人动作
  """
  defp schedule_robot_action(state, robot_id) do
    case Map.get(state.players, robot_id) do
      nil ->
        Logger.warning("🤖 [TEEN_PATTI_ROBOT] 机器人不存在: #{robot_id}")
        state

      robot ->
        # 计算基础延迟
        base_delay = TeenPattiRobot.calculate_action_delay(robot, :think)

        # 安排机器人动作
        Process.send_after(self(), {:robot_action, robot_id}, base_delay)

        Logger.info("🤖 [TEEN_PATTI_ROBOT] 安排机器人动作 - 机器人: #{robot_id}, 延迟: #{base_delay}ms")

        state
    end
  end

  @doc """
  构建机器人游戏上下文
  """
  defp build_robot_game_context(state, robot_id) do
    %{
      current_bet: state.game_data.current_bet,
      pot_total: state.game_data.pot_total,
      turn_count: state.game_data.turn_count,
      active_players: length(get_active_players(state)),
      active_player_ids: Enum.map(get_active_players(state), & &1.numeric_id),
      is_seen: Map.get(state.game_data.player_seen_status, robot_id, false),
      player_money_low: check_player_money_low(get_active_players(state)),
      stock_status: :normal  # 临时实现
    }
  end

  @doc """
  处理机器人下注
  """
  defp handle_robot_bet(state, robot_id, fill) do
    # 调用现有的下注处理逻辑
    bet_amount = state.game_data.current_bet * fill

    Logger.info("🤖 [TEEN_PATTI_ROBOT] 机器人下注 - 机器人: #{robot_id}, 倍数: #{fill}, 金额: #{bet_amount}")

    # 模拟机器人下注消息
    send(self(), {:player_bet, robot_id, fill})

    state
  end

  @doc """
  处理机器人弃牌
  """
  defp handle_robot_fold(state, robot_id) do
    Logger.info("🤖 [TEEN_PATTI_ROBOT] 机器人弃牌 - 机器人: #{robot_id}")

    # 模拟机器人弃牌消息
    send(self(), {:player_fold, robot_id})

    state
  end

  @doc """
  处理机器人看牌
  """
  defp handle_robot_look(state, robot_id) do
    Logger.info("🤖 [TEEN_PATTI_ROBOT] 机器人看牌 - 机器人: #{robot_id}")

    # 更新机器人看牌状态
    game_data = %{state.game_data |
      player_seen_status: Map.put(state.game_data.player_seen_status, robot_id, true)
    }

    updated_state = %{state | game_data: game_data}

    # 继续机器人的下注决策
    Process.send_after(self(), {:robot_action, robot_id}, 1000)

    updated_state
  end

  @doc """
  处理机器人比牌
  """
  defp handle_robot_competition(state, robot_id, target_id) do
    if target_id && Map.has_key?(state.players, target_id) do
      Logger.info("🤖 [TEEN_PATTI_ROBOT] 机器人比牌 - 机器人: #{robot_id}, 目标: #{target_id}")

      # 模拟机器人比牌消息
      send(self(), {:player_competition, robot_id, target_id})

      state
    else
      Logger.warning("🤖 [TEEN_PATTI_ROBOT] 机器人比牌目标无效 - 机器人: #{robot_id}, 目标: #{target_id}")

      # 改为下注
      handle_robot_bet(state, robot_id, 1)
    end
  end

  # 检查并开始游戏 - 参考pot_blind实现，满足最低人数后3秒自动准备
  defp check_and_start_game(state) do
    active_players = get_active_players(state)
    total_players = length(active_players)
    real_players = count_real_players(state)
    robot_players = count_robot_players(state)
    min_players = state.game_data.config.min_players

    Logger.info("🃏 [GAME_START_CHECK] 总玩家:#{total_players}(真人:#{real_players} 机器人:#{robot_players}) 最低要求:#{min_players} 状态:#{state.game_data.state}")

    cond do
      # 🎯 修复：如果游戏不在等待状态，不处理（避免游戏进行中的玩家加入触发倒计时）
      state.game_data.state != 0 ->
        Logger.info("🃏 [TEEN_PATTI_GAME_START] 游戏不在等待状态: #{state.game_data.state}，跳过开始检查")
        state

      # 如果人数不足，取消现有的准备定时器
      total_players < min_players or real_players < 1 ->
        Logger.info("🃏 [TEEN_PATTI_GAME_START] 人数不足，取消准备定时器")
        cancel_ready_timer(state)

      # 如果已经有准备定时器在运行，不重复启动
      Map.get(state, :ready_timer_ref) != nil ->
        Logger.info("⏰ [TEEN_PATTI_GAME_START] 准备定时器已在运行")
        state

      # 满足开始条件，启动3秒延迟定时器
      true ->
        Logger.info("🎯 [TEEN_PATTI_GAME_START] 满足开始条件，启动3秒准备定时器")
        start_ready_timer(state)
    end
  end

  # 启动准备定时器 (3秒延迟)
  defp start_ready_timer(state) do
    # 取消之前的定时器（如果存在）
    cancel_ready_timer(state)

    # 启动3秒延迟定时器
    timer_ref = Process.send_after(self(), :ready_timer_expired, 3000)

    # 保存定时器引用到状态中
    updated_state = Map.put(state, :ready_timer_ref, timer_ref)

    Logger.info("⏰ [TEEN_PATTI_READY] 启动准备定时器: 3秒后所有玩家自动准备并开始游戏")

    # 广播房间状态，包含3秒倒计时
    broadcast_room_status_with_ready_countdown(updated_state, 3)
  end

  # 取消准备定时器
  defp cancel_ready_timer(state) do
    case Map.get(state, :ready_timer_ref) do
      nil ->
        state

      timer_ref ->
        Process.cancel_timer(timer_ref)
        updated_state = Map.put(state, :ready_timer_ref, nil)
        Logger.info("🚫 [TEEN_PATTI_READY] 取消准备定时器")

        # 广播房间状态，取消倒计时
        broadcast_room_status_with_ready_countdown(updated_state, 0)
    end
  end

  # 设置所有玩家为准备状态
  defp set_all_players_ready(state) do
    Logger.info("🃏 [TEEN_PATTI_READY] 设置所有玩家为准备状态")

    # 广播所有玩家状态变更为准备状态
    Enum.each(state.players, fn {_player_id, player} ->
      broadcast_player_status_change(state, player, 1)  # 1=准备状态
    end)

    state
  end

  # 广播房间状态（包含准备倒计时）
  defp broadcast_room_status_with_ready_countdown(state, countdown_seconds) do
    message = %{
      "mainId" => 4,
      "subId" => 5,
      "data" => %{
        "roomstate" => 0,  # 等待状态
        "autoreadytime" => countdown_seconds
      }
    }

    Logger.info("🃏 [ROOM_STATE] 准备倒计时:#{countdown_seconds}秒 [4][5]")

    broadcast_to_room(state, message)
    state
  end

  # 🎯 新增：广播机器人进入消息 用不到准备删除
  defp broadcast_robot_entries(state) do
    # 获取所有机器人玩家
    robot_players = Map.values(state.players)
    |> Enum.filter(fn player -> Map.get(player, :is_robot, false) end)

    # 为每个机器人广播进入消息（像真人一样）
    Enum.each(robot_players, fn robot ->
      robot_nickname = get_player_name(robot)
      Logger.info("🤖 [TEEN_PATTI] 广播机器人进入消息 - 机器人: #{robot.numeric_id}, 昵称: #{robot_nickname}")

      # 🎯 关键：机器人进入房间后要像真人一样广播进入消息
      # 这样前端就能立即看到机器人，不用等定时器
      broadcast_player_enter(state, robot)

      # 🎯 修改：机器人状态变更为准备状态（与真实玩家一致）
      Logger.info("🤖 [TEEN_PATTI_AUTO_READY] 机器人进入游戏后自动设置为准备状态 - 机器人: #{robot.numeric_id}")
      broadcast_player_status_change(state, robot, 1)  # 1=准备状态
    end)

    Logger.info("🤖 [TEEN_PATTI] 机器人进入广播完成 - 总机器人数: #{length(robot_players)}")
    state
  end


  @doc """
  重写父类的 broadcast_player_enter 函数，添加 Teen Patti 前端需要的额外字段
  注意：只有在 defoverridable 列表中的函数才能被重写，所以直接在这里处理数据获取
  """
  defp broadcast_player_enter(state, player) do
    Logger.debug("🚨 [BROADCAST_PLAYER_ENTER] 玩家 #{player.numeric_id} 进入房间")

    # 🎯 关键修复：检查玩家是否已有座位，如果没有则预计算一个座位用于广播
    player_seat = case Map.get(player, :seat) do
      seat when is_integer(seat) and seat > 0 ->
        # 玩家已有座位
        Logger.debug("🚨 [BROADCAST_PLAYER_ENTER_DEBUG] 玩家已有座位: #{seat}")
        seat
      _ ->
        # 玩家没有座位，预计算一个座位用于广播（不修改 state）
        Logger.debug("🚨 [BROADCAST_PLAYER_ENTER_DEBUG] 玩家没有座位，预计算座位用于广播")
        # 获取已占用的座位
        occupied_seats = get_occupied_seats(state)
        # 找到第一个可用座位
        available_seat = find_next_available_seat(occupied_seats, state.max_players)
        Logger.debug("🚨 [BROADCAST_PLAYER_ENTER_DEBUG] 预计算的座位: #{available_seat}")
        available_seat
    end

    player_money = get_player_points(state, player)

    Logger.debug("🚨 [BROADCAST_PLAYER_ENTER] 玩家座位信息 - 玩家: #{player.numeric_id}, 座位: #{player_seat}")

    # 获取玩家昵称 - 区分机器人和真实玩家
    player_nickname = if Map.get(player, :is_robot, false) do
      # 🎯 修复：机器人使用真实的印度人名，不暴露机器人身份
      Map.get(player, :nickname, "Player#{player.numeric_id}")
    else
      # 🎯 修复：真实玩家从 user 对象中获取用户名作为昵称
      case player do
        %{user: %{username: username}} when is_binary(username) and username != "" ->
          username
        %{user: %{nickname: nickname}} when is_binary(nickname) and nickname != "" ->
          nickname
        %{nickname: nickname} when is_binary(nickname) and nickname != "" ->
          nickname
        %{username: username} when is_binary(username) and username != "" ->
          username
        _ ->
          "玩家#{player.numeric_id}"
      end
    end

    # 获取玩家头像信息 - 区分机器人和真实玩家
    {headid, wxheadurl} = if Map.get(player, :is_robot, false) do
      # 机器人使用预设的头像ID
      robot_headid = Map.get(player, :headid, 1)
      {robot_headid, ""}
    else
      # 真实玩家从 user 对象中获取头像信息
      user_headid = case player do
        %{user: %{avatar_id: id}} when is_integer(id) -> id
        %{headid: id} when is_integer(id) -> id
        _ -> 1
      end

      user_wxheadurl = case player do
        %{user: %{avatar_url: url}} when is_binary(url) and url != "" -> url
        _ -> ""
      end

      {user_headid, user_wxheadurl}
    end

    # 获取玩家游戏状态 - 直接处理
    is_gaming = if state.game_data.state == 0, do: 0, else: 1

    # 构建玩家进入消息，包含前端需要的所有字段
    message = %{
      "mainId" => 4,
      "subId" => 12,
      "data" => %{
        "playerid" => player.numeric_id,
        "seat" => player_seat,  # 🎯 修复：使用正确获取的座位号
        "money" => player_money,  # 使用父类函数获取积分
        "nickname" => player_nickname,  # 直接获取的昵称
        "headid" => headid,  # 🎯 修复：前端需要的头像ID
        "wxheadurl" => wxheadurl,  # 🎯 修复：微信头像URL
        "level" => 1,  # 固定等级
        # Teen Patti 前端需要的额外字段
        "iscustomhead" => 0,
        "playerstate" => 0,  # 初始状态：等待
        "isgaming" => is_gaming,
        "gameState" => 1  # 固定为1，表示游戏状态
      }
    }

    Logger.debug("🃏 [TEEN_PATTI_PROTOCOL] 广播玩家进入: #{player.numeric_id}, 座位: #{player_seat}")
    broadcast_to_room(state, message)
    state
  end

  # 广播玩家状态变更 (对照旧项目日志: [4][4]玩家状态)
  defp broadcast_player_status_change(state, player, player_state) do
    # 🎯 关键修复：直接从玩家数据中获取固定座位号
    seat_number = Map.get(player, :seat, 1)

    message = %{
      "mainId" => 4,
      "subId" => 4,
      "data" => %{
        "playerid" => player.numeric_id,
        "playerstate" => player_state,  # 0=等待, 1=准备, 2=游戏中
        "seat" => seat_number  # 🎯 关键修复：使用玩家的固定座位号
      }
    }

    Logger.info("🃏 [PLAYER_STATE] 玩家#{player.numeric_id} 状态:#{player_state} [4][4]")

    broadcast_to_room(state, message)
    state
  end

  # 广播房间状态 (对照旧项目日志: [4][5]房间状态)
  defp broadcast_room_status(state) do
    room_state = case state.game_data.state do
      0 -> 0      # 🎯 修复：等待开始
      1 -> 1      # 🎯 修复：游戏中
      _ -> 0
    end

    message = %{
      "mainId" => 4,
      "subId" => 5,
      "data" => %{
        "roomstate" => room_state,
        "autoreadytime" => if(room_state == 0, do: 3, else: 0)  # 自动准备时间
      }
    }

    Logger.debug("🃏 [TEEN_PATTI_PROTOCOL] 广播房间状态: #{room_state}")

    broadcast_to_room(state, message)
    state
  end



  # 广播房间状态变更 (指定状态值)
  defp broadcast_room_status_change(state, room_state) do
    message = %{
      "mainId" => 4,
      "subId" => 5,
      "data" => %{
        "roomstate" => room_state,
        "autoreadytime" => if(room_state == 0, do: 3, else: 0)
      }
    }

    Logger.debug("🃏 [TEEN_PATTI_PROTOCOL] 房间状态变更为: #{room_state}")

    broadcast_to_room(state, message)
    state
  end

  # 🎯 新增：广播房间状态变更（不包含自动准备时间）
  # 用于中途加入的玩家，避免发送错误的自动准备倒计时
  defp broadcast_room_status_change_without_auto_ready(state, room_state) do
    message = %{
      "mainId" => 4,
      "subId" => 5,
      "data" => %{
        "roomstate" => room_state,
        "autoreadytime" => 0  # 🎯 关键：不发送自动准备时间
      }
    }

    Logger.info("🃏 [ROOM_STATE] 状态变更:#{room_state} (无自动准备) [4][5]")

    broadcast_to_room(state, message)
    state
  end

  # 广播所有玩家状态变更
  defp broadcast_all_players_status_change(state, player_state) do
    state.players
    |> Map.values()
    |> Enum.each(fn player ->
      # 🎯 关键修复：直接从玩家数据中获取固定座位号
      seat_number = Map.get(player, :seat, 1)

      message = %{
        "mainId" => 4,
        "subId" => 4,
        "data" => %{
          "playerid" => player.numeric_id,
          "playerstate" => player_state,
          "seat" => seat_number  # 🎯 关键修复：使用玩家的固定座位号
        }
      }

      Logger.info("🃏 [TEEN_PATTI_PROTOCOL_SEND] 玩家状态变更: #{player.numeric_id} -> #{player_state}")
      broadcast_to_room(state, message)
    end)

    state
  end

  # 注意：get_player_seat/2 函数不在 defoverridable 列表中，无法被重写
  # 所以我们直接在需要的地方使用 get_player_seat_by_id/2 函数

  # 辅助函数：获取玩家状态
  defp get_player_state(player) do
    Map.get(player, :state, 0)
  end

  # 注意：get_player_name 和 get_player_avatar 函数已在上面重写父类函数，不需要重复定义

  # 注意：get_player_gaming_status 函数已经不再使用，
  # 因为我们直接在 broadcast_player_enter 中处理游戏状态

  # 统计真实玩家数量
  defp count_real_players(state) do
    state.players
    |> Map.values()
    |> Enum.count(fn player -> not Map.has_key?(state.game_data.robots, player.numeric_id) end)
  end

  # 统计机器人玩家数量
  defp count_robot_players(state) do
    map_size(state.game_data.robots)
  end

  defp send_game_config(state, player) do
    message = TeenPattiMessageBuilder.build_game_config(state)
    send_to_player(state, player, message)
  end

  defp send_current_game_state(state, player) do
    message = TeenPattiMessageBuilder.build_current_state(state, player)
    send_to_player(state, player, message)
  end

  defp send_player_list(state, player) do
    # 创建积分获取函数，使用room_base中的get_player_points方法
    get_points_fn = fn numeric_id -> get_player_points(state, numeric_id) end
    message = TeenPattiMessageBuilder.build_room_info(state, get_points_fn)

    Logger.info("🃏 [ROOM_INFO] 发送房间信息给玩家#{player.numeric_id} [4][2]")

    send_to_player(state, player, message)
  end



  defp broadcast_game_start(state) do
    # 确定庄家
    state_with_banker = determine_banker(state)

    message = TeenPattiMessageBuilder.build_game_config(state_with_banker)

    Logger.info("🃏 [GAME_START] 游戏开始 庄家:#{state_with_banker.game_data.banker_id} [5][1000]")

    broadcast_to_room(state_with_banker, message)
    state_with_banker
  end

  # 确定庄家
  defp determine_banker(state) do
    active_players = get_active_players(state)

    if length(active_players) == 0 do
      Logger.warning("🃏 [TEEN_PATTI] 没有活跃玩家，无法确定庄家")
      state
    else

    # 庄家确定规则：
    # 1. 第一局：按座位顺序选择第一个玩家
    # 2. 后续局：轮流担任庄家
    banker = if state.game_data.round == 1 do
      # 第一局：选择座位号最小的玩家作为庄家
      active_players
      |> Enum.min_by(fn player -> get_player_seat_by_id(state, player.numeric_id) end)
    else
      # 后续局：轮流担任庄家
      current_banker_id = state.game_data.banker_id
      current_index = Enum.find_index(active_players, fn p -> p.numeric_id == current_banker_id end)

      if current_index != nil do
        next_index = rem(current_index + 1, length(active_players))
        Enum.at(active_players, next_index)
      else
        # 如果当前庄家不在活跃玩家中，选择第一个
        hd(active_players)
      end
    end

    banker_id = banker.numeric_id
    banker_seat = get_player_seat_by_id(state, banker_id)

    Logger.info("🃏 [TEEN_PATTI] 确定庄家 - 玩家: #{banker_id}, 座位: #{banker_seat}")

    # 更新游戏数据中的庄家信息
    game_data = %{state.game_data |
      banker_id: banker_id,
      banker_seat: banker_seat
    }

    %{state | game_data: game_data}
    end
  end

  defp broadcast_dealing(state) do
    active_players = get_active_players(state)

    if length(active_players) > 0 do
      first_player = hd(active_players)
      first_player_seat = Map.get(first_player, :seat, 1)

      Logger.info("🃏 [DEALING] 发牌给#{length(active_players)}个玩家，当前操作玩家: #{first_player.numeric_id}(座位#{first_player_seat})")

      # 给每个玩家发送包含自己卡牌数据的发牌协议
      Enum.each(active_players, fn player ->
        player_cards = Map.get(state.game_data.player_cards, player.numeric_id, [])
        formatted_cards = TeenPattiDataFormatter.format_cards_for_client(player_cards)
        player_seat = Map.get(player, :seat, 1)

        # 获取玩家幸运值和类型信息并打印发牌信息
        luck_value = get_player_luck_value(state, player.numeric_id)
        player_type_name = get_player_type_name(state, player.numeric_id)
        card_type = TeenPattiLogic.calculate_card_type(player_cards)
        card_type_name = TeenPattiLogic.get_card_type_name(card_type)

        Logger.info("🎴 [DEALING] 玩家#{player.numeric_id}(#{player_type_name}) 幸运值:#{luck_value} 发牌:#{inspect(formatted_cards)} 牌型:#{card_type_name}")

        # 构建包含玩家卡牌数据的发牌协议
        message = %{
          "mainId" => 5,
          "subId" => 1001,
          "data" => %{
            "state" => TeenPattiDataFormatter.format_game_state(:send_card),
            "playerid" => first_player.numeric_id,
            "seatid" => first_player_seat,
            "bettype" => 0,
            "curtimes" => state.game_data.current_times,
            "allbet" => state.game_data.pot_total,
            "turnnum" => state.game_data.turn_count,
            "round" => state.game_data.round || 1,
            "dealtime" => state.game_data.config.send_card_time,
            "cards" => formatted_cards,
            "myseat" => player_seat,
            "myplayerid" => player.numeric_id
          }
        }

        user_id = Map.get(player, :user_id) || player.numeric_id
        CypridinaWeb.Endpoint.broadcast!("user:#{user_id}", "private_message", message)
      end)

      Logger.info("🃏 [DEALING] ✅ 发牌协议发送完成")
    else
      Logger.warning("🃏 [DEALING] ❌ 没有活跃玩家，无法发送发牌协议")
    end

    # 发牌后，机器人自动看牌
    schedule_robot_look_cards(state)
  end

  # 安排机器人看牌 (对照旧项目日志: 机器人在发牌后自动看牌)
  defp schedule_robot_look_cards(state) do
    robot_players = state.players
    |> Map.values()
    |> Enum.filter(fn player -> Map.get(player, :is_robot, false) end)

    # 机器人按概率看牌，延迟1-3秒执行
    Enum.each(robot_players, fn robot ->
      look_probability = TeenPattiGame.robot_behavior().look_probability

      if :rand.uniform() < look_probability do
        delay = 1000 + :rand.uniform(2000)  # 1-3秒延迟
        Logger.info("🤖 [TEEN_PATTI] 机器人 #{robot.numeric_id} 将在 #{delay}ms 后看牌")
        Process.send_after(self(), {:robot_look_card, robot.numeric_id}, delay)
      end
    end)
  end

  # 处理机器人看牌
  def handle_robot_look_card(state, robot_id) do
    robot = Map.get(state.players, robot_id)

    if robot && Map.get(robot, :is_robot, false) do
      Logger.info("🤖 [TEEN_PATTI] 机器人看牌 - 机器人: #{robot_id}")

      # 更新机器人看牌状态
      game_data = %{state.game_data |
        player_seen_status: Map.put(state.game_data.player_seen_status, robot_id, true)
      }

      updated_state = %{state | game_data: game_data}

      # 广播看牌协议 (对照旧项目日志: [5][1005])
      broadcast_look_card(updated_state, robot_id)

      updated_state
    else
      Logger.warning("🤖 [TEEN_PATTI] 无效的机器人看牌请求 - 机器人: #{robot_id}")
      state
    end
  end

  # 🎯 修复：发送看牌数据给看牌玩家（包含牌的数据）
  defp send_look_card_data_to_player(state, player) do
    player_cards = Map.get(state.game_data.player_cards, player.numeric_id, [])
    formatted_cards = TeenPattiDataFormatter.format_cards_for_client(player_cards)
    player_seat = Map.get(player, :seat, 1)

    # 🎯 关键修复：计算牌型，前端期望cardtype字段
    card_type = TeenPattiDataFormatter.get_card_type_value(player_cards)

    # 构建包含牌数据的看牌协议，发送给看牌玩家
    message = %{
      "mainId" => 5,
      "subId" => 1005,  # SC_TEENPATTI_LOOK_P
      "data" => %{
        "playerid" => player.numeric_id,
        "seatid" => player_seat,
        "_roomid" => state.id,
        "_playerid" => state.game_data.current_player || player.numeric_id,
        # 🎯 关键修复：添加牌的数据，让前端能显示牌面
        "cards" => formatted_cards,
        # 🎯 关键修复：添加cardtype字段，前端需要此字段显示牌型
        "cardtype" => card_type,
        "myseat" => player_seat,
        "myplayerid" => player.numeric_id
      }
    }

    Logger.info("🃏 [LOOK_CARD] 玩家#{player.numeric_id} 看牌:#{inspect(formatted_cards)} 牌型:#{card_type} [5][1005]")

    # 🎯 修复：机器人不需要发送看牌协议，只有真实玩家需要
    # 机器人看牌是自动的，不需要前端显示
    if not Map.get(player, :is_robot, false) do
      # 只给真实玩家发送看牌数据
      user_id = Map.get(player, :user_id) || player.numeric_id
      CypridinaWeb.Endpoint.broadcast!("user:#{user_id}", "private_message", message)
      Logger.info("🃏 [TEEN_PATTI_PROTOCOL_SEND] ✅ 看牌协议已发送给真实玩家: #{player.numeric_id}")
    else
      Logger.info("🃏 [TEEN_PATTI_PROTOCOL_SEND] 🤖 机器人看牌无需发送协议: #{player.numeric_id}")
    end
  end

  # 广播看牌协议（告诉其他玩家谁看了牌）
  defp broadcast_look_card(state, look_player_id) do
    # 获取当前操作玩家（如果有的话）
    current_player_id = state.game_data.current_player || look_player_id

    # 🎯 关键修复：构建纯通知消息（不包含牌数据），只告诉其他玩家谁看了牌
    message = %{
      "mainId" => 5,
      "subId" => 1005,  # SC_TEENPATTI_LOOK_P
      "data" => %{
        "playerid" => look_player_id,
        "seatid" => get_player_seat_by_id(state, look_player_id),
        "_roomid" => state.id,
        "_playerid" => current_player_id
        # 🎯 注意：这里不包含 cards 和 cardtype 字段，只是通知消息
      }
    }

    Logger.info("🃏 [LOOK_CARD_BROADCAST] 玩家#{look_player_id} 看牌广播通知 当前玩家:#{current_player_id} [5][1005]")

    # 🎯 关键修复：排除看牌玩家自己，避免覆盖包含牌数据的协议
    look_player = Map.get(state.players, look_player_id)
    if look_player && !Map.get(look_player, :is_robot, false) do
      # 真实玩家：排除看牌玩家自己，只广播给其他玩家
      Logger.info("🃏 [TEEN_PATTI_PROTOCOL_SEND] 🎯 排除看牌玩家自己，只广播通知给其他玩家")
      broadcast_to_room(state, message, [look_player])
    else
      # 机器人：广播给所有玩家（机器人不需要接收包含牌数据的协议）
      Logger.info("🃏 [TEEN_PATTI_PROTOCOL_SEND] 🤖 机器人看牌，广播通知给所有玩家")
      broadcast_to_room(state, message)
    end
  end

  # 🎯 修复：处理机器人下注（使用fill参数）
  defp handle_robot_bet_with_fill(state, robot_id, fill) do
    robot = Map.get(state.players, robot_id)

    if robot && Map.get(robot, :is_robot, false) do
      # 计算实际下注金额
      bet_amount = calculate_bet_amount(state, robot_id, fill)

      Logger.info("🤖 [TEEN_PATTI] 机器人下注 - 机器人: #{robot_id}, fill: #{fill}, 金额: #{bet_amount}")

      # 检查机器人金币是否足够
      robot_money = get_player_points(state, robot_id)
      if robot_money < bet_amount do
        Logger.warning("🤖 [TEEN_PATTI] 机器人金币不足 - 需要: #{bet_amount}, 拥有: #{robot_money}")
        # 机器人金币不足，自动弃牌
        handle_fold_action(state, robot_id)
      else
        # 执行下注
        execute_bet_action(state, robot_id, bet_amount, fill)
      end
    else
      Logger.warning("🤖 [TEEN_PATTI] 无效的机器人下注请求 - 机器人: #{robot_id}")
      state
    end
  end

  # 🎯 修复：计算下注金额 - 完全匹配前端callScore函数的计算逻辑
  defp calculate_bet_amount(state, player_id, fill) do
    # 🎯 参考前端TPGameCore.ts callScore函数的计算逻辑：
    # let curbet=this.curtimes*this.difen*fill;
    # if(this.isMeLook){
    #     curbet=curbet*2;
    # }

    base_bet = state.game_data.config.base_bet  # difen
    current_times = state.game_data.current_times || 1  # 🎯 关键修复：使用current_times而不是turn_count

    # 检查玩家是否看过牌
    is_seen = Map.get(state.game_data.player_seen_status, player_id, false)

    # 🎯 完全匹配前端计算逻辑
    bet_amount = current_times * base_bet * fill

    # 如果玩家看过牌，下注金额翻倍
    final_amount = if is_seen do
      bet_amount * 2
    else
      bet_amount
    end

    # 🎯 检查是否超过封顶限制（参考前端逻辑）
    chaal_limit = state.game_data.config.chaal_limit
    actual_amount = if final_amount > chaal_limit do
      chaal_limit
    else
      final_amount
    end

    Logger.info("🎯 [TEEN_PATTI_BET_CALC] ========== 下注金额计算 ==========")
    Logger.info("🎯 [TEEN_PATTI_BET_CALC] 玩家: #{player_id}, fill: #{fill}")
    Logger.info("🎯 [TEEN_PATTI_BET_CALC] 基础计算: #{current_times} × #{base_bet} × #{fill} = #{bet_amount}")
    Logger.info("🎯 [TEEN_PATTI_BET_CALC] 是否看牌: #{is_seen}, #{if is_seen, do: "翻倍后: #{final_amount}", else: "无需翻倍"}")
    Logger.info("🎯 [TEEN_PATTI_BET_CALC] 封顶限制: #{chaal_limit}, 最终金额: #{actual_amount}")

    actual_amount
  end



  # 处理机器人下注（旧版本，保持兼容性）
  defp handle_robot_bet(state, robot_id, amount) do
    robot = Map.get(state.players, robot_id)

    if robot && Map.get(robot, :is_robot, false) do
      Logger.info("🤖 [TEEN_PATTI] 机器人下注 - 机器人: #{robot_id}, 金额: #{amount}")

      # 扣除机器人积分
      subtract_player_points(state, robot_id, amount)

      # 更新下注记录
      game_data = %{state.game_data |
        player_bets: Map.put(state.game_data.player_bets, robot_id, amount),
        player_total_bets: Map.update(state.game_data.player_total_bets, robot_id, amount, &(&1 + amount)),
        pot_total: state.game_data.pot_total + amount,
        current_bet: max(state.game_data.current_bet || 0, amount)
      }

      updated_state = %{state | game_data: game_data}

      # 广播下注协议
      broadcast_robot_bet(updated_state, robot_id, amount)

      # 轮到下一个玩家
      next_player_turn(updated_state)
    else
      Logger.warning("🤖 [TEEN_PATTI] 无效的机器人下注请求 - 机器人: #{robot_id}")
      state
    end
  end

  # 处理机器人比牌
  defp handle_robot_competition(state, robot_id, target_id) do
    robot = Map.get(state.players, robot_id)
    target = Map.get(state.players, target_id)

    if robot && target && Map.get(robot, :is_robot, false) do
      Logger.info("🤖 [TEEN_PATTI] 机器人比牌 - 机器人: #{robot_id}, 目标: #{target_id}")

      # 🎯 修复：设置比牌状态，存储比牌信息（与真实玩家比牌逻辑一致）
      game_data = %{state.game_data |
        state: 3,  # 比牌状态
        competition_player: robot_id,
        competition_target: target_id
      }
      updated_state = %{state | game_data: game_data}

      # 广播比牌协议
      broadcast_robot_competition(updated_state, robot_id, target_id)

      # 🎯 修复：使用新的比牌逻辑函数，确保启动比牌动画定时器
      # 机器人比牌直接执行，不需要确认步骤
      execute_competition_logic(updated_state, target_id)
    else
      Logger.warning("🤖 [TEEN_PATTI] 无效的机器人比牌请求 - 机器人: #{robot_id}, 目标: #{target_id}")
      state
    end
  end

  # 处理机器人自动确认比牌
  defp handle_robot_auto_confirm_competition(state, robot_id) do
    robot = Map.get(state.players, robot_id)

    if robot && Map.get(robot, :is_robot, false) do
      # 检查游戏状态是否为比牌状态
      if state.game_data.state == 3 && state.game_data.competition_target == robot_id do
        Logger.info("🤖 [TEEN_PATTI_COMP] 机器人自动接受比牌 - 机器人: #{robot_id}")

        # 🎯 修复：机器人总是接受比牌（参考旧项目逻辑）
        # 按照旧项目逻辑，需要发送两次1009协议
        # 第一次：确认阶段协议（只包含确认信息）
        Logger.info("🤖 [TEEN_PATTI_COMP] 📤 机器人发送第一次1009协议 - 确认阶段")
        broadcast_competition_confirm(state, robot_id, true)

        # 第二次：在execute_competition_logic中发送结果协议（包含完整比牌结果）
        # 然后执行比牌逻辑
        execute_competition_logic(state, robot_id)
      else
        Logger.warning("🤖 [TEEN_PATTI_COMP] 机器人确认比牌时状态不正确 - 机器人: #{robot_id}")
        state
      end
    else
      Logger.warning("🤖 [TEEN_PATTI] 无效的机器人确认比牌请求 - 机器人: #{robot_id}")
      state
    end
  end



  # 广播机器人下注（旧版本，保持兼容性）
  defp broadcast_robot_bet(state, robot_id, amount) do
    # 🎯 关键修复：直接从玩家数据中获取固定座位号
    robot = Map.get(state.players, robot_id)
    seat_id = Map.get(robot, :seat, 1)

    message = %{
      "mainId" => 5,
      "subId" => 1003,
      "data" => %{
        "playerid" => robot_id,
        "seatid" => seat_id,  # 🎯 关键修复：使用玩家的固定座位号
        "bet" => amount,
        "bettype" => 1,  # 1=正常下注
        "curtimes" => state.game_data.current_times,  # 🎯 修复：使用current_times
        "allbet" => state.game_data.pot_total,
        "state" => TeenPattiDataFormatter.format_game_state(:bet)
      }
    }

    Logger.info("🤖 [TEEN_PATTI_PROTOCOL_SEND] ========== 机器人下注协议 ==========")
    Logger.info("🤖 [TEEN_PATTI_PROTOCOL_SEND] 机器人: #{robot_id}, 下注: #{amount}")

    broadcast_to_room(state, message)
  end

  # 广播机器人比牌
  defp broadcast_robot_competition(state, robot_id, target_id) do
    message = TeenPattiMessageBuilder.build_competition_request(state, robot_id, target_id)

    Logger.info("🤖 [TEEN_PATTI_PROTOCOL_SEND] ========== 机器人比牌协议 ==========")
    Logger.info("🤖 [TEEN_PATTI_PROTOCOL_SEND] 机器人: #{robot_id}, 目标: #{target_id}")

    broadcast_to_room(state, message)
  end



  # 广播比牌结果
  defp broadcast_competition_result(state, requester_id, target_id, winner_id) do
    message = TeenPattiMessageBuilder.build_competition_confirm_response(
      state, requester_id, target_id, true, winner_id
    )

    Logger.info("🤖 [TEEN_PATTI_PROTOCOL_SEND] ========== 比牌结果协议 ==========")
    Logger.info("🤖 [TEEN_PATTI_PROTOCOL_SEND] 协议: SC_TEENPATTI_COMPCONFIRM_P (1009) - 最终比牌结果")
    Logger.info("🤖 [TEEN_PATTI_PROTOCOL_SEND] 📤 这是第二次1009协议发送，包含完整比牌结果（compseat/optseat）")
    Logger.info("🤖 [TEEN_PATTI_PROTOCOL_SEND] 胜者: #{winner_id}")
    Logger.info("🤖 [TEEN_PATTI_PROTOCOL_SEND] 🎯 完整协议数据: #{inspect(message)}")

    # 🎯 调试：检查cards字段是否存在
    if message["data"]["optseat"] && message["data"]["compseat"] do
      Logger.info("🤖 [TEEN_PATTI_PROTOCOL_SEND] 🃏 optseat cards: #{inspect(message["data"]["optseat"]["cards"])}")
      Logger.info("🤖 [TEEN_PATTI_PROTOCOL_SEND] 🃏 compseat cards: #{inspect(message["data"]["compseat"]["cards"])}")
    else
      Logger.error("🤖 [TEEN_PATTI_PROTOCOL_SEND] ❌ 缺少optseat或compseat数据!")
    end

    broadcast_to_room(state, message)
  end

  defp broadcast_betting_start(state) do
    message = TeenPattiMessageBuilder.build_betting_start(state)

    Logger.info("🃏 [BETTING_START] 下注开始 [5][1014]")

    broadcast_to_room(state, message)
  end

  # 🎯 重构：启动操作定时器 - 参考pot_blind实现
  defp start_operation_timer(state, player_id) do
    Logger.info("⏰ [TEEN_PATTI] 启动操作定时器 - 玩家: #{player_id}")

    # 取消之前的定时器（如果存在）
    cancel_operation_timer(state)

    # 获取操作等待时间
    wait_time = state.game_data.config.operation_wait_time * 1000  # 转换为毫秒

    # 启动新的定时器
    timer_ref = Process.send_after(self(), {:operation_timeout, player_id}, wait_time)

    # 保存定时器引用到状态中
    updated_state = Map.put(state, :operation_timer_ref, timer_ref)

    Logger.info("⏰ [TEEN_PATTI] 启动操作倒计时: #{wait_time}ms，玩家: #{player_id}")
    updated_state
  end

  # 🎯 重构：取消操作定时器 - 参考pot_blind实现
  defp cancel_operation_timer(state) do
    case Map.get(state, :operation_timer_ref) do
      nil ->
        Logger.debug("🚫 [TEEN_PATTI] 没有需要取消的操作定时器")
        state

      timer_ref ->
        Process.cancel_timer(timer_ref)
        updated_state = Map.put(state, :operation_timer_ref, nil)
        Logger.debug("🚫 [TEEN_PATTI] 取消操作定时器")
        updated_state
    end
  end

  # 🎯 新增：启动比牌动画定时器 - 参考pot_blind实现
  defp start_competition_animation_timer(state, winner_id, requester_id, target_id) do
    Logger.info("🎬 [TEEN_PATTI] 启动比牌动画定时器，胜者: #{winner_id}")

    # 取消之前的比牌动画定时器（如果存在）
    cancel_competition_animation_timer(state)

    # 启动5秒倒计时，给前端足够时间播放比牌动画
    timer_ref = Process.send_after(self(), {:competition_animation_expired, winner_id, requester_id, target_id}, 5000)

    # 保存定时器引用和相关信息到状态中
    updated_state = state
    |> Map.put(:competition_animation_timer_ref, timer_ref)
    |> Map.put(:competition_winner_id, winner_id)

    Logger.info("🎬 [TEEN_PATTI] 比牌动画定时器已启动: 3秒后继续游戏流程")

    updated_state
  end

  # 🎯 新增：取消比牌动画定时器
  defp cancel_competition_animation_timer(state) do
    case Map.get(state, :competition_animation_timer_ref) do
      nil ->
        Logger.debug("🎬 [TEEN_PATTI] 没有比牌动画定时器需要取消")

      timer_ref ->
        Process.cancel_timer(timer_ref)
        Logger.info("🎬 [TEEN_PATTI] 已取消比牌动画定时器")
    end

    # 清除定时器引用和相关状态
    state
    |> Map.put(:competition_animation_timer_ref, nil)
    |> Map.put(:competition_winner_id, nil)
  end

  # 兼容旧版本的函数名
  defp set_operation_timer(state, player_id) do
    start_operation_timer(state, player_id)
  end

  defp handle_player_timeout(state, player_id) do
    Logger.info("🃏 [TEEN_PATTI] 玩家操作超时 - 玩家: #{player_id}")

    # 检查是否是真实玩家
    player = Map.get(state.players, player_id)
    if player && !Map.get(player, :is_robot, false) do
      # 真实玩家超时，进入暂离模式
      enter_zanli_mode(state, player_id)
    else
      # 机器人超时，直接弃牌
      handle_fold_action(state, player_id)
    end
  end

  defp handle_fold_action(state, player_id) do
    Logger.info("🃏 [TEEN_PATTI] 玩家弃牌 - 玩家: #{player_id}")

    # 更新玩家状态
    game_data = %{state.game_data |
      player_states: Map.put(state.game_data.player_states, player_id, :fold)
    }

    new_state = %{state | game_data: game_data}

    # 广播弃牌消息
    broadcast_fold(new_state, player_id)

    # 检查游戏结束条件
    check_game_end_condition(new_state)
  end

  defp check_game_end_condition(state) do
    active_players = get_active_players(state)

    if length(active_players) <= 1 do
      # 游戏结束，进行结算
      settle_game(state)
    else
      # 继续游戏，轮到下一个玩家
      next_player_turn(state)
    end
  end

  # 🎯 重构：轮到下一个玩家 - 参考pot_blind的set_next_seat_id实现
  defp next_player_turn(state) do
    Logger.info("🔄 [TEEN_PATTI] ========== 切换到下一个玩家 ==========")

    # 取消当前玩家的操作定时器
    cancel_operation_timer(state)

    # 找到下一个活跃玩家
    current_player_id = state.game_data.current_player
    next_player = find_next_active_player(state, current_player_id)

    case next_player do
      nil ->
        # 没有下一个玩家，检查游戏结束条件
        Logger.info("🔚 [TEEN_PATTI] 没有下一个活跃玩家，检查游戏结束条件")
        check_game_end_condition(state)

      player ->
        Logger.info("🔄 [TEEN_PATTI] 切换到下一个玩家: #{player.numeric_id}")

        # 更新当前操作玩家和轮次信息
        game_data = %{state.game_data |
          current_player: player.numeric_id,
          turn_count: state.game_data.turn_count + 1,
          turn_start_time: System.system_time(:millisecond)
        }
        updated_state = %{state | game_data: game_data}

        # 发送等待操作协议
        send_wait_operation_message(updated_state, player.numeric_id)

        # 启动操作定时器
        timer_state = start_operation_timer(updated_state, player.numeric_id)

        # 如果是机器人，安排AI操作
        if is_robot_player?(timer_state, player.numeric_id) do
          schedule_robot_action(timer_state, player.numeric_id)
        end

        timer_state
    end
  end

  defp settle_game(state) do
    Logger.info("🃏 [TEEN_PATTI] 游戏结算开始")

    # 计算获胜者和奖励分配
    settlement_result = calculate_settlement(state)

    # 更新玩家积分
    new_state = apply_settlement(state, settlement_result)

    # 广播结算结果
    broadcast_settlement(new_state, settlement_result)

    # 🎯 修复：检查暂离玩家，游戏结束后退回大厅
    Logger.info("🃏 [TEEN_PATTI_SETTLEMENT] 开始检查暂离玩家")
    state_after_zanli_check = check_zanli_players_after_game(new_state)

    # 重置游戏状态，准备下一轮
    Logger.info("🃏 [TEEN_PATTI_SETTLEMENT] 重置游戏状态，准备下一轮")
    reset_for_next_round(state_after_zanli_check)
  end

  defp calculate_settlement(state) do
    Logger.info("🃏 [TEEN_PATTI] 开始计算结算")

    # 获取所有参与游戏的玩家（有手牌的玩家）
    participating_players = state.players
    |> Map.values()
    |> Enum.filter(fn player ->
      player_cards = Map.get(state.game_data.player_cards, player.numeric_id, [])
      length(player_cards) > 0
    end)

    # 获取活跃玩家（未弃牌、未失败的玩家）
    active_players = participating_players
    |> Enum.filter(fn player ->
      player_state = Map.get(state.game_data.player_states, player.numeric_id, :play)
      player_state == :play
    end)

    # 🎯 修复：确定获胜者，处理边界情况
    winner = cond do
      length(active_players) == 0 ->
        # 没有活跃玩家，选择第一个参与玩家作为获胜者
        Logger.warning("🃏 [TEEN_PATTI] 没有活跃玩家，选择第一个参与玩家")
        List.first(participating_players)

      length(active_players) == 1 ->
        # 只剩一个玩家，该玩家获胜
        hd(active_players)

      true ->
        # 比较所有活跃玩家的牌型，确定最强者
        find_best_hand_player(active_players, state)
    end

    # 🎯 修复：检查获胜者是否为nil
    if winner == nil do
      Logger.error("🃏 [TEEN_PATTI] 无法确定获胜者，游戏结算失败")
      %{
        winner: nil,
        pot_total: state.game_data.pot_total,
        player_changes: %{}
      }
    else
      winner_id = winner.numeric_id
      pot_total = state.game_data.pot_total

      Logger.info("🃏 [TEEN_PATTI] 结算结果 - 获胜者: #{winner_id}, 奖池: #{pot_total}")

      # 计算每个玩家的金币变化
      player_changes = participating_players
      |> Enum.map(fn player ->
        change = if player.numeric_id == winner_id do
          # 获胜者获得奖池，减去自己的下注
          own_bet = Map.get(state.game_data.player_total_bets, player.numeric_id, 0)
          pot_total - own_bet
        else
          # 失败者失去自己的下注
          own_bet = Map.get(state.game_data.player_total_bets, player.numeric_id, 0)
          -own_bet
        end

        {player.numeric_id, change}
      end)
      |> Enum.into(%{})

      %{
        winner: winner_id,
        pot_total: pot_total,
        player_changes: player_changes
      }
    end


  end

  # 找到拥有最强牌型的玩家
  defp find_best_hand_player(players, state) do
    # 🎯 修复：检查玩家列表是否为空，避免Enum.EmptyError
    case players do
      [] ->
        Logger.warning("🃏 [TEEN_PATTI] 没有活跃玩家进行牌型比较")
        nil
      [single_player] ->
        # 只有一个玩家，直接返回
        single_player
      _ ->
        # 多个玩家，比较牌型强度
        players
        |> Enum.max_by(fn player ->
          player_cards = Map.get(state.game_data.player_cards, player.numeric_id, [])
          TeenPattiLogic.get_hand_strength(player_cards)
        end)
    end
  end

  defp apply_settlement(state, settlement_result) do
    Logger.info("🃏 [TEEN_PATTI] 应用结算结果")

    # 更新每个玩家的积分
    Enum.each(settlement_result.player_changes, fn {player_id, change} ->
      if change > 0 do
        add_player_points(state, player_id, change)
        Logger.info("🃏 [TEEN_PATTI] 玩家 #{player_id} 获得 #{change} 金币")
      else
        # 下注时已经扣除，这里不需要再扣除
        Logger.info("🃏 [TEEN_PATTI] 玩家 #{player_id} 失去 #{abs(change)} 金币")
      end
    end)

    # 更新所有参与游戏的玩家的游戏局数统计
    updated_state = update_players_game_stats(state, settlement_result)

    updated_state
  end

  @doc """
  更新所有参与游戏的玩家的游戏统计信息
  """
  defp update_players_game_stats(state, settlement_result) do
    Logger.info("🃏 [TEEN_PATTI] 开始更新玩家游戏统计")

    # 获取所有参与游戏的玩家（有手牌的玩家）
    participating_players = state.players
    |> Enum.filter(fn {_player_id, player} ->
      player_cards = Map.get(state.game_data.player_cards, player.numeric_id, [])
      length(player_cards) > 0
    end)
    |> Enum.into(%{})

    # 使用 TeenPattiPlayer 模块的批量更新函数
    updated_players = TeenPattiPlayer.update_players_game_stats(participating_players, settlement_result)

    # 合并更新后的玩家数据到原有的玩家列表中
    final_players = Map.merge(state.players, updated_players)

    Logger.info("🃏 [TEEN_PATTI] 完成更新 #{map_size(participating_players)} 个玩家的游戏统计")
    %{state | players: final_players}
  end

  defp broadcast_settlement(state, settlement_result) do
    message = TeenPattiMessageBuilder.build_settlement(state, settlement_result)
    broadcast_to_room(state, message)
  end

  defp broadcast_fold(state, player_id) do
    message = TeenPattiMessageBuilder.build_fold_message(state, player_id)
    broadcast_to_room(state, message)
  end

  defp reset_for_next_round(state) do
    Logger.info("🃏 [TEEN_PATTI] 重置游戏状态准备下一轮")

    # 重置游戏状态为等待下一轮
    game_data = %{state.game_data |
      state: 0,  # 🎯 修复：使用数字0而不是:start
      round: state.game_data.round + 1,
      player_cards: %{},
      player_bets: %{},
      player_total_bets: %{},
      player_seen_status: %{},
      player_states: %{},
      current_player: nil,
      turn_count: 0,
      current_times: 1,  # 🎯 修复：重置下注倍数
      pot_total: 0
    }

    new_state = %{state | game_data: game_data}

    # 🎯 修复：检查是否还有足够玩家继续游戏
    real_players = count_real_players(new_state)
    active_players = get_active_players(new_state)

    Logger.info("🃏 [TEEN_PATTI] 游戏结算后状态检查 - 真实玩家: #{real_players}, 活跃玩家: #{length(active_players)}")

    if real_players >= 1 and length(active_players) >= 2 do
      Logger.info("🃏 [TEEN_PATTI] 玩家数量足够，5秒后开始下一轮")
      # 延迟后开始下一轮
      Process.send_after(self(), :start_next_round, 5000)
    else
      Logger.info("🃏 [TEEN_PATTI] 玩家数量不足，等待更多玩家加入")
      # 广播房间状态变更为等待
      broadcast_room_status_change(new_state, 0)  # 0=等待状态
    end

    new_state
  end

  # ==================== 消息处理实现 ====================

  defp handle_bet_request(state, player, message) do
    Logger.info("🃏 [BET_REQ] 玩家#{player.numeric_id} 下注请求 [5][1002]")

    cond do
      # 检查是否轮到该玩家操作
      state.game_data.current_player != player.numeric_id ->
        Logger.warning("🃏 [TEEN_PATTI_BET_REQ] ❌ 不是该玩家的操作回合 - 当前: #{state.game_data.current_player}, 请求: #{player.numeric_id}")
        state

      # 检查游戏状态是否允许下注
      state.game_data.state != 2 ->  # 2 = bet状态
        Logger.warning("🃏 [TEEN_PATTI_BET_REQ] ❌ 当前游戏状态不允许下注 - 状态: #{state.game_data.state}")
        state

      true ->
        # 🎯 关键：获取下注倍数 (fill: 1=跟注, 2=加注) - 匹配前端callScore函数
        fill = Map.get(message["data"], "fill", 1)
        Logger.info("🃏 [TEEN_PATTI_BET_REQ] 下注倍数: #{fill} (#{if fill == 1, do: "跟注", else: "加注"})")

        # 🎯 使用修复后的计算函数
        bet_amount = calculate_bet_amount(state, player.numeric_id, fill)
        Logger.info("🃏 [TEEN_PATTI_BET_REQ] 计算下注金额: #{bet_amount}")

        # 检查玩家金币是否足够
        player_money = get_player_points(state, player.numeric_id)
        if player_money < bet_amount do
          Logger.warning("🃏 [TEEN_PATTI_BET_REQ] ❌ 玩家金币不足 - 需要: #{bet_amount}, 拥有: #{player_money}")
          # 🎯 发送等待充值协议（匹配前端期望）
          send_wait_recharge_message(state, player.numeric_id, bet_amount)
          state
        else
          Logger.info("🃏 [TEEN_PATTI_BET_REQ] ✅ 玩家金币充足，执行下注")
          # 🎯 执行下注动作
          execute_bet_action(state, player.numeric_id, bet_amount, fill)
        end
    end
  end

  defp handle_look_request(state, player, _message) do
    Logger.info("🃏 [TEEN_PATTI] 处理看牌请求 - 玩家: #{player.numeric_id}")

    cond do
      # 检查玩家是否已经看过牌
      Map.get(state.game_data.player_seen_status, player.numeric_id, false) ->
        Logger.info("🃏 [TEEN_PATTI] 玩家已经看过牌 - 玩家: #{player.numeric_id}")
        state

      # 检查玩家是否有手牌
      length(Map.get(state.game_data.player_cards, player.numeric_id, [])) == 0 ->
        Logger.warning("🃏 [TEEN_PATTI] 玩家没有手牌，无法看牌 - 玩家: #{player.numeric_id}")
        state

      true ->
        # 更新玩家看牌状态
        game_data = %{state.game_data |
          player_seen_status: Map.put(state.game_data.player_seen_status, player.numeric_id, true)
        }

        updated_state = %{state | game_data: game_data}

        # 🎯 修复：先给看牌玩家发送包含牌数据的看牌协议
        send_look_card_data_to_player(updated_state, player)

        # 然后广播看牌协议给其他玩家
        broadcast_look_card(updated_state, player.numeric_id)

        updated_state
    end
  end

  defp handle_competition_request(state, player, message) do
    Logger.info("🃏 [TEEN_PATTI_COMP] 比牌请求 - 发起者: #{player.numeric_id}, 轮次: #{state.game_data.turn_count}")

    # 🎯 前端发送空数据 {}，服务端自动选择比牌对象
    # 获取可比牌的对手列表（排除自己和已弃牌的玩家）
    active_players = get_active_players(state)
    |> Enum.filter(fn p ->
      p.numeric_id != player.numeric_id and
      Map.get(state.game_data.player_states, p.numeric_id, :play) == :play
    end)

    # 🎯 修复：自动选择下一个活跃玩家作为比牌对象
    target_player = List.first(active_players)

    Logger.debug("🃏 [TEEN_PATTI_COMP] 活跃玩家数: #{length(active_players)}, 目标: #{if target_player, do: target_player.numeric_id, else: "无"}")

    cond do
      # 检查是否轮到该玩家操作
      state.game_data.current_player != player.numeric_id ->
        Logger.warning("🃏 [TEEN_PATTI_COMP] ❌ 不是该玩家的操作回合")
        state

      # 检查游戏状态是否为下注状态
      state.game_data.state != 2 ->
        Logger.warning("🃏 [TEEN_PATTI_COMP] ❌ 当前不是下注状态，无法比牌")
        state

      # 检查是否满足比牌条件（参考旧项目：最小轮数限制）
      state.game_data.turn_count < state.game_data.config.comp_min_turn_num ->
        Logger.warning("🃏 [TEEN_PATTI_COMP] ❌ 未达到最小比牌回合数 (#{state.game_data.turn_count}/#{state.game_data.config.comp_min_turn_num})")
        state

      # 检查目标玩家是否有效
      target_player == nil ->
        Logger.warning("🃏 [TEEN_PATTI_COMP] ❌ 无效的比牌目标")
        state

      # 检查是否有可比牌的对手
      length(active_players) == 0 ->
        Logger.warning("🃏 [TEEN_PATTI_COMP] ❌ 没有可比牌的对手")
        state

      true ->
        # 🎯 修复：验证比牌金额（参考前端callCompetition逻辑）
        competition_amount = calculate_competition_amount(state, player.numeric_id)
        player_money = get_player_points(state, player.numeric_id)

        Logger.info("🃏 [TEEN_PATTI_COMP_MONEY] 比牌金额计算: #{competition_amount}")
        Logger.info("🃏 [TEEN_PATTI_COMP_MONEY] 玩家余额: #{player_money}")

        if player_money < competition_amount do
          Logger.warning("🃏 [TEEN_PATTI_COMP] ❌ 玩家余额不足，需要: #{competition_amount}, 当前: #{player_money}")
          # 发送等待充值协议
          send_wait_recharge_message(state, player.numeric_id, competition_amount)
          state
        else
          Logger.info("🃏 [TEEN_PATTI_COMP] ✅ 比牌条件满足，发起比牌")

          # 更新游戏状态为比牌状态
          game_data = %{state.game_data |
            state: 3,  # 3 = competition状态
            competition_player: player.numeric_id,
            competition_target: target_player.numeric_id
          }
          updated_state = %{state | game_data: game_data}

          # 广播比牌请求
          broadcast_competition_request(updated_state, player.numeric_id, target_player.numeric_id)

          # 🎯 关键修复：设置操作权限给被比牌的玩家（参考旧项目WaitOpt逻辑）
          final_state = send_wait_operation_message(updated_state, target_player.numeric_id)

          # 🎯 修复：如果目标是机器人，自动确认比牌
          target_player_data = Map.get(final_state.players, target_player.numeric_id)
          if target_player_data && Map.get(target_player_data, :is_robot, false) do
            Logger.info("🤖 [TEEN_PATTI_COMP] 目标是机器人，自动确认比牌")
            # 延迟1-2秒后自动确认，模拟思考时间
            delay = 1000 + :rand.uniform(1000)
            Process.send_after(self(), {:robot_auto_confirm_competition, target_player.numeric_id}, delay)
          end

          final_state
        end
    end
  end

  defp handle_competition_confirm(state, player, message) do
    Logger.info("🃏 [COMP_CONFIRM] 玩家#{player.numeric_id} 比牌确认 发起者:#{state.game_data.competition_player} 目标:#{state.game_data.competition_target} [5][1008]")

    # 获取确认结果
    confirm = Map.get(message["data"], "confirm", true)
    Logger.info("🃏 [TEEN_PATTI_COMP_CONFIRM] 确认结果: #{confirm}")

    # 🎯 修复：玩家操作时取消操作定时器，重置暂离时间
    cancel_operation_timer(state)

    # 检查游戏状态是否为比牌状态
    if state.game_data.state != 3 do  # 3 = competition状态
      Logger.warning("🃏 [TEEN_PATTI_COMP] ❌ 当前不是比牌状态")
      state
    else
      # 🎯 修复：检查确认玩家是否为比牌目标或发起者（当目标是机器人时）
      target_player = Map.get(state.players, state.game_data.competition_target)
      is_target = player.numeric_id == state.game_data.competition_target
      is_requester_with_robot_target = player.numeric_id == state.game_data.competition_player &&
                                       target_player && Map.get(target_player, :is_robot, false)

      Logger.info("🃏 [TEEN_PATTI_COMP_CONFIRM] 权限检查:")
      Logger.info("🃏 [TEEN_PATTI_COMP_CONFIRM]   - 确认玩家: #{player.numeric_id}")
      Logger.info("🃏 [TEEN_PATTI_COMP_CONFIRM]   - 比牌发起者: #{state.game_data.competition_player}")
      Logger.info("🃏 [TEEN_PATTI_COMP_CONFIRM]   - 比牌目标: #{state.game_data.competition_target}")
      Logger.info("🃏 [TEEN_PATTI_COMP_CONFIRM]   - 是否为目标: #{is_target}")
      Logger.info("🃏 [TEEN_PATTI_COMP_CONFIRM]   - 目标是否为机器人: #{target_player && Map.get(target_player, :is_robot, false)}")
      Logger.info("🃏 [TEEN_PATTI_COMP_CONFIRM]   - 是否为发起者且目标是机器人: #{is_requester_with_robot_target}")

      if not (is_target or is_requester_with_robot_target) do
        Logger.warning("🃏 [TEEN_PATTI_COMP] ❌ 只有比牌目标或发起者（当目标是机器人时）可以确认比牌")
        state
      else
        # 获取确认结果
        confirm = Map.get(message["data"], "confirm", false)

        if confirm do
          Logger.info("🃏 [TEEN_PATTI_COMP] ✅ 玩家接受比牌 - 玩家: #{player.numeric_id}")

          # 🎯 修复：按照旧项目逻辑，需要发送两次1009协议
          # 第一次：确认阶段协议（只包含确认信息）
          Logger.info("🃏 [TEEN_PATTI_COMP_CONFIRM] 📤 发送第一次1009协议 - 确认阶段")
          broadcast_competition_confirm(state, player.numeric_id, confirm)

          # 第二次：在execute_competition_logic中发送结果协议（包含完整比牌结果）
          # 执行比牌逻辑
          execute_competition_logic(state, player.numeric_id)
        else
          Logger.info("🃏 [TEEN_PATTI_COMP] ❌ 玩家拒绝比牌 - 玩家: #{player.numeric_id}")

          # 🎯 修复：拒绝比牌时发送确认协议
          broadcast_competition_confirm(state, player.numeric_id, confirm)

          # 拒绝比牌，回到下注状态
          game_data = %{state.game_data |
            state: 2,  # 回到bet状态
            competition_player: nil,
            competition_target: nil
          }
          updated_state = %{state | game_data: game_data}

          # 继续下注流程，轮到比牌发起者继续操作
          next_player_turn(updated_state)
        end
      end
    end
  end

  # 执行比牌逻辑 - 改进版本
  defp execute_competition_logic(state, target_id) do
    # 使用存储的比牌信息
    requester_id = state.game_data.competition_player
    stored_target_id = state.game_data.competition_target

    Logger.info("🃏 [COMP_EXECUTE] 执行比牌 发起者:#{requester_id} 目标:#{stored_target_id} 确认者:#{target_id}")

    if requester_id == nil or stored_target_id == nil do
      Logger.error("🃏 [TEEN_PATTI_COMP] ❌ 比牌参与者信息不完整")
      state
    else
      # 获取双方手牌
      requester_cards = Map.get(state.game_data.player_cards, requester_id, [])
      target_cards = Map.get(state.game_data.player_cards, stored_target_id, [])

      Logger.info("🃏 [TEEN_PATTI_COMP] 发起者手牌: #{inspect(requester_cards)}")
      Logger.info("🃏 [TEEN_PATTI_COMP] 目标手牌: #{inspect(target_cards)}")

      # 计算双方牌型
      requester_card_type = TeenPattiLogic.calculate_card_type(requester_cards)
      target_card_type = TeenPattiLogic.calculate_card_type(target_cards)

      Logger.info("🃏 [TEEN_PATTI_COMP] 发起者牌型: #{requester_card_type}")
      Logger.info("🃏 [TEEN_PATTI_COMP] 目标牌型: #{target_card_type}")

      # 比较牌型，确定胜负（参考旧项目C++逻辑）
      winner_id = case TeenPattiLogic.compare_hands(requester_cards, target_cards) do
        :greater -> requester_id
        :less -> stored_target_id
        :equal ->
          # 如果牌型相同，按照旧项目规则处理（通常是发起者败）
          Logger.info("🃏 [TEEN_PATTI_COMP] 牌型相同，发起者败")
          stored_target_id
      end

      loser_id = if winner_id == requester_id, do: stored_target_id, else: requester_id

      Logger.info("🃏 [TEEN_PATTI_COMP] ✅ 比牌结果 - 胜者: #{winner_id}, 败者: #{loser_id}")

      # 更新败者状态为弃牌，清除比牌信息
      game_data = %{state.game_data |
        player_states: Map.put(state.game_data.player_states, loser_id, :fold),
        state: 2,  # 回到下注状态
        competition_player: nil,
        competition_target: nil
      }

      updated_state = %{state | game_data: game_data}

      # 广播比牌结果
      broadcast_competition_result(updated_state, requester_id, stored_target_id, winner_id)

      # 🎯 修复：启动比牌动画定时器，等待前端动画播放完成后再继续游戏
      # 参考旧项目和pot_blind实现，给前端足够时间播放比牌动画
      Logger.info("🎬 [TEEN_PATTI_COMP] 启动比牌动画定时器，等待前端动画播放完成")
      start_competition_animation_timer(updated_state, winner_id, requester_id, stored_target_id)
    end
  end



  defp handle_fold_request(state, player, message) do
    Logger.info("🃏 [TEEN_PATTI] 处理弃牌请求 - 玩家: #{player.numeric_id}")

    # 🎯 修复：玩家操作时取消操作定时器，重置暂离时间
    cancel_operation_timer(state)

    # 实现弃牌请求处理
    handle_fold_action(state, player.numeric_id)
  end

  defp handle_wait_recharge_request(state, player, message) do
    Logger.info("🃏 [TEEN_PATTI] 处理等待充值请求 - 玩家: #{player.numeric_id}")

    # 🎯 修复：只有当前操作玩家发起充值时才取消操作定时器
    updated_state = if state.game_data.current_player == player.numeric_id do
      Logger.info("🃏 [TEEN_PATTI_RECHARGE] 当前操作玩家发起充值，取消操作定时器")
      cancel_operation_timer(state)
    else
      Logger.info("🃏 [TEEN_PATTI_RECHARGE] 非当前操作玩家发起充值，不影响当前操作")
      state
    end

    # 🎯 关键：启动充值等待定时器（250秒，约4分钟）
    recharge_wait_time = updated_state.game_data.config.recharge_wait_time * 1000  # 转换为毫秒
    timer_ref = Process.send_after(self(), {:recharge_timeout, player.numeric_id}, recharge_wait_time)

    # 保存充值等待定时器引用到玩家特定的字段中，避免冲突
    # 使用玩家ID作为key来存储多个充值定时器
    recharge_timers = Map.get(updated_state, :recharge_timers, %{})
    updated_recharge_timers = Map.put(recharge_timers, player.numeric_id, timer_ref)
    state_with_timer = Map.put(updated_state, :recharge_timers, updated_recharge_timers)

    # 标记玩家为充值等待状态
    updated_players = Map.update!(state_with_timer.players, player.numeric_id, fn p ->
      Map.put(p, :waiting_recharge, true)
    end)

    final_state = %{state_with_timer | players: updated_players}

    Logger.info("🃏 [TEEN_PATTI_RECHARGE] 玩家#{player.numeric_id} 进入充值等待状态，等待时间: #{recharge_wait_time}ms")

    # 发送充值等待确认协议
    send_recharge_wait_confirm(final_state, player.numeric_id)

    final_state
  end

  @doc """
  处理充值超时
  """
  defp handle_recharge_timeout(state, player_id) do
    Logger.info("🃏 [TEEN_PATTI_RECHARGE] 玩家#{player_id} 充值超时")

    # 清除该玩家的充值定时器
    state_without_timer = cancel_recharge_timer(state, player_id)

    # 清除充值等待状态
    updated_players = Map.update!(state_without_timer.players, player_id, fn p ->
      Map.put(p, :waiting_recharge, false)
    end)

    updated_state = %{state_without_timer | players: updated_players}

    # 发送充值超时通知
    send_recharge_timeout_message(updated_state, player_id)

    # 充值超时后，如果是当前操作玩家，则自动弃牌
    if updated_state.game_data.current_player == player_id do
      Logger.info("🃏 [TEEN_PATTI_RECHARGE] 当前操作玩家充值超时，自动弃牌")
      handle_fold_action(updated_state, player_id)
    else
      Logger.info("🃏 [TEEN_PATTI_RECHARGE] 非当前操作玩家充值超时，不影响游戏流程")
      updated_state
    end
  end

  @doc """
  发送充值等待确认协议
  """
  defp send_recharge_wait_confirm(state, player_id) do
    player_seat = get_player_seat_by_id(state, player_id)

    message = %{
      "mainId" => 5,
      "subId" => 1013,  # SC_TEENPATTI_WAITRECHARGE_P
      "data" => %{
        "playerid" => player_id,
        "seatid" => player_seat,
        "waittime" => state.game_data.config.recharge_wait_time,  # 充值等待时间（秒）
        "state" => TeenPattiDataFormatter.format_game_state(:wait_recharge)
      }
    }

    Logger.info("🃏 [TEEN_PATTI_RECHARGE] 发送充值等待确认协议 - 玩家: #{player_id}")
    broadcast_to_room(state, message)
  end

  @doc """
  发送充值超时消息
  """
  defp send_recharge_timeout_message(state, player_id) do
    player_seat = get_player_seat_by_id(state, player_id)

    message = %{
      "mainId" => 5,
      "subId" => 1013,  # SC_TEENPATTI_WAITRECHARGE_P
      "data" => %{
        "playerid" => player_id,
        "seatid" => player_seat,
        "timeout" => true,  # 标记为超时
        "state" => TeenPattiDataFormatter.format_game_state(:betting)
      }
    }

    Logger.info("🃏 [TEEN_PATTI_RECHARGE] 发送充值超时消息 - 玩家: #{player_id}")
    broadcast_to_room(state, message)
  end

  @doc """
  取消指定玩家的充值等待定时器
  """
  defp cancel_recharge_timer(state, player_id \\ nil) do
    recharge_timers = Map.get(state, :recharge_timers, %{})

    if player_id do
      # 取消指定玩家的充值定时器
      case Map.get(recharge_timers, player_id) do
        nil ->
          Logger.debug("🚫 [TEEN_PATTI_RECHARGE] 玩家#{player_id}没有需要取消的充值定时器")
          state

        timer_ref ->
          Process.cancel_timer(timer_ref)
          updated_timers = Map.delete(recharge_timers, player_id)
          updated_state = Map.put(state, :recharge_timers, updated_timers)
          Logger.debug("🚫 [TEEN_PATTI_RECHARGE] 取消玩家#{player_id}的充值定时器")
          updated_state
      end
    else
      # 取消所有充值定时器
      Enum.each(recharge_timers, fn {_player_id, timer_ref} ->
        Process.cancel_timer(timer_ref)
      end)
      updated_state = Map.put(state, :recharge_timers, %{})
      Logger.debug("🚫 [TEEN_PATTI_RECHARGE] 取消所有充值定时器")
      updated_state
    end
  end

  @doc """
  处理玩家充值完成（在游戏中）
  """
  def handle_player_recharge_completed(state, player_id, recharge_amount) do
    Logger.info("🃏 [TEEN_PATTI_RECHARGE] 玩家#{player_id} 充值完成，金额: #{recharge_amount}")

    # 取消该玩家的充值等待定时器
    updated_state = cancel_recharge_timer(state, player_id)

    # 清除充值等待状态
    updated_players = Map.update!(updated_state.players, player_id, fn p ->
      Map.put(p, :waiting_recharge, false)
    end)

    final_state = %{updated_state | players: updated_players}

    # 发送充值完成通知
    send_recharge_completed_message(final_state, player_id, recharge_amount)

    # 如果是当前操作玩家，重新启动操作定时器
    if final_state.game_data.current_player == player_id do
      Logger.info("🃏 [TEEN_PATTI_RECHARGE] 充值完成，重新启动操作定时器")
      start_operation_timer(final_state, player_id)
    else
      Logger.info("🃏 [TEEN_PATTI_RECHARGE] 非当前操作玩家充值完成，不影响当前操作流程")
      final_state
    end
  end

  @doc """
  发送充值完成消息
  """
  defp send_recharge_completed_message(state, player_id, recharge_amount) do
    player_seat = get_player_seat_by_id(state, player_id)

    message = %{
      "mainId" => 5,
      "subId" => 1013,  # SC_TEENPATTI_WAITRECHARGE_P
      "data" => %{
        "playerid" => player_id,
        "seatid" => player_seat,
        "completed" => true,  # 标记为完成
        "amount" => recharge_amount,
        "state" => TeenPattiDataFormatter.format_game_state(:betting)
      }
    }

    Logger.info("🃏 [TEEN_PATTI_RECHARGE] 发送充值完成消息 - 玩家: #{player_id}, 金额: #{recharge_amount}")
    broadcast_to_room(state, message)
  end

  @doc """
  简化：直接设置机器人的no_fold字段
  """
  defp set_robot_no_fold(state, robot_id, no_fold_value) do
    # 直接更新机器人数据中的no_fold字段
    updated_robots = Map.update(state.game_data.robots, robot_id, nil, fn robot ->
      if robot do
        Map.put(robot, :no_fold, no_fold_value)
      else
        robot
      end
    end)

    # 更新游戏数据
    updated_game_data = Map.put(state.game_data, :robots, updated_robots)
    updated_state = %{state | game_data: updated_game_data}

    Logger.info("🎴 [NO_FOLD] 机器人#{robot_id}的no_fold字段已设置为#{no_fold_value}")

    updated_state
  end

  @doc """
  简化：重置所有机器人的no_fold字段（新局开始时调用）
  """
  defp reset_robots_no_fold(state) do
    # 重置所有机器人的no_fold字段为false
    updated_robots = Map.new(state.game_data.robots, fn {robot_id, robot} ->
      {robot_id, Map.put(robot, :no_fold, false)}
    end)

    # 更新游戏数据
    updated_game_data = Map.put(state.game_data, :robots, updated_robots)
    updated_state = %{state | game_data: updated_game_data}

    Logger.info("🎴 [NO_FOLD] 已重置所有机器人的no_fold字段")

    updated_state
  end

  # ==================== 下注相关辅助函数 ====================



  # 🎯 修复：执行下注动作 - 增加总下注限额检查
  defp execute_bet_action(state, player_id, bet_amount, fill) do
    Logger.info("🃏 [BET_EXECUTE] 玩家#{player_id} 下注:#{bet_amount} 倍数:#{fill} 底池:#{state.game_data.pot_total}")

    # 🎯 修复：玩家操作时取消操作定时器，重置暂离时间
    cancel_operation_timer(state)

    # 扣除玩家金币
    subtract_player_points(state, player_id, bet_amount)

    # 更新下注记录
    new_pot_total = state.game_data.pot_total + bet_amount
    pot_limit = state.game_data.config.pot_limit

    Logger.info("🃏 [TEEN_PATTI_BET] 下注后总底池: #{new_pot_total}, 限额: #{pot_limit}")

    # 🎯 关键修复：根据fill值更新current_times（参考旧项目Fill函数）
    # 旧项目: m_nCurTimes *= GetChipMuti(nIndex);
    new_current_times = if fill > 1 do
      # 加注时，倍数乘以fill值
      state.game_data.current_times * fill
    else
      # 跟注时，保持当前倍数
      state.game_data.current_times
    end

    Logger.info("🎯 [TEEN_PATTI_BET] 下注倍数更新: #{state.game_data.current_times} -> #{new_current_times} (fill: #{fill})")

    game_data = %{state.game_data |
      player_bets: Map.put(state.game_data.player_bets, player_id, bet_amount),
      player_total_bets: Map.update(state.game_data.player_total_bets, player_id, bet_amount, &(&1 + bet_amount)),
      pot_total: new_pot_total,
      current_bet: max(state.game_data.current_bet || 0, bet_amount),
      turn_count: state.game_data.turn_count + 1,
      current_times: new_current_times  # 🎯 关键：更新下注倍数
    }

    updated_state = %{state | game_data: game_data}

    # 广播下注协议
    broadcast_bet_action(updated_state, player_id, bet_amount, fill)

    # 🎯 新增：检查是否达到总下注限额
    if new_pot_total >= pot_limit do
      Logger.info("🃏 [TEEN_PATTI_BET] ✅ 达到总下注限额 (#{new_pot_total}/#{pot_limit})，检查是否需要进入结算")
      check_pot_limit_settlement(updated_state)
    else
      Logger.info("🃏 [TEEN_PATTI_BET] 未达到限额，继续游戏 (#{new_pot_total}/#{pot_limit})")
      # 轮到下一个玩家
      next_player_turn(updated_state)
    end
  end

  # 🎯 新增：检查总下注限额是否需要结算
  defp check_pot_limit_settlement(state) do
    Logger.info("🃏 [POT_LIMIT_CHECK] 检查总下注限额结算")

    # 获取所有活跃玩家
    active_players = get_active_players(state)

    # 检查是否还有玩家没有下注过
    players_without_bet = active_players
    |> Enum.filter(fn player ->
      total_bet = Map.get(state.game_data.player_total_bets, player.numeric_id, 0)
      total_bet == 0
    end)

    Logger.info("🃏 [TEEN_PATTI_POT_LIMIT] 活跃玩家数: #{length(active_players)}")
    Logger.info("🃏 [TEEN_PATTI_POT_LIMIT] 未下注玩家数: #{length(players_without_bet)}")

    if length(players_without_bet) > 0 do
      Logger.info("🃏 [TEEN_PATTI_POT_LIMIT] 还有玩家未下注，允许继续下注")
      Logger.info("🃏 [TEEN_PATTI_POT_LIMIT] 未下注玩家: #{inspect(Enum.map(players_without_bet, & &1.numeric_id))}")

      # 轮到下一个玩家（优先让未下注的玩家下注）
      next_player = find_next_unbet_player(state, players_without_bet) || find_next_active_player(state, state.game_data.current_player)

      if next_player do
        Logger.info("🃏 [TEEN_PATTI_POT_LIMIT] 轮到下一个玩家: #{next_player.numeric_id}")
        start_player_turn(state, next_player.numeric_id)
      else
        Logger.info("🃏 [TEEN_PATTI_POT_LIMIT] 无法找到下一个玩家，进入结算")
        settle_game(state)
      end
    else
      Logger.info("🃏 [TEEN_PATTI_POT_LIMIT] 所有玩家都已下注且达到限额，进入结算阶段")
      settle_game(state)
    end
  end

  # 查找下一个未下注的玩家
  defp find_next_unbet_player(state, unbet_players) do
    # 按座位顺序排序，找到第一个未下注的玩家
    unbet_players
    |> Enum.sort_by(fn player -> get_player_seat_by_id(state, player.numeric_id) end)
    |> List.first()
  end

  # 开始指定玩家的回合
  defp start_player_turn(state, player_id) do
    Logger.info("🃏 [TEEN_PATTI_TURN] 开始玩家回合 - 玩家: #{player_id}")

    # 🎯 修复：取消之前的操作定时器，避免旧定时器干扰
    cancel_operation_timer(state)

    # 更新当前操作玩家
    game_data = %{state.game_data | current_player: player_id}
    updated_state = %{state | game_data: game_data}

    # 发送等待操作协议
    send_wait_operation_message(updated_state, player_id)

    # 🎯 修复：启动操作定时器
    timer_state = start_operation_timer(updated_state, player_id)

    # 🎯 修复：如果是机器人，安排AI操作
    if is_robot_player?(timer_state, player_id) do
      schedule_robot_action(timer_state, player_id)
    end

    timer_state
  end

  # 🎯 修复：广播下注动作 - 完全匹配前端期望格式
  defp broadcast_bet_action(state, player_id, bet_amount, fill) do
    player = Map.get(state.players, player_id)
    player_seat = get_player_seat_by_id(state, player_id)
    is_robot = Map.get(player, :is_robot, false)

    # 🎯 关键：计算玩家总下注金额（mybetall字段）
    player_total_bet = Map.get(state.game_data.player_total_bets, player_id, 0)

    # 🎯 完全匹配前端TPGameCore.ts onBetData函数期望的数据结构
    message = %{
      "mainId" => 5,
      "subId" => 1003,  # SC_TEENPATTI_BET_P
      "data" => %{
        "playerid" => player_id,                    # 前端: Common.toInt(info["playerid"])
        "seatid" => player_seat,                    # 🎯 座位号（旧项目有此字段）
        "bettype" => 1,                             # 前端: Common.toInt(info["bettype"]) - 1=正常下注
        "bet" => bet_amount,                        # 前端: Common.toInt(info["bet"])
        "allbet" => state.game_data.pot_total,      # 前端: this.allbet = Common.toInt(info["allbet"])
        "curtimes" => state.game_data.current_times,   # 🎯 修复：前端: this.curtimes = Common.toInt(info["curtimes"])
        "mybetall" => player_total_bet,             # 前端: this.mybetall = Common.toInt(info["mybetall"])
        "fill" => fill,                             # 🎯 关键：下注倍数，1=跟注，2=加注
        "state" => TeenPattiDataFormatter.format_game_state(:bet)  # 游戏状态
      }
    }

    Logger.info("🃏 [TEEN_PATTI_BET] 玩家下注 - #{player_id}(#{if is_robot, do: "机器人", else: "真实"}): #{bet_amount}, 底池: #{state.game_data.pot_total}")

    broadcast_to_room(state, message)
  end

  # 发送等待充值消息
  defp send_wait_recharge_message(state, player_id, need_money) do
    player_seat = get_player_seat_by_id(state, player_id)

    message = %{
      "mainId" => 5,
      "subId" => 1013,
      "data" => %{
        "playerid" => player_id,
        "seatid" => player_seat,
        "needmoney" => need_money,
        "state" => TeenPattiDataFormatter.format_game_state(:wait_recharge)
      }
    }

    Logger.info("🃏 [TEEN_PATTI_PROTOCOL_SEND] 发送等待充值协议 - 玩家: #{player_id}, 需要金额: #{need_money}")
    broadcast_to_room(state, message)
  end

  # 广播比牌请求 (SC_TEENPATTI_COMPETITION_P - 1007)
  defp broadcast_competition_request(state, requester_id, target_id) do
    requester_seat = get_player_seat_by_id(state, requester_id)
    target_seat = get_player_seat_by_id(state, target_id)

    # 🎯 修复：构建前端期望的比牌协议数据格式
    # 参考旧项目C++逻辑：optseatid是被比牌玩家，compseatid是发起比牌玩家
    # s["optseatid"] = pDstPlayer->GetSeat();   // 被比牌的玩家座位
    # s["compseatid"] = pPlayer->GetSeat();     // 比牌发起者座位
    message = %{
      "mainId" => 5,
      "subId" => 1007,  # SC_TEENPATTI_COMPETITION_P
      "data" => %{
        "state" => TeenPattiDataFormatter.format_game_state(:competition),  # 3 = 比牌状态
        "optseatid" => target_seat,       # 🎯 修复：被比牌的玩家座位号（前端会给这个玩家显示确认对话框）
        "compseatid" => requester_seat,   # 🎯 修复：发起比牌的玩家座位号
        "waittime" => state.game_data.config.competition_time || 30
      }
    }

    Logger.info("🃏 [COMP_REQUEST] 比牌请求 发起者:#{requester_id}(座位#{requester_seat}) 目标:#{target_id}(座位#{target_seat}) [5][1007]")

    broadcast_to_room(state, message)
  end

  # 广播比牌确认 (SC_TEENPATTI_COMPCONFIRM_P - 1009)
  defp broadcast_competition_confirm(state, confirmer_id, confirm) do
    confirmer_seat = get_player_seat_by_id(state, confirmer_id)
    requester_seat = get_player_seat_by_id(state, state.game_data.competition_player)
    target_seat = get_player_seat_by_id(state, state.game_data.competition_target)

    # 🎯 修复：构建前端期望的比牌确认协议数据格式
    # 参考前端 onCompConfirm 函数期望的数据结构
    # 注意：这里只是确认阶段的协议，不包含比牌结果
    message = %{
      "mainId" => 5,
      "subId" => 1009,  # SC_TEENPATTI_COMPCONFIRM_P
      "data" => %{
        "playerid" => confirmer_id,      # 🎯 关键：确认玩家ID
        "seatid" => confirmer_seat,      # 🎯 关键：确认玩家座位号
        "confirm" => confirm             # 🎯 关键：确认结果 true/false
        # 注意：这里不包含compseat和optseat，那些是比牌结果协议才有的
      }
    }

    Logger.info("🃏 [COMP_CONFIRM_SEND] 确认者:#{confirmer_id}(座位#{confirmer_seat}) 结果:#{confirm} [5][1009]")

    broadcast_to_room(state, message)
  end

  # ==================== 辅助函数 ====================

  # 计算比牌金额（参考前端callCompetition逻辑）
  defp calculate_competition_amount(state, player_id) do
    # 🎯 关键修复：基础金额使用current_times而不是turn_count
    # 参考前端: let curbet=this.curtimes*this.difen;
    base_amount = state.game_data.current_times * state.game_data.config.base_bet

    # 检查玩家是否已看牌
    is_looked = Map.get(state.game_data.player_seen_status, player_id, false)

    # 如果已看牌，金额翻倍
    amount = if is_looked do
      base_amount * 2
    else
      base_amount
    end

    # 应用封顶限制（比牌限制）
    final_amount = min(amount, state.game_data.config.chaal_limit)

    Logger.info("🎯 [TEEN_PATTI_COMP_CALC] 基础计算: #{state.game_data.current_times} × #{state.game_data.config.base_bet} = #{base_amount}")
    Logger.info("🎯 [TEEN_PATTI_COMP_CALC] 是否看牌: #{is_looked}, #{if is_looked, do: "翻倍后: #{amount}", else: "无需翻倍"}")
    Logger.info("🎯 [TEEN_PATTI_COMP_CALC] 封顶限制: #{state.game_data.config.chaal_limit}, 最终金额: #{final_amount}")

    final_amount
  end

  # 发送发牌消息给单个玩家（重连时使用）
  defp send_dealing_message_to_player(state, player) do
    active_players = get_active_players(state)
    if length(active_players) > 0 do
      first_player = hd(active_players)
      first_player_seat = get_player_seat_by_id(state, first_player.numeric_id)

      # 🎯 修复：重连时也需要发送玩家自己的卡牌数据
      player_cards = Map.get(state.game_data.player_cards, player.numeric_id, [])
      formatted_cards = TeenPattiDataFormatter.format_cards_for_client(player_cards)
      player_seat = get_player_seat_by_id(state, player.numeric_id)

      message = %{
        "mainId" => 5,
        "subId" => 1001,
        "data" => %{
          "state" => TeenPattiDataFormatter.format_game_state(:send_card),
          "playerid" => first_player.numeric_id,
          "seatid" => first_player_seat,
          "bettype" => 0,
          "curtimes" => state.game_data.current_times,  # 🎯 修复：使用current_times
          "allbet" => state.game_data.pot_total,
          "turnnum" => state.game_data.turn_count,
          "round" => state.game_data.round || 1,
          "dealtime" => state.game_data.config.send_card_time,
          # 🎯 关键修复：添加重连玩家自己的卡牌数据
          "cards" => formatted_cards,
          "myseat" => player_seat,
          "myplayerid" => player.numeric_id
        }
      }

      Logger.info("🃏 [TEEN_PATTI_REJOIN] 发送发牌协议给重连玩家: #{player.numeric_id}, 卡牌: #{inspect(formatted_cards)}")
      # 使用直接广播方式发送发牌协议
      CypridinaWeb.Endpoint.broadcast!("user:#{player.user_id}", "private_message", message)
    end
  end

  # 发送下注状态给单个玩家（重连时使用）
  defp send_betting_state_to_player(state, player) do
    if state.game_data.current_player do
      current_player_seat = get_player_seat_by_id(state, state.game_data.current_player)

      # 🎯 修复：重连时也需要包含下注金额信息
      current_bet = state.game_data.current_bet || state.game_data.config.base_bet
      is_player_seen = Map.get(state.game_data.player_seen_status, state.game_data.current_player, false)

      chaal_amount = if is_player_seen do
        current_bet * 2
      else
        current_bet
      end

      chaal_double_amount = if is_player_seen do
        current_bet * 4
      else
        current_bet * 2
      end

      message = %{
        "mainId" => 5,
        "subId" => 1014,  # SC_TEENPATTI_WAITOPT_P
        "data" => %{
          "state" => TeenPattiDataFormatter.format_game_state(:betting),
          "playerid" => state.game_data.current_player,
          "seatid" => current_player_seat,
          "waittime" => 30,  # 默认等待时间
          "allbet" => state.game_data.pot_total,
          # 🎯 修复：重连时也包含下注金额信息
          "currentbet" => current_bet,
          "chaalamount" => chaal_amount,
          "chaaldoubleamount" => chaal_double_amount,
          "isseen" => is_player_seen
        }
      }

      Logger.info("🃏 [TEEN_PATTI_REJOIN] 发送下注状态给重连玩家: #{player.numeric_id}")
      # 使用直接广播方式发送下注状态
      CypridinaWeb.Endpoint.broadcast!("user:#{player.user_id}", "private_message", message)
    end
  end

  # 发送比牌状态给单个玩家（重连时使用）
  defp send_competition_state_to_player(state, player) do
    if state.game_data.current_player do
      current_player_seat = get_player_seat_by_id(state, state.game_data.current_player)

      message = %{
        "mainId" => 5,
        "subId" => 1007,  # SC_TEENPATTI_COMPETITION_P
        "data" => %{
          "state" => TeenPattiDataFormatter.format_game_state(:competition),
          "playerid" => state.game_data.current_player,
          "seatid" => current_player_seat,
          "allbet" => state.game_data.pot_total
        }
      }

      Logger.info("🃏 [TEEN_PATTI_REJOIN] 发送比牌状态给重连玩家: #{player.numeric_id}")
      # 🎯 修复：使用正确的user_id获取方式
      user_id = Map.get(player, :user_id) || player.numeric_id
      CypridinaWeb.Endpoint.broadcast!("user:#{user_id}", "private_message", message)
    end
  end

  # 🎯 修复：处理玩家操作超时 - 完善暂离规则
  defp handle_player_operation_timeout(state, player_id) do
    Logger.info("🃏 [TIMEOUT] 玩家#{player_id} 操作超时 当前玩家:#{state.game_data.current_player} 状态:#{state.game_data.state}")

    # 检查是否是当前操作玩家超时
    if state.game_data.current_player == player_id do
      player = Map.get(state.players, player_id)

      # 检查游戏状态是否为下注或比牌状态
      game_state = state.game_data.state
      is_bet_or_competition_state = game_state == 2 or game_state == 3  # 2=下注状态, 3=比牌状态

      Logger.info("🃏 [TEEN_PATTI_TIMEOUT] 是否为下注或比牌状态: #{is_bet_or_competition_state}")

      cond do
        # 🎯 修复：机器人不应该超时，因为它们有AI调度
        # 如果机器人超时了，说明AI调度有问题，记录错误并执行AI动作
        player && Map.get(player, :is_robot, false) ->
          Logger.error("🃏 [TEEN_PATTI_TIMEOUT] ❌ 机器人不应该超时！AI调度可能有问题 - 机器人: #{player_id}")
          Logger.error("🃏 [TEEN_PATTI_TIMEOUT] 🤖 强制执行机器人AI动作")
          execute_robot_action(state, player_id)

        # 真实玩家在下注或比牌状态超时 -> 进入暂离模式
        player && !Map.get(player, :is_robot, false) && is_bet_or_competition_state ->
          Logger.info("🃏 [TEEN_PATTI_TIMEOUT] ✅ 真实玩家在下注/比牌状态超时，进入暂离模式")
          enter_zanli_mode(state, player_id)

        # 真实玩家在其他状态超时 -> 直接弃牌，不进入暂离
        player && !Map.get(player, :is_robot, false) ->
          Logger.info("🃏 [TEEN_PATTI_TIMEOUT] ⚠️ 真实玩家在非下注/比牌状态超时，直接弃牌")
          handle_fold_action(state, player_id)

        # 未知玩家类型 -> 直接弃牌
        true ->
          Logger.warning("🃏 [TEEN_PATTI_TIMEOUT] ❌ 未知玩家类型操作超时，直接弃牌")
          handle_fold_action(state, player_id)
      end
    else
      Logger.warning("🃏 [TEEN_PATTI_TIMEOUT] ⚠️ 非当前操作玩家超时，忽略")
      state
    end
  end

  # 开始下注阶段
  defp start_betting_phase(state) do
    Logger.info("🃏 [TEEN_PATTI] 开始下注阶段")

    # 更新游戏状态为下注阶段
    game_data = %{state.game_data | state: 2}  # 🎯 修复：bet状态
    new_state = %{state | game_data: game_data}

    # 确定第一个操作玩家并开始回合
    start_first_player_turn(new_state)
  end

  # 开始第一个玩家的回合 - 🎯 修复：庄家先操作
  defp start_first_player_turn(state) do
    active_players = get_active_players(state)

    if length(active_players) > 0 do
      # 🎯 修复：选择庄家作为第一个操作的玩家（参考旧项目逻辑）
      banker_id = state.game_data.banker_id
      first_player = case Enum.find(active_players, fn p -> p.numeric_id == banker_id end) do
        nil ->
          # 如果庄家不在活跃玩家中，选择第一个活跃玩家
          Logger.warning("🃏 [TEEN_PATTI] 庄家 #{banker_id} 不在活跃玩家中，选择第一个活跃玩家")
          hd(active_players)
        banker_player ->
          Logger.info("🃏 [TEEN_PATTI] 庄家 #{banker_id} 先开始操作")
          banker_player
      end

      # 更新当前操作玩家
      game_data = %{state.game_data |
        current_player: first_player.numeric_id,
        turn_count: 1,
        current_times: 1,  # 🎯 修复：重置下注倍数
        turn_start_time: System.system_time(:millisecond)
      }

      updated_state = %{state | game_data: game_data}

      # 发送等待操作协议
      send_wait_operation_message(updated_state, first_player.numeric_id)

      # 启动操作定时器
      timer_state = start_operation_timer(updated_state, first_player.numeric_id)

      # 如果是机器人，安排AI操作
      if is_robot_player?(timer_state, first_player.numeric_id) do
        schedule_robot_action(timer_state, first_player.numeric_id)
      end

      timer_state
    else
      Logger.warning("🃏 [TEEN_PATTI] 没有活跃玩家，无法开始下注阶段")
      state
    end
  end

  # 发送等待操作消息
  defp send_wait_operation_message(state, player_id) do
    Logger.info("🃏 [TEEN_PATTI] 发送等待操作消息给玩家: #{player_id}")

    # 🎯 修复：获取庄家座位信息，确保前端能正确显示庄家
    banker_seat = state.game_data.banker_seat || 1

    # 🎯 关键修复：获取操作玩家的座位号
    operation_seat = get_player_seat_by_id(state, player_id)

    # 🎯 关键修复：确定当前游戏状态，影响前端按钮显示逻辑
    current_game_state = case state.game_data.state do
      3 -> :competition  # 比牌状态
      _ -> :bet         # 下注状态
    end

    # 🎯 关键修复：检查操作玩家是否看过牌，影响下注金额计算
    is_player_seen = Map.get(state.game_data.player_seen_status, player_id, false)

    # 🎯 关键修复：完全匹配前端按钮金额计算逻辑
    # 前端逻辑：curbet = difen * curtimes * (isMelook ? 2 : 1)
    base_bet = state.game_data.config.base_bet  # difen
    current_times = state.game_data.current_times  # curtimes
    chaal_limit = state.game_data.config.chaal_limit  # chaallimit

    # 跟注金额计算（对应前端 curbet）
    chaal_amount = if is_player_seen do
      base_bet * current_times * 2  # 看牌后：difen * curtimes * 2
    else
      base_bet * current_times  # 盲注：difen * curtimes
    end

    # 应用封顶限制
    chaal_amount = if chaal_limit > 0 and chaal_amount > chaal_limit do
      chaal_limit
    else
      chaal_amount
    end

    # 加注金额计算（对应前端 doubleCurbet = curbet * 2）
    chaal_double_amount = chaal_amount * 2

    # 应用封顶限制
    chaal_double_amount = if chaal_limit > 0 and chaal_double_amount > chaal_limit do
      chaal_limit
    else
      chaal_double_amount
    end

    Logger.info("🎯 [TEEN_PATTI_PROTOCOL_SEND] ========== 按钮金额计算 ==========")
    Logger.info("🎯 [TEEN_PATTI_PROTOCOL_SEND] 基础参数: difen=#{base_bet}, curtimes=#{current_times}, 看牌=#{is_player_seen}")
    Logger.info("🎯 [TEEN_PATTI_PROTOCOL_SEND] 跟注金额: #{chaal_amount} (#{base_bet} × #{current_times}#{if is_player_seen, do: " × 2", else: ""})")
    Logger.info("🎯 [TEEN_PATTI_PROTOCOL_SEND] 加注金额: #{chaal_double_amount} (#{chaal_amount} × 2)")
    Logger.info("🎯 [TEEN_PATTI_PROTOCOL_SEND] 封顶限制: #{chaal_limit}")

    # 构建等待操作协议 (SC_TEENPATTI_WAITOPT_P - 1014)
    # 🎯 修复：确保协议数据格式与旧项目完全一致，包含前端按钮显示所需的下注金额
    message = %{
      "mainId" => 5,
      "subId" => 1014,
      "data" => %{
        "state" => TeenPattiDataFormatter.format_game_state(current_game_state),  # 🎯 关键：正确的游戏状态
        "optseatid" => operation_seat,  # 🎯 关键：操作座位号（前端用于判断是否轮到自己）
        "banker" => banker_seat,  # 庄家座位
        "curtimes" => state.game_data.current_times,  # 🎯 修复：当前下注倍数
        "allbet" => state.game_data.pot_total,  # 总下注
        "waittime" => state.game_data.config.operation_wait_time,  # 等待时间
        "turnnum" => state.game_data.turn_count,  # 轮数
        "optsumtime" => state.game_data.config.operation_wait_time,  # 总操作时间
        # 🎯 关键修复：添加前端按钮显示所需的下注金额信息
        "chaalamount" => chaal_amount,  # 跟注金额（前端Chaal按钮显示）
        "chaaldoubleamount" => chaal_double_amount,  # 加注金额（前端ChaalDouble按钮显示）
        "isseen" => is_player_seen  # 玩家是否看过牌
      }
    }

    Logger.info("🃏 [TEEN_PATTI_TURN] 等待玩家操作 - 玩家: #{player_id}, 跟注: #{chaal_amount}, 加注: #{chaal_double_amount}")

    broadcast_to_room(state, message)

    # 🎯 修复：不在这里设置超时定时器，因为 start_operation_timer 已经设置了
    # 避免重复设置导致的超时时间错误
    # Process.send_after(self(), {:operation_timeout, player_id}, state.game_data.config.operation_wait_time * 1000)
  end

  # 🎯 关键修复：为玩家分配固定座位号
  # 前端期望座位号稳定，不会因为其他玩家加入而改变
  defp assign_player_seat(state, player_id) do
    Logger.info("🃏 [SEAT_ASSIGN] 为玩家#{player_id}分配座位")

    # 🎯 修复：检查玩家是否已经有座位（包括传入的玩家对象）
    existing_player = Map.get(state.players, player_id)
    case existing_player do
      %{seat: seat} when is_integer(seat) and seat > 0 ->
        Logger.info("🃏 [SEAT_ASSIGN] 玩家#{player_id} 已有座位:#{seat}")
        seat
      _ ->
        occupied_seats = get_occupied_seats(state)
        available_seat = find_next_available_seat(occupied_seats, state.max_players)
        Logger.info("🃏 [SEAT_ASSIGN] 玩家#{player_id} 分配新座位:#{available_seat}")
        available_seat
    end
  end

  # 获取已占用的座位列表
  defp get_occupied_seats(state) do
    state.players
    |> Map.values()
    |> Enum.map(fn player -> Map.get(player, :seat) end)
    |> Enum.filter(fn seat -> is_integer(seat) and seat > 0 end)
    |> Enum.sort()
  end

  # 找到下一个可用座位 (从1开始)
  defp find_next_available_seat(occupied_seats, max_players) do
    1..max_players
    |> Enum.find(fn seat -> seat not in occupied_seats end)
    |> case do
      nil -> 1  # 如果所有座位都被占用，返回座位1
      seat -> seat
    end
  end

  # 根据玩家ID获取座位号 (对照旧项目：座位从1开始)
  defp get_player_seat_by_id(state, player_id) do
    # 🎯 关键修复：直接从玩家数据中获取固定座位号
    # 不再按ID排序重新分配，确保座位稳定
    player_data = Map.get(state.players, player_id)
    Logger.debug("🔍 [SEAT_DEBUG] 查询玩家座位 - 玩家: #{player_id}, 数据: #{inspect(player_data)}")

    case player_data do
      %{seat: seat} when is_integer(seat) and seat > 0 ->
        Logger.debug("🔍 [SEAT_DEBUG] 找到座位 - 玩家: #{player_id}, 座位: #{seat}")
        seat
      _ ->
        Logger.warning("🃏 [TEEN_PATTI_SEAT] 玩家没有座位信息 - 玩家: #{player_id}, 返回默认座位1")
        Logger.debug("🔍 [SEAT_DEBUG] 玩家数据结构: #{inspect(player_data)}")
        1
    end
  end

  # 🎯 修复：找到下一个活跃玩家（用于比牌后继续游戏）
  defp find_next_active_player(state, current_player_id) do
    # 🎯 关键修复：获取所有玩家（包括已弃牌的），按座位排序
    all_players = state.players
    |> Map.values()
    |> Enum.filter(fn player ->
      # 只考虑参与游戏的玩家（game_state为:playing）
      Map.get(player, :game_state) == :playing
    end)
    |> Enum.sort_by(fn player ->
      get_player_seat_by_id(state, player.numeric_id)
    end)

    Logger.debug("🔄 [TEEN_PATTI_NEXT_PLAYER] 所有参与玩家（按座位排序）: #{inspect(Enum.map(all_players, &(&1.numeric_id)))}")

    # 找到当前玩家的座位索引
    current_index = Enum.find_index(all_players, fn player ->
      player.numeric_id == current_player_id
    end)

    Logger.debug("🔄 [TEEN_PATTI_NEXT_PLAYER] 当前玩家 #{current_player_id} 的索引: #{current_index}")

    case current_index do
      nil ->
        # 当前玩家不在列表中，选择第一个未弃牌的玩家
        Logger.warning("🔄 [TEEN_PATTI_NEXT_PLAYER] 当前玩家 #{current_player_id} 不在参与列表中")
        find_first_active_player(state, all_players)
      index ->
        # 🎯 关键修复：从下一个座位开始，找到第一个未弃牌的玩家
        find_next_active_from_index(state, all_players, index)
    end
  end

  # 🎯 新增：从指定索引开始找下一个未弃牌的玩家
  defp find_next_active_from_index(state, all_players, current_index) do
    total_players = length(all_players)

    # 从下一个位置开始循环查找
    Enum.reduce_while(1..total_players, nil, fn offset, _acc ->
      next_index = rem(current_index + offset, total_players)
      next_player = Enum.at(all_players, next_index)

      # 检查该玩家是否未弃牌
      not_folded = Map.get(state.game_data.player_states, next_player.numeric_id, :none) != :fold

      Logger.debug("🔄 [TEEN_PATTI_NEXT_PLAYER] 检查玩家 #{next_player.numeric_id}(座位#{get_player_seat_by_id(state, next_player.numeric_id)}): 未弃牌=#{not_folded}")

      if not_folded do
        Logger.info("🔄 [TEEN_PATTI_NEXT_PLAYER] 找到下一个活跃玩家: #{next_player.numeric_id}(座位#{get_player_seat_by_id(state, next_player.numeric_id)})")
        {:halt, next_player}
      else
        {:cont, nil}
      end
    end)
  end

  # 🎯 新增：找到第一个未弃牌的玩家
  defp find_first_active_player(state, all_players) do
    Enum.find(all_players, fn player ->
      not_folded = Map.get(state.game_data.player_states, player.numeric_id, :none) != :fold
      Logger.debug("🔄 [TEEN_PATTI_NEXT_PLAYER] 检查第一个活跃玩家 #{player.numeric_id}: 未弃牌=#{not_folded}")
      not_folded
    end)
  end

  # 开始下一轮
  defp start_next_round(state) do
    Logger.info("🃏 [TEEN_PATTI] 开始下一轮游戏")

    # 重置游戏状态并开始新游戏
    reset_state = reset_game_state(state)
    on_game_start(reset_state)
  end

  # 重置游戏状态
  defp reset_game_state(state) do
    Logger.info("🃏 [TEEN_PATTI] 重置游戏状态")

    # 🎯 修复：重置等待下一局的玩家状态为正常等待状态
    updated_players =
      state.players
      |> Enum.map(fn {player_id, player} ->
        updated_player = case Map.get(player, :game_state) do
          :waiting_next_round ->
            Logger.info("🃏 [TEEN_PATTI] 重置玩家状态: #{player_id} 从 waiting_next_round -> waiting")
            Map.put(player, :game_state, :waiting)
          _ ->
            player
        end
        {player_id, updated_player}
      end)
      |> Enum.into(%{})

    # 重置游戏数据 - 🎯 修复：保留庄家信息用于轮换
    game_data = %{state.game_data |
      state: 0,  # 🎯 修复：使用数字0而不是:start
      player_cards: %{},
      player_bets: %{},
      player_total_bets: %{},
      player_seen_status: %{},
      current_player: nil,
      turn_count: 0,
      current_times: 1,  # 🎯 修复：重置下注倍数
      pot_total: 0,
      round: state.game_data.round + 1,  # 增加轮次而不是重置为0
      # 🎯 修复：保留庄家信息，在下次游戏开始时会重新确定庄家（轮换）
      player_states: %{},
      deck: [],
      current_bet: 0,
      competition_player: nil,
      competition_target: nil
    }

    %{state | players: updated_players, game_data: game_data}
  end

  # ==================== 暂离模式处理 ====================

  # 🎯 简化：移除额外的暂离定时器逻辑，直接在操作超时时判断暂离

  # 🎯 简化：玩家进入暂离模式
  defp enter_zanli_mode(state, player_id) do
    Logger.info("🃏 [ZANLI] 玩家#{player_id} 进入暂离模式")

    # 更新玩家状态为暂离
    updated_players = Map.update!(state.players, player_id, fn player ->
      Map.put(player, :zanli_mode, true)
    end)

    updated_state = %{state | players: updated_players}

    # 🎯 关键修复：发送暂离通知协议 XS_PLAYER_ZANLI_P (MainID=4, SubID=22)
    send_zanli_notification(updated_state, player_id)

    # 🎯 关键修复：发送暂离成功协议 SC_ROOM_ZANLI_SUCCESS_P (MainID=4, SubID=9)
    send_zanli_success_message(updated_state, player_id)

    # 自动弃牌
    handle_fold_action(updated_state, player_id)
  end

  # 🎯 修复：玩家退出暂离模式
  defp exit_zanli_mode(state, player_id) do
    Logger.info("🃏 [ZANLI] 玩家#{player_id} 退出暂离模式")

    # 更新玩家状态
    updated_players = Map.update!(state.players, player_id, fn player ->
      Map.put(player, :zanli_mode, false)
    end)

    updated_state = %{state | players: updated_players}

    # 🎯 关键修复：发送暂离回来成功协议 SC_ROOM_ZANLI_COMBACK_SUCCESS_P (MainID=4, SubID=11)
    send_zanli_comback_success_message(updated_state, player_id)

    updated_state
  end

  # 🎯 修复：发送暂离通知协议 XS_PLAYER_ZANLI_P
  defp send_zanli_notification(state, player_id) do
    player = Map.get(state.players, player_id)

    # XS_PLAYER_ZANLI_P 协议 (MainID=4, SubID=22)
    message = %{
      "mainId" => 4,
      "subId" => 22,  # XS_PLAYER_ZANLI_P
      "data" => %{
        "playerid" => player_id
      }
    }

    Logger.info("🃏 [ZANLI_NOTIFY] 玩家#{player_id} 暂离通知 [4][22]")

    # 发送给指定玩家
    try do
      CypridinaWeb.Endpoint.broadcast!("user:#{player.user_id}", "private_message", message)
      Logger.info("🃏 [TEEN_PATTI_ZANLI] ✅ 暂离通知协议发送成功")
    rescue
      error ->
        Logger.error("🃏 [TEEN_PATTI_ZANLI] ❌ 暂离通知协议发送失败: #{inspect(error)}")
    end
  end

  # 🎯 修复：发送暂离成功协议 SC_ROOM_ZANLI_SUCCESS_P
  defp send_zanli_success_message(state, player_id) do
    player = Map.get(state.players, player_id)

    # SC_ROOM_ZANLI_SUCCESS_P 协议 (MainID=4, SubID=9)
    message = %{
      "mainId" => 4,
      "subId" => 9,  # SC_ROOM_ZANLI_SUCCESS_P
      "data" => %{
        "playerid" => player_id
      }
    }

    Logger.info("🃏 [ZANLI_SUCCESS] 玩家#{player_id} 暂离成功 [4][9]")

    # 发送给指定玩家
    try do
      CypridinaWeb.Endpoint.broadcast!("user:#{player.user_id}", "private_message", message)
      Logger.info("🃏 [TEEN_PATTI_ZANLI] ✅ 暂离成功协议发送成功")
    rescue
      error ->
        Logger.error("🃏 [TEEN_PATTI_ZANLI] ❌ 暂离成功协议发送失败: #{inspect(error)}")
    end
  end

  # 🎯 修复：发送暂离回来成功协议 SC_ROOM_ZANLI_COMBACK_SUCCESS_P
  defp send_zanli_comback_success_message(state, player_id) do
    player = Map.get(state.players, player_id)

    # SC_ROOM_ZANLI_COMBACK_SUCCESS_P 协议 (MainID=4, SubID=11)
    message = %{
      "mainId" => 4,
      "subId" => 11,  # SC_ROOM_ZANLI_COMBACK_SUCCESS_P
      "data" => %{
        "playerid" => player_id
      }
    }

    Logger.info("🃏 [ZANLI_COMEBACK] 玩家#{player_id} 暂离回来成功 [4][11]")

    # 发送给指定玩家
    try do
      CypridinaWeb.Endpoint.broadcast!("user:#{player.user_id}", "private_message", message)
      Logger.info("🃏 [TEEN_PATTI_ZANLI] ✅ 暂离回来成功协议发送成功")
    rescue
      error ->
        Logger.error("🃏 [TEEN_PATTI_ZANLI] ❌ 暂离回来成功协议发送失败: #{inspect(error)}")
    end
  end

  defp broadcast_zanli_status(state, player_id, is_zanli) do
    message = TeenPattiMessageBuilder.build_zanli_message(player_id, is_zanli)
    broadcast_to_room(state, message)
  end

  defp check_zanli_players_after_game(state) do
    # 游戏结束后，检查暂离玩家是否需要退回大厅
    # 参考C++服务端：暂离玩家在游戏结束后自动退出房间
    zanli_players =
      state.players
      |> Enum.filter(fn {_id, player} ->
        Map.get(player, :zanli_mode, false) && !Map.get(player, :is_robot, false)
      end)

    # 将暂离玩家踢出房间
    updated_state = Enum.reduce(zanli_players, state, fn {player_id, player}, acc_state ->
      Logger.info("🃏 [TEEN_PATTI_ZANLI] 暂离玩家游戏结束后退回大厅 - 玩家: #{player_id}")

      # 🎯 修复：发送暂离退出消息（使用前端期望的协议格式）
      # 前端通过 onDeletePlayer 函数处理暂离退出，绑定到 SC_ROOM_DEL_PLAYER_P 协议
      # 协议：mainId=4, subId=18 (SC_ROOM_DEL_PLAYER_P)
      # 数据格式参考您提供的要求：{"money": -30, "bchange": 1, "totalmoney": 1080}

      # 🎯 关键修复：计算暂离玩家的实际积分变动（不是固定扣费）
      # 根据您的要求：玩家赢了显示+积分，输了显示-积分
      player_money = get_player_points(state, player_id) || 0

      # 计算玩家在当前游戏中的积分变动
      # 如果游戏已经结算，从结算结果中获取变动；否则计算下注损失
      change_money = case Map.get(state.game_data, :settlement_result) do
        nil ->
          # 游戏未结算，暂离玩家失去已下注的金额
          player_total_bet = Map.get(state.game_data.player_total_bets, player_id, 0)
          -player_total_bet  # 负数表示损失

        settlement ->
          # 游戏已结算，从结算结果中获取该玩家的积分变动
          Map.get(settlement.player_changes, player_id, 0)
      end

      # 计算暂离后的总积分（不实际扣除，只是显示变动）
      total_money = player_money + change_money

      # 🎯 注意：这里不实际扣除积分，因为：
      # 1. 如果游戏未结算，玩家的下注已经在下注时扣除了
      # 2. 如果游戏已结算，积分变动已经在结算时处理了
      # 协议7:7只是告诉前端显示积分变动情况
      updated_acc_state = acc_state

      # 🎯 关键修复：发送协议7:7暂离扣费消息（参考旧项目日志）
      # 根据旧项目日志：RECV PROTO:[7][7] {money: -60, bchange: 1, totalmoney: 770}
      zanli_fee_message = %{
        "mainId" => 7,
        "subId" => 7,
        "data" => %{
          "money" => change_money,
          "bchange" => 1,
          "totalmoney" => total_money
        }
      }

      # 发送暂离扣费消息
      try do
        CypridinaWeb.Endpoint.broadcast!("user:#{player.user_id}", "private_message", zanli_fee_message)
        Logger.info("🃏 [TEEN_PATTI_ZANLI] ✅ 暂离积分变动协议7:7发送成功")
        Logger.info("🃏 [TEEN_PATTI_ZANLI] 📤 协议: mainId=7, subId=7 (暂离积分变动)")
        Logger.info("🃏 [TEEN_PATTI_ZANLI] 📦 数据: #{inspect(zanli_fee_message["data"])}")
        Logger.info("🃏 [TEEN_PATTI_ZANLI] 💰 积分变动: #{change_money}, 当前积分: #{total_money}")
        Logger.info("🃏 [TEEN_PATTI_ZANLI] 🎯 #{if change_money >= 0, do: "玩家赢了显示+积分", else: "玩家输了显示-积分"}")
      rescue
        error ->
          Logger.error("🃏 [TEEN_PATTI_ZANLI] ❌ 暂离扣费协议发送失败: #{inspect(error)}")
      end

      # 🎯 修复：发送玩家退出房间协议4:14（参考旧项目日志）
      # 根据旧项目日志：RECV PROTO:[4][14] {playerid: 82606679, reason: 0}
      player_exit_message = %{
        "mainId" => 4,
        "subId" => 14,  # SC_ROOM_PLAYER_QUIT_P
        "data" => %{
          "playerid" => player_id,
          "reason" => 0  # 暂离退出原因
        }
      }

      # 🎯 修复：发送玩家退出消息给暂离玩家自己
      try do
        CypridinaWeb.Endpoint.broadcast!("user:#{player.user_id}", "private_message", player_exit_message)
        Logger.info("🃏 [TEEN_PATTI_ZANLI] ✅ 玩家退出协议4:14发送成功（给暂离玩家）")
        Logger.info("🃏 [TEEN_PATTI_ZANLI] 📤 协议: mainId=4, subId=14 (玩家退出)")
        Logger.info("🃏 [TEEN_PATTI_ZANLI] 📦 数据: #{inspect(player_exit_message["data"])}")
      rescue
        error ->
          Logger.error("🃏 [TEEN_PATTI_ZANLI] ❌ 玩家退出协议发送失败: #{inspect(error)}")
      end

      # 🎯 修复：广播玩家离开消息给房间内其他玩家（使用SC_ROOM_PLAYER_QUIT_P协议）
      # 参考协议定义：SC_ROOM_PLAYER_QUIT_P: 14 - 玩家离开房间(广播)
      player_leave_broadcast = %{
        "mainId" => 4,
        "subId" => 14,  # SC_ROOM_PLAYER_QUIT_P - 玩家离开房间(广播)
        "data" => %{
          "playerid" => player_id,
          "reason" => 1  # 暂离原因
        }
      }

      # 广播给房间内其他玩家（排除暂离玩家自己）
      Logger.info("🃏 [TEEN_PATTI_ZANLI] 📤 广播玩家离开消息给房间内其他玩家")
      Logger.info("🃏 [TEEN_PATTI_ZANLI] 📤 协议: mainId=4, subId=14 (SC_ROOM_PLAYER_QUIT_P)")
      Logger.info("🃏 [TEEN_PATTI_ZANLI] 📦 数据: #{inspect(player_leave_broadcast["data"])}")
      broadcast_to_room(updated_acc_state, player_leave_broadcast, [player])

      # 从房间中移除玩家
      new_players = Map.delete(updated_acc_state.players, player_id)

      # 清理游戏数据中的玩家信息
      game_data = %{updated_acc_state.game_data |
        player_states: Map.delete(updated_acc_state.game_data.player_states, player_id),
        player_seen_status: Map.delete(updated_acc_state.game_data.player_seen_status, player_id),
        player_bets: Map.delete(updated_acc_state.game_data.player_bets, player_id),
        player_total_bets: Map.delete(updated_acc_state.game_data.player_total_bets, player_id),
        player_cards: Map.delete(updated_acc_state.game_data.player_cards, player_id)
      }

      %{updated_acc_state | players: new_players, game_data: game_data}
    end)

    # 检查房间是否需要解散（没有真实玩家）
    check_room_dissolution_after_zanli(updated_state)
  end

  # 检查暂离后房间是否需要解散
  defp check_room_dissolution_after_zanli(state) do
    real_players = count_real_players(state)

    if real_players == 0 do
      Logger.info("🃏 [TEEN_PATTI_ZANLI] 房间内无真实玩家，准备解散房间 - 房间: #{state.id}")

      # 清理所有机器人
      clear_all_robots(state)

      # 设置房间状态为已结束，触发房间关闭
      game_data = %{state.game_data | state: :finished}
      updated_state = %{state | game_data: game_data, players: %{}}

      # 通知房间管理器关闭房间
      Process.send_after(self(), :close_room, 1000)

      updated_state
    else
      Logger.info("🃏 [TEEN_PATTI_ZANLI] 房间内还有 #{real_players} 个真实玩家，继续游戏")
      state
    end
  end

  # 清理所有机器人
  defp clear_all_robots(state) do
    robot_players =
      state.players
      |> Enum.filter(fn {_id, player} -> Map.get(player, :is_robot, false) end)

    Enum.each(robot_players, fn {robot_id, _robot} ->
      Logger.info("🃏 [TEEN_PATTI_ZANLI] 清理机器人 - 机器人: #{robot_id}")
    end)
  end
end
