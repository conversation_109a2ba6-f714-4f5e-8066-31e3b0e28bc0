defmodule <PERSON>pridina.Teen.GameSystem.Games.LongHu.LongHuMessageBuilder do
  @moduledoc """
  龙虎斗游戏消息构建器

  统一处理所有客户端协议消息的构建，减少重复代码
  """

  alias Cypridina.Teen.GameSystem.Games.LongHu.LongHuGame

  # 主协议ID
  @main_id 5

  @doc """
  构建基础协议消息结构
  """
  def build_message(sub_id, data, opts \\ []) do
    base_message = %{
      "mainId" => @main_id,
      "subId" => sub_id,
      "data" => data
    }

    if opts[:with_timestamp] do
      Map.put(base_message, "timestamp", DateTime.utc_now() |> DateTime.to_unix(:millisecond))
    else
      base_message
    end
  end

  @doc """
  构建游戏配置消息 (SC_LHD_CONFIG_P - 3900)
  """
  def build_game_config(state) do
    odds = LongHuGame.odds()
    bet_areas = LongHuGame.bet_areas()

    data = %{
      "Bet" => LongHuGame.chip_values(),
      "Odds" => %{
        "#{bet_areas.long}" => odds.long - 1.0,
        "#{bet_areas.hu}" => odds.hu - 1.0,
        "#{bet_areas.he}" => odds.he - 1.0
      },
      "BetMax" => state.game_data.config.max_bet,
      "BetNeed" => state.game_data.config.min_bet,
      "betLimit" => %{
        "#{bet_areas.long}" => state.game_data.config.area_bet_limits.long,
        "#{bet_areas.hu}" => state.game_data.config.area_bet_limits.hu,
        "#{bet_areas.he}" => state.game_data.config.area_bet_limits.he
      },
      "TimeLimit" => %{
        "BuyHorse" => state.game_data.config.bet_time
      },
      "Zhuang" => %{
        "1" => %{
          "Need" => state.game_data.config.banker.min_banker_money,
          "MinTurn" => state.game_data.config.banker.min_turns,
          "MaxTurn" => state.game_data.config.banker.max_turns
        }
      },
      "difen" => 1,
      "roundid" => state.game_data.round
    }

    build_message(3900, data)
  end

  @doc """
  构建游戏状态消息 (SC_LHD_GAMESTATE_P - 3901)
  """
  def build_game_state(state, game_state_data) do
    build_message(3901, game_state_data)
  end

  @doc """
  构建错误消息 (SC_LHD_OPER_ERROR_P - 3914)
  """
  def build_error_message(error_code) do
    build_message(3914, %{"errorcode" => error_code})
  end

  @doc """
  构建下注成功确认消息 (SC_LHD_BET_SUCCESS - 3926)
  """
  def build_bet_success(player_bets, direction, amount, total_bets, current_money, remaining_limits) do
    data = %{
      "code" => 1,
      "odds" => amount,
      "direction" => direction,
      "long" => player_bets.long,
      "hu" => player_bets.hu,
      "he" => player_bets.he,
      "long_total" => total_bets.long,
      "hu_total" => total_bets.hu,
      "he_total" => total_bets.he,
      "chouma" => current_money,
      "limit" => remaining_limits,
      "totalbet" => total_bets
    }

    build_message(3926, data)
  end

  @doc """
  构建下注同步消息 (SC_LHD_BET_SYNC - 3927)
  """
  def build_bet_sync(state, encoded_bets, remaining_limits, exclude_player_id \\ nil) do
    data = %{
      "roundid" => state.game_data.round,
      "total" => state.game_data.total_bets,
      "leftbet" => remaining_limits
    }

    # 添加玩家筹码增量信息
    data = if exclude_player_id do
      Map.put(data, exclude_player_id, encoded_bets)
    else
      data
    end

    build_message(3927, data)
  end

  @doc """
  构建结算消息 (SC_LHD_SETTLEMENT_P_NEW - 3928)
  """
  def build_settlement(state, result_pos, win_value, other_data, zhuang_data) do
    message_id = "settlement_#{state.game_data.round}_#{System.system_time(:millisecond)}"

    data = %{
      "roundid" => state.game_data.round,
      "resultPos" => result_pos,
      "win_value" => win_value,
      "other" => other_data,
      "_numeric_id" => nil,
      "nextat" => nil,
      "zhuang" => zhuang_data,
      "message_id" => message_id
    }

    build_message(3928, data)
  end

  @doc """
  构建发牌消息 (SC_LHD_FIRST_P - 3902)
  """
  def build_dealing(state) do
    data = %{
      "state" => 2,
      "round" => state.game_data.round,
      "deal_time" => state.game_data.config.deal_time
    }

    build_message(3902, data)
  end

  @doc """
  构建亮牌消息 (SC_LHD_SHOWCARD_P - 3912)
  """
  def build_revealing(state, long_card_data, hu_card_data) do
    data = %{
      "round" => state.game_data.round,
      "long" => long_card_data,
      "hu" => hu_card_data,
      "reveal_time" => state.game_data.config.reveal_time
    }

    build_message(3912, data)
  end

  @doc """
  构建庄家列表消息 (SC_LHD_ZHUANG_LIST_P - 3908)
  """
  def build_banker_list(banker_list_data) do
    build_message(3908, banker_list_data)
  end

  @doc """
  构建庄家信息消息 (SC_LHD_ZHUANG_INFO_P - 3909)
  """
  def build_banker_info(banker_info_data) do
    build_message(3909, banker_info_data)
  end

  @doc """
  构建玩家列表消息 (SC_LHD_ALLLIST_P - 3921)
  """
  def build_player_list(player_list_data) do
    build_message(3921, player_list_data)
  end

  @doc """
  构建玩家数量变化消息 (SC_LHD_PLAYER_COUNT_P - 3929)
  """
  def build_player_count_change(total_players) do
    build_message(3929, %{"totalplayernum" => total_players})
  end

  @doc """
  构建历史记录消息 (SC_LHD_HISTORY_P - 3916)
  """
  def build_history(page, count, total, formatted_history) do
    data = %{
      "data"=> %{

      "page" => page,
      "count" => count,
      "total" => total,
      "history" => formatted_history
      }
    }

    build_message(3916, data)
  end

  @doc """
  构建走势图历史记录消息 (SC_LHD_HISTORY_P - 3916)
  客户端期望的格式，包含统计数据和索引化的历史记录
  """
  def build_history_trend(formatted_data) do
    build_message(3916, formatted_data)
  end

  @doc """
  构建庄家操作成功/错误消息
  """
  def build_banker_response(code, message \\ nil) do
    data = %{"code" => code}
    data = if message, do: Map.put(data, "msg", message), else: data
    build_message(3914, data)
  end

  @doc """
  构建下庄通知消息 (SC_LHD_ZHUANG_OFF_P - 3910)
  """
  def build_banker_off_notice(message \\ "下庄成功") do
    build_message(3910, %{"code" => 1, "msg" => message})
  end

  @doc """
  构建庄家下庄提醒消息 (SC_LHD_NOTICE_NO_ZHUANG_P - 3911)
  """
  def build_banker_notice(remaining_rounds) do
    build_message(3911, %{"round" => remaining_rounds})
  end
end
