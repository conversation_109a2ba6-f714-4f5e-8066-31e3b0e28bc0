defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.SlotCat.SlotCatRoom do
  @moduledoc """
  SlotCat 老虎机游戏房间实现

  基于 RoomBase 模板实现的单人老虎机游戏房间。
  支持：
  - 5x3 转轮布局，9 条支付线
  - 免费游戏功能
  - Jackpot 奖池系统
  - 游戏控制机制
  - 任务系统
  - 统一的积分管理
  """

  use Cy<PERSON><PERSON><PERSON>.Teen.GameSystem.RoomBase, game_type: :slotcat
  require <PERSON><PERSON>

  alias <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.SlotCat.{SlotCatGameLogic, SlotCatConstants}

  # 游戏状态
  @game_state_waiting :waiting
  @game_state_playing :playing
  @game_state_free_game :free_game

  # 房间初始化
  @impl true
  def init_game_logic(state) do
    Logger.info("🎰 [SLOTCAT] 初始化 SlotCat 房间: #{state.id}")

    # 获取游戏配置
    game_config = %{
      # 最小下注
      min_bet: Map.get(state.config, :min_bet, 10),
      # 最大下注
      max_bet: Map.get(state.config, :max_bet, 10000),
      # 下注倍率选项
      bet_multipliers: SlotCatConstants.bet_multipliers(),
      # 底分
      difen: 100,
      # 固定倍率
      bet_rate_num: SlotCatConstants.bet_rate_num(),
      # 金币比例
      score_rate: 1,
      # RTP (Return to Player) 返还率
      rtp: 96.5
    }

    # 初始化游戏数据
    game_data = %{
      # 游戏配置
      config: game_config,
      # 当前状态
      status: @game_state_waiting,
      # 玩家下注记录
      bets: %{},
      # 当前玩家下注倍率
      current_odds: %{},
      # 游戏结果历史
      results: [],
      # 当前回合
      current_round: 0,

      # Jackpot 奖池数据 (SlotCat 有三个不同的奖池)
      jackpot_pools: %{
        center: %{    # 中间奖池 (5个Jackpot) - JACKPOT_TYPE.CENTER = 0
          current_amount: 5000000,
          min_amount: 2500000,
          max_amount: 50000000
        },
        right: %{     # 右边奖池 (4个Jackpot) - JACKPOT_TYPE.RIGHT = 1
          current_amount: 2500000,
          min_amount: 1250000,
          max_amount: 25000000
        },
        left: %{      # 左边奖池 (3个Jackpot) - JACKPOT_TYPE.LEFT = 2
          current_amount: 1250000,
          min_amount: 625000,
          max_amount: 12500000
        }
      },

      # 免费游戏数据
      free_game: %{
        remaining_times: 0,       # 剩余免费次数
        total_times: 0,           # 总免费次数
        total_win: 0              # 免费游戏总赢取
      },

      # Jackpot 中奖记录
      jackpot_winners: [],

      # 任务系统数据
      task_data: %{},

      # 玩家任务进度
      player_tasks: %{},

      # 游戏统计
      game_stats: %{
        total_games: 0,
        total_bet: 0,
        total_win: 0
      }
    }

    # 启动定时器
    schedule_jackpot_broadcast()

    %{state | game_data: game_data}
  end

  # 玩家加入房间
  @impl true
  def on_player_joined(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 玩家加入房间: #{user_id}, numeric_id: #{numeric_id}")

    # 获取玩家真实积分
    initial_points = get_player_points(state, numeric_id)
    Logger.info("🎰 [SLOTCAT] 玩家初始积分: #{initial_points} (机器人: #{player.is_robot})")

    # 更新用户信息
    updated_user_info = player.user
    |> Map.put(:money, initial_points)

    # 更新state中的玩家信息
    updated_player = %{player | user: updated_user_info}
    new_state = %{state | players: Map.put(state.players, numeric_id, updated_player)}

    # 初始化玩家游戏数据
    new_game_data = %{
      new_state.game_data |
      current_odds: Map.put(new_state.game_data.current_odds, user_id, 1)
    }
    new_state = %{new_state | game_data: new_game_data}

    # 发送初始化数据给玩家
    send_initial_data(new_state, player)

    new_state
  end

  # 玩家重连加入房间
  @impl true
  def on_player_rejoined(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 玩家重连加入: #{user_id}, numeric_id: #{numeric_id}")

    # 获取玩家真实积分
    current_points = get_player_points(state, numeric_id)
    Logger.info("🎰 [SLOTCAT] 重连玩家积分: #{current_points}")

    # 发送初始化数据给重连玩家
    send_initial_data(state, player)

    state
  end

  # 玩家离开房间
  @impl true
  def on_player_left(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 玩家离开房间: #{user_id}, numeric_id: #{numeric_id}")

    # 清理玩家游戏数据
    new_game_data = %{
      state.game_data |
      current_odds: Map.delete(state.game_data.current_odds, user_id),
      bets: Map.delete(state.game_data.bets, user_id)
    }

    %{state | game_data: new_game_data}
  end

  # 处理游戏消息
  @impl true
  def handle_game_message(state, player, message) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 收到游戏消息 - 房间: #{state.id}, 用户: #{user_id}, numeric_id: #{numeric_id}, 消息: #{inspect(message)}")

    case message do
      # 处理MainID=4的退出房间协议
      %{"mainId" => 4, "subId" => 40} ->
        data = message["data"] || %{}
        handle_exit_room_protocol(state, player, data)

      # 处理MainID=5的SlotCat协议消息
      %{"mainId" => 5, "subId" => sub_id} ->
        data = message["data"] || %{}
        handle_slotcat_protocol(state, player, sub_id, data)

      # 兼容性处理
      %{"cmd" => "get_room_info"} ->
        Logger.info("🏠 [ROOM_INFO_REQUEST] 房间信息请求 - 用户: #{user_id}")
        send_game_config(state, player)
        send_game_state_to_user(state, player)
        state

      %{"cmd" => "request_jackpot"} ->
        Logger.info("🎰 [JACKPOT] Jackpot信息已自动广播，无需手动请求 - 用户: #{user_id}")
        state

      _ ->
        Logger.info("ℹ️ [GAME_MESSAGE] 消息已通过客户端协议处理或为未知消息 - 用户: #{user_id}, 消息: #{inspect(message)}")
        state
    end
  end

  # 处理SlotCat协议消息
  defp handle_slotcat_protocol(state, player, sub_id, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT_PROTOCOL] 处理协议 - SubID: #{sub_id}, 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}")

    case sub_id do
      # 游戏配置协议
      0 ->
        handle_game_config_request(state, player, data)

      # 离开房间
      2 ->
        handle_leave_room_request(state, player, data)

      # 游戏开始 (CS_SLOTCAT_GAMESTART_P)
      1000 ->
        handle_game_start_request(state, player, data)

      # Jackpot记录请求 (CS_SLOTCAT_JPLIST_P)
      1003 ->
        handle_jackpot_list_request(state, player, data)

      # 领取任务奖励 (CS_SLOTCAT_GETTASK_P)
      1010 ->
        handle_get_task_request(state, player, data)

      # 切换下注倍率 (CS_SLOTS_SWITCH_BET_P)
      1012 ->
        handle_switch_bet_request(state, player, data)

      # 获取房间信息
      1006 ->
        handle_room_info_request(state, player, data)

      # 其他未实现的协议
      _ ->
        Logger.warning("🎰 [SLOTCAT] 未实现的子协议: #{sub_id}")
        send_error_response(state, player, sub_id, "未实现的协议")
        state
    end
  end

  # 发送游戏配置给玩家
  defp send_game_config(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("⚙️ [SEND_CONFIG] 发送游戏配置 - 用户: #{user_id}, numeric_id: #{numeric_id}, 房间: #{state.id}")

    # 使用统一的积分获取方式
    player_money = get_player_points(state, numeric_id)

    # 获取玩家上次使用的下注倍率
    last_odds = Map.get(state.game_data.current_odds, user_id, 1)

    player_name = "玩家#{numeric_id}"

    # 构建当前玩家信息
    player_info = %{
      "playerid" => numeric_id,
      "money" => player_money,
      "name" => player_name,
      "seat" => 1
    }

    # 构建玩家列表（slotcat是单机游戏，只包含当前玩家）
    playerlist = %{
      "1" => player_info
    }

    message = %{
      "mainId" => 5,
      "subId" => 0,
      "data" => %{
        # 底分配置
        "difen" => state.game_data.config.difen,
        # 下注倍率配置
        "odds" => %{
          "1" => 1,
          "2" => 2,
          "3" => 5,
          "4" => 10,
          "5" => 20,
          "6" => 50,
          "7" => 100
        },
        # 玩家上次使用的下注倍率
        "lastodds" => last_odds,
        # 下注限制
        "BetMax" => state.game_data.config.max_bet,
        "BetNeed" => state.game_data.config.min_bet,
        # 当前Jackpot金额 (参照前端 onGameConfig 方法的数据格式)
        "jackpot" => state.game_data.jackpot_pools.center.current_amount,      # 中间Jackpot的值
        "jackpotleft" => state.game_data.jackpot_pools.left.current_amount,   # 左边Jackpot的值
        "jackpotright" => state.game_data.jackpot_pools.right.current_amount, # 右边Jackpot的值
        # 玩家金币
        "money" => player_money,
        # 当前回合ID
        "room_id" => state.id,
        "roundid" => state.game_data.current_round,
        # 玩家列表
        "playerlist" => playerlist
      }
    }

    Logger.info("📤 [SEND_CONFIG] 配置消息内容 - 协议: 5/0, 底分: #{state.game_data.config.difen}, 上次倍率: #{last_odds}, 玩家: #{player_name}")
    send_to_player(state, player, message)
  end

  # 处理游戏开始请求 (增强版本，包含更好的验证和错误处理)
  defp handle_game_start_request(state, player, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 处理游戏开始请求: #{user_id}, 数据: #{inspect(data)}")

    # 验证输入数据
    case validate_game_start_data(data) do
      {:ok, validated_data} ->
        # 检查游戏状态
        case validate_game_state(state) do
          :ok ->
            # 检查玩家状态
            case validate_player_in_room(state, numeric_id) do
              :ok ->
                # 处理有效的游戏开始请求
                execute_game_logic(state, player, validated_data.odds)

              {:error, reason} ->
                Logger.warning("🎰 [SLOTCAT] 玩家验证失败: #{reason}")
                send_error_response(state, player, SlotCatConstants.sc_slotcat_gamestart_p(), reason)
                state
            end

          {:error, reason} ->
            Logger.warning("🎰 [SLOTCAT] 游戏状态验证失败: #{reason}")
            send_error_response(state, player, SlotCatConstants.sc_slotcat_gamestart_p(), reason)
            state
        end

      {:error, reason} ->
        Logger.error("🎰 [SLOTCAT] 输入数据验证失败: #{reason}")
        send_error_response(state, player, SlotCatConstants.sc_slotcat_gamestart_p(), reason)
        state
    end
  end

  # 验证游戏开始数据
  defp validate_game_start_data(data) do
    with {:ok, odds} <- validate_odds(data) do
      {:ok, %{odds: odds}}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  # 验证下注倍率
  defp validate_odds(data) do
    odds = Map.get(data, "odds", 1)
    valid_multipliers = SlotCatConstants.bet_multipliers()

    cond do
      not is_number(odds) -> {:error, "倍率必须是数字"}
      odds not in valid_multipliers -> {:error, "无效的下注倍率: #{odds}，有效倍率: #{inspect(valid_multipliers)}"}
      true -> {:ok, odds}
    end
  end

  # 验证游戏状态
  defp validate_game_state(state) do
    case state.game_data.status do
      @game_state_waiting -> :ok
      @game_state_free_game -> :ok  # 免费游戏期间也可以继续
      status -> {:error, "游戏状态不允许开始新回合: #{status}"}
    end
  end

  # 验证玩家是否在房间中
  defp validate_player_in_room(state, numeric_id) do
    if Map.has_key?(state.players, numeric_id) do
      :ok
    else
      {:error, "玩家不在房间中"}
    end
  end

  # 执行游戏逻辑
  defp execute_game_logic(state, player, odds) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 执行游戏逻辑: #{user_id}, 倍率: #{odds}")

    # 计算下注金额 (使用配置中的 bet_rate_num 和 score_rate，参考 Slot777 实现)
    {single_line_bet, total_bet_amount} = calculate_bet_amount(odds, state)

    Logger.info("🎰 [SLOTCAT] 下注计算 - 倍率: #{odds}, 单线下注: #{single_line_bet}, 总下注: #{total_bet_amount}")

    # 检查玩家余额 (提前检查积分是否够下注，不够下注，1000协议，后面的都不用走了)
    current_points = get_player_points(state, numeric_id)

    if current_points < total_bet_amount do
      Logger.warning("🎰 [SLOTCAT] 用户余额不足: #{user_id}, 余额: #{current_points}, 需要: #{total_bet_amount}")
      send_insufficient_balance_message(state, player, total_bet_amount)
      state
    else
      # 执行游戏
      process_game_round(state, player, total_bet_amount, single_line_bet, odds)
    end
  end

  # 计算下注金额 (参考 Slot777 的实现，使用配置中的 bet_rate_num 和 score_rate)
  defp calculate_bet_amount(odds, state) do
    # 获取游戏配置
    game_config = state.config.game_config

    # 使用配置中的值，参考 Slot777 的计算方式
    # 计算公式: difen × odds × bet_rate_num × score_rate
    difen = Map.get(game_config, :difen, 100)
    bet_rate_num = Map.get(game_config, :bet_rate_num, SlotCatConstants.bet_rate_num())
    score_rate = Map.get(game_config, :score_rate, 1)

    # 确保倍率在有效范围内
    bet_multipliers = SlotCatConstants.bet_multipliers()
    valid_odds = if odds in bet_multipliers, do: odds, else: 1

    # 计算总下注金额 (参考 Slot777 的公式)
    total_bet_amount = difen * valid_odds * bet_rate_num * score_rate

    # 计算单线下注金额 (总下注 / 线数)
    single_line_bet = div(total_bet_amount, SlotCatConstants.win_lines())

    Logger.info("🎰 [SLOTCAT] 下注计算详情:")
    Logger.info("🎰 [SLOTCAT] - 倍率: #{valid_odds}")
    Logger.info("🎰 [SLOTCAT] - 底分(difen): #{difen}")
    Logger.info("🎰 [SLOTCAT] - 固定倍率(bet_rate_num): #{bet_rate_num}")
    Logger.info("🎰 [SLOTCAT] - 金币比例(score_rate): #{score_rate}")
    Logger.info("🎰 [SLOTCAT] - 计算公式: #{difen} × #{valid_odds} × #{bet_rate_num} × #{score_rate} = #{total_bet_amount}")
    Logger.info("🎰 [SLOTCAT] - 单线下注: #{single_line_bet}")

    {single_line_bet, total_bet_amount}
  end

  # 处理游戏回合
  defp process_game_round(state, player, total_bet_amount, single_line_bet, odds) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 处理游戏回合: #{user_id}, 总下注: #{total_bet_amount}, 单线下注: #{single_line_bet}")

    # 扣除下注金额
    new_state = subtract_player_points(state, numeric_id, total_bet_amount)

    # 检查扣除后的余额
    remaining_points = get_player_points(new_state, numeric_id)

    if remaining_points < 0 do
      Logger.error("🎰 [SLOTCAT] 扣除下注金额后余额不足: #{remaining_points}")
      send_insufficient_balance_message(state, player, total_bet_amount)
      state
    else
      # 生成游戏结果 (传入单线下注金额和游戏配置)
      game_config = state.config.game_config
      case SlotCatGameLogic.generate_game_result(user_id, single_line_bet, 0, false, game_config) do
        {:ok, game_result} ->
          # 处理游戏结果
          handle_game_result(new_state, player, total_bet_amount, single_line_bet, odds, game_result)

        {:error, reason} ->
          Logger.error("🎰 [SLOTCAT] 生成游戏结果失败: #{reason}")
          # 退还下注金额
          refund_state = add_player_points(new_state, numeric_id, total_bet_amount)
          refund_state
      end
    end
  end

  # 定时广播 Jackpot 信息
  defp schedule_jackpot_broadcast do
    Process.send_after(self(), :jackpot_broadcast, 30_000)  # 30秒
  end

  # 发送初始化数据
  defp send_initial_data(state, player) do
     # 发送玩家列表
    send_player_list_to_user(state, player)

    # 发送游戏配置
    send_game_config(state, player)

    # 发送游戏状态
    send_game_state_to_user(state, player)


    # 发送 Jackpot 信息
    send_jackpot_info(state, player)

    # 发送任务信息
    send_task_info(state, player)
  end

  # 发送游戏状态给指定用户
  defp send_game_state_to_user(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # 使用统一的积分获取方式
    player_money = get_player_points(state, numeric_id)

    message = %{
      "mainId" => 4,
      "subId" => 3,
      "data" => %{
        "status" => state.game_data.status,
        "round" => state.game_data.current_round,
        "money" => player_money,
        "jackpot" => state.game_data.jackpot_pools.center.current_amount
      }
    }

    Logger.info("📤 [SEND_GAME_STATE] 发送游戏状态给用户 - 用户: #{user_id}, numeric_id: #{numeric_id}, 状态: #{state.game_data.status}")
    send_to_player(state, player, message)
  end

  # 发送玩家列表给指定用户（slotcat单机游戏简化版）
  defp send_player_list_to_user(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # slotcat是单机游戏，只发送当前玩家信息
    player_money = get_player_points(state, numeric_id)

    player_info = %{
      "playerid" => numeric_id,
      "money" => player_money,
      "name" => "玩家#{numeric_id}",
      "seat" => 1
    }

    message = %{
      "mainId" => 4,
      "subId" => 2,
      "data" => %{
        "playerlist" => [player_info],
        "totalplayernum" => 1
      }
    }

    Logger.info("📤 [SEND_PLAYER_LIST] 发送玩家信息给用户 - 用户: #{user_id}, numeric_id: #{numeric_id}")
    send_to_player(state, player, message)
  end

  # 发送 Jackpot 信息 (参照前端 onUpdateJackpot 方法的数据格式)
  defp send_jackpot_info(state, player) do
    # 前端期望的字段: jackpot (中间), jackpotleft (左边), jackpotright (右边)
    jackpot_info = %{
      "jackpot" => state.game_data.jackpot_pools.center.current_amount,      # 中间Jackpot的值
      "jackpotleft" => state.game_data.jackpot_pools.left.current_amount,   # 左边Jackpot的值
      "jackpotright" => state.game_data.jackpot_pools.right.current_amount  # 右边Jackpot的值
    }

    send_to_player(state, player, %{
      "mainId" => 5,
      "subId" => SlotCatConstants.sc_slotcat_jackpot_p(),  # 1005协议
      "data" => jackpot_info
    })
  end

  # 发送任务信息
  defp send_task_info(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # 获取玩家任务进度
    player_task = Map.get(state.game_data.player_tasks, user_id, %{
      icon_id: 1,
      current_count: 0,
      total_count: 10,
      reward_amount: 1000,
      min_bet_required: 180,
      completed: false
    })

    task_info = %{
      "taskicon" => player_task.icon_id,
      "taskcurnum" => player_task.current_count,
      "tasktotalnum" => player_task.total_count,
      "taskmoney" => player_task.reward_amount,
      "taskminbet" => player_task.min_bet_required
    }

    send_to_player(state, player, %{
      "mainId" => 5,
      "subId" => SlotCatConstants.sc_slotcat_taskinfo_p(),
      "data" => task_info
    })
  end

  # 更新玩家任务进度
  defp update_player_task_progress(state, user_id, bet_amount, game_result) do
    current_task = Map.get(state.game_data.player_tasks, user_id, %{
      icon_id: 1,
      current_count: 0,
      total_count: 10,
      reward_amount: 1000,
      min_bet_required: 180,
      completed: false
    })

    # 检查是否满足最小下注要求
    if bet_amount >= current_task.min_bet_required and not current_task.completed do
      # 检查是否获得了任务图标
      task_icon_count = count_icon_in_result(game_result, current_task.icon_id)

      if task_icon_count > 0 do
        new_count = min(current_task.current_count + task_icon_count, current_task.total_count)
        completed = new_count >= current_task.total_count

        updated_task = %{
          current_task |
          current_count: new_count,
          completed: completed
        }

        new_player_tasks = Map.put(state.game_data.player_tasks, user_id, updated_task)
        new_game_data = %{state.game_data | player_tasks: new_player_tasks}
        new_state = %{state | game_data: new_game_data}

        # 如果任务有更新，发送任务信息
        if new_count > current_task.current_count do
          player = %{user_id: user_id, numeric_id: get_numeric_id_from_user_id(state, user_id)}
          send_task_info(new_state, player)
        end

        new_state
      else
        state
      end
    else
      state
    end
  end

  # 计算游戏结果中指定图标的数量
  defp count_icon_in_result(game_result, target_icon) do
    game_result.icons
    |> List.flatten()
    |> Enum.count(fn icon -> icon == target_icon end)
  end

  # 从用户ID获取数字ID
  defp get_numeric_id_from_user_id(state, user_id) do
    case Enum.find(state.players, fn {_numeric_id, player_data} ->
      player_data.user.id == user_id
    end) do
      {numeric_id, _} -> numeric_id
      nil -> nil
    end
  end

  # 添加缺失的处理函数
  defp handle_game_config_request(state, player, _data) do
    send_game_config(state, player)
    state
  end

  defp handle_leave_room_request(state, player, _data) do
    user_id = player.user_id
    Logger.info("🎰 [SLOTCAT] 处理离开房间请求: #{user_id}")

    # 移除玩家
    {:ok, new_state} = handle_player_leave(state, user_id)
    new_state
  end

  defp handle_room_info_request(state, player, _data) do
    send_game_config(state, player)
    send_game_state_to_user(state, player)
    state
  end

  defp send_error_response(state, player, sub_id, message) do
    error_response = %{
      "mainId" => 5,
      "subId" => sub_id,
      "data" => %{
        "error" => true,
        "message" => message
      }
    }
    send_to_player(state, player, error_response)
  end

  # 处理Jackpot记录请求 (参照前端 onUpdateRecordInfo 方法的数据格式)
  defp handle_jackpot_list_request(state, player, _data) do
    user_id = player.user_id
    Logger.info("🎰 [SLOTCAT] 处理Jackpot记录请求: #{user_id}")

    # 构建符合前端期望的Jackpot记录数据格式
    # 前端期望: for (let key in info) { let player = info[key]; }
    # 所以返回的应该是一个对象，key为索引，value为记录数据
    jackpot_records = %{
      "1" => %{
        "playerid" => 12345,
        "name" => "玩家12345",
        "headid" => 1,                    # 玩家系统头像id
        "wxheadurl" => "",                # 自定义头像url
        "jackpotnum" => 5,                # jackpot的个数 (3=左边, 4=右边, 5=中间)
        "bet" => 1800,                    # 玩家下注额
        "winscore" => 50000,              # 玩家赢的金币
        "time" => format_time_for_display(System.system_time(:second))  # 当局时间
      },
      "2" => %{
        "playerid" => 67890,
        "name" => "玩家67890",
        "headid" => 2,
        "wxheadurl" => "",
        "jackpotnum" => 4,
        "bet" => 900,
        "winscore" => 25000,
        "time" => format_time_for_display(System.system_time(:second) - 3600)
      },
      "3" => %{
        "playerid" => 54321,
        "name" => "玩家54321",
        "headid" => 3,
        "wxheadurl" => "",
        "jackpotnum" => 3,
        "bet" => 450,
        "winscore" => 12000,
        "time" => format_time_for_display(System.system_time(:second) - 7200)
      }
    }

    # 直接发送记录数据，不包装在 "records" 字段中
    # 因为前端直接遍历 info: for (let key in info)
    send_to_player(state, player, %{
      "mainId" => 5,
      "subId" => SlotCatConstants.sc_slotcat_jplist_p(),  # 1004协议
      "data" => jackpot_records
    })

    state
  end

  # 格式化时间为显示格式
  defp format_time_for_display(timestamp) do
    datetime = DateTime.from_unix!(timestamp)
    Calendar.strftime(datetime, "%m-%d %H:%M")
  end

  # 处理任务奖励领取请求
  defp handle_get_task_request(state, player, _data) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 处理任务奖励领取请求: #{user_id}")

    # 模拟任务奖励
    task_reward = 1000

    # 增加玩家积分
    new_state = add_player_points(state, numeric_id, task_reward)

    # 发送任务奖励结果
    send_to_player(new_state, player, %{
      "mainId" => 5,
      "subId" => SlotCatConstants.sc_slotcat_gettask_p(),
      "data" => %{
        "taskmoney" => task_reward,
        "success" => true
      }
    })

    new_state
  end

  # 处理切换下注倍率请求
  defp handle_switch_bet_request(state, player, data) do
    user_id = player.user_id
    Logger.info("🎰 [SLOTCAT] 处理切换下注倍率请求: #{user_id}, 数据: #{inspect(data)}")

    # 获取新的下注倍率
    new_odds = Map.get(data, "odds", 1)

    # 更新玩家的下注倍率
    new_current_odds = Map.put(state.game_data.current_odds, user_id, new_odds)
    new_game_data = %{state.game_data | current_odds: new_current_odds}
    new_state = %{state | game_data: new_game_data}

    # 发送确认消息
    send_to_player(new_state, player, %{
      "mainId" => 5,
      "subId" => SlotCatConstants.cs_slots_switch_bet_p(),
      "data" => %{
        "odds" => new_odds,
        "success" => true
      }
    })

    new_state
  end

  # 处理游戏结果
  defp handle_game_result(state, player, total_bet_amount, single_line_bet, odds, game_result) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 处理游戏结果: #{user_id}, 中奖金额: #{game_result.win_amount}, 总下注: #{total_bet_amount}")

    # 计算实际赢取金额 (扣除抽水)
    net_win = game_result.win_amount - total_bet_amount
    actual_win = if net_win > 0 do
      # 抽水 5%
      draw_amount = div(net_win * 5, 100)
      game_result.win_amount - draw_amount
    else
      game_result.win_amount
    end

    # 更新用户余额
    new_state = if actual_win > 0 do
      add_player_points(state, numeric_id, actual_win)
    else
      state
    end

    # 更新玩家下注倍率
    new_game_data = %{
      new_state.game_data |
      current_odds: Map.put(new_state.game_data.current_odds, user_id, odds),
      current_round: new_state.game_data.current_round + 1
    }
    new_state = %{new_state | game_data: new_game_data}

    # 更新任务进度
    task_updated_state = update_player_task_progress(new_state, user_id, total_bet_amount, game_result)

    # 发送游戏结果给玩家
    send_game_result(task_updated_state, player, game_result, total_bet_amount, single_line_bet, actual_win, odds)

    # 发送积分更新通知（参考 Slot777）
    send_money_update_notification(task_updated_state, player)

    # 广播桌面玩家信息和游戏结果
    broadcast_desk_players_info(task_updated_state)
    broadcast_game_result(task_updated_state, player, game_result, total_bet_amount, actual_win, odds)

    # 处理免费游戏
    if game_result.free_times > 0 do
      handle_free_game(task_updated_state, player, game_result.free_times, total_bet_amount, single_line_bet, odds)
    else
      task_updated_state
    end
  end

  # 发送游戏结果
  defp send_game_result(state, player, game_result, total_bet_amount, single_line_bet, actual_win, _odds) do
    # 使用游戏逻辑模块的格式化函数，确保数据格式正确
    case SlotCatGameLogic.format_result_for_client(game_result) do
      {:ok, formatted_result} ->
        # 计算 Jackpot 奖金 (如果中了 Jackpot，根据不同奖池计算)
        jackpot_cash = if game_result.jackpot_count >= 3 do
          # 根据 Jackpot 数量确定奖池类型
          pool_data = case game_result.jackpot_count do
            3 -> state.game_data.jackpot_pools.left      # 左边奖池
            4 -> state.game_data.jackpot_pools.right     # 右边奖池
            5 -> state.game_data.jackpot_pools.center    # 中间奖池
            _ -> state.game_data.jackpot_pools.left      # 默认左边奖池
          end

          jackpot_percentage = SlotCatConstants.get_jackpot_cash_percentage(single_line_bet, game_result.jackpot_count, total_bet_amount)
          if jackpot_percentage > 0 do
            div(pool_data.current_amount * jackpot_percentage, 100)
          else
            0
          end
        else
          0
        end

        # 发送游戏开始结果 (确保包含前端期望的所有字段)
        result_data = %{
          "freetimes" => formatted_result["freetimes"],
          "jackpotnum" => formatted_result["jackpotnum"],
          "iconresult" => formatted_result["iconresult"],
          "linecount" => formatted_result["linecount"],
          "lineresult" => formatted_result["lineresult"],
          "totalmult" => formatted_result["totalmult"],
          "winmoney" => formatted_result["winmoney"],
          "changemoney" => actual_win - total_bet_amount,
          "jackpotcash" => jackpot_cash,  # 中Jackpot获得的金额
          "luckyjackpot" => formatted_result["luckyjackpot"]  # 转盘彩金
        }

        send_to_player(state, player, %{
          "mainId" => 5,
          "subId" => SlotCatConstants.sc_slotcat_gamestart_p(),  # 1001协议
          "data" => result_data
        })

      {:error, reason} ->
        Logger.error("🎰 [SLOTCAT] 格式化游戏结果失败: #{reason}")
        # 发送错误响应
        send_error_response(state, player, SlotCatConstants.sc_slotcat_gamestart_p(), "游戏结果格式化失败")
    end

    # 处理 Jackpot 中奖 (传入单线下注金额用于计算奖励比例)
    if game_result.jackpot_count >= 3 do
      handle_jackpot_win(state, player, game_result.jackpot_count, single_line_bet, total_bet_amount)
    end
  end

  # 发送积分更新通知（参考 Slot777 实现）
  defp send_money_update_notification(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    current_points = get_player_points(state, numeric_id)

    money_update_data = %{
      "playerid" => numeric_id,
      "coin" => current_points
    }

    response_money_update = %{
      "mainId" => 4,  # MainProto.Game
      "subId" => 8,   # Game.SC_ROOM_RESET_COIN_P
      "data" => money_update_data
    }

    Logger.info("💰 [SLOTCAT_MONEY_UPDATE] 发送积分更新协议 - 用户: #{user_id}, numeric_id: #{numeric_id}, 积分: #{current_points}")
    send_to_player(state, player, response_money_update)
  end

  # 处理免费游戏 (参考 Slot777 实现，一次性生成所有免费游戏结果)
  defp handle_free_game(state, player, free_times, total_bet_amount, single_line_bet, odds) do
    user_id = player.user_id
    Logger.info("🎰 [SLOTCAT] 触发免费游戏: #{user_id}, 次数: #{free_times}")

    # 生成所有免费游戏结果 (参考 Slot777 的实现方式)
    generate_and_send_free_games(state, player, free_times, single_line_bet)
  end

  # 生成并发送免费游戏结果 (参考 Slot777 实现)
  defp generate_and_send_free_games(state, player, free_times, single_line_bet) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 开始生成免费游戏 - 用户: #{user_id}, 免费次数: #{free_times}")

    # 生成免费游戏结果数组 (参考 Slot777 的实现)
    free_game_results = generate_free_game_results(free_times, single_line_bet, state)

    # 计算免费游戏总奖金
    total_free_game_winnings = calculate_total_free_game_winnings(free_game_results)
    Logger.info("🎰 [SLOTCAT] 免费游戏总奖金: #{total_free_game_winnings}")

    # 发送1002协议 - 免费游戏结果 (参考 Slot777 的协议格式)
    response_1002 = %{
      "mainId" => 5,
      "subId" => SlotCatConstants.sc_slotcat_freegame_p(),  # 1002
      "data" => free_game_results
    }

    Logger.info("🎰 [SLOTCAT] 发送1002协议 - 免费游戏结果数据")
    send_to_player(state, player, response_1002)

    # 更新用户积分 (如果有奖金)
    final_state = if total_free_game_winnings > 0 do
      updated_state = add_player_points(state, numeric_id, total_free_game_winnings)
      # 发送积分更新通知
      send_money_update_notification(updated_state, player)
      updated_state
    else
      state
    end

    # 重置游戏状态
    reset_free_game_state(final_state)
  end

  # 生成免费游戏结果数组 (参考 Slot777 实现)
  defp generate_free_game_results(free_times, single_line_bet, state) do
    game_config = state.config.game_config

    # 生成指定次数的免费游戏结果，转换为以1为起始下标的Map (参考 Slot777)
    1..free_times
    |> Enum.map(fn round ->
      # 每次免费游戏都生成新的结果
      case SlotCatGameLogic.generate_game_result(0, single_line_bet, 0, true, game_config) do
        {:ok, game_result} ->
          # 格式化结果为前端期望的格式
          case SlotCatGameLogic.format_result_for_client(game_result) do
            {:ok, formatted_result} ->
              # 免费游戏结果包含所有必要字段 (参考 Slot777 格式)
              result = %{
                "round" => round,
                "freetimes" => 0,  # 免费游戏中不再触发免费游戏
                "jackpotnum" => formatted_result["jackpotnum"],
                "iconresult" => formatted_result["iconresult"],
                "linecount" => formatted_result["linecount"],
                "lineresult" => formatted_result["lineresult"],
                "totalmult" => formatted_result["totalmult"],
                "winmoney" => formatted_result["winmoney"],
                "changemoney" => formatted_result["changemoney"],
                "jackpotcash" => formatted_result["jackpotcash"],
                "luckyjackpot" => Map.get(formatted_result, "luckyjackpot", 0)
              }

              {round, result}  # 返回{下标, 结果}的元组

            {:error, _reason} ->
              # 格式化失败，返回空结果
              {round, %{
                "round" => round,
                "freetimes" => 0,
                "jackpotnum" => 0,
                "iconresult" => %{},
                "linecount" => 0,
                "lineresult" => %{},
                "totalmult" => 0,
                "winmoney" => 0,
                "changemoney" => 0,
                "jackpotcash" => 0,
                "luckyjackpot" => 0
              }}
          end

        {:error, _reason} ->
          # 如果生成失败，返回空结果
          {round, %{
            "round" => round,
            "freetimes" => 0,
            "jackpotnum" => 0,
            "iconresult" => %{},
            "linecount" => 0,
            "lineresult" => %{},
            "totalmult" => 0,
            "winmoney" => 0,
            "changemoney" => 0,
            "jackpotcash" => 0,
            "luckyjackpot" => 0
          }}
      end
    end)
    |> Enum.into(%{})  # 转换为Map，下标从1开始
  end

  # 计算免费游戏总奖金 (参考 Slot777 实现)
  defp calculate_total_free_game_winnings(free_game_results) do
    free_game_results
    |> Enum.reduce(0, fn {_round, result}, acc ->
      acc + Map.get(result, "winmoney", 0)
    end)
  end

  # 重置免费游戏状态
  defp reset_free_game_state(state) do
    new_free_game = %{
      remaining_times: 0,
      total_times: 0,
      total_win: 0
    }

    new_game_data = %{
      state.game_data |
      free_game: new_free_game,
      status: @game_state_waiting
    }

    %{state | game_data: new_game_data}
  end

  # 处理 Jackpot 中奖 (支持三个不同的奖池，使用优化的算法)
  defp handle_jackpot_win(state, player, jackpot_count, single_line_bet, total_bet_amount) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # 验证 Jackpot 数量是否有效
    if not SlotCatConstants.valid_jackpot_count?(jackpot_count) do
      Logger.warning("🎰 [SLOTCAT] 无效的 Jackpot 数量: #{jackpot_count}")
      state
    else

    # 获取奖池类型和名称
    pool_type = SlotCatConstants.get_jackpot_pool_type(jackpot_count)
    pool_name = SlotCatConstants.get_jackpot_pool_name(jackpot_count)

    Logger.info("🎰 [SLOTCAT] Jackpot 中奖: #{user_id}, #{pool_name}(#{jackpot_count}个), 单线下注: #{single_line_bet}")

    # 获取对应的奖池数据
    pool_data = case pool_type do
      :left -> state.game_data.jackpot_pools.left      # 左边奖池
      :right -> state.game_data.jackpot_pools.right    # 右边奖池
      :center -> state.game_data.jackpot_pools.center  # 中间奖池
      _ ->
        Logger.error("🎰 [SLOTCAT] 未知的奖池类型: #{pool_type}")
        state
    end

    # 根据单线下注金额和 Jackpot 数量计算奖金比例
    jackpot_percentage = SlotCatConstants.get_jackpot_cash_percentage(single_line_bet, jackpot_count)

    if jackpot_percentage > 0 do
      # 计算 Jackpot 奖金 (从对应的奖池中扣除)
      jackpot_amount = div(pool_data.current_amount * jackpot_percentage, 100)

      Logger.info("🎉 [SLOTCAT_JACKPOT] #{pool_name} 中奖详情 - 用户: #{user_id}, 奖池金额: #{pool_data.current_amount}, 奖励比例: #{jackpot_percentage}%, 奖金: #{jackpot_amount}")

      # 更新用户余额
      new_state = add_player_points(state, numeric_id, jackpot_amount)

      # 更新对应的奖池
      updated_pool = %{
        pool_data |
        current_amount: max(
          pool_data.current_amount - jackpot_amount,
          pool_data.min_amount
        )
      }

      # 更新奖池数据
      new_jackpot_pools = case pool_type do
        :left -> %{new_state.game_data.jackpot_pools | left: updated_pool}
        :right -> %{new_state.game_data.jackpot_pools | right: updated_pool}
        :center -> %{new_state.game_data.jackpot_pools | center: updated_pool}
      end

      # 记录中奖信息 (包含更详细的信息)
      winner_info = %{
        user_id: user_id,
        user_name: "玩家#{numeric_id}",
        win_time: DateTime.utc_now(),
        bet_amount: total_bet_amount,
        single_line_bet: single_line_bet,
        win_amount: jackpot_amount,
        jackpot_count: jackpot_count,
        pool_type: pool_type,
        pool_name: pool_name,
        jackpot_percentage: jackpot_percentage,
        pool_amount_before: pool_data.current_amount,
        pool_amount_after: updated_pool.current_amount
      }

      new_jackpot_winners = [winner_info | Enum.take(state.game_data.jackpot_winners, 9)]

      # 更新房间状态
      new_game_data = %{
        new_state.game_data |
        jackpot_pools: new_jackpot_pools,
        jackpot_winners: new_jackpot_winners
      }

      final_state = %{new_state | game_data: new_game_data}

      # 发送 Jackpot 中奖通知 (参照前端 onOhterPalyerJackpot 方法)
      send_jackpot_award(final_state, player, jackpot_amount, jackpot_count, total_bet_amount)

      # 发送 Jackpot 中奖后的积分更新通知
      send_money_update_notification(final_state, player)

      # 广播更新后的 Jackpot 奖池信息给所有玩家
      broadcast_jackpot_update(final_state)

      final_state
    else
      Logger.warning("🎰 [SLOTCAT] Jackpot 奖励比例为0，不发放奖金")
      state
    end
    end
  end

  # 发送 Jackpot 中奖通知 (参照前端 onOhterPalyerJackpot 方法的数据格式)
  defp send_jackpot_award(state, player, jackpot_amount, jackpot_count, total_bet_amount) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # 构建符合前端期望的 Jackpot 中奖通知数据格式
    # 前端期望字段: playerid, name, headid, wxheadurl, winscore, jackpotnum, bet
    award_data = %{
      "playerid" => numeric_id,                                    # 玩家ID
      "name" => "玩家#{numeric_id}",                               # 玩家昵称
      "headid" => 1,                                              # 玩家系统头像id
      "wxheadurl" => "",                                          # 自定义头像url
      "winscore" => jackpot_amount,                               # 玩家赢的金币
      "jackpotnum" => jackpot_count,                              # JACKPOT的个数 (3=左边, 4=右边, 5=中间)
      "bet" => total_bet_amount                                   # 玩家下注额
    }

    # 广播给房间内所有玩家 (包括自己)
    Enum.each(state.players, fn {_player_numeric_id, player_data} ->
      target_player = %{user_id: player_data.user.id, numeric_id: player_data.user.numeric_id}
      send_to_player(state, target_player, %{
        "mainId" => 5,
        "subId" => SlotCatConstants.sc_slotcat_jpaward_p(),  # 1006协议
        "data" => award_data
      })
    end)

    Logger.info("🎉 [SLOTCAT_JACKPOT_AWARD] 广播Jackpot中奖通知 - 用户: #{user_id}, 奖金: #{jackpot_amount}, Jackpot数量: #{jackpot_count}")
  end

  # 广播 Jackpot 奖池更新 (参照前端 onUpdateJackpot 方法)
  defp broadcast_jackpot_update(state) do
    # 构建 Jackpot 奖池更新数据
    jackpot_info = %{
      "jackpot" => state.game_data.jackpot_pools.center.current_amount,      # 中间Jackpot的值
      "jackpotleft" => state.game_data.jackpot_pools.left.current_amount,   # 左边Jackpot的值
      "jackpotright" => state.game_data.jackpot_pools.right.current_amount  # 右边Jackpot的值
    }

    # 广播给房间内所有玩家
    Enum.each(state.players, fn {_player_numeric_id, player_data} ->
      target_player = %{user_id: player_data.user.id, numeric_id: player_data.user.numeric_id}
      send_to_player(state, target_player, %{
        "mainId" => 5,
        "subId" => SlotCatConstants.sc_slotcat_jackpot_p(),  # 1005协议
        "data" => jackpot_info
      })
    end)

    Logger.info("📢 [SLOTCAT_JACKPOT_UPDATE] 广播Jackpot奖池更新 - 中间: #{jackpot_info["jackpot"]}, 左边: #{jackpot_info["jackpotleft"]}, 右边: #{jackpot_info["jackpotright"]}")

    state
  end

  # 广播桌面玩家信息
  defp broadcast_desk_players_info(state) do
    # 构建桌面玩家列表
    desk_players = Enum.map(state.players, fn {numeric_id, player_data} ->
      %{
        "playerid" => numeric_id,
        "name" => Map.get(player_data.user, :nickname, "玩家#{numeric_id}"),
        "headid" => Map.get(player_data.user, :head_id, 1),
        "wxheadurl" => Map.get(player_data.user, :avatar_url, ""),
        "pmoney" => get_player_points(state, numeric_id)
      }
    end)

    # 广播给所有玩家
    Enum.each(state.players, fn {_numeric_id, player_data} ->
      player = %{user_id: player_data.user.id, numeric_id: player_data.user.numeric_id}
      send_to_player(state, player, %{
        "mainId" => 5,
        "subId" => SlotCatConstants.sc_slotcat_playerlist_p(),
        "data" => desk_players
      })
    end)
  end

  # 广播游戏结果
  defp broadcast_game_result(state, player, game_result, bet_amount, win_amount, odds) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    result_data = %{
      "playerid" => numeric_id,
      "winscore" => win_amount,
      "mult" => game_result.total_multiplier,
      "pmoney" => get_player_points(state, numeric_id)
    }

    # 广播给所有玩家
    Enum.each(state.players, fn {_id, player_data} ->
      target_player = %{user_id: player_data.user.id, numeric_id: player_data.user.numeric_id}
      send_to_player(state, target_player, %{
        "mainId" => 5,
        "subId" => SlotCatConstants.sc_slotcat_gameresult_p(),
        "data" => result_data
      })
    end)
  end

  # 处理退出房间协议
  defp handle_exit_room_protocol(state, player, _data) do
    user_id = player.user_id
    Logger.info("🎰 [SLOTCAT] 处理退出房间协议: #{user_id}")

    # 移除玩家
    {:ok, new_state} = handle_player_leave(state, user_id)
    new_state
  end

  # 发送余额不足消息 (使用1000协议，提前告知客户端没钱了)
  defp send_insufficient_balance_message(state, player, required_amount \\ 0) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    current_points = get_player_points(state, numeric_id)
    Logger.warning("🎰 [SLOTCAT] 发送余额不足消息: #{user_id}, 当前余额: #{current_points}, 需要金额: #{required_amount}")

    # 使用1000协议返回，告知客户端余额不足，后面的都不用走了
    send_to_player(state, player, %{
      "mainId" => 5,
      "subId" => SlotCatConstants.cs_slotcat_gamestart_p(),  # 1000协议
      "data" => %{
        "error" => 1000,  # 错误码1000表示余额不足
        "message" => "余额不足，无法下注",
        "current_balance" => current_points,
        "required_amount" => required_amount
      }
    })
  end

  # 处理定时器消息
  @impl true
  def handle_info(:jackpot_broadcast, state) do
    Logger.debug("🎰 [SLOTCAT] 广播 Jackpot 信息")

    # 向所有玩家广播 Jackpot 信息
    Enum.each(state.players, fn {_numeric_id, player} ->
      send_jackpot_info(state, player)
    end)

    # 重新调度下次广播
    schedule_jackpot_broadcast()

    {:noreply, state}
  end



  @impl true
  def handle_info(msg, state) do
    Logger.debug("🎰 [SLOTCAT_ROOM] 收到未处理的消息: #{inspect(msg)}")
    {:noreply, state}
  end
end
