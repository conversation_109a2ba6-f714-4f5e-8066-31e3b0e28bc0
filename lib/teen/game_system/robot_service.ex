defmodule Cypridina.Teen.GameSystem.RobotService do
  @moduledoc """
  机器人服务模块

  功能：
  - 管理游戏机器人
  - 提供智能下注策略
  - 模拟真实玩家行为
  - 维持游戏活跃度
  """

  use GenServer
  require Logger

  alias Cypridina.GameSystem.Games.LongHu.LongHuLogic

  # 机器人配置
  @robot_configs %{
    longhu: %{
      # 最小思考时间(毫秒)
      min_think_time: 1000,
      # 最大思考时间(毫秒)
      max_think_time: 8000,
      # 下注概率
      bet_probability: 0.8,
      # 激进下注比例
      aggressive_rate: 0.3,
      # 保守下注比例
      conservative_rate: 0.5,
      # 随机下注比例
      random_rate: 0.2
    }
  }

  # 机器人名称池
  @robot_names [
    "智能机器人",
    "幸运玩家",
    "高手在民间",
    "财神爷",
    "龙虎大师",
    "金币猎人",
    "运气王",
    "策略大师",
    "赌神传人",
    "机器战士",
    "AI玩家",
    "算法高手",
    "数据分析师",
    "概率专家",
    "统计学家"
  ]

  # 机器人头像池
  @robot_avatars [
    "robot_1",
    "robot_2",
    "robot_3",
    "robot_4",
    "robot_5",
    "ai_1",
    "ai_2",
    "ai_3",
    "ai_4",
    "ai_5"
  ]

  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @impl true
  def init(_opts) do
    Logger.info("🤖 [ROBOT_SERVICE] 机器人服务启动")

    state = %{
      # %{robot_id => robot_data}
      active_robots: %{},
      robot_counter: 0
    }

    {:ok, state}
  end

  @doc """
  为房间创建机器人

  ## 参数
  - room_id: 房间ID
  - game_type: 游戏类型
  - count: 机器人数量

  ## 返回
  {:ok, robot_list} | {:error, reason}
  """
  def create_robots_for_room(room_id, game_type, count) do
    GenServer.call(__MODULE__, {:create_robots, room_id, game_type, count})
  end

  @doc """
  移除房间的机器人

  ## 参数
  - room_id: 房间ID
  """
  def remove_robots_from_room(room_id) do
    GenServer.cast(__MODULE__, {:remove_robots, room_id})
  end

  @doc """
  获取机器人下注决策

  ## 参数
  - robot_id: 机器人ID
  - game_state: 游戏状态
  - game_type: 游戏类型

  ## 返回
  {:bet, area, amount} | :no_bet
  """
  def get_robot_bet_decision(robot_id, game_state, game_type) do
    GenServer.call(__MODULE__, {:get_bet_decision, robot_id, game_state, game_type})
  end

  @doc """
  更新机器人状态

  ## 参数
  - robot_id: 机器人ID
  - updates: 更新数据
  """
  def update_robot_state(robot_id, updates) do
    GenServer.cast(__MODULE__, {:update_robot, robot_id, updates})
  end

  @doc """
  获取机器人统计信息
  """
  def get_robot_stats() do
    GenServer.call(__MODULE__, :get_stats)
  end

  @impl true
  def handle_call({:create_robots, room_id, game_type, count}, _from, state) do
    Logger.info("🤖 [ROBOT_SERVICE] 为房间 #{room_id} 创建 #{count} 个机器人")

    robots =
      Enum.map(1..count, fn i ->
        # 使用负数作为机器人ID，从-1000000开始递减
        robot_id = -(1_000_000 + state.robot_counter + i)

        # 使用统一的机器人数据构造方法
        robot_base_data = %{
          numeric_id: robot_id,
          id: robot_id,
          nickname: Enum.random(@robot_names),
          avatar: Enum.random(@robot_avatars),
          level: :rand.uniform(10),
          # 随机余额
          money: 10000 + :rand.uniform(50000),
          balance: 10000 + :rand.uniform(50000),
          created_at: DateTime.utc_now()
        }

        robot_data =
          Cypridina.Teen.GameSystem.PlayerDataBuilder.create_robot_player_data(robot_base_data,
            is_ready: true
          )
          |> Map.merge(%{
            room_id: room_id,
            game_type: game_type,
            strategy: generate_robot_strategy(game_type),
            last_action: nil,
            stats: %{
              games_played: 0,
              total_bet: 0,
              total_win: 0
            }
          })

        {robot_id, robot_data}
      end)

    new_active_robots = Map.merge(state.active_robots, Map.new(robots))

    new_state = %{
      state
      | active_robots: new_active_robots,
        robot_counter: state.robot_counter + count
    }

    robot_list = Enum.map(robots, fn {_id, data} -> data end)

    {:reply, {:ok, robot_list}, new_state}
  end

  @impl true
  def handle_call({:get_bet_decision, robot_id, game_state, game_type}, _from, state) do
    case Map.get(state.active_robots, robot_id) do
      nil ->
        {:reply, :no_bet, state}

      robot_data ->
        decision = make_bet_decision(robot_data, game_state, game_type)

        # 更新机器人最后行动时间
        updated_robot = %{robot_data | last_action: DateTime.utc_now()}
        new_active_robots = Map.put(state.active_robots, robot_id, updated_robot)
        new_state = %{state | active_robots: new_active_robots}

        {:reply, decision, new_state}
    end
  end

  @impl true
  def handle_cast({:remove_robots, room_id}, state) do
    Logger.info("🤖 [ROBOT_SERVICE] 移除房间 #{room_id} 的机器人")

    new_active_robots =
      state.active_robots
      |> Enum.reject(fn {_id, robot} -> robot.room_id == room_id end)
      |> Map.new()

    new_state = %{state | active_robots: new_active_robots}

    {:noreply, new_state}
  end

  @impl true
  def handle_cast({:update_robot, robot_id, updates}, state) do
    case Map.get(state.active_robots, robot_id) do
      nil ->
        {:noreply, state}

      robot_data ->
        updated_robot = Map.merge(robot_data, updates)
        new_active_robots = Map.put(state.active_robots, robot_id, updated_robot)
        new_state = %{state | active_robots: new_active_robots}

        {:noreply, new_state}
    end
  end

  # 私有函数

  defp generate_robot_strategy(game_type) do
    base_strategy = %{
      # 激进程度 0-1
      aggression: :rand.uniform(),
      # 风险承受度 0-1
      risk_tolerance: :rand.uniform(),
      # 跟随模式倾向 0-1
      pattern_following: :rand.uniform(),
      bet_size_preference: Enum.random([:small, :medium, :large])
    }

    # 根据游戏类型调整策略
    case game_type do
      :longhu ->
        Map.merge(base_strategy, %{
          favorite_area: Enum.random([:long, :hu, :he]),
          # 60%-90%的局数会下注
          bet_frequency: 0.6 + :rand.uniform() * 0.3
        })

      _ ->
        base_strategy
    end
  end

  defp make_bet_decision(robot_data, game_state, game_type) do
    config = Map.get(@robot_configs, game_type, %{})

    # 检查是否应该下注
    if should_robot_bet?(robot_data, game_state) do
      case game_type do
        :longhu ->
          make_longhu_bet_decision(robot_data, game_state, config)

        _ ->
          :no_bet
      end
    else
      :no_bet
    end
  end

  defp should_robot_bet?(robot_data, _game_state) do
    # 基于机器人策略决定是否下注
    bet_frequency = Map.get(robot_data.strategy, :bet_frequency, 0.7)
    :rand.uniform() < bet_frequency
  end

  defp make_longhu_bet_decision(robot_data, game_state, config) do
    # 获取历史数据和当前下注情况
    history = Map.get(game_state, :history, [])
    total_bets = Map.get(game_state, :total_bets, %{long: 0, hu: 0, he: 0})

    # 使用游戏逻辑生成下注决策
    game_config = %{
      min_bet: 10,
      max_bet: 1000
    }

    {area, base_amount} = LongHuLogic.generate_robot_bet(history, total_bets, game_config)

    # 根据机器人个性调整下注金额
    adjusted_amount = adjust_bet_amount(robot_data, base_amount)

    # 添加随机延迟模拟思考时间
    think_time =
      Map.get(config, :min_think_time, 1000) +
        :rand.uniform(
          Map.get(config, :max_think_time, 5000) - Map.get(config, :min_think_time, 1000)
        )

    Logger.info(
      "🤖 [ROBOT_SERVICE] 机器人 #{robot_data.id} 决定下注: #{area} #{adjusted_amount} (思考时间: #{think_time}ms)"
    )

    {:bet, area, adjusted_amount, think_time}
  end

  defp adjust_bet_amount(robot_data, base_amount) do
    strategy = robot_data.strategy

    # 根据激进程度调整
    aggression_multiplier = 0.5 + strategy.aggression * 1.5

    # 根据下注偏好调整
    size_multiplier =
      case strategy.bet_size_preference do
        # 0.5-0.8
        :small -> 0.5 + :rand.uniform() * 0.3
        # 0.8-1.2
        :medium -> 0.8 + :rand.uniform() * 0.4
        # 1.2-2.0
        :large -> 1.2 + :rand.uniform() * 0.8
      end

    # 添加随机因子
    # 0.8-1.2
    random_factor = 0.8 + :rand.uniform() * 0.4

    adjusted = base_amount * aggression_multiplier * size_multiplier * random_factor

    # 确保在合理范围内
    max(10, min(1000, round(adjusted)))
  end

  @impl true
  def handle_call(:get_stats, _from, state) do
    stats = %{
      total_robots: map_size(state.active_robots),
      robots_by_game: count_robots_by_game(state.active_robots),
      robots_by_room: count_robots_by_room(state.active_robots)
    }

    {:reply, stats, state}
  end

  defp count_robots_by_game(robots) do
    robots
    |> Enum.group_by(fn {_id, robot} -> robot.game_type end)
    |> Enum.map(fn {game_type, robot_list} -> {game_type, length(robot_list)} end)
    |> Map.new()
  end

  defp count_robots_by_room(robots) do
    robots
    |> Enum.group_by(fn {_id, robot} -> robot.room_id end)
    |> Enum.map(fn {room_id, robot_list} -> {room_id, length(robot_list)} end)
    |> Map.new()
  end
end
