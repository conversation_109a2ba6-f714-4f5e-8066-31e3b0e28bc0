defmodule Teen.ItemActions.DuplicateConfig do
  @moduledoc """
  复制奖励配置的项目操作
  
  允许管理员复制现有的奖励配置，创建一个新的配置
  """
  
  use Backpex.ItemAction

  @impl Backpex.ItemAction
  def label, do: "复制配置"

  @impl Backpex.ItemAction
  def icon, do: "hero-document-duplicate"

  @impl Backpex.ItemAction
  def confirm_label, do: "确认复制"

  @impl Backpex.ItemAction
  def confirm_text, do: "确定要复制此奖励配置吗？"

  @impl Backpex.ItemAction
  def handle(socket, item) do
    try do
      # 准备复制的数据，排除ID和时间戳字段
      duplicate_data = %{
        activity_id: item.activity_id,
        round_number: item.round_number + 1, # 轮次加1
        task_type: item.task_type,
        reward_type: item.reward_type,
        min_reward: item.min_reward,
        max_reward: item.max_reward,
        required_progress: item.required_progress,
        probability: item.probability,
        sort_order: item.sort_order + 1 # 排序加1
      }

      case Teen.ActivitySystem.InviteRewardConfig.create(duplicate_data) do
        {:ok, new_config} ->
          socket = 
            socket
            |> Phoenix.LiveView.put_flash(:info, "成功复制配置，新轮次: #{new_config.round_number}")
          
          {:ok, socket}

        {:error, error} ->
          socket = Phoenix.LiveView.put_flash(socket, :error, "复制失败: #{inspect(error)}")
          {:error, socket}
      end
    rescue
      error ->
        socket = Phoenix.LiveView.put_flash(socket, :error, "复制过程中发生错误: #{inspect(error)}")
        {:error, socket}
    end
  end
end
