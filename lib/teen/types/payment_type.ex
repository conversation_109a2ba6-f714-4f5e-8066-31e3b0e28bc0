defmodule Teen.Types.PaymentType do
  @moduledoc """
  支付类型枚举

  定义所有支持的支付方式类型及其中文描述
  """

  use Ash.Type.Enum,
    values: [
      # 支付宝相关
      alipay: "支付宝",
      alipay_h5: "支付宝H5",
      alipay_scan: "支付宝扫码",
      alipay_wap: "支付宝WAP",
      alipay_app: "支付宝APP",
      
      # 微信相关
      wechat: "微信支付",
      wechat_h5: "微信H5",
      wechat_scan: "微信扫码",
      wechat_jsapi: "微信JSAPI",
      wechat_app: "微信APP",
      wechat_native: "微信原生",
      
      # 银行卡相关
      bank_card: "银行卡",
      quick_pay: "快捷支付",
      online_banking: "网银支付",
      
      # QQ支付
      qq_pay: "QQ钱包",
      qq_scan: "QQ扫码",
      
      # 京东支付
      jd_pay: "京东支付",
      
      # 云闪付
      union_pay: "云闪付",
      
      # 数字货币
      usdt: "USDT",
      btc: "比特币",
      eth: "以太坊",
      
      # 其他
      balance: "余额支付",
      points: "积分支付"
    ]

  @doc """
  获取支付类型的中文描述
  """
  def description(type) do
    case type do
      :alipay -> "支付宝"
      :alipay_h5 -> "支付宝H5"
      :alipay_scan -> "支付宝扫码"
      :alipay_wap -> "支付宝WAP"
      :alipay_app -> "支付宝APP"
      :wechat -> "微信支付"
      :wechat_h5 -> "微信H5"
      :wechat_scan -> "微信扫码"
      :wechat_jsapi -> "微信JSAPI"
      :wechat_app -> "微信APP"
      :wechat_native -> "微信原生"
      :bank_card -> "银行卡"
      :quick_pay -> "快捷支付"
      :online_banking -> "网银支付"
      :qq_pay -> "QQ钱包"
      :qq_scan -> "QQ扫码"
      :jd_pay -> "京东支付"
      :union_pay -> "云闪付"
      :usdt -> "USDT"
      :btc -> "比特币"
      :eth -> "以太坊"
      :balance -> "余额支付"
      :points -> "积分支付"
      _ -> "未知支付方式"
    end
  end

  @doc """
  获取所有支付类型的选项列表，用于下拉选择
  """
  def options do
    values()
    |> Enum.map(fn
      {key, desc} -> {desc, Atom.to_string(key)}
      key when is_atom(key) -> {description(key), Atom.to_string(key)}
    end)
  end

  @doc """
  根据分类获取支付类型
  """
  def by_category(category) do
    case category do
      :alipay -> [:alipay, :alipay_h5, :alipay_scan, :alipay_wap, :alipay_app]
      :wechat -> [:wechat, :wechat_h5, :wechat_scan, :wechat_jsapi, :wechat_app, :wechat_native]
      :bank -> [:bank_card, :quick_pay, :online_banking]
      :qq -> [:qq_pay, :qq_scan]
      :crypto -> [:usdt, :btc, :eth]
      :other -> [:jd_pay, :union_pay, :balance, :points]
      _ -> []
    end
  end

  @doc """
  获取支付类型的分类
  """
  def category(type) do
    cond do
      type in by_category(:alipay) -> :alipay
      type in by_category(:wechat) -> :wechat
      type in by_category(:bank) -> :bank
      type in by_category(:qq) -> :qq
      type in by_category(:crypto) -> :crypto
      type in by_category(:other) -> :other
      true -> :unknown
    end
  end
end
