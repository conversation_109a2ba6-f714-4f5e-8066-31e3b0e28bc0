defmodule Teen.Resources.Inventory.SuggestionCalculator do
  @moduledoc """
  建议计算器
  
  基于趋势分析、场景检测和运维配置计算最终的概率调整建议。
  使用立方根算法实现平滑调控，避免极端反应。
  """
  
  require Logger
  
  # 游戏类型稳定区间配置
  @stability_thresholds %{
    1 => %{collect_threshold: -1.5, release_threshold: +2.5},  # 单人游戏
    2 => %{collect_threshold: -2.0, release_threshold: +3.0},  # 多人游戏  
    3 => %{collect_threshold: -3.0, release_threshold: +4.0}   # 百人游戏
  }
  
  @doc """
  计算综合概率调整建议
  """
  def calculate_comprehensive_suggestion(trend_analysis, scenario_info, multi_day_analysis, 
                                        collect_ratio, release_ratio, game_type) do
    
    # 1. 检查是否需要连续下降干预
    suggestion = if multi_day_analysis.severity in [:critical, :severe, :concerning] do
      Logger.warn("📉 [MULTI_DAY] 游戏 #{trend_analysis.game_id} 连续#{multi_day_analysis.consecutive_days}天下降")
      calculate_multi_day_decline_adjustment(multi_day_analysis, collect_ratio)
    else
      calculate_normal_suggestion(trend_analysis, scenario_info, collect_ratio, release_ratio, game_type)
    end
    
    # 2. 应用场景特定处理
    scenario_adjusted = apply_scenario_specific_processing(
      suggestion, 
      trend_analysis, 
      scenario_info, 
      collect_ratio, 
      release_ratio
    )
    
    # 3. 应用高放分保护机制
    protected_suggestion = apply_high_release_protection(
      scenario_adjusted, 
      collect_ratio, 
      release_ratio, 
      multi_day_analysis
    )
    
    # 4. 限制在游戏类型最大范围内
    max_adjustment = get_max_adjustment_by_type(game_type)
    final_suggestion = clamp(protected_suggestion, -max_adjustment, max_adjustment)
    
    # 5. 计算置信度和推理
    confidence = calculate_suggestion_confidence(trend_analysis, scenario_info, multi_day_analysis)
    reasoning = generate_suggestion_reasoning(final_suggestion, trend_analysis, scenario_info, multi_day_analysis)
    
    %{
      suggestion: round(final_suggestion),
      confidence: confidence,
      reasoning: reasoning,
      scenario_type: scenario_info.scenario_type,
      multi_day_severity: multi_day_analysis.severity
    }
  end
  
  # 正常情况的建议计算
  defp calculate_normal_suggestion(trend_analysis, scenario_info, collect_ratio, release_ratio, game_type) do
    # 1. 应用场景平滑处理
    smoothed_trend = apply_scenario_smoothing(trend_analysis, scenario_info)
    
    # 2. 计算加权趋势
    weighted_trend = calculate_weighted_trend(smoothed_trend, game_type)
    
    # 3. 应用稳定区间判断
    thresholds = @stability_thresholds[game_type]
    
    cond do
      # 超过收分阈值：明确亏损，需要收分
      weighted_trend < thresholds.collect_threshold ->
        excess_loss = abs(weighted_trend) - abs(thresholds.collect_threshold)
        trend_cube_root = :math.pow(excess_loss, 1/3)
        base_strength = trend_cube_root * (collect_ratio / 20)
        min(base_strength, collect_ratio / 3)
        
      # 超过放分阈值：明确盈利，可以放分
      weighted_trend > thresholds.release_threshold ->
        excess_profit = weighted_trend - thresholds.release_threshold
        trend_cube_root = :math.pow(excess_profit, 1/3)
        base_strength = trend_cube_root * (release_ratio / 10)
        -min(base_strength, release_ratio / 2)
        
      # 在稳定区间内：保持稳定或微调
      true ->
        calculate_stability_zone_adjustment(smoothed_trend, collect_ratio, release_ratio)
    end
  end
  
  # 场景平滑处理
  defp apply_scenario_smoothing(trend_analysis, scenario_info) do
    case scenario_info.scenario_type do
      :whale_dominated ->
        # 鲸鱼主导：大幅平滑
        apply_smoothing(trend_analysis, 0.2)
        
      :high_concurrency_imbalanced ->
        # 高并发不均衡：强力平滑
        concentration = scenario_info.bet_concentration || 0.5
        smoothing_factor = 0.3 + (1 - concentration) * 0.3
        apply_smoothing(trend_analysis, smoothing_factor)
        
      :high_concurrency_balanced ->
        # 高并发均衡：中等平滑
        apply_smoothing(trend_analysis, 0.6)
        
      :small_group_high_stakes ->
        # 小群体高额：适度平滑
        apply_smoothing(trend_analysis, 0.5)
        
      _ ->
        # 其他场景：轻微平滑
        apply_smoothing(trend_analysis, 0.8)
    end
  end
  
  # 应用平滑处理
  defp apply_smoothing(trend_analysis, smoothing_factor) do
    %{trend_analysis |
      short_trend: trend_analysis.short_trend * smoothing_factor,
      medium_trend: trend_analysis.medium_trend * smoothing_factor * 1.1,
      long_trend: trend_analysis.long_trend * 0.9
    }
  end
  
  # 加权趋势计算（不同游戏类型权重不同）
  defp calculate_weighted_trend(trend_analysis, game_type) do
    weights = case game_type do
      1 -> %{short: 0.6, medium: 0.3, long: 0.1}  # 单人游戏更注重短期
      2 -> %{short: 0.4, medium: 0.4, long: 0.2}  # 多人游戏平衡短中期
      3 -> %{short: 0.3, medium: 0.4, long: 0.3}  # 百人游戏更注重长期
    end
    
    trend_analysis.short_trend * weights.short +
    trend_analysis.medium_trend * weights.medium +
    trend_analysis.long_trend * weights.long
  end
  
  # 稳定区间内的微调
  defp calculate_stability_zone_adjustment(trend_analysis, collect_ratio, release_ratio) do
    # 在稳定区间内，根据长期趋势和当前余额进行微调
    long_term_trend = trend_analysis.long_trend
    current_balance = trend_analysis.current_balance
    
    cond do
      # 长期亏损且当前余额很低：轻微收分倾向
      long_term_trend < -5.0 and current_balance < -1000 ->
        collect_ratio / 50  # 很小的收分倾向
        
      # 长期盈利且当前余额很高：轻微放分倾向  
      long_term_trend > 8.0 and current_balance > 5000 ->
        -(release_ratio / 50)  # 很小的放分倾向
        
      # 其他情况：完全不调整
      true -> 
        0
    end
  end
  
  # 连续下降调整
  defp calculate_multi_day_decline_adjustment(multi_day_analysis, collect_ratio) do
    case multi_day_analysis.severity do
      :critical ->
        # 连续多天大幅下降：强力干预
        base_adjustment = :math.pow(abs(multi_day_analysis.cumulative_decline), 1/3) * 
                         (collect_ratio / 15)
        day_multiplier = min(2.0, multi_day_analysis.consecutive_days / 3)
        min(base_adjustment * day_multiplier, collect_ratio / 1.5)
        
      :severe ->
        # 连续下降严重：明确干预
        base_adjustment = :math.pow(abs(multi_day_analysis.cumulative_decline), 1/3) * 
                         (collect_ratio / 20)
        day_multiplier = min(1.5, multi_day_analysis.consecutive_days / 2.5)
        min(base_adjustment * day_multiplier, collect_ratio / 2)
        
      :concerning ->
        # 连续下降需要关注：适度干预
        base_adjustment = :math.pow(abs(multi_day_analysis.cumulative_decline), 1/3) * 
                         (collect_ratio / 25)
        day_multiplier = min(1.3, multi_day_analysis.consecutive_days / 2)
        min(base_adjustment * day_multiplier, collect_ratio / 3)
    end
  end
  
  # 场景特定处理
  defp apply_scenario_specific_processing(suggestion, trend_analysis, scenario_info, collect_ratio, release_ratio) do
    case scenario_info.scenario_type do
      :whale_dominated ->
        # 鲸鱼主导场景：延迟响应，保守调整
        suggestion * 0.5
        
      :high_concurrency_imbalanced ->
        # 高并发不均衡：复合处理策略
        suggestion * 0.6
        
      :small_group_high_stakes ->
        # 小群体高额：精确调整
        suggestion * 0.9
        
      _ ->
        # 其他场景：正常处理
        suggestion
    end
  end
  
  # 高放分保护机制
  defp apply_high_release_protection(suggestion, collect_ratio, release_ratio, multi_day_analysis) do
    admin_bias = release_ratio - collect_ratio
    
    cond do
      # 高放分策略 (放分比例明显高于收分)
      release_ratio >= 25 and admin_bias > 10 ->
        apply_aggressive_release_protection(suggestion, release_ratio, multi_day_analysis)
        
      # 中等放分策略
      release_ratio >= 15 and admin_bias > 5 ->
        apply_moderate_release_protection(suggestion, release_ratio, multi_day_analysis)
        
      # 其他情况：正常执行
      true ->
        suggestion
    end
  end
  
  # 积极放分期间的保护
  defp apply_aggressive_release_protection(suggestion, release_ratio, multi_day_analysis) do
    cond do
      # 极端情况：必须干预
      multi_day_analysis.consecutive_days >= 5 and multi_day_analysis.cumulative_decline < -20 ->
        min(suggestion, release_ratio / 3)
        
      # 严重情况：限制性干预
      multi_day_analysis.consecutive_days >= 3 and multi_day_analysis.cumulative_decline < -15 ->
        max(0, min(suggestion, release_ratio / 5))
        
      # 一般下降：最小干预
      multi_day_analysis.consecutive_days >= 2 ->
        if suggestion > release_ratio / 6, do: release_ratio / 8, else: 0
        
      # 正常情况：尊重放分策略
      true ->
        if suggestion < 0, do: suggestion, else: 0
    end
  end
  
  # 适度放分期间的保护
  defp apply_moderate_release_protection(suggestion, release_ratio, multi_day_analysis) do
    cond do
      # 严重连续下降：适度干预
      multi_day_analysis.consecutive_days >= 3 and multi_day_analysis.cumulative_decline < -12 ->
        min(suggestion, release_ratio / 3)
        
      # 一般连续下降：轻微干预
      multi_day_analysis.consecutive_days >= 2 and multi_day_analysis.cumulative_decline < -8 ->
        min(suggestion, release_ratio / 4)
        
      # 其他情况：限制干预强度
      true ->
        min(suggestion, release_ratio / 2)
    end
  end
  
  # 计算建议置信度
  defp calculate_suggestion_confidence(trend_analysis, scenario_info, multi_day_analysis) do
    base_confidence = 85  # 基础置信度85%
    
    # 数据质量影响
    data_penalty = if trend_analysis.data_points < 10, do: -20, else: 0
    
    # 场景复杂度影响
    scenario_penalty = case scenario_info.scenario_type do
      :whale_dominated -> -30
      :high_concurrency_imbalanced -> -25
      :high_concurrency_balanced -> -15
      _ -> 0
    end
    
    # 连续下降影响
    decline_bonus = case multi_day_analysis.severity do
      :critical -> 10  # 连续下降时置信度反而提高
      :severe -> 5
      _ -> 0
    end
    
    final_confidence = base_confidence + data_penalty + scenario_penalty + decline_bonus
    max(20, min(95, final_confidence))  # 限制在20%-95%之间
  end
  
  # 生成建议推理
  defp generate_suggestion_reasoning(suggestion, trend_analysis, scenario_info, multi_day_analysis) do
    parts = []
    
    # 场景描述
    parts = [scenario_description(scenario_info.scenario_type) | parts]
    
    # 趋势描述
    if abs(trend_analysis.short_trend) > 2 do
      trend_desc = if trend_analysis.short_trend > 0, do: "短期上涨#{trend_analysis.short_trend}%", else: "短期下降#{abs(trend_analysis.short_trend)}%"
      parts = [trend_desc | parts]
    end
    
    # 连续下降描述
    if multi_day_analysis.consecutive_days > 0 do
      decline_desc = "连续#{multi_day_analysis.consecutive_days}天下降#{abs(multi_day_analysis.cumulative_decline)}%"
      parts = [decline_desc | parts]
    end
    
    # 建议描述
    suggestion_desc = if suggestion > 0, do: "建议收分#{suggestion}%", else: "建议放分#{abs(suggestion)}%"
    parts = [suggestion_desc | parts]
    
    Enum.join(Enum.reverse(parts), "，")
  end
  
  # 场景描述
  defp scenario_description(scenario_type) do
    case scenario_type do
      :whale_dominated -> "鲸鱼玩家主导"
      :high_concurrency_balanced -> "高并发均衡"
      :high_concurrency_imbalanced -> "高并发不均衡"
      :medium_mixed -> "中等混合"
      :small_group_high_stakes -> "小群体高额"
      :small_group_low_stakes -> "小群体低额"
      _ -> "标准场景"
    end
  end
  
  # 游戏类型最大调整限制
  defp get_max_adjustment_by_type(game_type) do
    case game_type do
      1 -> 10  # 单人游戏：最大±10%
      2 -> 8   # 多人游戏：最大±8%
      3 -> 6   # 百人游戏：最大±6%
    end
  end
  
  # 工具函数
  defp clamp(value, min_val, max_val) do
    value |> max(min_val) |> min(max_val)
  end
end
