defmodule Teen.Resources.Inventory.ScenarioDetector do
  @moduledoc """
  综合场景检测器
  
  同时考虑人数、下注额、波动等多个维度，识别不同的游戏场景：
  - 鲸鱼主导场景：少数大额玩家主导
  - 高并发均衡场景：很多人，下注相对均衡
  - 高并发不均衡场景：很多人，但下注很不均衡
  - 中等混合场景：中等人数，混合下注
  - 小群体高额场景：少数人，高额下注
  - 小群体低额场景：少数人，小额下注
  """
  
  require Logger
  
  @doc """
  检测当前游戏的综合场景
  """
  def detect_comprehensive_scenario(game_id) do
    # 1. 基础数据收集
    player_metrics = analyze_player_composition(game_id)
    transaction_metrics = analyze_transaction_patterns(game_id)
    balance_metrics = analyze_balance_changes(game_id)
    
    # 2. 多维度分析
    scenario = %{
      # 玩家维度
      player_count: player_metrics.total_players,
      player_composition: classify_player_composition(player_metrics),
      
      # 下注维度
      bet_distribution: analyze_bet_distribution(transaction_metrics),
      bet_concentration: calculate_bet_concentration(transaction_metrics),
      
      # 影响维度
      balance_impact: classify_balance_impact(balance_metrics),
      volatility_source: identify_volatility_source(player_metrics, transaction_metrics),
      
      # 综合分类
      scenario_type: classify_comprehensive_scenario(player_metrics, transaction_metrics, balance_metrics),
      complexity_level: determine_complexity_level(player_metrics, transaction_metrics),
      
      # 并发检测
      concurrency_info: detect_concurrency_scenario(player_metrics, transaction_metrics)
    }
    
    Logger.info("🎯 [SCENARIO] 游戏 #{game_id} 场景检测: #{scenario.scenario_type}")
    scenario
  end
  
  # 分析玩家构成
  defp analyze_player_composition(game_id) do
    recent_players = get_recent_active_players(game_id, 10)  # 最近10分钟
    
    # 按下注额分类玩家
    {small_players, medium_players, large_players, whale_players} = 
      classify_players_by_bet_size(recent_players)
    
    total_players = length(recent_players)
    
    %{
      total_players: total_players,
      small_players: length(small_players),    # 下注 < 500
      medium_players: length(medium_players),  # 下注 500-2000
      large_players: length(large_players),    # 下注 2000-10000
      whale_players: length(whale_players),    # 下注 > 10000
      
      # 玩家分布比例
      small_ratio: safe_divide(length(small_players), total_players),
      large_ratio: safe_divide(length(large_players) + length(whale_players), total_players),
      whale_ratio: safe_divide(length(whale_players), total_players)
    }
  end
  
  # 按下注额分类玩家
  defp classify_players_by_bet_size(players) do
    Enum.reduce(players, {[], [], [], []}, fn player, {small, medium, large, whale} ->
      case player.avg_bet_amount do
        bet when bet < 500 -> {[player | small], medium, large, whale}
        bet when bet < 2000 -> {small, [player | medium], large, whale}
        bet when bet < 10000 -> {small, medium, [player | large], whale}
        _ -> {small, medium, large, [player | whale]}
      end
    end)
  end
  
  # 分析交易模式
  defp analyze_transaction_patterns(game_id) do
    recent_transactions = get_recent_transactions(game_id, 10)  # 最近10分钟
    
    %{
      recent_transactions: recent_transactions,
      transaction_count: length(recent_transactions),
      transaction_frequency: calculate_transaction_frequency(recent_transactions),
      bet_distribution: analyze_bet_distribution_from_transactions(recent_transactions)
    }
  end
  
  # 分析下注分布
  defp analyze_bet_distribution_from_transactions(transactions) do
    if length(transactions) == 0 do
      %{
        total_bet_amount: 0,
        average_bet: 0,
        gini_coefficient: 0,
        concentration_ratio: 0,
        bet_variance: 0
      }
    else
      bet_amounts = Enum.map(transactions, & &1.bet_amount)
      total_bet_amount = Enum.sum(bet_amounts)
      
      # 计算基尼系数（衡量下注不平等程度）
      gini_coefficient = calculate_gini_coefficient(bet_amounts)
      
      # 计算集中度 - 前10%玩家的下注占比
      sorted_bets = Enum.sort(bet_amounts, :desc)
      top_10_percent_count = max(1, div(length(bet_amounts), 10))
      top_10_percent_bets = Enum.take(sorted_bets, top_10_percent_count)
      top_10_percent_amount = Enum.sum(top_10_percent_bets)
      concentration_ratio = safe_divide(top_10_percent_amount, total_bet_amount)
      
      %{
        total_bet_amount: total_bet_amount,
        average_bet: safe_divide(total_bet_amount, length(bet_amounts)),
        gini_coefficient: gini_coefficient,  # 0-1，越接近1越不平等
        concentration_ratio: concentration_ratio,  # 前10%玩家的下注占比
        bet_variance: calculate_variance(bet_amounts)
      }
    end
  end
  
  # 计算基尼系数
  defp calculate_gini_coefficient(values) when length(values) <= 1, do: 0
  defp calculate_gini_coefficient(values) do
    sorted_values = Enum.sort(values)
    n = length(sorted_values)
    
    # 基尼系数计算公式
    sum_weighted = sorted_values
    |> Enum.with_index(1)
    |> Enum.reduce(0, fn {value, index}, acc ->
      acc + value * (2 * index - n - 1)
    end)
    
    total_sum = Enum.sum(sorted_values)
    
    if total_sum > 0 do
      sum_weighted / (n * total_sum)
    else
      0
    end
  end
  
  # 计算方差
  defp calculate_variance(values) when length(values) <= 1, do: 0
  defp calculate_variance(values) do
    mean = Enum.sum(values) / length(values)
    variance = values
    |> Enum.map(fn x -> :math.pow(x - mean, 2) end)
    |> Enum.sum()
    |> Kernel./(length(values))
    
    variance
  end
  
  # 综合场景分类
  defp classify_comprehensive_scenario(player_metrics, transaction_metrics, balance_metrics) do
    player_count = player_metrics.total_players
    large_player_ratio = player_metrics.large_ratio
    whale_ratio = player_metrics.whale_ratio
    concentration_ratio = transaction_metrics.bet_distribution.concentration_ratio
    
    cond do
      # 鲸鱼主导场景：少数大额玩家主导
      concentration_ratio > 0.8 and (large_player_ratio > 0.3 or whale_ratio > 0.1) ->
        :whale_dominated
        
      # 高并发均衡场景：很多人，下注相对均衡
      player_count > 50 and concentration_ratio < 0.4 ->
        :high_concurrency_balanced
        
      # 高并发不均衡场景：很多人，但下注很不均衡
      player_count > 50 and concentration_ratio > 0.6 ->
        :high_concurrency_imbalanced
        
      # 中等混合场景：中等人数，混合下注
      player_count > 20 and concentration_ratio > 0.4 and concentration_ratio < 0.7 ->
        :medium_mixed
        
      # 小群体高额场景：少数人，高额下注
      player_count <= 20 and large_player_ratio > 0.5 ->
        :small_group_high_stakes
        
      # 小群体低额场景：少数人，小额下注
      player_count <= 20 and large_player_ratio < 0.2 ->
        :small_group_low_stakes
        
      # 默认：标准场景
      true ->
        :standard
    end
  end
  
  # 检测并发场景
  defp detect_concurrency_scenario(player_metrics, transaction_metrics) do
    concurrent_players = player_metrics.total_players
    transaction_freq = transaction_metrics.transaction_frequency
    
    scenario_type = cond do
      # 超高并发：>100人同时在线，每分钟>500笔交易
      concurrent_players > 100 and transaction_freq > 500 ->
        :ultra_high_concurrency
        
      # 高并发：50-100人，每分钟200-500笔交易
      concurrent_players > 50 and transaction_freq > 200 ->
        :high_concurrency
        
      # 中等并发：20-50人，每分钟50-200笔交易
      concurrent_players > 20 and transaction_freq > 50 ->
        :medium_concurrency
        
      # 低并发：<20人
      true ->
        :low_concurrency
    end
    
    %{
      scenario_type: scenario_type,
      concurrent_players: concurrent_players,
      transaction_frequency: transaction_freq
    }
  end
  
  # 安全除法，避免除零错误
  defp safe_divide(numerator, denominator) when denominator == 0, do: 0
  defp safe_divide(numerator, denominator), do: numerator / denominator
  
  # 占位函数 - 需要实现具体的数据获取逻辑
  defp get_recent_active_players(game_id, minutes) do
    # TODO: 从钱包快照或交易记录中获取最近活跃玩家
    []
  end
  
  defp get_recent_transactions(game_id, minutes) do
    # TODO: 获取最近的交易记录
    []
  end
  
  defp calculate_transaction_frequency(transactions) do
    # TODO: 计算交易频率（每分钟交易数）
    length(transactions)
  end
  
  defp analyze_balance_changes(game_id) do
    # TODO: 分析余额变化
    %{}
  end
  
  defp classify_player_composition(player_metrics) do
    # TODO: 分类玩家构成
    :balanced
  end
  
  defp analyze_bet_distribution(transaction_metrics) do
    # TODO: 分析下注分布
    %{}
  end
  
  defp calculate_bet_concentration(transaction_metrics) do
    # TODO: 计算下注集中度
    0.5
  end
  
  defp classify_balance_impact(balance_metrics) do
    # TODO: 分类余额影响
    :normal
  end
  
  defp identify_volatility_source(player_metrics, transaction_metrics) do
    # TODO: 识别波动来源
    :distributed
  end
  
  defp determine_complexity_level(player_metrics, transaction_metrics) do
    # TODO: 确定复杂度等级
    :medium
  end
end
