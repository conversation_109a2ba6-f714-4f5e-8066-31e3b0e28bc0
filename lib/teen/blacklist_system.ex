defmodule Teen.BlacklistSystem do
  @moduledoc """
  黑名单管理域

  包含支付黑名单、兑换黑名单、银行卡黑名单、作弊黑名单等功能
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  admin do
    show? true
  end

  resources do
    resource Teen.BlacklistSystem.PaymentBlacklist
    # resource Teen.BlacklistSystem.ExchangeBlacklist
    # resource Teen.BlacklistSystem.BankCardBlacklist
    # resource Teen.BlacklistSystem.CheatBlacklist
  end
end
