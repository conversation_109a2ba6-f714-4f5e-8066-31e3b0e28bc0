defmodule Teen.ResourceActions.BulkUpdateProbability do
  @moduledoc """
  批量更新奖励配置概率的资源操作
  
  允许管理员选择多个奖励配置，然后批量更新它们的概率值
  """
  
  use Backpex.ResourceAction

  @impl Backpex.ResourceAction
  def label, do: "批量更新概率"

  @impl Backpex.ResourceAction
  def icon, do: "hero-calculator"

  @impl Backpex.ResourceAction
  def confirm_label, do: "确认更新"

  @impl Backpex.ResourceAction
  def confirm_text, do: "确定要批量更新选中配置的概率吗？"

  @impl Backpex.ResourceAction
  def fields do
    [
      probability: %{
        module: Backpex.Fields.Number,
        label: "新概率",
        help_text: "输入新的概率值（0.0 - 1.0）",
        required: true
      }
    ]
  end

  @impl Backpex.ResourceAction
  def handle(socket, data, selected_items) do
    %{probability: probability} = data

    # 验证概率值
    case validate_probability(probability) do
      {:ok, validated_probability} ->
        try do
          # 批量更新选中的配置
          results = 
            selected_items
            |> Enum.map(fn item ->
              Teen.ActivitySystem.InviteRewardConfig.update(item, %{probability: validated_probability})
            end)

          # 检查是否有失败的更新
          failed_updates = Enum.filter(results, fn
            {:error, _} -> true
            _ -> false
          end)

          if Enum.empty?(failed_updates) do
            socket = 
              socket
              |> Phoenix.LiveView.put_flash(:info, "成功更新 #{length(selected_items)} 个配置的概率")
              |> Phoenix.LiveView.assign(selected_items: [])
            
            {:ok, socket}
          else
            socket = Phoenix.LiveView.put_flash(socket, :error, "部分配置更新失败，请检查后重试")
            {:error, socket}
          end
        rescue
          error ->
            socket = Phoenix.LiveView.put_flash(socket, :error, "更新过程中发生错误: #{inspect(error)}")
            {:error, socket}
        end

      {:error, message} ->
        socket = Phoenix.LiveView.put_flash(socket, :error, message)
        {:error, socket}
    end
  end

  # 验证概率值
  defp validate_probability(probability) when is_number(probability) do
    cond do
      probability < 0.0 ->
        {:error, "概率值不能小于 0.0"}
      
      probability > 1.0 ->
        {:error, "概率值不能大于 1.0"}
      
      true ->
        {:ok, probability}
    end
  end

  defp validate_probability(probability) when is_binary(probability) do
    case Float.parse(probability) do
      {float_value, ""} ->
        validate_probability(float_value)
      
      _ ->
        {:error, "概率值必须是有效的数字"}
    end
  end

  defp validate_probability(_) do
    {:error, "概率值必须是数字"}
  end
end
