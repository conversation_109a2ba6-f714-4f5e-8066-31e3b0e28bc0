defmodule Teen.ResourceActions.DuplicateConfig do
  @moduledoc """
  复制支付配置的资源操作
  """

  use Backpex.ResourceAction

  import Ecto.Changeset

  @impl Backpex.ResourceAction
  def title, do: "复制支付配置"

  @impl Backpex.ResourceAction
  def label, do: "复制配置"

  @impl Backpex.ResourceAction
  def fields do
    [
      gateway_name: %{
        module: Backpex.Fields.Text,
        label: "网关名称",
        type: :string
      },
      payment_type: %{
        module: Backpex.Fields.Select,
        label: "支付类型",
        options: Teen.Types.PaymentType.options(),
        type: :string
      },
      payment_type_name: %{
        module: Backpex.Fields.Text,
        label: "支付类型名称",
        type: :string
      },
      min_amount: %{
        module: Backpex.Fields.Number,
        label: "最小金额（分）",
        type: :decimal
      },
      max_amount: %{
        module: Backpex.Fields.Number,
        label: "最大金额（分）",
        type: :decimal
      },
      fee_rate: %{
        module: Backpex.Fields.Number,
        label: "手续费率（%）",
        type: :decimal
      },
      deduction_rate: %{
        module: Backpex.Fields.Number,
        label: "扣除费率（%）",
        type: :decimal
      }
    ]
  end

  @required_fields ~w[gateway_name payment_type payment_type_name min_amount max_amount fee_rate deduction_rate]a

  @impl Backpex.ResourceAction
  def changeset(change, attrs, _metadata \\ []) do
    change
    |> cast(attrs, @required_fields)
    |> validate_required(@required_fields)
    |> validate_number(:min_amount, greater_than: 0)
    |> validate_number(:max_amount, greater_than: 0)
    |> validate_number(:fee_rate, greater_than_or_equal_to: 0)
    |> validate_number(:deduction_rate, greater_than_or_equal_to: 0)
  end

  @impl Backpex.ResourceAction
  def handle(socket, data) do
    %{selected_items: selected_items} = socket.assigns

    # 获取第一个选中的配置作为模板
    case selected_items do
      [config_id | _] ->
        case Teen.PaymentSystem.PaymentConfig.read(config_id) do
          {:ok, config} ->
            # 创建新配置
            new_config_data =
              data
              |> Map.put(:gateway_id, config.gateway_id)
              |> Map.put(:status, 0)  # 默认禁用
              |> Map.put(:sort_order, 0)

            case Teen.PaymentSystem.PaymentConfig.create(new_config_data) do
              {:ok, _new_config} ->
                socket =
                  socket
                  |> Phoenix.LiveView.put_flash(:info, "成功复制支付配置")
                  |> Phoenix.LiveView.assign(selected_items: [])

                {:ok, socket}

              {:error, changeset} ->
                {:error, changeset}
            end

          {:error, _reason} ->
            socket = Phoenix.LiveView.put_flash(socket, :error, "无法找到要复制的配置")
            {:ok, socket}
        end

      [] ->
        socket = Phoenix.LiveView.put_flash(socket, :error, "请先选择要复制的配置")
        {:ok, socket}
    end
  end
end
