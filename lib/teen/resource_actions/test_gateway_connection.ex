defmodule Teen.ResourceActions.TestGatewayConnection do
  @moduledoc """
  测试支付网关连接的资源操作
  """

  use Backpex.ResourceAction

  @impl Backpex.ResourceAction
  def title, do: "测试网关连接"

  @impl Backpex.ResourceAction
  def label, do: "测试连接"

  @impl Backpex.ResourceAction
  def fields, do: []

  @impl Backpex.ResourceAction
  def changeset(change, attrs, _metadata \\ []) do
    change
    |> Ecto.Changeset.cast(attrs, [])
  end

  @impl Backpex.ResourceAction
  def handle(socket, _data) do
    %{selected_items: selected_items} = socket.assigns

    results =
      selected_items
      |> Enum.map(fn gateway_id ->
        case Teen.PaymentSystem.test_gateway_connection(gateway_id) do
          {:ok, _result} -> {:ok, gateway_id}
          {:error, reason} -> {:error, {gateway_id, reason}}
        end
      end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    error_count = Enum.count(results, fn {status, _} -> status == :error end)

    socket =
      if error_count == 0 do
        socket
        |> Phoenix.LiveView.put_flash(:info, "成功测试 #{success_count} 个网关连接")
        |> Phoenix.LiveView.assign(selected_items: [])
      else
        socket
        |> Phoenix.LiveView.put_flash(:warning, "测试完成：#{success_count} 个成功，#{error_count} 个失败")
        |> Phoenix.LiveView.assign(selected_items: [])
      end

    {:ok, socket}
  end
end
