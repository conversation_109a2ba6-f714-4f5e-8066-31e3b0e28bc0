defmodule Teen.ResourceActions.DisableChannel do
  @moduledoc """
  禁用渠道的批量操作
  """

  use Backpex.ResourceAction

  @impl Backpex.ResourceAction
  def fields, do: []

  @impl Backpex.ResourceAction
  def handle(socket, items, _params) do
    try do
      # 批量禁用渠道
      results = Enum.map(items, fn item ->
        case Ash.update(item, :disable) do
          {:ok, updated_item} ->
            {:ok, updated_item}
          {:error, error} ->
            {:error, "禁用渠道 #{item.channel_name} 失败: #{inspect(error)}"}
        end
      end)

      # 检查是否有失败的操作
      failed_results = Enum.filter(results, fn
        {:error, _} -> true
        _ -> false
      end)

      if Enum.empty?(failed_results) do
        success_count = length(items)
        socket
        |> Phoenix.LiveView.put_flash(:info, "成功禁用 #{success_count} 个渠道")
        |> Phoenix.LiveView.assign(selected_items: [])
      else
        error_messages = Enum.map(failed_results, fn {:error, msg} -> msg end)
        socket
        |> Phoenix.LiveView.put_flash(:error, "部分操作失败: #{Enum.join(error_messages, "; ")}")
        |> Phoenix.LiveView.assign(selected_items: [])
      end
    rescue
      error ->
        socket
        |> Phoenix.LiveView.put_flash(:error, "禁用渠道时发生错误: #{inspect(error)}")
        |> Phoenix.LiveView.assign(selected_items: [])
    end
  end
end
