defmodule Teen.ResourceActions.EnableTask do
  @moduledoc """
  启用任务资源操作
  """

  use Backpex.ResourceAction

  @impl Backpex.ResourceAction
  def label, do: "启用任务"

  @impl Backpex.ResourceAction
  def icon, do: "hero-check"

  @impl Backpex.ResourceAction
  def confirm_label, do: "确认启用选中的任务？"

  @impl Backpex.ResourceAction
  def fields, do: []

  @impl Backpex.ResourceAction
  def handle(socket, items, _params) do
    case enable_tasks(items) do
      {:ok, count} ->
        Phoenix.LiveView.put_flash(socket, :info, "成功启用 #{count} 个任务")

      {:error, reason} ->
        Phoenix.LiveView.put_flash(socket, :error, "启用任务失败: #{inspect(reason)}")
    end
  end

  defp enable_tasks(items) do
    try do
      count = Enum.reduce(items, 0, fn item, acc ->
        case update_task_status(item, :enabled) do
          {:ok, _} -> acc + 1
          {:error, _} -> acc
        end
      end)

      {:ok, count}
    rescue
      e -> {:error, e}
    end
  end

  defp update_task_status(item, status) do
    # 根据不同的资源类型调用相应的更新方法
    case item.__struct__ do
      Teen.ActivitySystem.GameTask ->
        Teen.ActivitySystem.GameTask.enable_task(item)
      Teen.ActivitySystem.WeeklyCard ->
        Teen.ActivitySystem.WeeklyCard.enable_card(item)
      Teen.ActivitySystem.SevenDayTask ->
        Teen.ActivitySystem.SevenDayTask.enable_task(item)
      Teen.ActivitySystem.VipGift ->
        Teen.ActivitySystem.VipGift.enable_gift(item)
      Teen.ActivitySystem.RechargeTask ->
        Teen.ActivitySystem.RechargeTask.enable_task(item)
      Teen.ActivitySystem.RechargeWheel ->
        Teen.ActivitySystem.RechargeWheel.enable_wheel(item)
      Teen.ActivitySystem.ScratchCardActivity ->
        Teen.ActivitySystem.ScratchCardActivity.enable_activity(item)
      Teen.ActivitySystem.FirstRechargeGift ->
        Teen.ActivitySystem.FirstRechargeGift.enable_gift(item)
      Teen.ActivitySystem.LossRebateJar ->
        Teen.ActivitySystem.LossRebateJar.enable_jar(item)
      Teen.ActivitySystem.InviteCashActivity ->
        Teen.ActivitySystem.InviteCashActivity.enable_activity(item)
      Teen.ActivitySystem.BindingReward ->
        Teen.ActivitySystem.BindingReward.enable_reward(item)
      Teen.ActivitySystem.FreeBonusTask ->
        Teen.ActivitySystem.FreeBonusTask.enable_task(item)
      _ ->
        {:error, :unsupported_resource}
    end
  end
end
