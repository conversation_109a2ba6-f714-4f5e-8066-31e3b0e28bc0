defmodule Teen.ActivitySystem.InviteService do
  @moduledoc """
  邀请任务服务

  处理邀请任务的核心业务逻辑，包括：
  - 邀请关系验证
  - 任务进度跟踪
  - 奖励计算和发放
  - 邀请码生成和验证
  """

  require Logger

  alias Teen.ActivitySystem.{
    InviteCashActivity,
    InviteRewardConfig,
    UserActivityParticipation,
    RewardClaimRecord
  }
  alias Cypridina.Accounts.{User, AgentRelationship}

  @doc """
  处理用户邀请注册
  """
  def handle_invite_register(inviter_id, invitee_id, _invite_code \\ nil) do
    with {:ok, inviter} <- User.read(inviter_id),
         {:ok, invitee} <- User.read(invitee_id),
         :ok <- validate_invite_eligibility(inviter, invitee),
         {:ok, _relationship} <- create_invite_relationship(inviter_id, invitee_id),
         {:ok, _} <- update_invite_progress(inviter_id, :invite_register) do

      Logger.info("邀请注册成功: #{inviter_id} -> #{invitee_id}")
      {:ok, :invite_registered}
    else
      {:error, reason} ->
        Logger.error("邀请注册失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  处理用户邀请充值
  """
  def handle_invite_recharge(inviter_id, invitee_id, recharge_amount) do
    with {:ok, _relationship} <- get_invite_relationship(inviter_id, invitee_id),
         :ok <- validate_recharge_amount(recharge_amount),
         {:ok, _} <- update_invite_progress(inviter_id, :invite_recharge, recharge_amount) do

      Logger.info("邀请充值成功: #{inviter_id} -> #{invitee_id}, 金额: #{recharge_amount}")
      {:ok, :invite_recharged}
    else
      {:error, reason} ->
        Logger.error("邀请充值失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  处理用户邀请游戏
  """
  def handle_invite_play(inviter_id, invitee_id, game_count \\ 1) do
    with {:ok, _relationship} <- get_invite_relationship(inviter_id, invitee_id),
         {:ok, _} <- update_invite_progress(inviter_id, :invite_play, game_count) do

      Logger.info("邀请游戏成功: #{inviter_id} -> #{invitee_id}, 游戏次数: #{game_count}")
      {:ok, :invite_played}
    else
      {:error, reason} ->
        Logger.error("邀请游戏失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  处理用户邀请留存
  """
  def handle_invite_retention(inviter_id, invitee_id, retention_days) do
    with {:ok, _relationship} <- get_invite_relationship(inviter_id, invitee_id),
         :ok <- validate_retention_days(retention_days),
         {:ok, _} <- update_invite_progress(inviter_id, :invite_retention, retention_days) do

      Logger.info("邀请留存成功: #{inviter_id} -> #{invitee_id}, 留存天数: #{retention_days}")
      {:ok, :invite_retained}
    else
      {:error, reason} ->
        Logger.error("邀请留存失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  检查用户是否可以领取邀请奖励
  """
  def can_claim_invite_reward?(user_id, activity_id, round_number) do
    with {:ok, activity} <- InviteCashActivity.read(activity_id),
         {:ok, config} <- get_reward_config(activity_id, round_number),
         {:ok, participation} <- get_user_participation(user_id, activity_id),
         :ok <- validate_activity_status(activity),
         :ok <- validate_task_completion(participation, config),
         :ok <- validate_not_already_claimed(user_id, activity_id, round_number) do
      {:ok, true}
    else
      {:error, _reason} -> {:ok, false}
    end
  end

  @doc """
  领取邀请奖励
  """
  def claim_invite_reward(user_id, activity_id, round_number) do
    case can_claim_invite_reward?(user_id, activity_id, round_number) do
      {:ok, true} ->
        with {:ok, config} <- get_reward_config(activity_id, round_number),
             {:ok, reward_amount} <- calculate_reward_amount(config),
             {:ok, record} <- create_reward_record(user_id, activity_id, round_number, reward_amount, config.reward_type),
             {:ok, _} <- distribute_reward(user_id, reward_amount, config.reward_type) do

          Logger.info("邀请奖励领取成功: 用户#{user_id}, 活动#{activity_id}, 轮次#{round_number}, 奖励#{reward_amount}")
          {:ok, record}
        else
          {:error, reason} ->
            Logger.error("邀请奖励发放失败: #{inspect(reason)}")
            {:error, reason}
        end

      {:ok, false} ->
        {:error, :not_eligible}
    end
  end

  @doc """
  获取用户邀请统计
  """
  def get_user_invite_stats(user_id) do
    with {:ok, participations} <- list_user_invite_participations(user_id),
         {:ok, rewards} <- list_user_invite_rewards(user_id) do

      stats = %{
        total_invites: calculate_total_invites(participations),
        successful_invites: calculate_successful_invites(participations),
        total_rewards: calculate_total_rewards(rewards),
        active_activities: length(participations)
      }

      {:ok, stats}
    end
  end

  @doc """
  生成邀请码
  """
  def generate_invite_code(user_id) do
    code = "INV" <>
           (user_id |> String.slice(0, 8)) <>
           (:crypto.strong_rand_bytes(4) |> Base.encode16() |> String.slice(0, 6))

    {:ok, code}
  end

  @doc """
  验证邀请码
  """
  def validate_invite_code(invite_code) do
    case String.starts_with?(invite_code, "INV") and String.length(invite_code) == 17 do
      true ->
        user_id_part = String.slice(invite_code, 3, 8)
        # 邀请码格式正确，但由于我们没有真实的用户数据，这里返回错误
        # 在实际应用中，这里应该查询数据库验证用户是否存在
        {:error, :invalid_invite_code}
      false ->
        {:error, :invalid_invite_code}
    end
  end

  # 私有函数

  defp validate_invite_eligibility(inviter, invitee) do
    cond do
      inviter.id == invitee.id ->
        {:error, :cannot_invite_self}

      inviter.status != 1 ->
        {:error, :inviter_inactive}

      invitee.status != 1 ->
        {:error, :invitee_inactive}

      true ->
        :ok
    end
  end

  defp create_invite_relationship(inviter_id, invitee_id) do
    # 检查是否已存在关系
    case get_invite_relationship(inviter_id, invitee_id) do
      {:ok, relationship} ->
        {:ok, relationship}

      {:error, :not_found} ->
        # 创建新的代理关系作为邀请关系
        AgentRelationship.create_relationship(%{
          agent_id: inviter_id,
          subordinate_id: invitee_id,
          level: 1,
          commission_rate: Decimal.new("0.0"),
          status: 1
        })
    end
  end

  defp get_invite_relationship(inviter_id, invitee_id) do
    case AgentRelationship.check_relationship(%{
      agent_id: inviter_id,
      subordinate_id: invitee_id
    }) do
      {:ok, relationship} when not is_nil(relationship) ->
        {:ok, relationship}

      {:ok, nil} ->
        {:error, :not_found}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp update_invite_progress(inviter_id, task_type, progress_delta \\ 1) do
    # 获取或创建用户参与记录
    case get_or_create_participation(inviter_id, task_type) do
      {:ok, participation} ->
        new_progress = participation.progress + progress_delta

        UserActivityParticipation.update_progress(participation, %{
          progress: new_progress,
          participation_data: Map.merge(
            participation.participation_data || %{},
            %{
              "last_updated" => DateTime.utc_now(),
              "task_type" => task_type
            }
          )
        })

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp get_or_create_participation(user_id, task_type) do
    case UserActivityParticipation.get_user_progress(%{
      user_id: user_id,
      activity_type: :invite_cash
    }) do
      {:ok, participation} when not is_nil(participation) ->
        {:ok, participation}

      {:ok, nil} ->
        UserActivityParticipation.create(%{
          user_id: user_id,
          activity_type: :invite_cash,
          progress: 0,
          status: :active,
          participation_data: %{"task_type" => task_type}
        })

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp validate_recharge_amount(amount) do
    if Decimal.compare(amount, Decimal.new("0")) == :gt do
      :ok
    else
      {:error, :invalid_recharge_amount}
    end
  end

  defp validate_retention_days(days) when is_integer(days) and days > 0, do: :ok
  defp validate_retention_days(_), do: {:error, :invalid_retention_days}

  defp get_reward_config(activity_id, round_number) do
    InviteRewardConfig.list_by_activity(%{activity_id: activity_id})
    |> case do
      {:ok, configs} ->
        case Enum.find(configs, &(&1.round_number == round_number)) do
          nil -> {:error, :config_not_found}
          config -> {:ok, config}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp get_user_participation(user_id, activity_id) do
    UserActivityParticipation.get_user_progress(%{
      user_id: user_id,
      activity_type: :invite_cash,
      activity_id: activity_id
    })
  end

  defp validate_activity_status(activity) do
    case activity.status do
      :enabled -> :ok
      _ -> {:error, :activity_disabled}
    end
  end

  defp validate_task_completion(participation, config) do
    required_progress = case config.task_type do
      :invite_register -> 1
      :invite_recharge -> 1
      :invite_play -> 5
      :invite_retention -> 7
    end

    if participation.progress >= required_progress do
      :ok
    else
      {:error, :task_not_completed}
    end
  end

  defp validate_not_already_claimed(user_id, activity_id, round_number) do
    case RewardClaimRecord.list_by_user(%{user_id: user_id}) do
      {:ok, records} ->
        already_claimed = Enum.any?(records, fn record ->
          record.activity_id == activity_id and
          Map.get(record.reward_data || %{}, "round_number") == round_number
        end)

        if already_claimed do
          {:error, :already_claimed}
        else
          :ok
        end

      {:error, _} ->
        :ok
    end
  end

  defp calculate_reward_amount(config) do
    case config.reward_amount do
      nil ->
        # 如果没有固定金额，在最小值和最大值之间随机
        min = config.min_reward
        max = config.max_reward

        if Decimal.compare(min, max) == :lt do
          range = Decimal.sub(max, min)
          random_factor = :rand.uniform() |> Decimal.from_float()
          random_amount = Decimal.mult(range, random_factor)
          amount = Decimal.add(min, random_amount)
          {:ok, amount}
        else
          {:ok, min}
        end

      amount ->
        {:ok, amount}
    end
  end

  defp create_reward_record(user_id, activity_id, round_number, reward_amount, reward_type) do
    RewardClaimRecord.create(%{
      user_id: user_id,
      activity_type: :invite_cash,
      activity_id: activity_id,
      reward_type: reward_type,
      reward_amount: reward_amount,
      reward_data: %{
        "round_number" => round_number,
        "claimed_via" => "invite_service"
      }
    })
  end

  defp distribute_reward(user_id, reward_amount, reward_type) do
    # 这里应该调用相应的系统来发放奖励
    # 例如积分系统、金币系统等
    Logger.info("发放奖励: 用户#{user_id}, 类型#{reward_type}, 金额#{reward_amount}")

    case reward_type do
      :coins ->
        # Cypridina.Ledger.add_coins(user_id, reward_amount, :invite_reward)
        {:ok, :distributed}

      :cash ->
        # Cypridina.Ledger.add_cash(user_id, reward_amount, :invite_reward)
        {:ok, :distributed}

      :points ->
        # Cypridina.Ledger.add_points(user_id, reward_amount, :invite_reward)
        {:ok, :distributed}

      _ ->
        {:ok, :distributed}
    end
  end

  defp list_user_invite_participations(user_id) do
    UserActivityParticipation.list_by_user(%{user_id: user_id})
    |> case do
      {:ok, participations} ->
        invite_participations = Enum.filter(participations, &(&1.activity_type == :invite_cash))
        {:ok, invite_participations}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp list_user_invite_rewards(user_id) do
    RewardClaimRecord.list_by_user(%{user_id: user_id})
    |> case do
      {:ok, records} ->
        invite_records = Enum.filter(records, &(&1.activity_type == :invite_cash))
        {:ok, invite_records}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp calculate_total_invites(participations) do
    Enum.reduce(participations, 0, fn participation, acc ->
      acc + participation.progress
    end)
  end

  defp calculate_successful_invites(participations) do
    Enum.count(participations, fn participation ->
      participation.status == :active and participation.progress > 0
    end)
  end

  defp calculate_total_rewards(rewards) do
    Enum.reduce(rewards, Decimal.new("0"), fn reward, acc ->
      Decimal.add(acc, reward.reward_amount || Decimal.new("0"))
    end)
  end
end
