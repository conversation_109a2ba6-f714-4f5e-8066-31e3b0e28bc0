defmodule Teen.ActivitySystem.ScratchCardService do
  @moduledoc """
  刮刮卡活动业务逻辑服务

  提供刮刮卡活动的统一业务逻辑处理，包括：
  - 活动配置验证
  - 奖励计算和分发
  - 用户参与状态管理
  - 数据统计和分析
  """

  alias Teen.ActivitySystem.{
    ScratchCardActivity,
    ScratchCardTaskLevel,
    ScratchCardTaskRound,
    ScratchCardLevelReward
  }

  @doc """
  获取活动的完整配置信息
  """
  def get_activity_config(activity_id) do
    with {:ok, activity} <- ScratchCardActivity.read(activity_id),
         {:ok, levels} <- ScratchCardTaskLevel.list_by_activity(activity_id),
         {:ok, rounds} <- ScratchCardTaskRound.list_by_activity(activity_id) do

      # 为每个等级加载奖励配置
      levels_with_rewards = Enum.map(levels, fn level ->
        case ScratchCardLevelReward.list_by_config_and_level(activity_id, level.task_level) do
          {:ok, rewards} -> Map.put(level, :rewards, rewards)
          _ -> Map.put(level, :rewards, [])
        end
      end)

      config = %{
        activity: activity,
        levels: levels_with_rewards,
        rounds: rounds,
        stats: calculate_activity_stats(activity, levels_with_rewards, rounds)
      }

      {:ok, config}
    else
      error -> error
    end
  end

  @doc """
  验证活动配置的完整性
  """
  def validate_activity_config(activity_id) do
    case get_activity_config(activity_id) do
      {:ok, config} ->
        errors = []

        # 检查基础配置
        errors = if config.activity.status != :enabled do
          ["活动未启用" | errors]
        else
          errors
        end

        # 检查等级配置
        errors = if length(config.levels) == 0 do
          ["未配置任务等级" | errors]
        else
          errors
        end

        # 检查轮次配置
        errors = if length(config.rounds) == 0 do
          ["未配置任务轮次" | errors]
        else
          errors
        end

        # 检查奖励配置
        levels_without_rewards = Enum.filter(config.levels, fn level ->
          length(level.rewards) == 0
        end)

        errors = if length(levels_without_rewards) > 0 do
          level_numbers = Enum.map(levels_without_rewards, & &1.task_level)
          ["等级 #{Enum.join(level_numbers, ", ")} 未配置奖励" | errors]
        else
          errors
        end

        # 检查概率总和
        probability_errors = Enum.flat_map(config.levels, fn level ->
          total_probability = Enum.reduce(level.rewards, Decimal.new("0"), fn reward, acc ->
            Decimal.add(acc, reward.probability)
          end)

          if Decimal.compare(total_probability, Decimal.new("100")) != :eq do
            ["等级 #{level.task_level} 的奖励概率总和不等于100%"]
          else
            []
          end
        end)

        errors = errors ++ probability_errors

        if length(errors) == 0 do
          {:ok, "配置验证通过"}
        else
          {:error, errors}
        end

      error -> error
    end
  end

  @doc """
  计算用户可以参与的等级
  """
  def calculate_user_eligible_level(user_id, activity_id, user_recharge_amount) do
    case ScratchCardTaskLevel.list_by_activity(activity_id) do
      {:ok, levels} ->
        # 按充值金额从高到低排序，找到用户符合条件的最高等级
        eligible_levels = levels
        |> Enum.filter(fn level ->
          Decimal.compare(user_recharge_amount, level.recharge_amount) != :lt
        end)
        |> Enum.sort_by(& &1.recharge_amount, {:desc, Decimal})

        case eligible_levels do
          [highest_level | _] -> {:ok, highest_level}
          [] -> {:error, :insufficient_recharge}
        end

      error -> error
    end
  end

  @doc """
  随机获取等级奖励
  """
  def get_random_reward(activity_id, level) do
    case ScratchCardLevelReward.get_random_reward(activity_id, level) do
      {:ok, rewards} when length(rewards) > 0 ->
        # 基于概率进行随机选择
        random_value = :rand.uniform(100)
        select_reward_by_probability(rewards, Decimal.new(random_value), Decimal.new("0"))

      {:ok, []} -> {:error, :no_rewards_configured}
      error -> error
    end
  end

  # 私有函数

  defp calculate_activity_stats(activity, levels, rounds) do
    total_rewards = Enum.reduce(levels, 0, fn level, acc ->
      acc + length(level.rewards)
    end)

    min_recharge = case levels do
      [] -> Decimal.new("0")
      levels ->
        levels
        |> Enum.map(& &1.recharge_amount)
        |> Enum.min_by(&Decimal.to_float/1)
    end

    max_recharge = case levels do
      [] -> Decimal.new("0")
      levels ->
        levels
        |> Enum.map(& &1.recharge_amount)
        |> Enum.max_by(&Decimal.to_float/1)
    end

    %{
      total_levels: length(levels),
      total_rounds: length(rounds),
      total_rewards: total_rewards,
      min_recharge_amount: min_recharge,
      max_recharge_amount: max_recharge,
      avg_probability: activity.reward_probability
    }
  end

  defp select_reward_by_probability([], _random_value, _cumulative) do
    {:error, :no_matching_reward}
  end

  defp select_reward_by_probability([reward | rest], random_value, cumulative) do
    new_cumulative = Decimal.add(cumulative, reward.probability)

    if Decimal.compare(random_value, new_cumulative) != :gt do
      # 使用新的奖励结构，直接使用 reward_amount
      reward_result = %{
        reward_type: reward.reward_type,
        amount: reward.reward_amount,
        actual_amount: reward.reward_amount
      }

      {:ok, reward_result}
    else
      select_reward_by_probability(rest, random_value, new_cumulative)
    end
  end

  @doc """
  批量创建活动配置
  """
  def create_complete_activity(activity_params, levels_params, rounds_params \\ []) do
    # 使用事务确保数据一致性
    Cypridina.Repo.transaction(fn ->
      with {:ok, activity} <- ScratchCardActivity.create(activity_params),
           {:ok, _levels} <- create_levels_for_activity(activity.id, levels_params),
           {:ok, _rounds} <- create_rounds_for_activity(activity.id, rounds_params) do
        activity
      else
        {:error, reason} -> Cypridina.Repo.rollback(reason)
      end
    end)
  end

  defp create_levels_for_activity(activity_id, levels_params) do
    results = Enum.map(levels_params, fn level_params ->
      params_with_activity = Map.put(level_params, :activity_id, activity_id)
      ScratchCardTaskLevel.create(params_with_activity)
    end)

    case Enum.find(results, fn result -> match?({:error, _}, result) end) do
      nil -> {:ok, Enum.map(results, fn {:ok, level} -> level end)}
      {:error, reason} -> {:error, reason}
    end
  end

  defp create_rounds_for_activity(activity_id, rounds_params) do
    results = Enum.map(rounds_params, fn round_params ->
      params_with_activity = Map.put(round_params, :activity_id, activity_id)
      ScratchCardTaskRound.create(params_with_activity)
    end)

    case Enum.find(results, fn result -> match?({:error, _}, result) end) do
      nil -> {:ok, Enum.map(results, fn {:ok, round} -> round end)}
      {:error, reason} -> {:error, reason}
    end
  end
end
