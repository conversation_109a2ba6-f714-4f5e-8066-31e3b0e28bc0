defmodule Teen.ActivitySystem.ActivityService do
  @moduledoc """
  活动系统业务逻辑服务

  提供各种活动的业务逻辑处理，包括：
  - 奖励计算
  - 条件判断
  - 状态管理
  - 活动参与
  - 奖励发放
  """

  alias Teen.ActivitySystem.{
    GameTask,
    WeeklyCard,
    SevenDayTask,
    VipGift,
    RechargeTask,
    RechargeWheel,
    WheelPrizeConfig,
    ScratchCardActivity,
    FirstRechargeGift,
    LossRebateJar,
    InviteCashActivity,
    BindingReward,
    FreeBonusTask,
    CdkeyActivity,
    UserActivityParticipation,
    RewardClaimRecord
  }

  alias Cypridina.Accounts.User

  @doc """
  检查用户是否可以参与指定活动
  """
  def can_participate?(user_id, activity_type, activity_id \\ nil) do
    case get_user_participation(user_id, activity_type, activity_id) do
      {:ok, nil} -> true
      {:ok, participation} ->
        participation.status in [:active, :paused]
      {:error, _} -> false
    end
  end

  @doc """
  用户参与活动
  """
  def participate_activity(user_id, activity_type, activity_id \\ nil, initial_data \\ %{}) do
    case can_participate?(user_id, activity_type, activity_id) do
      true ->
        UserActivityParticipation.create(%{
          user_id: user_id,
          activity_type: activity_type,
          activity_id: activity_id,
          progress: 0,
          status: :active,
          participation_data: initial_data
        })
      false ->
        {:error, :already_participating}
    end
  end

  @doc """
  更新用户活动进度
  """
  def update_progress(user_id, activity_type, activity_id \\ nil, progress_delta \\ 1) do
    case get_user_participation(user_id, activity_type, activity_id) do
      {:ok, participation} when not is_nil(participation) ->
        new_progress = participation.progress + progress_delta
        UserActivityParticipation.update_progress(participation, %{
          progress: new_progress,
          participation_data: Map.put(participation.participation_data || %{}, :last_updated, DateTime.utc_now())
        })
      {:ok, nil} ->
        {:error, :not_participating}
      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  检查用户是否可以领取奖励
  """
  def can_claim_reward?(user_id, activity_type, activity_id \\ nil) do
    case get_user_participation(user_id, activity_type, activity_id) do
      {:ok, participation} when not is_nil(participation) ->
        check_reward_eligibility(participation, activity_type, activity_id)
      {:ok, nil} ->
        false
      {:error, _} ->
        false
    end
  end

  @doc """
  用户领取奖励
  """
  def claim_reward(user_id, activity_type, activity_id \\ nil) do
    case can_claim_reward?(user_id, activity_type, activity_id) do
      true ->
        with {:ok, reward_amount} <- calculate_reward(user_id, activity_type, activity_id),
             {:ok, _record} <- create_reward_record(user_id, activity_type, activity_id, reward_amount),
             {:ok, _} <- distribute_reward(user_id, reward_amount) do
          {:ok, reward_amount}
        else
          {:error, reason} -> {:error, reason}
        end
      false ->
        {:error, :not_eligible}
    end
  end

  @doc """
  获取用户活动参与记录
  """
  def get_user_participation(user_id, activity_type, activity_id \\ nil) do
    UserActivityParticipation.get_user_progress(%{
      user_id: user_id,
      activity_type: activity_type,
      activity_id: activity_id
    })
  end

  @doc """
  获取用户活动统计
  """
  def get_user_activity_stats(user_id, start_date \\ nil, end_date \\ nil) do
    RewardClaimRecord.get_user_total_rewards(%{
      user_id: user_id,
      start_date: start_date,
      end_date: end_date
    })
  end

  # 私有函数

  defp check_reward_eligibility(participation, activity_type, activity_id) do
    case activity_type do
      :game_task -> check_game_task_eligibility(participation, activity_id)
      :seven_day_task -> check_seven_day_eligibility(participation)
      :weekly_card -> check_weekly_card_eligibility(participation, activity_id)
      :vip_gift -> check_vip_gift_eligibility(participation)
      :recharge_task -> check_recharge_task_eligibility(participation, activity_id)
      :first_recharge_gift -> check_first_recharge_eligibility(participation, activity_id)
      :binding_reward -> check_binding_reward_eligibility(participation, activity_id)
      _ -> false
    end
  end

  defp check_game_task_eligibility(participation, activity_id) do
    case GameTask.read(activity_id) do
      {:ok, task} -> participation.progress >= task.required_count
      {:error, _} -> false
    end
  end

  defp check_seven_day_eligibility(participation) do
    # 检查连续登录天数
    participation.progress >= 1
  end

  defp check_weekly_card_eligibility(participation, activity_id) do
    case WeeklyCard.read(activity_id) do
      {:ok, card} ->
        # 检查是否已充值且在有效期内
        participation.status == :active
      {:error, _} -> false
    end
  end

  defp check_vip_gift_eligibility(participation) do
    # 检查VIP等级和时间限制
    participation.status == :active
  end

  defp check_recharge_task_eligibility(participation, activity_id) do
    case RechargeTask.read(activity_id) do
      {:ok, task} -> participation.progress >= task.recharge_amount
      {:error, _} -> false
    end
  end

  defp check_first_recharge_eligibility(participation, activity_id) do
    case FirstRechargeGift.read(activity_id) do
      {:ok, gift} ->
        # 检查用户注册天数和首充状态
        participation.status == :active
      {:error, _} -> false
    end
  end

  defp check_binding_reward_eligibility(participation, activity_id) do
    case BindingReward.read(activity_id) do
      {:ok, reward} -> participation.status == :active
      {:error, _} -> false
    end
  end

  defp calculate_reward(user_id, activity_type, activity_id) do
    case activity_type do
      :game_task -> calculate_game_task_reward(activity_id)
      :seven_day_task -> calculate_seven_day_reward(user_id)
      :weekly_card -> calculate_weekly_card_reward(activity_id)
      :vip_gift -> calculate_vip_gift_reward(user_id)
      :recharge_task -> calculate_recharge_task_reward(activity_id)
      :first_recharge_gift -> calculate_first_recharge_reward(activity_id)
      :binding_reward -> calculate_binding_reward(activity_id)
      _ -> {:error, :unsupported_activity}
    end
  end

  defp calculate_game_task_reward(activity_id) do
    case GameTask.read(activity_id) do
      {:ok, task} -> {:ok, task.reward_amount}
      {:error, reason} -> {:error, reason}
    end
  end

  defp calculate_seven_day_reward(user_id) do
    # 根据用户连续登录天数计算奖励
    {:ok, Decimal.new("100")}
  end

  defp calculate_weekly_card_reward(activity_id) do
    case WeeklyCard.read(activity_id) do
      {:ok, card} -> {:ok, card.daily_reward}
      {:error, reason} -> {:error, reason}
    end
  end

  defp calculate_vip_gift_reward(user_id) do
    # 根据用户VIP等级计算奖励
    {:ok, Decimal.new("50")}
  end

  defp calculate_recharge_task_reward(activity_id) do
    case RechargeTask.read(activity_id) do
      {:ok, task} -> {:ok, task.reward_amount}
      {:error, reason} -> {:error, reason}
    end
  end

  defp calculate_first_recharge_reward(activity_id) do
    case FirstRechargeGift.read(activity_id) do
      {:ok, gift} -> {:ok, gift.reward_amount}
      {:error, reason} -> {:error, reason}
    end
  end

  defp calculate_binding_reward(activity_id) do
    case BindingReward.read(activity_id) do
      {:ok, reward} -> {:ok, reward.reward_amount}
      {:error, reason} -> {:error, reason}
    end
  end

  defp create_reward_record(user_id, activity_type, activity_id, reward_amount) do
    RewardClaimRecord.create(%{
      user_id: user_id,
      activity_type: activity_type,
      activity_id: activity_id,
      reward_type: :coins,
      reward_amount: reward_amount,
      reward_data: %{claimed_via: "activity_service"}
    })
  end

  defp distribute_reward(user_id, reward_amount) do
    # 这里应该调用积分系统来发放奖励
    # Cypridina.Ledger.add_points(user_id, reward_amount, :activity_reward)
    {:ok, :distributed}
  end
end
