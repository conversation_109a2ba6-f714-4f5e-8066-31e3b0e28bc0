defmodule Teen.ActivitySystem.WheelService do
  @moduledoc """
  转盘服务

  处理充值转盘相关的业务逻辑，包括：
  - 转盘次数计算
  - 随机奖品生成
  - 奖金池管理
  - 中奖记录
  """

  alias Teen.ActivitySystem.{
    RechargeWheel,
    WheelPrizeConfig,
    UserActivityParticipation,
    RewardClaimRecord
  }

  @doc """
  获取用户可用的转盘次数
  """
  def get_available_spins(user_id) do
    case get_user_cumulative_recharge(user_id) do
      {:ok, total_recharge} ->
        case RechargeWheel.get_by_recharge_amount(%{recharge_amount: total_recharge}) do
          {:ok, wheels} ->
            total_spins = Enum.reduce(wheels, 0, fn wheel, acc ->
              acc + wheel.wheel_spins
            end)

            used_spins = get_used_spins(user_id)
            available_spins = max(0, total_spins - used_spins)

            {:ok, available_spins}
          {:error, reason} ->
            {:error, reason}
        end
      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  执行转盘抽奖
  """
  def spin_wheel(user_id) do
    case get_available_spins(user_id) do
      {:ok, available_spins} when available_spins > 0 ->
        case generate_prize() do
          {:ok, prize} ->
            # 记录使用的转盘次数
            record_spin_usage(user_id)

            # 发放奖励
            case distribute_prize(user_id, prize) do
              {:ok, _} ->
                {:ok, prize}
              {:error, reason} ->
                {:error, reason}
            end
          {:error, reason} ->
            {:error, reason}
        end
      {:ok, 0} ->
        {:error, :no_spins_available}
      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取转盘奖品配置
  """
  def get_prize_configs do
    WheelPrizeConfig.list_active_prizes()
  end

  @doc """
  获取当前活跃的转盘活动
  """
  def get_current_wheel_activity do
    RechargeWheel.get_current_wheel()
  end

  @doc """
  检查用户是否有资格参与转盘
  """
  def can_participate?(user_id) do
    case get_current_wheel_activity() do
      {:ok, wheel} ->
        case get_user_cumulative_recharge(user_id) do
          {:ok, total_recharge} ->
            Decimal.compare(total_recharge, wheel.cumulative_recharge) != :lt
          {:error, _} ->
            false
        end
      {:error, _} ->
        false
    end
  end

  @doc """
  获取用户转盘参与统计
  """
  def get_user_wheel_stats(user_id) do
    with {:ok, total_recharge} <- get_user_cumulative_recharge(user_id),
         {:ok, available_spins} <- get_available_spins(user_id) do
      used_spins = get_used_spins(user_id)

      {:ok, %{
        total_recharge: total_recharge,
        available_spins: available_spins,
        used_spins: used_spins,
        can_participate: available_spins > 0
      }}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  计算奖金池总额
  """
  def get_jackpot_pool do
    case RechargeWheel.get_current_wheel() do
      {:ok, wheel} ->
        {:ok, wheel.jackpot_pool}
      {:error, _} ->
        {:ok, Decimal.new("0")}
    end
  end

  @doc """
  更新奖金池
  """
  def update_jackpot_pool(amount) do
    case RechargeWheel.get_current_wheel() do
      {:ok, wheel} ->
        RechargeWheel.update_jackpot_pool(wheel, %{jackpot_pool: amount})
      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  增加奖金池金额
  """
  def add_to_jackpot_pool(amount) do
    case get_jackpot_pool() do
      {:ok, current_pool} ->
        new_amount = Decimal.add(current_pool, amount)
        update_jackpot_pool(new_amount)
      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  从奖金池扣除金额
  """
  def deduct_from_jackpot_pool(amount) do
    case get_jackpot_pool() do
      {:ok, current_pool} ->
        if Decimal.compare(current_pool, amount) != :lt do
          new_amount = Decimal.sub(current_pool, amount)
          update_jackpot_pool(new_amount)
        else
          {:error, :insufficient_jackpot}
        end
      {:error, reason} ->
        {:error, reason}
    end
  end

  # 私有函数

  defp get_user_cumulative_recharge(user_id) do
    # 这里应该从充值记录中计算用户累计充值金额
    # 暂时返回模拟数据
    {:ok, Decimal.new("1000000")}  # 1000元
  end

  defp get_used_spins(user_id) do
    # 从用户活动参与记录中获取已使用的转盘次数
    case UserActivityParticipation.get_user_progress(%{
      user_id: user_id,
      activity_type: :recharge_wheel
    }) do
      {:ok, participation} when not is_nil(participation) ->
        Map.get(participation.participation_data || %{}, "used_spins", 0)
      {:ok, nil} ->
        0
      {:error, _} ->
        0
    end
  end

  defp generate_prize do
    case WheelPrizeConfig.list_active_prizes() do
      {:ok, prizes} when length(prizes) > 0 ->
        # 根据概率随机选择奖品
        selected_prize = select_prize_by_probability(prizes)

        case selected_prize.prize_type do
          :jackpot_percentage ->
            # 奖金池百分比奖励
            case get_jackpot_pool() do
              {:ok, pool_amount} ->
                prize_amount = Decimal.mult(pool_amount, Decimal.div(selected_prize.prize_value, 100))
                {:ok, %{
                  type: :jackpot_percentage,
                  amount: prize_amount,
                  percentage: selected_prize.prize_value,
                  description: "奖金池#{selected_prize.prize_value}%奖励"
                }}
              {:error, reason} ->
                {:error, reason}
            end
          _ ->
            {:ok, %{
              type: selected_prize.prize_type,
              amount: selected_prize.prize_value,
              description: get_prize_description(selected_prize)
            }}
        end
      {:ok, []} ->
        {:error, :no_prizes_configured}
      {:error, reason} ->
        {:error, reason}
    end
  end

  defp select_prize_by_probability(prizes) do
    # 计算总概率
    total_probability = Enum.reduce(prizes, Decimal.new("0"), fn prize, acc ->
      Decimal.add(acc, prize.probability)
    end)

    # 生成随机数
    random_value = Decimal.mult(total_probability, Decimal.from_float(:rand.uniform()))

    # 根据概率选择奖品
    {selected_prize, _} = Enum.reduce_while(prizes, {nil, Decimal.new("0")}, fn prize, {_, acc} ->
      new_acc = Decimal.add(acc, prize.probability)
      if Decimal.compare(random_value, new_acc) <= 0 do
        {:halt, {prize, new_acc}}
      else
        {:cont, {prize, new_acc}}
      end
    end)

    selected_prize || List.first(prizes)
  end

  defp get_prize_description(prize) do
    case prize.prize_type do
      :coins -> "金币 #{prize.prize_value}"
      :cash -> "现金 #{prize.prize_value}"
      :jackpot_percentage -> "奖金池 #{prize.prize_value}%"
      _ -> "未知奖品"
    end
  end

  defp record_spin_usage(user_id) do
    case UserActivityParticipation.get_user_progress(%{
      user_id: user_id,
      activity_type: :recharge_wheel
    }) do
      {:ok, participation} when not is_nil(participation) ->
        current_used = Map.get(participation.participation_data || %{}, "used_spins", 0)
        new_data = Map.put(participation.participation_data || %{}, "used_spins", current_used + 1)

        UserActivityParticipation.update_progress(participation, %{
          participation_data: new_data
        })
      {:ok, nil} ->
        # 创建新的参与记录
        UserActivityParticipation.create(%{
          user_id: user_id,
          activity_type: :recharge_wheel,
          progress: 0,
          status: :active,
          participation_data: %{"used_spins" => 1}
        })
      {:error, reason} ->
        {:error, reason}
    end
  end

  defp distribute_prize(user_id, prize) do
    # 创建奖励记录
    case RewardClaimRecord.create(%{
      user_id: user_id,
      activity_type: :recharge_wheel,
      reward_type: prize.type,
      reward_amount: prize.amount,
      reward_data: %{
        prize_description: prize.description,
        spin_time: DateTime.utc_now()
      }
    }) do
      {:ok, record} ->
        # 发放实际奖励
        case prize.type do
          :jackpot_percentage ->
            # 奖金池奖励需要特殊处理
            distribute_jackpot_prize(user_id, prize.amount)
          _ ->
            # 普通奖励
            distribute_regular_prize(user_id, prize.amount, prize.type)
        end

        {:ok, record}
      {:error, reason} ->
        {:error, reason}
    end
  end

  defp distribute_jackpot_prize(user_id, amount) do
    # 发放奖金池奖励
    # 这里应该调用积分系统
    # Cypridina.Ledger.add_points(user_id, amount, :jackpot_prize)

    # 更新奖金池（减少相应金额）
    update_jackpot_pool(Decimal.negate(amount))

    {:ok, :distributed}
  end

  defp distribute_regular_prize(user_id, amount, type) do
    # 发放普通奖励
    case type do
      :coins ->
        # Cypridina.Ledger.add_points(user_id, amount, :wheel_prize)
        {:ok, :distributed}
      :cash ->
        # 处理现金奖励
        {:ok, :distributed}
      _ ->
        {:error, :unsupported_prize_type}
    end
  end
end
