defmodule Teen.PaymentSystem.GatewaySelector do
  @moduledoc """
  支付网关选择器

  根据业务规则选择最合适的支付网关
  """

  require Logger
  alias Teen.PaymentSystem.PaymentGateway

  @doc """
  选择最佳支付网关
  """
  def select_gateway(opts \\ %{}) do
    gateway_type = Map.get(opts, :gateway_type, "recharge")
    amount = Map.get(opts, :amount)
    currency = Map.get(opts, :currency, "CNY")
    user_id = Map.get(opts, :user_id)

    Logger.info("💰 [GATEWAY_SELECTOR] 选择网关 - 类型: #{gateway_type}, 金额: #{amount}, 币种: #{currency}")

    case find_best_gateway(gateway_type, amount, currency) do
      {:ok, gateway} ->
        config = build_gateway_config(gateway)
        Logger.info("💰 [GATEWAY_SELECTOR] 选中网关: #{config.name} (#{config.channel_id})")
        {:ok, config}

      {:error, :no_available_gateway} ->
        Logger.error("💰 [GATEWAY_SELECTOR] 未找到可用网关 - 类型: #{gateway_type}, 金额: #{amount}")
        {:error, "暂无可用的支付渠道"}

      error ->
        Logger.error("💰 [GATEWAY_SELECTOR] 网关选择失败: #{inspect(error)}")
        {:error, "支付服务暂时不可用"}
    end
  end

  @doc """
  选择代收网关（充值）
  """
  def select_recharge_gateway(amount, currency \\ "CNY", user_id \\ nil) do
    select_gateway(%{
      gateway_type: "recharge",
      amount: amount,
      currency: currency,
      user_id: user_id
    })
  end

  @doc """
  选择代付网关（提现）
  """
  def select_withdraw_gateway(amount, currency \\ "CNY", user_id \\ nil) do
    select_gateway(%{
      gateway_type: "withdraw",
      amount: amount,
      currency: currency,
      user_id: user_id
    })
  end

  @doc """
  验证网关是否支持指定金额
  """
  def validate_amount(gateway_config, amount) do
    cond do
      gateway_config.min_amount && Decimal.compare(amount, gateway_config.min_amount) == :lt ->
        {:error, "金额低于最小限额 #{gateway_config.min_amount}"}

      gateway_config.max_amount && Decimal.compare(amount, gateway_config.max_amount) == :gt ->
        {:error, "金额超过最大限额 #{gateway_config.max_amount}"}

      true ->
        :ok
    end
  end

  @doc """
  验证网关是否支持指定币种
  """
  def validate_currency(gateway_config, currency) do
    if currency in gateway_config.supported_currencies do
      :ok
    else
      {:error, "不支持的币种: #{currency}"}
    end
  end

  @doc """
  获取网关完整URL
  """
  def get_gateway_url(gateway_config, path) do
    base_url = String.trim_trailing(gateway_config.gateway_url, "/")
    path = String.trim_leading(path, "/")
    "#{base_url}/#{path}"
  end

  @doc """
  获取创建订单URL
  """
  def get_create_order_url(gateway_config) do
    get_gateway_url(gateway_config, gateway_config.create_order_path)
  end

  @doc """
  获取查询订单URL
  """
  def get_query_order_url(gateway_config) do
    get_gateway_url(gateway_config, gateway_config.query_order_path)
  end

  @doc """
  检查网关健康状态
  """
  def check_gateway_health(gateway_config) do
    # 这里可以实现网关健康检查逻辑
    # 比如发送ping请求或者检查最近的成功率
    :ok
  end

  # 私有函数
  defp find_best_gateway(gateway_type, amount, currency) do
    amount_decimal = if amount, do: Decimal.new(amount), else: Decimal.new("0")

    case PaymentGateway.find_best_gateway!(gateway_type, amount_decimal, currency) do
      [gateway | _] -> {:ok, gateway}
      [] -> {:error, :no_available_gateway}
    end
  rescue
    error ->
      Logger.error("💰 [GATEWAY_SELECTOR] 查找网关失败: #{inspect(error)}")
      {:error, :gateway_query_failed}
  end

  defp build_gateway_config(gateway) do
    # 从环境变量获取敏感信息
    env_prefix = gateway.name |> String.upcase() |> String.replace(["-", " "], "_")

    %{
      id: gateway.id,
      name: gateway.name,
      gateway_type: gateway.gateway_type,
      gateway_url: gateway.gateway_url,
      create_order_path: gateway.create_order_path,
      query_order_path: gateway.query_order_path,
      channel_id: gateway.channel_id,
      priority: gateway.priority,
      min_amount: gateway.min_amount,
      max_amount: gateway.max_amount,
      supported_currencies: gateway.supported_currencies,
      timeout_seconds: gateway.timeout_seconds,
      config_data: gateway.config_data || %{},

      # 从环境变量获取敏感信息
      merchant_id: get_env_config("PAYMENT_MERCHANT_ID", env_prefix),
      secret_key: get_env_config("PAYMENT_SECRET_KEY", env_prefix),
      api_key: get_env_config("PAYMENT_API_KEY", env_prefix),

      # 回调地址
      notify_url: get_env_config("PAYMENT_NOTIFY_URL"),
      return_url: get_env_config("PAYMENT_RETURN_URL")
    }
  end

  defp get_env_config(base_key, prefix \\ nil) do
    keys = if prefix do
      ["#{prefix}_#{base_key}", base_key]
    else
      [base_key]
    end

    Enum.find_value(keys, fn key ->
      System.get_env(key)
    end)
  end
end
