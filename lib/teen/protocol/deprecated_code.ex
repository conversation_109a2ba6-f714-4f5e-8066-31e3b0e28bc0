# # ==================== Game 协议处理 ====================
# # 处理注册请求 (RegLogin.CS_NORMAL_REG_P)
# def handle_message(
#       %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_normal_reg_p} = message,
#       state
#     ) do
#   Logger.info("用户注册请求: #{inspect(message.data)}")

#   data = message.data || %{}
#   username = Map.get(data, "username", "")
#   password = Map.get(data, "password", "")

#   # 模拟注册验证
#   response_data =
#     cond do
#       String.length(username) < 3 ->
#         %{
#           "status" => 1,
#           "message" => "用户名长度不能少于3个字符"
#         }

#       String.length(password) < 6 ->
#         %{
#           "status" => 2,
#           "message" => "密码长度不能少于6个字符"
#         }

#       true ->
#         user_id = "user_#{:rand.uniform(100_000)}"

#         %{
#           "status" => 0,
#           "message" => "注册成功",
#           "userid" => user_id,
#           "username" => username
#         }
#     end

#   response_map = %{
#     "mainId" => @main_proto_reg_login,
#     "subId" => @reg_login_sc_normal_reg_p,
#     "data" => response_data
#   }

#   {:reply, response_map, state}
# end

# # 处理登录请求 (RegLogin.CS_LOGIN_P)
# def handle_message(
#       %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_login_p} = message,
#       state
#     ) do
#   Logger.info(
#     "📥 [PROTOCOL] 用户登录请求 - MainID: #{@main_proto_reg_login}, SubID: #{@reg_login_cs_login_p}, Data: #{inspect(message.data)}"
#   )

#   data = message.data || %{}
#   account = Map.get(data, "account", "")
#   password = Map.get(data, "password", "")
#   # 默认为快速登录
#   accounttype = Map.get(data, "accounttype", 3)
#   uniquecode = Map.get(data, "uniquecode", "")
#   siteid = Map.get(data, "siteid", 1)
#   promotionid = Map.get(data, "promotionid", 0)
#   smscode = Map.get(data, "smscode", "")
#   token = Map.get(data, "token", "")

#   # 获取客户端IP地址
#   ip_address = get_client_ip_address(state)

#   # 通用封禁检查：IP和设备封禁（与登录方式无关）
#   with :ok <- check_ip_ban(ip_address),
#        :ok <- check_device_ban(uniquecode) do
#     # 处理不同类型的登录逻辑
#     cond do
#       smscode != "" ->
#         # 手机验证码登录
#         handle_phone_verification_login(account, smscode, message, state)

#       token != "" ->
#         handle_token_login(token, message, state)

#       true ->
#         # 快速登录 - 通过 device_id 创建或获取游客账户
#         handle_quick_login(uniquecode, message, state)
#         # _ ->
#         # 普通登录 - 验证用户名密码
#         # handle_normal_login(account, password, message, state)
#     end
#   else
#     {:error, :ip_banned} ->
#       Logger.warning("🚫 [LOGIN] IP被封禁，拒绝登录 - IP: #{ip_address}")
#       build_login_error_response("登录失败: IP地址已被封禁", message, state, @const_login_result_forbid)

#     {:error, :device_banned} ->
#       Logger.warning("🚫 [LOGIN] 设备被封禁，拒绝登录 - 设备ID: #{uniquecode}")
#       build_login_error_response("登录失败: 设备已被封禁", message, state, @const_login_result_forbid)

#     {:error, reason} ->
#       Logger.error("🚫 [LOGIN] 封禁检查失败 - 原因: #{inspect(reason)}")
#       build_login_error_response("登录失败: 系统错误", message, state, @const_login_result_psw_error)
#   end
# end

# defp handle_token_login(token, message, state) do
#   Logger.info("🔑 [TOKEN_LOGIN] 使用Token登录 - Token: #{token}")

#   with {:ok, %{"sub" => sub, "tenant" => tenant} = _claims, _} <-
#          AshAuthentication.Jwt.verify(token, Cypridina.Accounts.User),
#        {:ok, user} <-
#          AshAuthentication.subject_to_user(sub, Cypridina.Accounts.User, tenant: tenant) do
#     Logger.info("🔑 [TOKEN_LOGIN] 找到用户 - 用户ID: #{user.id}, 用户名: #{user.username}")
#     build_login_success_response(user, 3, message, state)
#   else
#     :error ->
#       Logger.error("🔑 [TOKEN_LOGIN] Token验证失败")
#       build_login_error_response("Token验证失败", message, state, @const_login_result_psw_error)

#     {:error, reason} ->
#       Logger.error("🔑 [TOKEN_LOGIN] Token验证失败, 原因: #{inspect(reason)}")
#       build_login_error_response("Token验证失败", message, state, @const_login_result_psw_error)
#   end
# end

# # 处理快速登录
# defp handle_quick_login(device_id, %{data: %{"siteid" => siteid}} = message, state)
#      when device_id != "" do
#   Logger.info("🚀 [QUICK_LOGIN] 快速登录请求 - device_id: #{device_id}")

#   # 获取客户端IP地址
#   ip_address =
#     case get_in(state, [:socket_info, :peer_data, :address]) do
#       {a, b, c, d} ->
#         "#{a}.#{b}.#{c}.#{d}"

#       _ ->
#         Map.get(state, :ip_address, "unknown")
#     end

#   case handle_device_login(device_id, ip_address) do
#     {:ok, user} ->
#       Logger.info("🚀 [QUICK_LOGIN] 找到现有用户 - 用户ID: #{user.id}, 用户名: #{user.username}")
#       build_login_success_response(user, 3, message, state)

#     {:error, :user_banned} ->
#       Logger.warning("🚀 [QUICK_LOGIN] 用户被封禁 - 设备ID: #{device_id}")
#       build_login_error_response("登录失败: 账号已被封禁", message, state, @const_login_result_forbid)

#     {:error, _reason} ->
#       Logger.info("🚀 [QUICK_LOGIN] 设备不存在，创建新游客账户")

#       case Cypridina.Accounts.User.create_guest_user(%{}, tenant: siteid) do
#         {:ok, user} ->
#           Logger.info("🚀 [QUICK_LOGIN] 游客用户创建成功 - 用户ID: #{user.id}, 用户名: #{user.username}")

#           # 创建设备记录
#           case Cypridina.Accounts.UserDevice.record_login(%{
#                  user_id: user.id,
#                  device_id: device_id,
#                  login_ip: ip_address
#                }) do
#             {:ok, _device} ->
#               Logger.info("🚀 [QUICK_LOGIN] 设备记录创建成功 - 设备ID: #{device_id}")

#             {:error, reason} ->
#               Logger.error("🚀 [QUICK_LOGIN] 设备记录创建失败 - 原因: #{inspect(reason)}")
#           end

#           build_login_success_response(user, 3, message, state)

#         {:error, reason} ->
#           Logger.error("🚀 [QUICK_LOGIN] 创建游客用户失败 - 原因: #{inspect(reason)}")

#           build_login_error_response(
#             "快速登录失败: #{inspect(reason)}",
#             message,
#             state,
#             @const_login_result_psw_error
#           )
#       end
#   end
# end

# defp handle_quick_login("", _message, state) do
#   Logger.warning("🚀 [QUICK_LOGIN] 快速登录失败 - 缺少设备ID")
#   build_login_error_response("缺少设备ID", nil, state, @const_login_result_psw_error)
# end

# # 检查用户封禁状态
# defp check_user_ban(user_id) do
#   case Teen.BanSystem.is_user_banned?(user_id) do
#     {:ok, false} ->
#       :ok

#     {:ok, true, ban_info} ->
#       Logger.warning("🚫 [BAN_CHECK] 用户被封禁 - 用户ID: #{user_id}, 封禁信息: #{inspect(ban_info)}")
#       {:error, :user_banned}

#     {:error, reason} ->
#       Logger.error("🚫 [BAN_CHECK] 检查用户封禁状态失败 - 用户ID: #{user_id}, 原因: #{inspect(reason)}")
#       # 如果检查失败，为了安全起见，允许登录但记录错误
#       :ok
#   end
# end

# # 检查IP封禁状态
# defp check_ip_ban(ip_address) do
#   case query_ip_ban(ip_address) do
#     {:ok, false} ->
#       :ok

#     {:ok, true} ->
#       Logger.warning("🚫 [BAN_CHECK] IP被封禁 - IP: #{ip_address}")
#       {:error, :ip_banned}

#     {:error, reason} ->
#       Logger.error("🚫 [BAN_CHECK] 检查IP封禁状态失败 - IP: #{ip_address}, 原因: #{inspect(reason)}")
#       # 如果检查失败，为了安全起见，允许登录但记录错误
#       :ok
#   end
# end

# # 检查设备封禁状态
# defp check_device_ban(device_id) do
#   case query_device_ban(device_id) do
#     {:ok, false} ->
#       :ok

#     {:ok, true} ->
#       Logger.warning("🚫 [BAN_CHECK] 设备被封禁 - 设备ID: #{device_id}")
#       {:error, :device_banned}

#     {:error, reason} ->
#       Logger.error("🚫 [BAN_CHECK] 检查设备封禁状态失败 - 设备ID: #{device_id}, 原因: #{inspect(reason)}")
#       # 如果检查失败，为了安全起见，允许登录但记录错误
#       :ok
#   end
# end

# # 处理设备登录逻辑，支持多用户场景
# defp handle_device_login(device_id, ip_address) do
#   # 策略1: 优先返回在线用户
#   case Cypridina.Accounts.UserDevice.get_active_by_device_id(%{device_id: device_id}) do
#     {:ok, device} ->
#       Logger.info("🚀 [DEVICE_LOGIN] 找到在线用户设备 - 用户ID: #{device.user_id}")

#       # 检查用户封禁状态
#       case check_user_ban(device.user_id) do
#         :ok ->
#           # 更新登录信息
#           Cypridina.Accounts.UserDevice.update_login_info(device, %{login_ip: ip_address})
#           # 获取用户
#           case Ash.get(Cypridina.Accounts.User, device.user_id) do
#             {:ok, user} -> {:ok, user}
#             {:error, reason} -> {:error, reason}
#           end

#         {:error, reason} ->
#           {:error, reason}
#       end

#     {:error, _} ->
#       # 策略2: 如果没有在线用户，返回最近登录的用户
#       case Cypridina.Accounts.UserDevice.get_by_device_id(%{device_id: device_id}) do
#         {:ok, device} ->
#           Logger.info("🚀 [DEVICE_LOGIN] 找到最近登录用户设备 - 用户ID: #{device.user_id}")

#           # 检查用户封禁状态
#           case check_user_ban(device.user_id) do
#             :ok ->
#               # 更新登录信息
#               Cypridina.Accounts.UserDevice.update_login_info(device, %{login_ip: ip_address})
#               # 获取用户
#               case Ash.get(Cypridina.Accounts.User, device.user_id) do
#                 {:ok, user} -> {:ok, user}
#                 {:error, reason} -> {:error, reason}
#               end

#             {:error, reason} ->
#               {:error, reason}
#           end

#         {:error, reason} ->
#           Logger.info("🚀 [DEVICE_LOGIN] 设备不存在: #{device_id}")
#           {:error, reason}
#       end
#   end
# end

# # 查询IP封禁记录
# defp query_ip_ban(ip_address) do
#   try do
#     case Teen.BanSystem.UserBan.list_active_bans() do
#       {:ok, bans} ->
#         ip_banned =
#           Enum.any?(bans, fn ban ->
#             ban.ban_type == 3 and ban.ip_address == ip_address
#           end)

#         {:ok, ip_banned}

#       {:error, reason} ->
#         {:error, reason}
#     end
#   rescue
#     error ->
#       Logger.error("🚫 [BAN_QUERY] 查询IP封禁记录异常 - IP: #{ip_address}, 错误: #{inspect(error)}")
#       {:error, error}
#   end
# end

# # 查询设备封禁记录
# defp query_device_ban(device_id) do
#   try do
#     case Teen.BanSystem.UserBan.list_active_bans() do
#       {:ok, bans} ->
#         device_banned =
#           Enum.any?(bans, fn ban ->
#             ban.ban_type == 2 and ban.device_id == device_id
#           end)

#         {:ok, device_banned}

#       {:error, reason} ->
#         {:error, reason}
#     end
#   rescue
#     error ->
#       Logger.error("🚫 [BAN_QUERY] 查询设备封禁记录异常 - 设备ID: #{device_id}, 错误: #{inspect(error)}")
#       {:error, error}
#   end
# end

# # 记录手机登录的设备信息
# defp record_phone_login_device(user_id, ip_address) do
#   # 生成设备ID（基于用户ID和IP的组合）
#   device_id = "phone_#{user_id}_#{:crypto.hash(:md5, ip_address) |> Base.encode16(case: :lower)}"

#   case Cypridina.Accounts.UserDevice.record_login(%{
#          user_id: user_id,
#          device_id: device_id,
#          login_ip: ip_address
#        }) do
#     {:ok, _device} ->
#       Logger.info("📱 [PHONE_DEVICE] 设备记录创建成功 - 用户ID: #{user_id}, 设备ID: #{device_id}")
#       {:ok, device_id}

#     {:error, reason} ->
#       Logger.error("📱 [PHONE_DEVICE] 设备记录创建失败 - 用户ID: #{user_id}, 原因: #{inspect(reason)}")
#       {:error, reason}
#   end
# end

# # 格式化封禁原因
# defp format_ban_reason(reason) do
#   case reason do
#     :user_banned -> "账号已被封禁"
#     :ip_banned -> "IP地址已被封禁"
#     :device_banned -> "设备已被封禁"
#     _ -> "登录受限"
#   end
# end

# # 获取客户端IP地址
# defp get_client_ip_address(state) do
#   case get_in(state, [:socket_info, :peer_data, :address]) do
#     {a, b, c, d} ->
#       "#{a}.#{b}.#{c}.#{d}"

#     _ ->
#       Map.get(state, :ip_address, "unknown")
#   end
# end

# # 处理手机验证码登录
# defp handle_phone_verification_login(
#        phone,
#        smscode,
#        %{data: %{"siteid" => siteid}} = message,
#        state
#      ) do
#   Logger.info("📱 [PHONE_LOGIN] 手机验证码登录请求 - 手机号: #{phone}, 验证码: #{smscode}")

#   # 获取客户端IP地址
#   ip_address = get_client_ip_address(state)

#   # 添加国际区号前缀（如果没有的话）
#   normalized_phone =
#     phone
#     |> StringUtils.normalize_phone()
#     |> StringUtils.add_default_country_code(91)

#   case Cypridina.Communications.VerificationCode.verify_code(%{
#          phone_number: normalized_phone,
#          code: smscode,
#          # 登录验证码类型
#          code_type: 2
#        }) do
#     {:ok, _verification} ->
#       Logger.info("📱 [PHONE_LOGIN] 验证码验证成功 - 手机号: #{normalized_phone}")

#       # 查找或创建用户
#       case find_or_create_phone_user(siteid, normalized_phone, smscode, ip_address) do
#         {:ok, user} ->
#           Logger.info("📱 [PHONE_LOGIN] 手机登录成功 - 用户ID: #{user.id}, 手机号: #{normalized_phone}")
#           build_login_success_response(user, 7, message, state)

#         {:error, reason} ->
#           Logger.error(
#             "📱 [PHONE_LOGIN] 创建用户失败 - 手机号: #{normalized_phone}, 原因: #{inspect(reason)}"
#           )

#           build_login_error_response(
#             "创建用户失败: #{inspect(reason)}",
#             message,
#             state,
#             @const_login_result_psw_error
#           )
#       end

#     {:error, %Ash.Error.Unknown{} = reason} ->
#       reason = Exception.message(reason)
#       Logger.error("📱 [PHONE_LOGIN] 验证码验证失败 - 手机号: #{normalized_phone}, 原因: #{inspect(reason)}")

#       build_login_error_response(
#         "验证码验证失败: #{inspect(reason)}",
#         message,
#         state,
#         @const_login_result_psw_error
#       )
#   end
# end

# # 查找或创建手机用户
# defp find_or_create_phone_user(siteid, phone, verification_code, ip_address) do
#   case Cypridina.Accounts.User.get_by_phone(phone) do
#     {:ok, user} ->
#       Logger.info("📱 [PHONE_USER] 找到现有用户 - 手机号: #{phone}, 用户ID: #{user.id}")

#       # 检查用户封禁状态
#       case check_user_ban(user.id) do
#         :ok ->
#           # 记录登录设备信息
#           record_phone_login_device(user.id, ip_address)
#           {:ok, user}

#         {:error, reason} ->
#           {:error, reason}
#       end

#     {:error, _} ->
#       Logger.info("📱 [PHONE_USER] 创建新用户 - 手机号: #{phone}")

#       random_password = :crypto.strong_rand_bytes(8) |> Base.encode64() |> binary_part(0, 8)
#       # 使用 register_with_phone 创建用户，然后绑定手机号
#       case Cypridina.Accounts.User.register_with_phone(
#              %{
#                phone: phone,
#                verification_code: verification_code,
#                # 默认密码，可以根据需要修改
#                password: random_password,
#                password_confirmation: random_password
#              },
#              tenant: siteid
#            ) do
#         {:ok, user} ->
#           Logger.info("📱 [PHONE_USER] 用户创建并绑定手机成功 - 手机号: #{phone}, 用户ID: #{user.id}")

#           # 记录登录设备信息
#           record_phone_login_device(user.id, ip_address)

#           {:ok, user}

#         {:error, reason} ->
#           Logger.error("📱 [PHONE_USER] 用户创建失败 - 手机号: #{phone}, 原因: #{inspect(reason)}")
#           {:error, reason}
#       end
#   end
# end

# # 处理普通登录
# defp handle_normal_login(account, password, message, state) do
#   login_success = String.length(account) >= 3 and String.length(password) >= 6

#   if login_success do
#     # 模拟用户信息
#     user = %{
#       id: "user_#{:rand.uniform(100_000)}",
#       username: account,
#       numeric_id: :rand.uniform(999_999)
#     }

#     build_login_success_response(user, 1, message, state)
#   else
#     build_login_error_response("用户名或密码错误", message, state, @const_login_result_psw_error)
#   end
# end

# # 构建登录成功响应
# defp build_login_success_response(user, accounttype, message, state) do
#   # 从用户对象获取信息
#   player_id = user.numeric_id
#   user_id = user.id
#   username = user.username

#   # 加载用户profile信息
#   user_with_profile =
#     user
#     |> Ash.load!([:profile])

#   profile = user_with_profile.profile || %{}

#   Logger.info(
#     "🔍 [TOKEN_DEBUG] 用户信息 - ID: #{user_id}, Channel ID: #{inspect(user.channel_id)} (类型: #{inspect(user.channel_id |> to_string() |> String.length())})"
#   )

#   # 确保 channel_id 不为 nil，如果为 nil 则使用默认值
#   tenant_value = user.channel_id || 501
#   Logger.info("🔍 [TOKEN_DEBUG] 使用 Tenant 值: #{inspect(tenant_value)}")

#   {:ok, token, _claims} = AshAuthentication.Jwt.token_for_user(user, %{tenant: tenant_value})

#   Logger.info(
#     "🚀 [LOGIN_SUCCESS] 登录成功 - 用户ID: #{user_id}, 用户名: #{username}, PlayerID: #{player_id}, user: #{inspect(user)}"
#   )

#   # 实现顶号机制：检查是否有其他设备已经登录同一个用户
#   kick_result = handle_duplicate_login(user_id, state)

#   # 使用Phoenix Presence跟踪用户在线状态
#   current_session_id = Map.get(state, :session_id)

#   if current_session_id do
#     # 获取设备和IP信息
#     ip_address = get_client_ip_address(state)
#     device_info = Map.get(state, :device_info, %{})

#     # 构建Presence元数据
#     presence_metadata = %{
#       session_id: current_session_id,
#       ip_address: ip_address,
#       device_info: device_info,
#       account_type: accounttype
#     }

#     # 跟踪用户在线状态 - 使用self()作为进程PID
#     case Teen.UserPresence.track_user(self(), user_id, presence_metadata) do
#       :ok ->
#         Logger.info("👤 [USER_PRESENCE] 用户在线状态跟踪成功 - 用户: #{user_id}, 会话: #{current_session_id}")

#       {:error, reason} ->
#         Logger.error(
#           "👤 [USER_PRESENCE] 用户在线状态跟踪失败 - 用户: #{user_id}, 会话: #{current_session_id}, 原因: #{inspect(reason)}"
#         )
#     end
#   end

#   # 构建完整的登录响应数据，符合客户端期望的格式
#   response_data = %{
#     # CONST_LOGIN_RESULT_SUCCESS
#     "code" => 0,
#     "msg" => "登录成功",
#     "playerid" => player_id,
#     "firstlogin" => 0,
#     "random" => "#{:rand.uniform(999_999)}",
#     "isgame" => 0,
#     "serverport" => 0,

#     # 用户功能数据 - 客户端期望的Function字段
#     "Function" => %{
#       # BaseInfo 相关数据
#       "6" => %{
#         "nickname" => profile.nickname || username || "游客#{player_id}",
#         "headid" => profile.head_id || 1,
#         "headframeid" => 1,
#         "sex" =>
#           case profile.gender do
#             :male -> 1
#             :female -> 2
#             _ -> 1
#           end,
#         # 获取用户金币
#         "money" => Cypridina.Accounts.get_user_points(user_id) || 10000,
#         # 保险柜金币
#         "walletmoney" => 0,
#         # 今日赢取金币
#         "GameWinAmount" => 0,
#         "AduitStatus" => 0,
#         "CustomHeadUrl" => profile.avatar_url || "",
#         "wxheadurl" => "",
#         # 添加真实的手机号和邮箱数据
#         "phone" => user.phone || "",
#         "email" => user.email || ""
#       },
#       # Money 相关数据
#       "7" => %{
#         "BankName" => "",
#         "BankAccountNum" => "",
#         "BankAccountName" => "",
#         "VipLevel" => 0,
#         "VipExp" => 0
#       }
#     },

#     # 登录参数 - 客户端期望的loginparam字段
#     "loginparam" => %{
#       "account" => if(accounttype == 3, do: "#{player_id}", else: username),
#       "userToken" => token,
#       "password" => "",
#       "accounttype" => accounttype,
#       "openid" => "",
#       "nickname" => "",
#       "headimgurl" => "",
#       "city" => "",
#       "sex" => 1
#     },
#     # 游戏房间列表 - 提供完整的游戏房间数据
#     "gamelist" => %{
#       # TeenPatti房间
#       "1" => %{
#         "gameid" => 1,
#         "serverid" => 1001,
#         "port" => 4000,
#         "ip" => "127.0.0.1",
#         "orderid" => 1,
#         "difen" => 10,
#         "money" => 100,
#         "dingfen" => 0,
#         "gamemaxnum" => 6,
#         "bundleName" => "teenpatti"
#       },
#       # TeenPatti房间2
#       "2" => %{
#         "gameid" => 1,
#         "serverid" => 1002,
#         "port" => 4000,
#         "ip" => "127.0.0.1",
#         "orderid" => 2,
#         "difen" => 50,
#         "money" => 500,
#         "dingfen" => 0,
#         "gamemaxnum" => 6,
#         "bundleName" => "teenpatti"
#       },
#       # DragonTiger房间
#       "3" => %{
#         "gameid" => 22,
#         "serverid" => 2201,
#         "port" => 4000,
#         "ip" => "127.0.0.1",
#         "orderid" => 1,
#         "difen" => 20,
#         "money" => 200,
#         "dingfen" => 0,
#         "gamemaxnum" => 8,
#         "bundleName" => "longhu"
#       },
#       # slot777房间
#       "4" => %{
#         "gameid" => 40,
#         "serverid" => 4001,
#         "port" => 4000,
#         "ip" => "127.0.0.1",
#         "orderid" => 1,
#         "difen" => 100,
#         "money" => 200,
#         "dingfen" => 0,
#         "gamemaxnum" => 1,
#         "bundleName" => "slot777"
#       },
#       # slotniu房间
#       "5" => %{
#         "gameid" => 41,
#         "serverid" => 4101,
#         "port" => 4000,
#         "ip" => "127.0.0.1",
#         "orderid" => 1,
#         "difen" => 100,
#         "money" => 200,
#         "dingfen" => 0,
#         "gamemaxnum" => 1,
#         "bundleName" => "slotniu"
#       },
#       # SlotCat房间
#       "6" => %{
#         "gameid" => 42,
#         "serverid" => 4201,
#         "port" => 4000,
#         "ip" => "127.0.0.1",
#         "orderid" => 1,
#         "difen" => 100,
#         "money" => 1000,
#         "dingfen" => 0,
#         "gamemaxnum" => 1,
#         "bundleName" => "slotcat"
#       },
#       # Jhandi Munda房间
#       "7" => %{
#         "gameid" => 21,
#         "serverid" => 2101,
#         "port" => 4000,
#         "ip" => "127.0.0.1",
#         "orderid" => 1,
#         "difen" => 20,
#         "money" => 200,
#         "dingfen" => 0,
#         "gamemaxnum" => 7,
#         "bundleName" => "jhandi_munda"
#       },

#       # Pot Blind房间
#       "8" => %{
#         "gameid" => 3,
#         "serverid" => 301,
#         "port" => 4000,
#         "ip" => "127.0.0.1",
#         "orderid" => 1,
#         "difen" => 50,
#         "money" => 500,
#         "dingfen" => 0,
#         "gamemaxnum" => 6,
#         "bundleName" => "pot_blind"
#       },
#       # Safari of Wealth房间
#       "9" => %{
#         "gameid" => 54,
#         "serverid" => 5401,
#         "port" => 4000,
#         "ip" => "127.0.0.1",
#         "orderid" => 1,
#         "difen" => 100,
#         "money" => 1000,
#         "dingfen" => 0,
#         "gamemaxnum" => 1,
#         "bundleName" => "safariofwealth"
#       }
#     },

#     # 站点游戏列表 - 提供游戏配置信息
#     "sitegamelist1" => %{
#       "1" => %{
#         "gameid" => 1,
#         # 2-正常运行
#         "status" => 2,
#         "mode" => "",
#         "name" => "teenpatti"
#       },
#       "2" => %{
#         "gameid" => 22,
#         # 2-正常运行
#         "status" => 2,
#         "mode" => "",
#         "name" => "longhu"
#       },
#       "3" => %{
#         "gameid" => 40,
#         # 2-正常运行
#         "status" => 2,
#         "mode" => "",
#         "name" => "slot777"
#       },
#       "4" => %{
#         "gameid" => 41,
#         # 2-正常运行
#         "status" => 2,
#         "mode" => "",
#         "name" => "slotniu"
#       },
#       "5" => %{
#         "gameid" => 42,
#         # 2-正常运行
#         "status" => 2,
#         "mode" => "",
#         "name" => "slotcat"
#       },
#       "6" => %{
#         "gameid" => 21,
#         # 2-正常运行
#         "status" => 2,
#         "mode" => "",
#         "name" => "jhandi_munda"
#       },
#       "7" => %{
#         "gameid" => 3,
#         # 2-正常运行
#         "status" => 2,
#         "mode" => "",
#         "name" => "pot_blind"
#       },
#       "8" => %{
#         "gameid" => 54,
#         # 2-正常运行
#         "status" => 2,
#         "mode" => "",
#         "name" => "safariofwealth"
#       }
#     },
#     # 其他配置信息
#     "agentextips" => "",
#     "bankextips" => "",
#     "isbindaccount" => 0,
#     "regsendmoney" => 0,
#     "officalwebdisplay" => 1,
#     "IsShowCode" => 0,
#     "promotion" => 0,
#     "styleid" => 1,
#     "Url" => ""
#   }

#   # 更新会话状态
#   new_state =
#     Map.merge(state, %{
#       user_id: user_id,
#       player_id: player_id,
#       account: username,
#       accounttype: accounttype,
#       authenticate: true,
#       current_user: user
#     })

#   response_map = %{
#     "mainId" => @main_proto_reg_login,
#     "subId" => @reg_login_sc_login_p,
#     "data" => response_data
#   }

#   # 如果有顶号情况，发送额外的SC_LOGIN_OTHER_P消息
#   case kick_result do
#     {:kicked_others, kicked_count} ->
#       # 构建顶号通知消息
#       login_other_message = build_login_other_message(kicked_count)

#       # 延迟发送顶号消息，确保登录响应先发送
#       Process.send_after(self(), {:send_login_other_message, login_other_message}, 100)

#       Logger.info("🔄 [DUPLICATE_LOGIN] 将发送顶号通知消息 - 被挤下线设备数: #{kicked_count}")

#     _ ->
#       :ok
#   end

#   {:reply, response_map, new_state}
# end

# # 构建登录错误响应（带错误码）
# defp build_login_error_response(error_msg, _message, state, error_code) do
#   response_data = %{
#     "code" => error_code,
#     "msg" => error_msg
#   }

#   response_map = %{
#     "mainId" => @main_proto_reg_login,
#     "subId" => @reg_login_sc_login_p,
#     "data" => response_data
#   }

#   {:reply, response_map, state}
# end
