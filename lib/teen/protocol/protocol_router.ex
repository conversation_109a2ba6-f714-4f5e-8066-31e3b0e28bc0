defmodule Teen.Protocol.ProtocolRouter do
  @moduledoc """
  协议路由器
  
  负责将客户端发送的协议分发到对应的处理器
  对应客户端的 MainProto 枚举
  """

  require Logger
  alias Teen.Protocol.HallActivityProtocol

  # 主协议类型定义 - 对应客户端 MainProto
  @main_proto_reg_login 1
  @main_proto_db_server 2
  @main_proto_base_info 3
  @main_proto_game 4
  @main_proto_find_psw 5
  @main_proto_money 6
  @main_proto_rank 7
  @main_proto_mail_manager 8
  @main_proto_notice_manager 9
  @main_proto_xc 10
  @main_proto_task 11
  @main_proto_hall_activity 12

  @doc """
  路由协议到对应的处理器
  """
  def route_protocol(main_protocol, sub_protocol, data, user_id) do
    Logger.info("🚀 [PROTOCOL_ROUTER] 路由协议: 主协议=#{main_protocol}, 子协议=#{sub_protocol}, 用户=#{user_id}")

    case main_protocol do
      @main_proto_hall_activity ->
        HallActivityProtocol.handle_protocol(sub_protocol, data, user_id)

      @main_proto_reg_login ->
        handle_reg_login_protocol(sub_protocol, data, user_id)

      @main_proto_db_server ->
        handle_db_server_protocol(sub_protocol, data, user_id)

      @main_proto_base_info ->
        handle_base_info_protocol(sub_protocol, data, user_id)

      @main_proto_game ->
        handle_game_protocol(sub_protocol, data, user_id)

      @main_proto_money ->
        handle_money_protocol(sub_protocol, data, user_id)

      @main_proto_rank ->
        handle_rank_protocol(sub_protocol, data, user_id)

      @main_proto_mail_manager ->
        handle_mail_protocol(sub_protocol, data, user_id)

      @main_proto_task ->
        handle_task_protocol(sub_protocol, data, user_id)

      _ ->
        Logger.warning("🚀 [PROTOCOL_ROUTER] 未知主协议: #{main_protocol}")
        {:error, :unknown_main_protocol}
    end
  end

  # 注册登录协议处理
  defp handle_reg_login_protocol(sub_protocol, data, user_id) do
    Logger.info("🔐 [REG_LOGIN] 处理协议: #{sub_protocol}")
    # 这里可以调用现有的登录系统
    {:ok, sub_protocol + 1, %{code: 0, msg: "success"}}
  end

  # 数据库服务协议处理
  defp handle_db_server_protocol(sub_protocol, data, user_id) do
    Logger.info("💾 [DB_SERVER] 处理协议: #{sub_protocol}")
    {:ok, sub_protocol + 1, %{code: 0, msg: "success"}}
  end

  # 基础信息协议处理
  defp handle_base_info_protocol(sub_protocol, data, user_id) do
    Logger.info("ℹ️ [BASE_INFO] 处理协议: #{sub_protocol}")
    {:ok, sub_protocol + 1, %{code: 0, msg: "success"}}
  end

  # 游戏协议处理
  defp handle_game_protocol(sub_protocol, data, user_id) do
    Logger.info("🎮 [GAME] 处理协议: #{sub_protocol}")
    {:ok, sub_protocol + 1, %{code: 0, msg: "success"}}
  end

  # 金币协议处理
  defp handle_money_protocol(sub_protocol, data, user_id) do
    Logger.info("💰 [MONEY] 处理协议: #{sub_protocol}")
    {:ok, sub_protocol + 1, %{code: 0, msg: "success"}}
  end

  # 排行榜协议处理
  defp handle_rank_protocol(sub_protocol, data, user_id) do
    Logger.info("🏆 [RANK] 处理协议: #{sub_protocol}")
    {:ok, sub_protocol + 1, %{code: 0, msg: "success"}}
  end

  # 邮件协议处理
  defp handle_mail_protocol(sub_protocol, data, user_id) do
    Logger.info("📧 [MAIL] 处理协议: #{sub_protocol}")
    {:ok, sub_protocol + 1, %{code: 0, msg: "success"}}
  end

  # 任务协议处理
  defp handle_task_protocol(sub_protocol, data, user_id) do
    Logger.info("📋 [TASK] 处理协议: #{sub_protocol}")
    {:ok, sub_protocol + 1, %{code: 0, msg: "success"}}
  end

  @doc """
  获取主协议类型名称
  """
  def get_main_protocol_name(main_protocol) do
    case main_protocol do
      @main_proto_reg_login -> "REG_LOGIN"
      @main_proto_db_server -> "DB_SERVER"
      @main_proto_base_info -> "BASE_INFO"
      @main_proto_game -> "GAME"
      @main_proto_find_psw -> "FIND_PSW"
      @main_proto_money -> "MONEY"
      @main_proto_rank -> "RANK"
      @main_proto_mail_manager -> "MAIL_MANAGER"
      @main_proto_notice_manager -> "NOTICE_MANAGER"
      @main_proto_xc -> "XC"
      @main_proto_task -> "TASK"
      @main_proto_hall_activity -> "HALL_ACTIVITY"
      _ -> "UNKNOWN"
    end
  end

  @doc """
  获取所有支持的主协议
  """
  def get_supported_main_protocols do
    [
      @main_proto_reg_login,
      @main_proto_db_server,
      @main_proto_base_info,
      @main_proto_game,
      @main_proto_find_psw,
      @main_proto_money,
      @main_proto_rank,
      @main_proto_mail_manager,
      @main_proto_notice_manager,
      @main_proto_xc,
      @main_proto_task,
      @main_proto_hall_activity
    ]
  end
end
