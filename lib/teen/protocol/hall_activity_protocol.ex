defmodule Teen.Protocol.HallActivityProtocol do
  @moduledoc """
  大厅活动协议处理器

  对应客户端的 HallActivity 协议，包括：
  - 登录活动 (LoginCash)
  - 七日签到 (SevenDays)
  - 游戏任务 (GameTask)
  - 30次刮卡 (ThirtyCard)
  - 礼包活动 (GiftCharge)
  - 周卡月卡 (CardTask)
  - VIP礼包 (VipGift)
  - 免费积分 (FreeBonus)
  - 免费提现 (FreeCash)
  - 破产补助 (BrokeAward)
  - 邮件奖励 (MailAward)
  - 绑定奖励 (BindPhone/BindMail)
  - CDKEY (CdkeyAward)
  """

  require Logger
  alias Teen.ActivitySystem.ActivityService

  # 协议常量定义 - 对应客户端 HallActivity
  @cs_logincash_info_p 0
  @sc_logincash_info_p 1
  @cs_fetch_logincash_award_p 2
  @sc_fetch_logincash_award_p 3
  @cs_get_user_money_p 4
  @sc_get_user_money_p 5
  @cs_fetch_user_bonus_p 6
  @sc_fetch_user_bonus_p 7
  @cs_get_seven_days_p 8
  @sc_get_seven_days_p 9
  @cs_fetch_seven_days_award_p 10
  @sc_fetch_seven_days_award_p 11
  @cs_get_thirty_card_p 12
  @sc_get_thirty_card_p 13
  @cs_fetch_thirty_card_p 14
  @sc_fetch_thirty_card_p 15
  @cs_game_task_p 16
  @sc_game_task_p 17
  @cs_fetch_game_task_award_p 18
  @sc_fetch_game_task_award_p 19
  @cs_get_gift_charge_p 20
  @sc_get_gift_charge_p 21
  @cs_get_card_task_p 22
  @sc_get_card_task_p 23
  @cs_fetch_card_task_p 24
  @sc_fetch_card_task_p 25
  @cs_get_vip_gift_p 26
  @sc_get_vip_gift_p 27
  @cs_fetch_vip_gift_p 28
  @sc_fetch_vip_gift_p 29
  @cs_get_free_bonus_p 30
  @sc_get_free_bonus_p 31
  @cs_fetch_free_bonus_p 32
  @sc_fetch_free_bonus_p 33
  @cs_get_free_cash_p 34
  @sc_get_free_cash_p 35
  @cs_fetch_free_cash_p 36
  @sc_fetch_free_cash_p 37
  @cs_get_free_cash_invitation_p 38
  @sc_get_free_cash_invitation_p 39
  @cs_fetch_broke_award_p 40
  @sc_fetch_broke_award_p 41
  @cs_fetch_mail_award_p 42
  @sc_fetch_mail_award_p 43
  @cs_bind_phone_user_p 44
  @sc_bind_phone_user_p 45
  @cs_bind_mail_user_p 46
  @sc_bind_mail_user_p 47
  @cs_fetch_cdkey_award_p 48
  @sc_fetch_cdkey_award_p 49

  @doc """
  处理大厅活动协议
  """
  def handle_protocol(sub_protocol, data, user_id) do
    Logger.info("🎯 [HALL_ACTIVITY] 处理协议: #{sub_protocol}, 用户: #{user_id}, 数据: #{inspect(data)}")

    case sub_protocol do
      @cs_logincash_info_p ->
        handle_get_logincash_info(user_id)

      @cs_fetch_logincash_award_p ->
        handle_fetch_logincash_award(user_id, data)

      @cs_get_user_money_p ->
        handle_get_user_money(user_id)

      @cs_fetch_user_bonus_p ->
        handle_fetch_user_bonus(user_id, data)

      @cs_get_seven_days_p ->
        handle_get_seven_days(user_id)

      @cs_fetch_seven_days_award_p ->
        handle_fetch_seven_days_award(user_id, data)

      @cs_get_thirty_card_p ->
        handle_get_thirty_card(user_id)

      @cs_fetch_thirty_card_p ->
        handle_fetch_thirty_card(user_id, data)

      @cs_game_task_p ->
        handle_get_game_task(user_id)

      @cs_fetch_game_task_award_p ->
        handle_fetch_game_task_award(user_id, data)

      @cs_get_gift_charge_p ->
        handle_get_gift_charge(user_id)

      @cs_get_card_task_p ->
        handle_get_card_task(user_id)

      @cs_fetch_card_task_p ->
        handle_fetch_card_task(user_id, data)

      @cs_get_vip_gift_p ->
        handle_get_vip_gift(user_id)

      @cs_fetch_vip_gift_p ->
        handle_fetch_vip_gift(user_id, data)

      @cs_get_free_bonus_p ->
        handle_get_free_bonus(user_id)

      @cs_fetch_free_bonus_p ->
        handle_fetch_free_bonus(user_id, data)

      @cs_get_free_cash_p ->
        handle_get_free_cash(user_id)

      @cs_fetch_free_cash_p ->
        handle_fetch_free_cash(user_id, data)

      @cs_get_free_cash_invitation_p ->
        handle_get_free_cash_invitation(user_id, data)

      @cs_fetch_broke_award_p ->
        handle_fetch_broke_award(user_id, data)

      @cs_fetch_mail_award_p ->
        handle_fetch_mail_award(user_id, data)

      @cs_bind_phone_user_p ->
        handle_bind_phone(user_id, data)

      @cs_bind_mail_user_p ->
        handle_bind_mail(user_id, data)

      @cs_fetch_cdkey_award_p ->
        handle_fetch_cdkey_award(user_id, data)

      _ ->
        Logger.warning("🎯 [HALL_ACTIVITY] 未知协议: #{sub_protocol}")
        {:error, :unknown_protocol}
    end
  end

  # 获取登录活动信息
  defp handle_get_logincash_info(user_id) do
    case ActivityService.get_user_activity_stats(user_id) do
      {:ok, stats} ->
        response_data = %{
          user: %{
            bonuscash: get_user_bonus_cash(user_id),
            totalbonuscash: get_user_total_bonus_cash(user_id),
            fetchnumber: get_user_fetch_number(user_id),
            currentday: get_user_current_day(user_id)
          },
          activities: stats
        }
        {:ok, @sc_logincash_info_p, response_data}

      {:error, reason} ->
        Logger.error("🎯 [HALL_ACTIVITY] 获取登录活动信息失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # 领取登录活动奖励
  defp handle_fetch_logincash_award(user_id, %{"fetchtype" => fetch_type} = data) do
    ip = Map.get(data, "ip", "")

    case fetch_type do
      0 -> # 登录奖励
        claim_login_reward(user_id, ip)
      1 -> # 充值奖励
        claim_recharge_reward(user_id, ip)
      2 -> # 游戏局数奖励
        claim_game_rounds_reward(user_id, ip)
      3 -> # 转盘奖励
        claim_wheel_reward(user_id, ip)
      4 -> # 游戏赢分奖励
        claim_game_win_reward(user_id, ip)
      10 -> # 完成奖励
        claim_completion_reward(user_id, ip)
      _ ->
        {:error, :invalid_fetch_type}
    end
  end

  # 获取用户金币信息
  defp handle_get_user_money(user_id) do
    try do
      case Cypridina.Accounts.get_user_points(user_id) do
        {:ok, points} ->
          response_data = %{
            money: points,
            wallet_money: points
          }
          {:ok, @sc_get_user_money_p, response_data}
        {:error, _reason} ->
          {:error, :user_not_found}
      end
    rescue
      Ash.Error.Invalid ->
        {:error, :user_not_found}
    end
  end

  # 领取用户积分
  defp handle_fetch_user_bonus(user_id, %{"fetchamount" => amount} = data) do
    ip = Map.get(data, "ip", "")

    case Cypridina.Accounts.add_points(user_id, amount, "bonus_fetch") do
      {:ok, _} ->
        response_data = %{
          code: 0,
          fetchamount: amount,
          msg: "积分领取成功"
        }
        {:ok, @sc_fetch_user_bonus_p, response_data}

      {:error, reason} ->
        response_data = %{
          code: 1,
          msg: "积分领取失败: #{inspect(reason)}"
        }
        {:ok, @sc_fetch_user_bonus_p, response_data}
    end
  end

  # 获取七日签到信息
  defp handle_get_seven_days(user_id) do
    case ActivityService.get_user_participation(user_id, "seven_day_task") do
      {:ok, participation} ->
        seven_days_data = build_seven_days_data(participation)
        {:ok, @sc_get_seven_days_p, seven_days_data}
      {:error, _reason} ->
        seven_days_data = build_seven_days_data(nil)
        {:ok, @sc_get_seven_days_p, seven_days_data}
    end
  end

  # 领取七日签到奖励
  defp handle_fetch_seven_days_award(user_id, %{"fetchid" => fetch_id} = data) do
    ip = Map.get(data, "ip", "")

    case ActivityService.claim_activity_reward(user_id, "seven_day_task", fetch_id) do
      {:ok, record} ->
        response_data = %{
          code: 0,
          fetchaward: record.reward_amount,
          msg: "签到奖励领取成功"
        }
        {:ok, @sc_fetch_seven_days_award_p, response_data}

      {:error, reason} ->
        response_data = %{
          code: 1,
          msg: "签到奖励领取失败: #{inspect(reason)}"
        }
        {:ok, @sc_fetch_seven_days_award_p, response_data}
    end
  end

  # 获取30次刮卡信息
  defp handle_get_thirty_card(user_id) do
    case ActivityService.get_user_activity_participations(user_id, "scratch_card") do
      participations ->
        scratch_card_data = build_scratch_card_data(participations)
        {:ok, @sc_get_thirty_card_p, scratch_card_data}
    end
  end

  # 领取刮卡奖励
  defp handle_fetch_thirty_card(user_id, %{"fetchtype" => fetch_type}) do
    # fetch_type 对应不同的刮卡等级或类型
    case ActivityService.claim_activity_reward(user_id, "scratch_card", fetch_type) do
      {:ok, record} ->
        response_data = %{
          code: 0,
          fetchaward: record.reward_amount,
          msg: "刮卡奖励领取成功"
        }
        {:ok, @sc_fetch_thirty_card_p, response_data}

      {:error, reason} ->
        response_data = %{
          code: 1,
          msg: "刮卡奖励领取失败: #{inspect(reason)}"
        }
        {:ok, @sc_fetch_thirty_card_p, response_data}
    end
  end

  # 获取游戏任务信息
  defp handle_get_game_task(user_id) do
    case ActivityService.get_user_available_activities(user_id) do
      {:ok, activities} ->
        game_tasks_data = build_game_tasks_data(activities.game_tasks, user_id)
        {:ok, @sc_game_task_p, game_tasks_data}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # 领取游戏任务奖励
  defp handle_fetch_game_task_award(user_id, %{"taskid" => task_id}) do
    case ActivityService.claim_activity_reward(user_id, "game_task", task_id) do
      {:ok, record} ->
        response_data = %{
          code: 0,
          fetchaward: record.reward_amount,
          msg: "任务奖励领取成功"
        }
        {:ok, @sc_fetch_game_task_award_p, response_data}

      {:error, reason} ->
        response_data = %{
          code: 1,
          msg: "任务奖励领取失败: #{inspect(reason)}"
        }
        {:ok, @sc_fetch_game_task_award_p, response_data}
    end
  end

  # 其他协议处理函数的占位符实现
  defp handle_get_gift_charge(user_id), do: {:ok, @sc_get_gift_charge_p, %{}}
  defp handle_get_card_task(user_id), do: {:ok, @sc_get_card_task_p, %{}}
  defp handle_fetch_card_task(user_id, data), do: {:ok, @sc_fetch_card_task_p, %{code: 0}}
  defp handle_get_vip_gift(user_id), do: {:ok, @sc_get_vip_gift_p, %{}}
  defp handle_fetch_vip_gift(user_id, data), do: {:ok, @sc_fetch_vip_gift_p, %{code: 0}}
  defp handle_get_free_bonus(user_id), do: {:ok, @sc_get_free_bonus_p, %{}}
  defp handle_fetch_free_bonus(user_id, data), do: {:ok, @sc_fetch_free_bonus_p, %{code: 0}}
  defp handle_get_free_cash(user_id), do: {:ok, @sc_get_free_cash_p, %{}}
  defp handle_fetch_free_cash(user_id, data), do: {:ok, @sc_fetch_free_cash_p, %{code: 0}}
  defp handle_get_free_cash_invitation(user_id, data), do: {:ok, @sc_get_free_cash_invitation_p, %{}}
  defp handle_fetch_broke_award(user_id, data), do: {:ok, @sc_fetch_broke_award_p, %{code: 0}}
  defp handle_fetch_mail_award(user_id, data), do: {:ok, @sc_fetch_mail_award_p, %{code: 0}}
  defp handle_bind_phone(user_id, data), do: {:ok, @sc_bind_phone_user_p, %{code: 0}}
  defp handle_bind_mail(user_id, data), do: {:ok, @sc_bind_mail_user_p, %{code: 0}}
  defp handle_fetch_cdkey_award(user_id, data), do: {:ok, @sc_fetch_cdkey_award_p, %{code: 0}}

  # 辅助函数
  defp get_user_bonus_cash(user_id), do: 0
  defp get_user_total_bonus_cash(user_id), do: 0
  defp get_user_fetch_number(user_id), do: 0
  defp get_user_current_day(user_id), do: 1

  defp claim_login_reward(user_id, ip), do: {:ok, @sc_fetch_logincash_award_p, %{code: 0, fetchaward: 100}}
  defp claim_recharge_reward(user_id, ip), do: {:ok, @sc_fetch_logincash_award_p, %{code: 0, fetchaward: 200}}
  defp claim_game_rounds_reward(user_id, ip), do: {:ok, @sc_fetch_logincash_award_p, %{code: 0, fetchaward: 150}}
  defp claim_wheel_reward(user_id, ip), do: {:ok, @sc_fetch_logincash_award_p, %{code: 0, fetchaward: 500}}
  defp claim_game_win_reward(user_id, ip), do: {:ok, @sc_fetch_logincash_award_p, %{code: 0, fetchaward: 300}}
  defp claim_completion_reward(user_id, ip), do: {:ok, @sc_fetch_logincash_award_p, %{code: 0, fetchaward: 1000}}

  defp build_seven_days_data(nil), do: %{days: []}
  defp build_seven_days_data(_participations), do: %{days: []}
  defp build_scratch_card_data(participations), do: %{cards: []}
  defp build_game_tasks_data(tasks, user_id), do: %{tasks: tasks}
end
