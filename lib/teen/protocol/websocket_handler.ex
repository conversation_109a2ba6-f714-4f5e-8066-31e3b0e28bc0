defmodule Cypridina.Protocol.WebSocketHandlerNew do
  @moduledoc """
  WebSocket协议处理器

  处理客户端发送的各种协议消息，包括：
  - 系统协议 (MainID: 0)
  - 登录注册协议 (MainID: 1)
  - 数据库服务协议 (MainID: 2)
  - 基础信息协议 (MainID: 3)
  - 游戏协议 (MainID: 4)
  - 金币协议 (MainID: 6)
  - 活动系统协议 (MainID: 12)
  """

  require Logger
  alias Teen.Protocol.ProtocolRouter

  # 主协议常量定义
  @main_proto_system 0
  @main_proto_reg_login 1
  @main_proto_db_server 2
  @main_proto_base_info 3
  @main_proto_game 4
  @main_proto_find_psw 5
  @main_proto_money 6
  @main_proto_rank 7
  @main_proto_mail_manager 8
  @main_proto_notice_manager 9
  @main_proto_xc 10
  @main_proto_task 11
  @main_proto_hall_activity 12

  # 系统协议子协议常量
  @system_heartbeat_request 19
  @system_heartbeat_response 20

  @doc """
  处理WebSocket消息的主入口函数
  """
  def handle_message(%{main_id: main_id, sub_id: sub_id, data: data} = message, state) do
    Logger.info("🔵 [WEBSOCKET_HANDLER] 处理消息 - MainID: #{main_id}, SubID: #{sub_id}, Data: #{inspect(data)}")

    case main_id do
      @main_proto_system ->
        handle_system_protocol(sub_id, data, state)

      @main_proto_hall_activity ->
        handle_hall_activity_protocol(sub_id, data, state)

      @main_proto_reg_login ->
        handle_reg_login_protocol(sub_id, data, state)

      @main_proto_db_server ->
        handle_db_server_protocol(sub_id, data, state)

      @main_proto_base_info ->
        handle_base_info_protocol(sub_id, data, state)

      @main_proto_game ->
        handle_game_protocol(sub_id, data, state)

      @main_proto_money ->
        handle_money_protocol(sub_id, data, state)

      @main_proto_rank ->
        handle_rank_protocol(sub_id, data, state)

      @main_proto_mail_manager ->
        handle_mail_protocol(sub_id, data, state)

      @main_proto_task ->
        handle_task_protocol(sub_id, data, state)

      _ ->
        Logger.warning("🔴 [WEBSOCKET_HANDLER] 未知主协议: #{main_id}")
        {:ok, state}
    end
  end

  # 处理系统协议
  defp handle_system_protocol(sub_id, data, state) do
    case sub_id do
      @system_heartbeat_request ->
        handle_heartbeat(data, state)

      _ ->
        Logger.info("ℹ️ [SYSTEM] 未处理的系统协议: #{sub_id}")
        {:ok, state}
    end
  end

  # 处理心跳协议
  defp handle_heartbeat(_data, state) do
    response = %{
      "mainId" => @main_proto_system,
      "subId" => @system_heartbeat_response,
      "data" => %{
        "server_time" => System.system_time(:millisecond)
      }
    }

    {:reply, response, state}
  end

  # 处理活动系统协议
  defp handle_hall_activity_protocol(sub_id, data, state) do
    user_id = Map.get(state, :user_id)

    case ProtocolRouter.route_protocol(@main_proto_hall_activity, sub_id, data, user_id) do
      {:ok, response_sub_id, response_data} ->
        response = %{
          "mainId" => @main_proto_hall_activity,
          "subId" => response_sub_id,
          "data" => response_data
        }
        {:reply, response, state}

      {:error, reason} ->
        Logger.error("❌ [HALL_ACTIVITY] 协议处理失败: #{inspect(reason)}")
        error_response = %{
          "mainId" => @main_proto_hall_activity,
          "subId" => sub_id + 1,
          "data" => %{
            "code" => 1,
            "msg" => "协议处理失败: #{inspect(reason)}"
          }
        }
        {:reply, error_response, state}
    end
  end

  # 处理登录注册协议
  defp handle_reg_login_protocol(sub_id, data, state) do
    Logger.info("🔐 [REG_LOGIN] 处理协议: #{sub_id}")

    # 这里可以调用现有的登录系统
    response = %{
      "mainId" => @main_proto_reg_login,
      "subId" => sub_id + 1,
      "data" => %{
        "code" => 0,
        "msg" => "登录协议处理成功"
      }
    }

    {:reply, response, state}
  end

  # 处理数据库服务协议
  defp handle_db_server_protocol(sub_id, data, state) do
    Logger.info("💾 [DB_SERVER] 处理协议: #{sub_id}")

    response = %{
      "mainId" => @main_proto_db_server,
      "subId" => sub_id + 1,
      "data" => %{
        "code" => 0,
        "msg" => "数据库协议处理成功"
      }
    }

    {:reply, response, state}
  end

  # 处理基础信息协议
  defp handle_base_info_protocol(sub_id, data, state) do
    Logger.info("ℹ️ [BASE_INFO] 处理协议: #{sub_id}")

    response = %{
      "mainId" => @main_proto_base_info,
      "subId" => sub_id + 1,
      "data" => %{
        "code" => 0,
        "msg" => "基础信息协议处理成功"
      }
    }

    {:reply, response, state}
  end

  # 处理游戏协议
  defp handle_game_protocol(sub_id, data, state) do
    Logger.info("🎮 [GAME] 处理协议: #{sub_id}")

    response = %{
      "mainId" => @main_proto_game,
      "subId" => sub_id + 1,
      "data" => %{
        "code" => 0,
        "msg" => "游戏协议处理成功"
      }
    }

    {:reply, response, state}
  end

  # 处理金币协议
  defp handle_money_protocol(sub_id, data, state) do
    Logger.info("💰 [MONEY] 处理协议: #{sub_id}")

    response = %{
      "mainId" => @main_proto_money,
      "subId" => sub_id + 1,
      "data" => %{
        "code" => 0,
        "msg" => "金币协议处理成功"
      }
    }

    {:reply, response, state}
  end

  # 处理排行榜协议
  defp handle_rank_protocol(sub_id, data, state) do
    Logger.info("🏆 [RANK] 处理协议: #{sub_id}")

    response = %{
      "mainId" => @main_proto_rank,
      "subId" => sub_id + 1,
      "data" => %{
        "code" => 0,
        "msg" => "排行榜协议处理成功"
      }
    }

    {:reply, response, state}
  end

  # 处理邮件协议
  defp handle_mail_protocol(sub_id, data, state) do
    Logger.info("📧 [MAIL] 处理协议: #{sub_id}")

    response = %{
      "mainId" => @main_proto_mail_manager,
      "subId" => sub_id + 1,
      "data" => %{
        "code" => 0,
        "msg" => "邮件协议处理成功"
      }
    }

    {:reply, response, state}
  end

  # 处理任务协议
  defp handle_task_protocol(sub_id, data, state) do
    Logger.info("📋 [TASK] 处理协议: #{sub_id}")

    response = %{
      "mainId" => @main_proto_task,
      "subId" => sub_id + 1,
      "data" => %{
        "code" => 0,
        "msg" => "任务协议处理成功"
      }
    }

    {:reply, response, state}
  end

  @doc """
  获取主协议类型名称
  """
  def get_main_protocol_name(main_id) do
    case main_id do
      @main_proto_system -> "SYSTEM"
      @main_proto_reg_login -> "REG_LOGIN"
      @main_proto_db_server -> "DB_SERVER"
      @main_proto_base_info -> "BASE_INFO"
      @main_proto_game -> "GAME"
      @main_proto_find_psw -> "FIND_PSW"
      @main_proto_money -> "MONEY"
      @main_proto_rank -> "RANK"
      @main_proto_mail_manager -> "MAIL_MANAGER"
      @main_proto_notice_manager -> "NOTICE_MANAGER"
      @main_proto_xc -> "XC"
      @main_proto_task -> "TASK"
      @main_proto_hall_activity -> "HALL_ACTIVITY"
      _ -> "UNKNOWN"
    end
  end
end

defmodule Cypridina.Protocol.WebSocketHandler do
  @moduledoc """
  cypridina项目的WebSocket消息处理模块，替换IndiaGameServer和webproject
  实现IndiaGameClient中Protocol.ts定义的所有协议处理
  保持客户端协议枚举命名不变，以方便跨项目查找
  """

  require Logger

  alias Cypridina.Utils.StringUtils

  # WebSocket 处理状态结构
  @type socket_state :: %{
          session_id: String.t(),
          user_id: String.t() | nil,
          connected_at: integer(),
          authenticate: boolean(),
          room_id: String.t() | nil,
          socket_info: map()
        }

  # 主协议常量 - 与TPMasterClient1/Protocol.ts的MainProto保持完全一致
  # RegLogin: 注册登录
  @main_proto_reg_login 0
  # FindPsw: 找回密码
  @main_proto_find_psw 1
  # Game: 游戏逻辑
  @main_proto_game 4
  # XC: 子游戏服务器和客户端交互的协议
  @main_proto_xc 5
  # BaseInfo: 基本信息
  @main_proto_base_info 6
  # Money: 金钱钱包相关
  @main_proto_money 7
  # MailManager: 邮件系统
  @main_proto_mail_manager 14
  # NoticeManager: 公告系统
  @main_proto_notice_manager 15
  # DBServer: HallServer,GameServer,FCServer和DBServer通信
  @main_proto_db_server 34
  # Rank: 排行榜
  @main_proto_rank 40
  # Task: 活动任务
  @main_proto_task 42
  # HallActivity: 大厅活动相关协议
  @main_proto_hall_activity 101

  # 登录结果错误码常量 - 与客户端Define.ts的CONST_LOGIN_RESULT保持一致
  @const_login_result_success 0          # 登录成功
  @const_login_result_success_leave 1    # 登录成功,玩家此刻处于离线托管状态
  @const_login_result_forbid 2           # 账号被封
  @const_login_result_no_account 3       # 没有此账号
  @const_login_result_psw_error 4        # 密码错误
  @const_login_result_version 10         # 版本错误

  # RegLogin 子协议常量 - 与TPMasterClient1/Protocol.ts的RegLogin保持完全一致
  # SC_NORMAL_REG_P
  @reg_login_sc_normal_reg_p 1
  # SC_LOGIN_P
  @reg_login_sc_login_p 3
  # CS_LOGIN_OUT_P: 登出
  @reg_login_cs_login_out_p 4
  # SC_OHTER_LOGIN_P: 你的账号在别处登录,你已经被挤下线
  @reg_login_sc_other_login_p 10
  # SC_LOGIN_OTHER_P: 你的账号在别处登录,你把它挤下线
  @reg_login_sc_login_other_p 11
  # SC_SERVER_STOP_P: 服务器处于停机维护状态
  @reg_login_sc_server_stop_p 12
  # SC_FULLCONNECT_ATTACK_P: 因为全连接攻击,你被断开连接
  @reg_login_sc_fullconnect_attack_p 14
  # CS_GAMESERVER_LOGIN_P: GameServer
  @reg_login_cs_gameserver_login_p 16
  # SC_GAMESERVER_LOGIN_P
  @reg_login_sc_gameserver_login_p 17
  # CS_HEART_CHECK_P: 心跳检查
  @reg_login_cs_heart_check_p 19
  # SC_HALL_SERVER_VERSION_P: 大厅版本号
  @reg_login_sc_hall_server_version_p 20
  # SC_GAME_SERVER_VERSION_P: 游戏服务版本号
  @reg_login_sc_game_server_version_p 21
  # CS_REQUEST_REG_PHONECODE_P: 手机注册请求手机验证码
  @reg_login_cs_request_reg_phonecode_p 29
  # SC_REQUEST_REG_PHONECODE_P
  @reg_login_sc_request_reg_phonecode_p 30
  # CS_PHONECODE_REG_P: 手机注册
  @reg_login_cs_phonecode_reg_p 31
  # SC_PHONECODE_REG_P
  @reg_login_sc_phonecode_reg_p 32
  # CS_REQUEST_SERVER_VERSION_P: 请求版本号
  @reg_login_cs_request_server_version_p 38
  # CD_REQUEST_SYSTEM_STATUS_P: 请求系统配置
  @reg_login_cd_request_system_status_p 49
  # DC_REQUEST_SYSTEM_STATUS_P: 返回系统配置
  @reg_login_dc_request_system_status_p 50
  # CS_REQUEST_GAMEVERSIONS_P: 请求游戏版本号列表
  @reg_login_cs_request_gameversions_p 51
  # SC_REQUEST_GAMEVERSIONS_P: 服务器下发游戏版本号列表
  @reg_login_sc_request_gameversions_p 52

  # BaseInfo 子协议常量 - 与TPMasterClient1/Protocol.ts的BaseInfo保持完全一致
  # CS_SET_NICKNAME_P: 设置昵称
  @base_info_cs_set_nickname_p 0
  # SC_SET_NICKNAME_P
  @base_info_sc_set_nickname_p 2
  # CS_SET_HEADID_P: 设置头像ID
  @base_info_cs_set_headid_p 3
  # FindPsw 子协议常量 - 与TPMasterClient1/Protocol.ts的FindPsw保持完全一致
  # CS_FINDPSW_SET_NEW_PSW_P: 验证结束,设置新密码
  @find_psw_cs_findpsw_set_new_psw_p 7
  # SC_FINDPSW_SET_NEW_PSW_RESULT_P
  @find_psw_sc_findpsw_set_new_psw_result_p 8

  # Game 子协议常量
  @game_sc_add_gamelist_p 0
  @game_sc_del_gamelist_p 1
  @game_sc_room_info_p 2
  @game_cs_room_set_player_state_p 3
  @game_sc_room_set_player_state_p 4
  @game_sc_room_set_state_p 5
  @game_cs_room_chat_p 6
  @game_sc_room_chat_p 7
  @game_sc_room_reset_coin_p 8
  @game_sc_room_zanli_success_p 9
  @game_cs_room_zanli_comback_p 10
  @game_sc_room_zanli_comback_success_p 11
  @game_sc_room_player_enter_p 12
  @game_sc_room_watch_enter_p 13
  @game_sc_room_player_quit_p 14
  @game_sc_room_watch_quit_p 15
  @game_sc_room_del_p 16
  @game_sc_room_prepare_timeout_p 17
  @game_sc_room_del_player_p 18
  @game_sc_room_del_watch_p 19

  @game_xs_register_p 20
  @game_xs_del_watch_p 21
  @game_xs_player_zanli_p 22
  @game_xs_player_result_p 23
  @game_xs_result_p 24
  @game_xs_reset_coin_p 25
  @game_sx_create_game_p 26
  @game_sx_add_watch_p 27
  @game_sx_del_watch_p 28
  @game_sx_reset_coin_p 29
  @game_sx_player_leave_p 30
  @game_sx_player_online_p 31
  @game_sx_player_zanli_comback_p 32
  @game_sx_quit_p 33
  # CS_GAME_PLAYER_NUM_P: 请求每个游戏玩家人数表
  @game_cs_game_player_num_p 34
  # SC_GAME_PLAYER_NUM_P: 返回每个游戏玩家人数表
  @game_sc_game_player_num_p 35

  # DbServer 子协议常量 - 与TPMasterClient1/Protocol.ts的DbServer保持完全一致
  # SC_SET_HEADID_P: 设置头像返回
  @db_server_sc_set_headid_p 43
  # SC_WEB_CHANGE_ATTRIB_P: web请求变更玩家的属性
  @db_server_sc_web_change_attrib_p 37
  # CS_CUSTSRV_REPLY_P: 获取客服消息数据
  @db_server_cs_custsrv_reply_p 110
  # SC_CUSTSRV_REPLY_P: 响应客服消息数据
  @db_server_sc_custsrv_reply_p 111
  # CS_NEW_CHARGE_LIST_P: 获取未读充值消息
  @db_server_cs_new_charge_list_p 112
  # SC_NEW_CHARGE_LIST_P: 获取未读充值消息
  @db_server_sc_new_charge_list_p 113

  # MailManager 子协议常量
  # CS_REQUEST_NEW_MAIL_COUNT_P: 请求新邮件数量
  @mail_manager_cs_request_new_mail_count_p 7
  # SC_REQUEST_NEW_MAIL_COUNT_P: 返回新邮件数量
  @mail_manager_sc_request_new_mail_count_p 8

  # Task 子协议常量
  # CS_GET_TODAY_MATCH_LIST: 获取今日比赛列表信息
  @task_cs_get_today_match_list 15
  # SC_GET_TODAY_MATCH_LIST: 获取今日比赛列表信息
  @task_sc_get_today_match_list 16


  @game_cs_quit_p 40
  @game_cs_huanzhuo_p 43
  @game_sc_huanzhuo_p 44
  @game_cs_mode1_enter_p 45
  @game_cs_mode1_robot_enter_p 46
  @game_sc_mode1_enter_p 47
  @game_cs_mode1_enter_pipei_p 93
  @game_sc_mode1_enter_pipei_p 94
  @game_sc_mode1_pipei_over_p 95
  @game_sc_mode1_quit_pipei_success_p 96
  @game_cs_room_player_ready 1102

  # XC 子协议常量 - 与TPMasterClient1/Protocol.ts的XC保持完全一致
  # XC_ROOM_INFO_P: 房间数据
  @xc_room_info_p 0
  # XC_JIESUAN_P: 结算数据
  @xc_jiesuan_p 1
  # SC_VIRTUAL_BROADCAST_TIPS_P: 虚拟主播游戏进行时的TIPS
  @xc_sc_virtual_broadcast_tips_p 2
  # XC_BROADCAST_PROTOCOL_P: 子游戏发送的广播消息
  @xc_broadcast_protocol_p 3
  # XC_ROBOT_BROADCAST_P: 子游戏广播给所有机器人的协议
  @xc_robot_broadcast_p 4

  # Money 子协议常量 - 与TPMasterClient1/Protocol.ts的Money保持完全一致
  # SC_SET_MONEY_P: 金钱改变
  @money_sc_set_money_p 0
  # SC_SET_WALLETMONEY_P: 钱包改变
  @money_sc_set_walletmoney_p 1
  # CS_SAVE_MONEY_P: 存钱
  @money_cs_save_money_p 2
  # SC_SAVE_MONEY_RESULT_P
  @money_sc_save_money_result_p 3
  # CS_GET_MONEY_P: 取钱
  @money_cs_get_money_p 4
  # SC_GET_MONEY_RESULT_P
  @money_sc_get_money_result_p 5
  # SC_SET_GAME_MONEY_P: 金钱改变
  @money_sc_set_game_money_p 7
  # CD_MONEY_CHANG_RMB_P: 游戏币兑换现金
  @money_cd_money_chang_rmb_p 29
  # DS_MONEY_CHANG_RMB_P
  @money_ds_money_chang_rmb_p 30
  # CD_SEND_MSG_GUEST_SERVER_P: 发送消息给客服务
  @money_cd_send_msg_guest_server_p 31
  # DC_SEND_MSG_GUEST_SERVER_P
  @money_dc_send_msg_guest_server_p 32
  # CD_BIND_BANK_P: 绑定银行卡
  @money_cd_bind_bank_p 33
  # DS_BIND_BANK_P: 绑定银行卡返回
  @money_ds_bind_bank_p 34
  # CS_PAY: 请求支付地址
  @money_cs_pay 49
  # SC_PAY: 请求支付返回
  @money_sc_pay 50

  # NoticeManager 子协议常量 - 与TPMasterClient1/Protocol.ts的NoticeManager保持完全一致
  # SC_NOTICE_P
  @notice_manager_sc_notice_p 0

  # MailManager 子协议常量 - 与IndiaGameClient/Protocol.ts的MailManager保持完全一致
  # CS_REQUEST_MAIL_INFO_P: 请求一封邮件的内容
  @mail_manager_cs_request_mail_info_p 0
  # SC_REQUEST_MAIL_INFO_P
  @mail_manager_sc_request_mail_info_p 1
  # CS_MAIL_SET_READ_P: 请求将一封邮件设置为已读
  @mail_manager_cs_mail_set_read_p 2
  # CS_DEL_MAIL_INFO_P: 请求删除一封邮件
  @mail_manager_cs_del_mail_info_p 3
  # SC_ADD_MAIL_P: 添加一封邮件
  @mail_manager_sc_add_mail_p 4
  # CS_REQUEST_MAILLIST_P: 请求邮件列表
  @mail_manager_cs_request_maillist_p 5
  # SC_REQUEST_MAILLIST_P
  @mail_manager_sc_request_maillist_p 6
  # CS_REQUEST_NEW_MAIL_COUNT_P
  @mail_manager_cs_request_new_mail_count_p 7
  # SC_REQUEST_NEW_MAIL_COUNT_P
  @mail_manager_sc_request_new_mail_count_p 8

  # Rank 子协议常量 - 与IndiaGameClient/Protocol.ts的Rank保持完全一致
  # CS_RANK_DATA: 获得排行榜信息
  @rank_cs_rank_data 0
  # SC_RANK_DATA: 后端返回排行榜信息
  @rank_sc_rank_data 1
  # CD_RANK_LIST: 向dbserver获得排行榜信息
  @rank_cd_rank_list 2
  # DC_RANK_LIST: dbserver返回排行榜信息
  @rank_dc_rank_list 3
  # CS_SELF_RANK_DATA_P: 自己的 今日金币排行榜
  @rank_cs_self_rank_data_p 4
  # SC_SELF_RANK_DATA_P
  @rank_sc_self_rank_data_p 5
  # CD_SELF_RANK_DATA
  @rank_cd_self_rank_data 6
  # DC_SELF_RANK_DATA
  @rank_dc_self_rank_data 7

  # QMAgent 子协议常量 - 与IndiaGameClient/Protocol.ts的QMAgent保持完全一致
  # CS_AGENT_PROMOTIONDATA: 获得推广佣金信息
  @qm_agent_cs_agent_promotiondata 0
  # SC_AGENT_PROMOTIONDATA: 返回获得推广佣金信息
  @qm_agent_sc_agent_promotiondata 1
  # CS_AGENT_GETMONEY: 领取佣金
  @qm_agent_cs_agent_getmoney 2
  # SC_AGENT_GETMONEY: 领取佣金
  @qm_agent_sc_agent_getmoney 3
  # CS_AGENT_MONEYDETAIL: 佣金明细
  @qm_agent_cs_agent_moneydetail 4
  # SC_AGENT_MONEYDETAIL: 佣金明细
  @qm_agent_sc_agent_moneydetail 5
  # CS_AGENT_MYTEAM: 我的团队
  @qm_agent_cs_agent_myteam 6
  # SC_AGENT_MYTEAM: 我的团队
  @qm_agent_sc_agent_myteam 7
  # CD_GET_AGENT_CONFIG_P: 获取代理配置
  @qm_agent_cd_get_agent_config_p 3000
  # DC_GET_AGENT_CONFIG_P: 返回代理配置
  @qm_agent_dc_get_agent_config_p 3001
  # CD_GET_AGENT_DIRECT_TOTAL_P: 自己的代理信息
  @qm_agent_cd_get_agent_direct_total_p 3002
  # DC_GET_AGENT_DIRECT_TOTAL_P: 返回代理信息
  @qm_agent_dc_get_agent_direct_total_p 3003
  # CD_GET_AGENT_DIRECT_DETAIL_P: 自己的收益明细
  @qm_agent_cd_get_agent_direct_detail_p 3004
  # DC_GET_AGENT_DIRECT_DETAIL_P: 返回收益明细
  @qm_agent_dc_get_agent_direct_detail_p 3005
  # CD_GET_AGENT_LIST_P: 获取下级列表
  @qm_agent_cd_get_agent_list_p 3006
  # DC_GET_AGENT_LIST_P: 返回下级列表
  @qm_agent_dc_get_agent_list_p 3007
  # CD_GET_AGENT_RANK_P: 代理收益排行榜
  @qm_agent_cd_get_agent_rank_p 3008
  # DC_GET_AGENT_RANK_P: 返回代理收益排行榜
  @qm_agent_dc_get_agent_rank_p 3009
  # CD_GET_AGENT_EXCHANGE_P: 代理兑换
  @qm_agent_cd_get_agent_exchange_p 3010
  # DC_GET_AGENT_EXCHANGE_P: 返回兑换消息
  @qm_agent_dc_get_agent_exchange_p 3011
  # CD_BAND_AGENT_RAND_CRAD_P: 代理绑定银行卡或者修改银行卡
  @qm_agent_cd_band_agent_rand_crad_p 3012
  # DC_BAND_AGENT_RAND_CRAD_P: 返回绑定银行卡
  @qm_agent_dc_band_agent_rand_crad_p 3013

  # Task 子协议常量 - 与IndiaGameClient/Protocol.ts的Task保持完全一致
  # CS_UPDATE_TASK_LIST: 更新任务列表信息
  @task_cs_update_task_list 0
  # SC_UPDATE_TASK_LIST: 更新任务列表信息
  @task_sc_update_task_list 1
  # CS_GET_TASK_REWARD: 领取任务奖励
  @task_cs_get_task_reward 2
  # SC_GET_TASK_REWARD: 领取任务奖励返回
  @task_sc_get_task_reward 3
  # SC_UPDATE_MATCH_ATTR: 更新比赛状态
  @task_sc_update_match_attr 10
  # CS_GET_MATCH_LIST: 获取比赛列表信息
  @task_cs_get_match_list 11
  # SC_GET_MATCH_LIST: 获取比赛列表信息
  @task_sc_get_match_list 12
  # CS_GET_MATCH_RANK_REWARD: 获取比赛排名和奖励
  @task_cs_get_match_rank_reward 13
  # SC_GET_MATCH_RANK_REWARD: 获取比赛排名和奖励返回
  @task_sc_get_match_rank_reward 14
  # CS_GET_TODAY_MATCH_LIST: 获取今日比赛列表信息
  @task_cs_get_today_match_list 15
  # SC_GET_TODAY_MATCH_LIST: 获取今日比赛列表信息
  @task_sc_get_today_match_list 16

  # HallActivity 子协议常量 - 与IndiaGameClient/Protocol.ts的HallActivity保持完全一致
  # CS_LOGINCASH_INFO_P: 请求登录活动信息
  @hall_activity_cs_logincash_info_p 0
  # SC_LOGINCASH_INFO_P: 请求登录活动信息返回
  @hall_activity_sc_logincash_info_p 1
  # CS_FETCH_LOGINCASH_AWARD_P: 领取登录活动奖励
  @hall_activity_cs_fetch_logincash_award_p 2
  # SC_FETCH_LOGINCASH_AWARD_P: 领取登录活动奖励返回
  @hall_activity_sc_fetch_logincash_award_p 3
  # CS_GET_USER_MONEY_P: 获取用户金币信息
  @hall_activity_cs_get_user_money_p 4
  # SC_GET_USER_MONEY_P: 返回用户金币信息
  @hall_activity_sc_get_user_money_p 5
  # CS_FETCH_USER_BONUS_P: 领取用户积分信息
  @hall_activity_cs_fetch_user_bonus_p 6
  # SC_FETCH_USER_BONUS_P: 返回领取用户积分信息
  @hall_activity_sc_fetch_user_bonus_p 7
  # CS_GET_SEVEN_DAYS_P: 请求7日签到活动信息
  @hall_activity_cs_get_seven_days_p 8
  # SC_GET_SEVEN_DAYS_P: 返回7日签到活动信息
  @hall_activity_sc_get_seven_days_p 9
  # CS_BIND_PHONE_P: 绑定手机号
  @hall_activity_cs_bind_phone_p 44
  # SC_BIND_PHONE_P: 绑定手机号返回
  @hall_activity_sc_bind_phone_p 45

  # DbServer 子协议常量 - 与IndiaGameClient/Protocol.ts的DbServer保持完全一致
  # SC_SET_HEADID_P: 设置头像返回
  @db_server_sc_set_headid_p 43
  # SC_WEB_CHANGE_ATTRIB_P: web请求变更玩家的属性
  @db_server_sc_web_change_attrib_p 37
  # CS_CUSTSRV_REPLY_P: 获取客服消息数据
  @db_server_cs_custsrv_reply_p 110
  # SC_CUSTSRV_REPLY_P: 响应客服消息数据
  @db_server_sc_custsrv_reply_p 111
  # CS_NEW_CHARGE_LIST_P: 获取未读充值消息
  @db_server_cs_new_charge_list_p 112
  # SC_NEW_CHARGE_LIST_P: 获取未读充值消息
  @db_server_sc_new_charge_list_p 113
  # CS_CUSTSRV_REDAY_MESSAGE: 发送已读客服信息
  @db_server_cs_custsrv_reday_message 114
  # CD_QUERY_GIFT_PACK_P: 新的查询礼包信息
  @db_server_cd_query_gift_pack_p 192
  # DC_QUERY_GIFT_PACK_P: 返回查询结果
  @db_server_dc_query_gift_pack_p 193
  # CD_STATISTICS_CHANNEL_DATA: FBC统计数据
  @db_server_cd_statistics_channel_data 194

  @doc """
  处理收到的WebSocket消息，根据主协议ID和子协议ID路由到相应的处理函数

  ## 参数
    - message: 解码后的消息 %{main_id: main_id, sub_id: sub_id, data: data}
    - state: 当前WebSocket连接状态

  ## 返回
    - {:reply, response_map, new_state} 回复消息（map格式，由msgpack序列化器处理）
    - {:ok, new_state} 不回复消息
  """

  # ==================== FindPsw 协议处理 ====================

  # 处理找回密码相关请求，委托给ProtocolHandlers模块
  def handle_message(%{main_id: @main_proto_find_psw, sub_id: sub_id} = message, state)
      when sub_id in [0, 2, 4, 5, 7] do
    case sub_id do
      0 ->
        Cypridina.Protocol.ProtocolHandlers.handle_find_password(message, state)

      2 ->
        Cypridina.Protocol.ProtocolHandlers.handle_find_password_request_code(message, state)

      4 ->
        Cypridina.Protocol.ProtocolHandlers.handle_find_password_crypt(message, state)

      5 ->
        Cypridina.Protocol.ProtocolHandlers.handle_find_password_phone_code(message, state)

      7 ->
        Cypridina.Protocol.ProtocolHandlers.handle_find_password_set_new_password(message, state)
    end
  end

  # ==================== NoticeManager 协议处理 ====================

  # 处理公告管理相关请求，委托给ProtocolHandlers模块
  def handle_message(%{main_id: @main_proto_notice_manager, sub_id: sub_id} = message, state)
      when sub_id in [1, 3, 5] do
    case sub_id do
      1 -> Cypridina.Protocol.ProtocolHandlers.handle_send_notice(message, state)
      3 -> Cypridina.Protocol.ProtocolHandlers.handle_request_notice_need(message, state)
      5 -> Cypridina.Protocol.ProtocolHandlers.handle_request_system_notice(message, state)
    end
  end

  # ==================== MailManager 协议处理 ====================

  # 处理邮件管理相关请求，委托给ProtocolHandlers模块
  def handle_message(%{main_id: @main_proto_mail_manager, sub_id: sub_id} = message, state)
      when sub_id in [0, 2, 3, 5, 7] do
    case sub_id do
      0 -> Cypridina.Protocol.ProtocolHandlers.handle_request_mail_info(message, state)
      2 -> Cypridina.Protocol.ProtocolHandlers.handle_mail_set_read(message, state)
      3 -> Cypridina.Protocol.ProtocolHandlers.handle_delete_mail(message, state)
      5 -> Cypridina.Protocol.ProtocolHandlers.handle_request_mail_list(message, state)
      7 -> Cypridina.Protocol.ProtocolHandlers.handle_request_new_mail_count(message, state)
    end
  end

  # ==================== Rank 协议处理 ====================

  # 处理排行榜相关请求，委托给ProtocolHandlers模块
  def handle_message(%{main_id: @main_proto_rank, sub_id: sub_id} = message, state)
      when sub_id in [0, 4] do
    case sub_id do
      0 -> Cypridina.Protocol.ProtocolHandlers.handle_rank_data(message, state)
      4 -> Cypridina.Protocol.ProtocolHandlers.handle_self_rank_data(message, state)
    end
  end

  # ==================== QMAgent 协议处理 ====================

  # 处理全民代理相关请求，委托给ExtendedHandlers模块
  def handle_message(%{main_id: @main_proto_qm_agent, sub_id: sub_id} = message, state)
      when sub_id in [0, 2, 4, 6] do
    case sub_id do
      0 -> Cypridina.Protocol.ExtendedHandlers.handle_agent_promotion_data(message, state)
      2 -> Cypridina.Protocol.ExtendedHandlers.handle_agent_get_money(message, state)
      4 -> Cypridina.Protocol.ExtendedHandlers.handle_agent_money_detail(message, state)
      6 -> Cypridina.Protocol.ExtendedHandlers.handle_agent_my_team(message, state)
    end
  end

  # ==================== Task 协议处理 ====================

  # 处理任务相关请求，委托给ExtendedHandlers模块
  def handle_message(%{main_id: @main_proto_task, sub_id: sub_id} = message, state)
      when sub_id in [0, 2, 11, 13] do
    case sub_id do
      0 -> Cypridina.Protocol.ExtendedHandlers.handle_update_task_list(message, state)
      2 -> Cypridina.Protocol.ExtendedHandlers.handle_get_task_reward(message, state)
      11 -> Cypridina.Protocol.ExtendedHandlers.handle_get_match_list(message, state)
      13 -> Cypridina.Protocol.ExtendedHandlers.handle_get_match_rank_reward(message, state)
    end
  end

  # ==================== HallActivity 协议处理 ====================

  # 处理大厅活动相关请求，委托给ExtendedHandlers模块
  def handle_message(%{main_id: @main_proto_hall_activity, sub_id: sub_id} = message, state)
      when sub_id in [0, 2, 4, 6, 8, 44] do
    case sub_id do
      0 -> Cypridina.Protocol.ExtendedHandlers.handle_login_cash_info(message, state)
      2 -> Cypridina.Protocol.ExtendedHandlers.handle_fetch_login_cash_award(message, state)
      4 -> Cypridina.Protocol.ExtendedHandlers.handle_get_user_money(message, state)
      6 -> Cypridina.Protocol.ExtendedHandlers.handle_fetch_user_bonus(message, state)
      8 -> Cypridina.Protocol.ExtendedHandlers.handle_get_seven_days(message, state)
      44 -> Cypridina.Protocol.ExtendedHandlers.handle_bind_phone(message, state)
    end
  end

  # 处理心跳请求 (RegLogin.CS_HEART_CHECK_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_heart_check_p} = _message,
        state
      ) do
    Logger.debug("收到心跳请求: #{inspect(state.user_id)}")

    # 回复心跳响应
    response_data = %{
      "server_time" => System.system_time(:millisecond)
    }

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_heart_check_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理请求服务器版本号 (RegLogin.CS_REQUEST_SERVER_VERSION_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_request_server_version_p} =
          message,
        state
      ) do
    Logger.info("收到请求服务器版本号: #{inspect(message.data)}")

    # 返回服务器版本信息
    response_data = %{
      # 大厅版本号
      "hall_version" => "1.0.0",
      # 游戏版本号
      "game_version" => "1.0.0",
      # 服务器名称
      "server_name" => "cypridina",
      # 构建时间
      "build_time" => "2024-01-01",
      # 成功状态
      "status" => 0
    }

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_request_server_version_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理请求系统状态配置 (RegLogin.CD_REQUEST_SYSTEM_STATUS_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cd_request_system_status_p} =
          message,
        state
      ) do
    Logger.info("收到请求系统状态配置: #{inspect(message.data)}")

    data = message.data || %{}
    siteid = Map.get(data, "siteid", 1)
    userid = Map.get(data, "userid", 0)

    # 获取用户ID（如果已登录）
    user_id = case state.user_id do
      nil -> userid
      id -> id
    end

    # 获取系统配置信息（包含银行配置和充值配置）
    system_config = case Teen.PaymentSystem.get_system_config(user_id) do
      {:ok, config} -> config
      {:error, _reason} -> %{"BankConfig" => %{}, "BindBankConfig" => %{}, "PaymentConfig" => %{}}
    end

    # 参考IndiaGameServer格式构建返回数据
    response_data = %{
      "code" => 0,
      "msg" => "",
      # 系统基础配置
      "Url" => "",
      "MaxMoney" => 1000000,
      "Transfer" => 1,
      "Exchange" => 1,
      "AlipayEx" => 1,
      "BankEx" => 1,
      "BankInfo" => "银行转账信息",
      "AgentEx" => 1,
      "Merchant" => 1,
      "Expansion" => "",
      "Promotion" => "推广信息",
      "Promotiontype" => 1,
      "ComplaintWX" => "客服微信",
      "ComplaintQQ" => "客服QQ",
      "Agentsort" => 1,
      "IsShowCode" => 1,
      "BintExLimit" => 1,
      "ExChangeMin" => 10000,  # 最低兑换金额（分）
      "ExReserve" => 1000,     # 兑换保留金额（分）
      # 充值配置
      "RechargeConfig" => Map.get(system_config, "PaymentConfig", %{}),
      # 银行配置
      "BankConfig" => Map.get(system_config, "BankConfig", %{}),
      # 用户绑定银行卡配置
      "BindBankConfig" => Map.get(system_config, "BindBankConfig", %{})
    }

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_dc_request_system_status_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理登出请求 (RegLogin.CS_LOGIN_OUT_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_login_out_p} = message,
        state
      ) do
    Logger.info("用户登出请求: #{inspect(message.data)}")

    # 清理用户状态
    new_state = %{state | user_id: nil, authenticate: false, room_id: nil}

    # 回复登出成功（客户端期望SC_ONLINE_P响应）
    response_data = %{
      "status" => 0,
      "message" => "登出成功"
    }

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_online_p,
      "data" => response_data
    }

    {:reply, response_map, new_state}
  end

  # 处理检查注册信息请求 (RegLogin.CS_CHECK_REGINFO_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_check_reginfo_p} = message,
        state
      ) do
    Logger.info("检查注册信息请求: #{inspect(message.data)}")

    data = message.data || %{}
    username = Map.get(data, "username", "")

    # 模拟检查用户名是否已存在
    # 30%概率用户名已存在
    exists = :rand.uniform(10) > 7

    response_data = %{
      "status" => if(exists, do: 1, else: 0),
      "message" => if(exists, do: "用户名已存在", else: "用户名可用"),
      "username" => username
    }

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_check_reginfo_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理获取随机昵称请求 (RegLogin.CS_GET_RANDOM_NICKNAME_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_get_random_nickname_p} = message,
        state
      ) do
    Logger.info("获取随机昵称请求: #{inspect(message.data)}")

    # 随机昵称库
    adjectives = ["快乐的", "勇敢的", "聪明的", "幸运的", "神秘的", "闪耀的", "强大的", "优雅的"]
    nouns = ["玩家", "勇士", "法师", "射手", "骑士", "刺客", "守护者", "探险家"]

    random_nickname = Enum.random(adjectives) <> Enum.random(nouns) <> "#{:rand.uniform(999)}"

    response_data = %{
      "status" => 0,
      "nickname" => random_nickname
    }

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_get_random_nickname_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理请求验证码 (RegLogin.CS_REQUEST_VERCODE_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_request_vercode_p} = message,
        state
      ) do
    Cypridina.Protocol.SystemHandlers.handle_request_vercode(message, state)
  end

  # 处理响应验证码 (RegLogin.CS_RESPONSE_VERCODE_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_response_vercode_p} = message,
        state
      ) do
    Cypridina.Protocol.SystemHandlers.handle_response_vercode(message, state)
  end

  # 处理请求游戏版本号列表 (RegLogin.CS_REQUEST_GAMEVERSIONS_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_request_gameversions_p} = message,
        state
      ) do
    Cypridina.Protocol.SystemHandlers.handle_request_game_versions(message, state)
  end

  # 处理请求手机验证码 (RegLogin.CS_REQUEST_PHONE_VERIFICATION_CODE_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_request_phone_verification_code_p} =
          message,
        state
      ) do
    handle_request_phone_verification_code(message, state)
  end

  # ==================== BaseInfo 协议处理 ====================

  # 处理设置昵称请求 (BaseInfo.CS_SET_NICKNAME_P)
  def handle_message(
        %{main_id: @main_proto_base_info, sub_id: @base_info_cs_set_nickname_p} = message,
        state
      ) do
    Logger.info("设置昵称请求: #{inspect(message.data)}")

    data = message.data || %{}
    nickname = Map.get(data, "nickname", "")

    # 验证昵称
    response_data =
      cond do
        String.length(nickname) < 2 ->
          %{
            "code" => 1,
            "message" => "昵称长度不能少于2个字符"
          }

        String.length(nickname) > 20 ->
          %{
            "code" => 2,
            "message" => "昵称长度不能超过20个字符"
          }

        true ->
          # 更新用户资料中的昵称
          case update_user_profile(state.user_id, %{nickname: nickname}) do
            {:ok, _profile} ->
              %{
                "code" => 0,
                "nickname" => nickname
              }

            {:error, reason} ->
              Logger.error("更新昵称失败: #{inspect(reason)}")
              %{
                "code" => 3,
                "message" => "昵称设置失败，请稍后重试"
              }
          end
      end

    response_map = %{
      "mainId" => @main_proto_base_info,
      "subId" => @base_info_sc_set_nickname_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理设置头像ID请求 (BaseInfo.CS_SET_HEADID_P)
  def handle_message(
        %{main_id: @main_proto_base_info, sub_id: @base_info_cs_set_headid_p} = message,
        state
      ) do
    Logger.info("设置头像ID请求: #{inspect(message.data)}")

    data = message.data || %{}
    # 客户端发送的字段名是 "id" 和 "frameid"
    head_id = Map.get(data, "id", Map.get(data, "head_id", 1))
    frame_id = Map.get(data, "frameid", Map.get(data, "frame_id", 0))

    # 验证头像ID范围
    response_data =
      cond do
        head_id < 1 or head_id > 999 ->
          %{
            "code" => 1,
            "message" => "无效的头像ID"
          }

        true ->
          # 只存储头像ID，清空自定义头像URL（因为使用预设头像）
          Logger.info("开始更新用户头像: user_id=#{state.user_id}, head_id=#{head_id}, frame_id=#{frame_id}")
          case update_user_profile(state.user_id, %{head_id: head_id, avatar_url: nil}) do
            {:ok, _profile} ->
              Logger.info("头像更新成功")
              %{
                "code" => 0,
                "message" => "头像设置成功",
                "id" => head_id,
                "frameid" => frame_id,
                "user_id" => state.user_id
              }

            {:error, reason} ->
              Logger.error("更新头像失败: #{inspect(reason)}")
              %{
                "code" => 2,
                "message" => "头像设置失败，请稍后重试"
              }
          end
      end

    response_map = %{
      "mainId" => @main_proto_db_server,
      "subId" => @db_server_sc_set_headid_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end



  # 处理修改密码请求 (BaseInfo.CS_CHANGE_PSW_P)
  def handle_message(
        %{main_id: @main_proto_base_info, sub_id: @base_info_cs_change_psw_p} = message,
        state
      ) do
    Logger.info("修改密码请求: #{inspect(message.data)}")

    data = message.data || %{}
    old_password = Map.get(data, "old_password", "")
    new_password = Map.get(data, "new_password", "")

    # 验证密码
    response_data =
      cond do
        String.length(old_password) == 0 ->
          %{
            "status" => 1,
            "message" => "请输入原密码"
          }

        String.length(new_password) < 6 ->
          %{
            "status" => 2,
            "message" => "新密码长度不能少于6个字符"
          }

        true ->
          %{
            "status" => 0,
            "message" => "密码修改成功",
            "user_id" => state.user_id
          }
      end

    response_map = %{
      "mainId" => @main_proto_base_info,
      "subId" => @base_info_sc_change_psw_result_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理设置性别请求 (BaseInfo.CD_SET_SEX_P)
  def handle_message(
        %{main_id: @main_proto_base_info, sub_id: @base_info_cd_set_sex_p} = message,
        state
      ) do
    Logger.info("设置性别请求: #{inspect(message.data)}")

    data = message.data || %{}
    # 1-男, 2-女
    sex = Map.get(data, "sex", 1)

    response_data = %{
      "status" => 0,
      "message" => "性别设置成功",
      "sex" => sex,
      "user_id" => state.user_id
    }

    response_map = %{
      "mainId" => @main_proto_base_info,
      "subId" => @base_info_dc_set_sex_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理BaseInfo协议的其他请求
  def handle_message(%{main_id: @main_proto_base_info, sub_id: sub_id} = message, state) do
    Logger.info("BaseInfo协议消息: [#{message.main_id}][#{sub_id}] - #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "message" => "base_info_received",
      "original_sub_id" => sub_id
    }

    response_map = %{
      "mainId" => @main_proto_base_info,
      "subId" => sub_id + 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理游戏准备消息
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_cs_room_player_ready} = message,
        state
      ) do
    Logger.info("玩家准备游戏: #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "message" => "ready_success"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_cs_room_player_ready + 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理退出请求
  def handle_message(%{main_id: @main_proto_game, sub_id: @game_cs_quit_p} = message, state) do
    Logger.info("🚪 [QUIT] 玩家请求退出: #{inspect(message.data)}")

    # 如果玩家在房间中，从房间中移除
    if state.room_id do
      Logger.info("🚪 [QUIT] 玩家从房间 #{state.room_id} 退出")
      # 这里可以添加从房间管理器中移除玩家的逻辑
    end

    # 清理玩家状态
    new_state = %{
      state
      | room_id: nil,
        seat_id: nil,
        game_room_entered: false
    }

    # 根据原始IndiaGameServer的实现，退出请求通常不需要特定的响应
    # 客户端发送退出请求后会直接关闭连接或返回大厅
    # 我们返回一个简单的确认，但不使用错误的协议ID
    Logger.info("🚪 [QUIT] 玩家退出处理完成")

    # 不发送响应，让客户端自行处理退出逻辑
    {:ok, new_state}
  end

  # 处理聊天消息
  def handle_message(%{main_id: @main_proto_game, sub_id: @game_cs_room_chat_p} = message, state) do
    Logger.info("玩家聊天: #{inspect(message.data)}")

    # 广播聊天消息给房间内其他玩家
    response_data =
      Map.merge(message.data || %{}, %{
        "user_id" => state.user_id,
        "timestamp" => System.system_time(:millisecond)
      })

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_cs_room_chat_p + 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理房间设置玩家状态请求
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_cs_room_set_player_state_p} = message,
        state
      ) do
    Logger.info("设置玩家状态: #{inspect(message.data)}")

    data = message.data || %{}
    player_state = Map.get(data, "state", 0)

    response_data = %{
      "status" => 0,
      "user_id" => state.user_id,
      "player_state" => player_state,
      "message" => "状态设置成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_room_set_player_state_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理换桌请求
  def handle_message(%{main_id: @main_proto_game, sub_id: @game_cs_huanzhuo_p} = message, state) do
    Logger.info("玩家请求换桌: #{inspect(message.data)}")

    # 模拟换桌成功
    new_room_id = "room_#{:rand.uniform(1000)}"
    new_state = %{state | room_id: new_room_id}

    response_data = %{
      "status" => 0,
      "room_id" => new_room_id,
      "seat_id" => :rand.uniform(6),
      "message" => "换桌成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_huanzhuo_p,
      "data" => response_data
    }

    {:reply, response_map, new_state}
  end

  # 处理断线重连请求
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_cs_room_zanli_comback_p} = message,
        state
      ) do
    Logger.info("玩家断线重连: #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "room_id" => state.room_id || "room_default",
      "user_id" => state.user_id,
      "message" => "重连成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_cs_room_zanli_comback_p + 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理机器人进入请求
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_cs_mode1_robot_enter_p} = message,
        state
      ) do
    Logger.info("机器人进入请求: #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "robot_count" => :rand.uniform(3),
      "message" => "机器人已加入"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_mode1_enter_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理请求游戏玩家人数表 (Game.CS_GAME_PLAYER_NUM_P)
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_cs_game_player_num_p} = message,
        state
      ) do
    Logger.info("收到请求游戏玩家人数表: #{inspect(message.data)}")

    # 模拟各个游戏的玩家人数数据
    game_player_nums = %{
      # Teen Patti
      "1" => %{
        "game_id" => 1,
        "game_name" => "Teen Patti",
        "total_players" => :rand.uniform(500) + 100,
        "online_players" => :rand.uniform(200) + 50,
        "rooms_count" => :rand.uniform(20) + 5
      },
      # Rummy
      "2" => %{
        "game_id" => 2,
        "game_name" => "Rummy",
        "total_players" => :rand.uniform(300) + 80,
        "online_players" => :rand.uniform(150) + 30,
        "rooms_count" => :rand.uniform(15) + 3
      },
      # Andar Bahar
      "3" => %{
        "game_id" => 3,
        "game_name" => "Andar Bahar",
        "total_players" => :rand.uniform(400) + 120,
        "online_players" => :rand.uniform(180) + 40,
        "rooms_count" => :rand.uniform(18) + 4
      },
      # Dragon Tiger
      "4" => %{
        "game_id" => 4,
        "game_name" => "Dragon Tiger",
        "total_players" => :rand.uniform(250) + 60,
        "online_players" => :rand.uniform(120) + 25,
        "rooms_count" => :rand.uniform(12) + 2
      }
    }

    response_data = %{
      "status" => 0,
      "game_player_nums" => game_player_nums,
      "total_games" => map_size(game_player_nums),
      "server_time" => System.system_time(:millisecond),
      "message" => "获取游戏玩家人数成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_game_player_num_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理其他游戏逻辑相关请求（排除已有专门处理器的消息）
  def handle_message(%{main_id: @main_proto_game, sub_id: sub_id} = message, state)
      when sub_id not in [
             @game_cs_mode1_enter_p,
             @game_cs_mode1_enter_pipei_p,
             @game_cs_quit_p,
             @game_cs_huanzhuo_p
           ] do
    Logger.info(
      "🎮 [GAME_GENERIC] 通用游戏消息: [#{message.main_id}][#{sub_id}] - #{inspect(message.data)}"
    )

    # 通用游戏消息处理
    response_data = %{
      # 客户端期望的字段
      "code" => 0,
      # 错误消息（成功时为空）
      "msg" => "",
      "status" => 0,
      "message" => "received",
      "original_sub_id" => sub_id
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => sub_id + 1,
      "data" => response_data
    }

    Logger.info("🎮 [GAME_GENERIC] 通用响应: MainID=#{@main_proto_game}, SubID=#{sub_id + 1}")

    {:reply, response_map, state}
  end

  # 处理XC协议 - 结算
  def handle_message(%{main_id: @main_proto_xc, sub_id: @xc_jiesuan_p} = message, state) do
    Logger.info("XC结算请求: #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      # 随机输赢金额
      "win_amount" => :rand.uniform(1000) - 500,
      "total_score" => :rand.uniform(100),
      "game_result" => "game_finished"
    }

    response_map = %{
      "mainId" => @main_proto_xc,
      "subId" => @xc_jiesuan_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理XC协议的其他请求
  def handle_message(%{main_id: @main_proto_xc, sub_id: sub_id} = message, state) do
    Logger.info("XC协议消息: [#{message.main_id}][#{sub_id}] - #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "message" => "xc_received",
      "original_sub_id" => sub_id
    }

    response_map = %{
      "mainId" => @main_proto_xc,
      "subId" => sub_id,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理手机注册验证码请求 (RegLogin.CS_REQUEST_REG_PHONECODE_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_request_reg_phonecode_p} = message,
        state
      ) do
    Logger.info("手机注册验证码请求: #{inspect(message.data)}")

    data = message.data || %{}
    phone_number = Map.get(data, "phone", "")
    type = Map.get(data, "type", 0)
    siteid = Map.get(data, "siteid", 1)

    # 验证手机号格式
    response_data =
      if String.length(phone_number) >= 10 do
        # 模拟发送验证码成功
        %{
          "code" => 0,
          "message" => "验证码发送成功",
          "phone" => phone_number,
          "type" => type,
          "siteid" => siteid
        }
      else
        %{
          "code" => 1,
          "message" => "无效的手机号码",
          "phone" => phone_number
        }
      end

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_request_reg_phonecode_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理手机注册请求 (RegLogin.CS_PHONECODE_REG_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_phonecode_reg_p} = message,
        state
      ) do
    Logger.info("手机注册请求: #{inspect(message.data)}")

    data = message.data || %{}
    phone_number = Map.get(data, "phoneNumber", "")
    phone_code = Map.get(data, "phoneCode", "")
    password = Map.get(data, "password", "")
    siteid = Map.get(data, "siteid", 1)

    # 验证注册信息
    response_data =
      cond do
        String.length(phone_number) < 10 ->
          %{
            "code" => 1,
            "message" => "无效的手机号码"
          }

        String.length(phone_code) != 6 ->
          %{
            "code" => 2,
            "message" => "验证码格式错误"
          }

        String.length(password) < 6 ->
          %{
            "code" => 3,
            "message" => "密码长度不能少于6位"
          }

        true ->
          # 模拟注册成功
          %{
            "code" => 0,
            "message" => "注册成功",
            "user_id" => :rand.uniform(1_000_000),
            "phone_number" => phone_number,
            "siteid" => siteid
          }
      end

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_phonecode_reg_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== FindPsw 协议处理 ====================

  # 处理设置新密码请求 (FindPsw.CS_FINDPSW_SET_NEW_PSW_P)
  def handle_message(
        %{main_id: @main_proto_find_psw, sub_id: @find_psw_cs_findpsw_set_new_psw_p} = message,
        state
      ) do
    Logger.info("设置新密码请求: #{inspect(message.data)}")

    data = message.data || %{}
    phone_number = Map.get(data, "phoneNumber", "")
    phone_code = Map.get(data, "phoneCode", "")
    password = Map.get(data, "password", "")
    siteid = Map.get(data, "siteid", 1)

    # 验证手机验证码和密码
    response_data =
      cond do
        String.length(phone_number) < 10 ->
          %{
            "code" => 1,
            "message" => "无效的手机号码"
          }

        String.length(phone_code) != 6 ->
          %{
            "code" => 2,
            "message" => "验证码格式错误"
          }

        String.length(password) < 6 ->
          %{
            "code" => 3,
            "message" => "密码长度不能少于6位"
          }

        true ->
          # 模拟密码重置成功
          %{
            "code" => 0,
            "message" => "密码重置成功",
            "phone_number" => phone_number
          }
      end

    response_map = %{
      "mainId" => @main_proto_find_psw,
      "subId" => @find_psw_sc_findpsw_set_new_psw_result_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== Money 协议处理 ====================

  # 处理Money协议 - 设置金币
  def handle_message(
        %{main_id: @main_proto_money, sub_id: @money_sc_set_money_p} = message,
        state
      ) do
    Logger.info("设置金币请求: #{inspect(message.data)}")

    data = message.data || %{}
    money_amount = Map.get(data, "money", 0)

    response_data = %{
      "status" => 0,
      "money" => money_amount,
      "user_id" => state.user_id,
      "message" => "金币设置成功"
    }

    response_map = %{
      "mainId" => @main_proto_money,
      "subId" => @money_sc_set_money_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理Money协议 - 设置钱包金额
  def handle_message(
        %{main_id: @main_proto_money, sub_id: @money_sc_set_walletmoney_p} = message,
        state
      ) do
    Logger.info("设置钱包金额请求: #{inspect(message.data)}")

    data = message.data || %{}
    wallet_money = Map.get(data, "wallet_money", 0)

    response_data = %{
      "status" => 0,
      "wallet_money" => wallet_money,
      "user_id" => state.user_id,
      "message" => "钱包金额设置成功"
    }

    response_map = %{
      "mainId" => @main_proto_money,
      "subId" => @money_sc_set_walletmoney_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理获取金币请求 (Money.CS_GET_MONEY_P)
  def handle_message(
        %{main_id: @main_proto_money, sub_id: @money_cs_get_money_p} = message,
        state
      ) do
    Logger.info("获取金币请求: #{inspect(message.data)}")

    # 模拟获取用户金币信息
    response_data = %{
      "status" => 0,
      "money" => 10000,
      "wallet_money" => 50000,
      "user_id" => state.user_id,
      "message" => "获取金币信息成功"
    }

    response_map = %{
      "mainId" => @main_proto_money,
      "subId" => @money_sc_get_money_result_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理存钱请求 (Money.CS_SAVE_MONEY_P)
  def handle_message(
        %{main_id: @main_proto_money, sub_id: @money_cs_save_money_p} = message,
        state
      ) do
    Logger.info("存钱请求: #{inspect(message.data)}")

    data = message.data || %{}
    money = Map.get(data, "money", 0)

    response_data = %{
      "status" => 0,
      "money" => money,
      "user_id" => state.user_id,
      "message" => "存钱成功"
    }

    response_map = %{
      "mainId" => @main_proto_money,
      "subId" => @money_sc_save_money_result_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理变更金币请求 (Money.CS_CHANGE_MONEY_P)
  def handle_message(
        %{main_id: @main_proto_money, sub_id: @money_cs_change_money_p} = message,
        state
      ) do
    Logger.info("变更金币请求: #{inspect(message.data)}")

    data = message.data || %{}
    change_amount = Map.get(data, "amount", 0)
    # 1-增加, 2-减少
    change_type = Map.get(data, "type", 1)

    response_data = %{
      "status" => 0,
      "change_amount" => change_amount,
      "change_type" => change_type,
      "new_money" => 10000 + if(change_type == 1, do: change_amount, else: -change_amount),
      "user_id" => state.user_id,
      "message" => "金币变更成功"
    }

    response_map = %{
      "mainId" => @main_proto_money,
      "subId" => @money_sc_change_money_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理支付请求 (Money.CS_PAY)
  def handle_message(
        %{main_id: @main_proto_money, sub_id: @money_cs_pay} = message,
        state
      ) do
    Logger.info("💰 [MONEY_PAY] 收到支付请求: #{inspect(message.data)}")

    data = message.data || %{}
    user_id = state.user_id

    # 检查用户是否已登录
    if is_nil(user_id) do
      response_data = %{
        "code" => 1,
        "msg" => "用户未登录",
        "status" => 1
      }

      response_map = %{
        "mainId" => @main_proto_money,
        "subId" => @money_sc_pay,
        "data" => response_data
      }

      {:reply, response_map, state}
    else
      # 获取支付参数
      amount = Map.get(data, "amount", 0)
      pay_type = Map.get(data, "paytype", "alipay")
      charge_id = Map.get(data, "chargeid", "")

      # 验证支付参数
      cond do
        amount <= 0 ->
          response_data = %{
            "code" => 2,
            "msg" => "支付金额无效",
            "status" => 2
          }

          response_map = %{
            "mainId" => @main_proto_money,
            "subId" => @money_sc_pay,
            "data" => response_data
          }

          {:reply, response_map, state}

        String.length(charge_id) == 0 ->
          response_data = %{
            "code" => 3,
            "msg" => "充值配置ID无效",
            "status" => 3
          }

          response_map = %{
            "mainId" => @main_proto_money,
            "subId" => @money_sc_pay,
            "data" => response_data
          }

          {:reply, response_map, state}

        true ->
          # 调用支付系统创建订单
          case Teen.PaymentSystem.PaymentService.create_order(%{
                 user_id: user_id,
                 amount: Decimal.new(amount),
                 currency: "CNY",
                 channel_id: pay_type,
                 notify_url: get_payment_notify_url(),
                 return_url: get_payment_return_url()
               }) do
            {:ok, payment_result} ->
              response_data = %{
                "code" => 0,
                "msg" => "支付订单创建成功",
                "status" => 0,
                "orderid" => payment_result.order_id,
                "payurl" => payment_result.payment_url,
                "amount" => amount,
                "paytype" => pay_type
              }

              response_map = %{
                "mainId" => @main_proto_money,
                "subId" => @money_sc_pay,
                "data" => response_data
              }

              {:reply, response_map, state}

            {:error, reason} ->
              Logger.error("💰 [MONEY_PAY] 创建支付订单失败: #{inspect(reason)}")

              response_data = %{
                "code" => 4,
                "msg" => "支付订单创建失败: #{inspect(reason)}",
                "status" => 4
              }

              response_map = %{
                "mainId" => @main_proto_money,
                "subId" => @money_sc_pay,
                "data" => response_data
              }

              {:reply, response_map, state}
          end
      end
    end
  end

  # 处理Money协议的其他请求
  def handle_message(%{main_id: @main_proto_money, sub_id: sub_id} = message, state) do
    Logger.info("Money协议消息: [#{message.main_id}][#{sub_id}] - #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "message" => "money_received",
      "original_sub_id" => sub_id
    }

    response_map = %{
      "mainId" => @main_proto_money,
      "subId" => sub_id,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== Pay 协议处理 ====================

  # 处理获取支付列表请求 (Pay.CS_GET_PAY_LIST_P)
  def handle_message(%{main_id: @main_proto_pay, sub_id: @pay_cs_get_pay_list_p} = message, state) do
    Logger.info("获取支付列表请求: #{inspect(message.data)}")

    # 模拟支付商品列表
    pay_list = [
      %{
        "id" => 1,
        "name" => "金币包小",
        "price" => 6.0,
        "money" => 1000,
        "bonus" => 100,
        "icon" => "coin_small.png"
      },
      %{
        "id" => 2,
        "name" => "金币包中",
        "price" => 30.0,
        "money" => 6000,
        "bonus" => 1000,
        "icon" => "coin_medium.png"
      },
      %{
        "id" => 3,
        "name" => "金币包大",
        "price" => 98.0,
        "money" => 20000,
        "bonus" => 5000,
        "icon" => "coin_large.png"
      }
    ]

    response_data = %{
      "status" => 0,
      "pay_list" => pay_list,
      "message" => "获取支付列表成功"
    }

    response_map = %{
      "mainId" => @main_proto_pay,
      "subId" => @pay_sc_get_pay_list_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理支付请求 (Pay.CS_PAY_P)
  def handle_message(%{main_id: @main_proto_pay, sub_id: @pay_cs_pay_p} = message, state) do
    Logger.info("支付请求: #{inspect(message.data)}")

    data = message.data || %{}
    pay_id = Map.get(data, "pay_id", 0)
    pay_method = Map.get(data, "pay_method", "alipay")

    # 模拟支付处理
    response_data = %{
      "status" => 0,
      "pay_id" => pay_id,
      "order_id" => "order_#{:rand.uniform(1_000_000)}",
      "pay_url" => "https://example.com/pay/#{pay_id}",
      "message" => "支付订单创建成功"
    }

    response_map = %{
      "mainId" => @main_proto_pay,
      "subId" => @pay_sc_pay_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理检查支付状态请求 (Pay.CS_CHECK_PAY_P)
  def handle_message(%{main_id: @main_proto_pay, sub_id: @pay_cs_check_pay_p} = message, state) do
    Logger.info("检查支付状态请求: #{inspect(message.data)}")

    data = message.data || %{}
    order_id = Map.get(data, "order_id", "")

    # 模拟支付状态检查
    # 70%概率支付成功
    pay_status = :rand.uniform(10) > 3

    response_data = %{
      "status" => 0,
      "order_id" => order_id,
      # 1-成功, 0-处理中
      "pay_status" => if(pay_status, do: 1, else: 0),
      "message" => if(pay_status, do: "支付成功", else: "支付处理中")
    }

    response_map = %{
      "mainId" => @main_proto_pay,
      "subId" => @pay_sc_check_pay_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理Pay协议的其他请求
  def handle_message(%{main_id: @main_proto_pay, sub_id: sub_id} = message, state) do
    Logger.info("Pay协议消息: [#{message.main_id}][#{sub_id}] - #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "message" => "pay_received",
      "original_sub_id" => sub_id
    }

    response_map = %{
      "mainId" => @main_proto_pay,
      "subId" => sub_id + 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== Email 协议处理 ====================

  # 处理获取邮件列表请求 (Email.CS_GET_EMAIL_LIST_P)
  def handle_message(
        %{main_id: @main_proto_email, sub_id: @email_cs_get_email_list_p} = message,
        state
      ) do
    Logger.info("获取邮件列表请求: #{inspect(message.data)}")

    # 模拟邮件列表
    email_list = [
      %{
        "id" => 1,
        "title" => "欢迎来到游戏",
        "sender" => "系统",
        "content" => "欢迎您加入我们的游戏世界！",
        "is_read" => false,
        "has_attachment" => true,
        "send_time" => System.system_time(:millisecond) - 86_400_000,
        "attachments" => [
          %{"type" => "money", "amount" => 1000},
          %{"type" => "item", "item_id" => 1, "count" => 5}
        ]
      },
      %{
        "id" => 2,
        "title" => "每日签到奖励",
        "sender" => "系统",
        "content" => "恭喜您获得每日签到奖励！",
        "is_read" => true,
        "has_attachment" => false,
        "send_time" => System.system_time(:millisecond) - 3_600_000,
        "attachments" => []
      }
    ]

    response_data = %{
      "status" => 0,
      "email_list" => email_list,
      "total_count" => length(email_list),
      "unread_count" => Enum.count(email_list, fn email -> !email["is_read"] end),
      "message" => "获取邮件列表成功"
    }

    response_map = %{
      "mainId" => @main_proto_email,
      "subId" => @email_sc_get_email_list_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理读取邮件请求 (Email.CS_READ_EMAIL_P)
  def handle_message(
        %{main_id: @main_proto_email, sub_id: @email_cs_read_email_p} = message,
        state
      ) do
    Logger.info("读取邮件请求: #{inspect(message.data)}")

    data = message.data || %{}
    email_id = Map.get(data, "email_id", 0)

    response_data = %{
      "status" => 0,
      "email_id" => email_id,
      "is_read" => true,
      "message" => "邮件已标记为已读"
    }

    response_map = %{
      "mainId" => @main_proto_email,
      "subId" => @email_sc_read_email_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理删除邮件请求 (Email.CS_DELETE_EMAIL_P)
  def handle_message(
        %{main_id: @main_proto_email, sub_id: @email_cs_delete_email_p} = message,
        state
      ) do
    Logger.info("删除邮件请求: #{inspect(message.data)}")

    data = message.data || %{}
    email_id = Map.get(data, "email_id", 0)

    response_data = %{
      "status" => 0,
      "email_id" => email_id,
      "message" => "邮件删除成功"
    }

    response_map = %{
      "mainId" => @main_proto_email,
      "subId" => @email_sc_delete_email_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理Email协议的其他请求
  def handle_message(%{main_id: @main_proto_email, sub_id: sub_id} = message, state) do
    Logger.info("Email协议消息: [#{message.main_id}][#{sub_id}] - #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "message" => "email_received",
      "original_sub_id" => sub_id
    }

    response_map = %{
      "mainId" => @main_proto_email,
      "subId" => sub_id + 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== DbServer 协议处理 ====================

  # 处理客服消息请求 (DbServer.CS_CUSTSRV_REPLY_P)
  def handle_message(
        %{main_id: @main_proto_db_server, sub_id: @db_server_cs_custsrv_reply_p} = message,
        state
      ) do
    Logger.info("客服消息请求: #{inspect(message.data)}")

    # 模拟客服消息列表
    customer_service_messages = [
      %{
        "id" => 1,
        "title" => "欢迎咨询",
        "content" => "您好，有什么可以帮助您的吗？",
        "sender" => "客服小助手",
        "send_time" => System.system_time(:millisecond) - 3_600_000,
        "is_read" => false
      },
      %{
        "id" => 2,
        "title" => "游戏指南",
        "content" => "这里是游戏的基本操作指南...",
        "sender" => "客服小助手",
        "send_time" => System.system_time(:millisecond) - 7_200_000,
        "is_read" => true
      }
    ]

    response_data = %{
      "status" => 0,
      "messages" => customer_service_messages,
      "total_count" => length(customer_service_messages),
      "unread_count" => Enum.count(customer_service_messages, fn msg -> !msg["is_read"] end)
    }

    response_map = %{
      "mainId" => @main_proto_db_server,
      "subId" => @db_server_sc_custsrv_reply_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理充值消息列表请求 (DbServer.CS_NEW_CHARGE_LIST_P)
  def handle_message(
        %{main_id: @main_proto_db_server, sub_id: @db_server_cs_new_charge_list_p} = message,
        state
      ) do
    Logger.info("充值消息列表请求: #{inspect(message.data)}")

    # 模拟充值记录
    charge_list = [
      %{
        "id" => 1,
        "amount" => 30.0,
        "money" => 6000,
        "bonus" => 1000,
        # 1-成功, 0-处理中, -1-失败
        "status" => 1,
        "charge_time" => System.system_time(:millisecond) - 1_800_000,
        "order_id" => "charge_#{:rand.uniform(1_000_000)}"
      },
      %{
        "id" => 2,
        "amount" => 6.0,
        "money" => 1000,
        "bonus" => 100,
        "status" => 1,
        "charge_time" => System.system_time(:millisecond) - 86_400_000,
        "order_id" => "charge_#{:rand.uniform(1_000_000)}"
      }
    ]

    response_data = %{
      "status" => 0,
      "charge_list" => charge_list,
      "total_count" => length(charge_list),
      "total_amount" => Enum.reduce(charge_list, 0, fn charge, acc -> acc + charge["amount"] end)
    }

    response_map = %{
      "mainId" => @main_proto_db_server,
      "subId" => @db_server_sc_new_charge_list_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理礼包查询请求 (DbServer.CD_QUERY_GIFT_PACK_P)
  def handle_message(
        %{main_id: @main_proto_db_server, sub_id: @db_server_cd_query_gift_pack_p} = message,
        state
      ) do
    Logger.info("礼包查询请求: #{inspect(message.data)}")

    # 模拟礼包信息
    gift_packs = [
      %{
        "id" => 1,
        "name" => "新手礼包",
        "description" => "为新手玩家准备的丰厚礼包",
        # 0表示免费
        "price" => 0,
        "items" => [
          %{"type" => "money", "amount" => 5000},
          %{"type" => "item", "item_id" => 1, "count" => 10}
        ],
        "is_available" => true,
        "expire_time" => System.system_time(:millisecond) + 86_400_000 * 7
      },
      %{
        "id" => 2,
        "name" => "豪华礼包",
        "description" => "包含大量金币和道具的豪华礼包",
        "price" => 98.0,
        "items" => [
          %{"type" => "money", "amount" => 50000},
          %{"type" => "item", "item_id" => 2, "count" => 50}
        ],
        "is_available" => true,
        "expire_time" => System.system_time(:millisecond) + 86_400_000 * 30
      }
    ]

    response_data = %{
      "status" => 0,
      "gift_packs" => gift_packs,
      "total_count" => length(gift_packs)
    }

    response_map = %{
      "mainId" => @main_proto_db_server,
      "subId" => @db_server_dc_query_gift_pack_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理获取代理配置 (QMAgent.CD_GET_AGENT_CONFIG_P 通过DBServer)
  def handle_message(
        %{main_id: @main_proto_db_server, sub_id: @qm_agent_cd_get_agent_config_p} = message,
        state
      ) do
    Logger.info("收到获取代理配置请求: #{inspect(message.data)}")

    # 模拟代理配置数据
    response_data = %{
      "status" => 0,
      "agent_config" => %{
        # 佣金比例 5%
        "commission_rate" => 0.05,
        # 最小提现金额
        "min_withdraw" => 100,
        # 最大提现金额
        "max_withdraw" => 10000,
        # 提现手续费 2%
        "withdraw_fee" => 0.02,
        # 一级代理佣金比例
        "level_1_rate" => 0.03,
        # 二级代理佣金比例
        "level_2_rate" => 0.02,
        # 三级代理佣金比例
        "level_3_rate" => 0.01,
        # 推广奖励
        "promotion_bonus" => 50,
        # 是否启用代理功能
        "is_agent_enabled" => true
      },
      "user_agent_info" => %{
        # 当前用户是否为代理
        "is_agent" => false,
        # 代理等级
        "agent_level" => 0,
        # 总佣金
        "total_commission" => 0,
        # 可提现佣金
        "available_commission" => 0,
        # 直推人数
        "direct_members" => 0,
        # 总团队人数
        "total_members" => 0
      },
      "message" => "获取代理配置成功"
    }

    response_map = %{
      "mainId" => @main_proto_db_server,
      "subId" => @qm_agent_dc_get_agent_config_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end
  # ==================== MailManager 协议处理 ====================

  # 处理请求新邮件数量 (MailManager.CS_REQUEST_NEW_MAIL_COUNT_P)
  def handle_message(
        %{main_id: @main_proto_mail_manager, sub_id: @mail_manager_cs_request_new_mail_count_p} =
          message,
        state
      ) do
    Logger.info("收到请求新邮件数量: #{inspect(message.data)}")

    # 模拟新邮件数量
    response_data = %{
      "status" => 0,
      # 随机0-5封新邮件
      "count" => :rand.uniform(5),
      "message" => "获取新邮件数量成功"
    }

    response_map = %{
      "mainId" => @main_proto_mail_manager,
      "subId" => @mail_manager_sc_request_new_mail_count_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== DbServer 协议处理 ====================

  # 处理客服消息请求 (DbServer.CS_CUSTSRV_REPLY_P)
  def handle_message(
        %{main_id: @main_proto_db_server, sub_id: @db_server_cs_custsrv_reply_p} = message,
        state
      ) do
    Logger.info("客服消息请求: #{inspect(message.data)}")

    # 模拟客服消息列表
    customer_service_messages = [
      %{
        "id" => 1,
        "title" => "欢迎咨询",
        "content" => "您好，有什么可以帮助您的吗？",
        "sender" => "客服小助手",
        "send_time" => System.system_time(:millisecond) - 3_600_000,
        "is_read" => false
      },
      %{
        "id" => 2,
        "title" => "游戏指南",
        "content" => "这里是游戏的基本操作指南...",
        "sender" => "客服小助手",
        "send_time" => System.system_time(:millisecond) - 7_200_000,
        "is_read" => true
      }
    ]

    response_data = %{
      "status" => 0,
      "messages" => customer_service_messages,
      "total_count" => length(customer_service_messages)
    }

    response_map = %{
      "mainId" => @main_proto_db_server,
      "subId" => @db_server_sc_custsrv_reply_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理充值消息列表请求 (DbServer.CS_NEW_CHARGE_LIST_P)
  def handle_message(
        %{main_id: @main_proto_db_server, sub_id: @db_server_cs_new_charge_list_p} = message,
        state
      ) do
    Logger.info("充值消息列表请求: #{inspect(message.data)}")

    # 模拟充值记录
    charge_list = [
      %{
        "id" => 1,
        "amount" => 30.0,
        "money" => 6000,
        "bonus" => 1000,
        # 1-成功, 0-处理中, -1-失败
        "status" => 1,
        "charge_time" => System.system_time(:millisecond) - 1_800_000,
        "order_id" => "charge_#{:rand.uniform(1_000_000)}"
      },
      %{
        "id" => 2,
        "amount" => 6.0,
        "money" => 1000,
        "bonus" => 100,
        "status" => 1,
        "charge_time" => System.system_time(:millisecond) - 86_400_000,
        "order_id" => "charge_#{:rand.uniform(1_000_000)}"
      }
    ]

    response_data = %{
      "status" => 0,
      "charge_list" => charge_list,
      "total_count" => length(charge_list),
      "total_amount" => Enum.reduce(charge_list, 0, fn charge, acc -> acc + charge["amount"] end)
    }

    response_map = %{
      "mainId" => @main_proto_db_server,
      "subId" => @db_server_sc_new_charge_list_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== Task 协议处理 ====================

  # 处理获取今日比赛列表 (Task.CS_GET_TODAY_MATCH_LIST)
  def handle_message(
        %{main_id: @main_proto_task, sub_id: @task_cs_get_today_match_list} = message,
        state
      ) do
    Logger.info("收到获取今日比赛列表请求: #{inspect(message.data)}")

    # 模拟今日比赛数据
    current_time = System.system_time(:millisecond)

    today_matches = [
      %{
        "id" => 1,
        "name" => "Teen Patti Championship",
        "game_id" => 1,
        # 30分钟后开始
        "start_time" => current_time + 1_800_000,
        # 2小时后结束
        "end_time" => current_time + 7_200_000,
        # 1: 未开始, 2: 进行中, 3: 已结束
        "status" => 1,
        "entry_fee" => 100,
        "prize_pool" => 10000,
        "max_players" => 100,
        "current_players" => 45
      },
      %{
        "id" => 2,
        "name" => "Rummy Tournament",
        "game_id" => 2,
        # 1小时后开始
        "start_time" => current_time + 3_600_000,
        # 3小时后结束
        "end_time" => current_time + 10_800_000,
        "status" => 1,
        "entry_fee" => 50,
        "prize_pool" => 5000,
        "max_players" => 50,
        "current_players" => 23
      }
    ]

    response_data = %{
      "status" => 0,
      "matches" => today_matches,
      "total_count" => length(today_matches),
      "server_time" => current_time,
      "message" => "获取今日比赛列表成功"
    }

    response_map = %{
      "mainId" => @main_proto_task,
      "subId" => @task_sc_get_today_match_list,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== 通用消息处理 ====================

  def handle_message(%{type: "ping"} = _message, state) do
    # 处理PING消息
    response_map = %{
      "mainId" => 0,
      "subId" => 19,
      "data" => %{
        "time" => System.system_time(:millisecond)
      }
    }

    {:reply, response_map, state}
  end

  # 处理未实现的主协议
  def handle_message(%{main_id: main_id, sub_id: sub_id} = message, state)
      when is_integer(main_id) and is_integer(sub_id) do
    Logger.warning(
      "🔴 [UNIMPLEMENTED] 收到未实现的协议 - MainID: #{main_id}, SubID: #{sub_id}, Data: #{inspect(message.data)}"
    )

    # 根据主协议ID返回相应的通用响应
    protocol_name =
      case main_id do
        @main_proto_find_psw -> "find_password"
        @main_proto_id_record -> "id_record"
        @main_proto_level_exp -> "level_exp"
        @main_proto_phone -> "phone"
        @main_proto_lottery -> "lottery"
        @main_proto_shop -> "shop"
        @main_proto_bag -> "bag"
        @main_proto_friend -> "friend"
        @main_proto_notice -> "notice"
        @main_proto_activity -> "activity"
        @main_proto_sign -> "sign"
        @main_proto_share -> "share"
        @main_proto_feedback -> "feedback"
        @main_proto_task_manager -> "task_manager"
        @main_proto_present -> "present"
        @main_proto_present_manager -> "present_manager"
        @main_proto_game_record -> "game_record"
        @main_proto_present_notice -> "present_notice"
        @main_proto_vip -> "vip"
        @main_proto_player_timer -> "player_timer"
        @main_proto_card -> "card"
        @main_proto_account_info -> "account_info"
        @main_proto_chat_server -> "chat_server"
        @main_proto_local_server -> "local_server"
        @main_proto_rank -> "rank"
        @main_proto_task -> "task"
        @main_proto_hall_activity -> "hall_activity"
        _ -> "unknown_protocol"
      end

    response_data = %{
      "status" => 0,
      "message" => "#{protocol_name}_received",
      "original_main_id" => main_id,
      "original_sub_id" => sub_id,
      "note" => "该协议处理函数尚未实现"
    }

    response_map = %{
      "mainId" => main_id,
      "subId" => sub_id + 1,
      "data" => response_data
    }

    Logger.info(
      "🟡 [UNIMPLEMENTED_RESPONSE] 返回未实现协议的默认响应 - MainID: #{main_id}, SubID: #{sub_id + 1}"
    )

    {:reply, response_map, state}
  end

  def handle_message(message, state) do
    # 处理其他未知类型的消息
    Logger.error("🔴 [UNKNOWN_FORMAT] 未知消息格式: #{inspect(message)}")

    response_map = %{
      "mainId" => 0,
      "subId" => 0,
      "data" => %{
        "type" => "error",
        "code" => "unknown_message",
        "message" => "未知消息类型或格式错误"
      }
    }

    Logger.info("🔴 [ERROR_RESPONSE] 返回错误响应 - MainID: 0, SubID: 0")
    {:reply, response_map, state}
  end

  @doc """
  处理请求手机验证码 (RegLogin.CS_REQUEST_PHONE_VERIFICATION_CODE_P)
  """
  defp handle_request_phone_verification_code(%{main_id: 0, sub_id: 29} = message, state) do
    Logger.info("📱 [PHONE_VERIFICATION] 请求手机验证码: #{inspect(message.data)}")

    data = message.data || %{}
    # 使用统一的手机号格式化函数
    phone =
      Map.get(data, "phone", "")
      |> StringUtils.normalize_phone()
      # 默认为登录类型
      |> StringUtils.add_default_country_code(91)

    type = Map.get(data, "type", 2)

    siteid = Map.get(data, "siteid", 1)

    # 获取客户端IP地址用于速率限制
    ip_address =
      case get_in(state, [:socket_info, :peer_data, :address]) do
        {a, b, c, d} ->
          "#{a}.#{b}.#{c}.#{d}"

        _ ->
          # 如果没有socket_info，尝试从state中直接获取ip_address
          Map.get(state, :ip_address, "unknown")
      end

    # 验证手机号格式
    cond do
      String.length(phone) == 0 ->
        response_data = %{
          "status" => 1,
          "message" => "手机号不能为空",
          "phone" => phone
        }

        response_map = %{
          "mainId" => 0,
          "subId" => 30,
          "data" => response_data
        }

        {:reply, response_map, state}

      not StringUtils.valid_phone?(phone) ->
        response_data = %{
          "status" => 2,
          "message" => "手机号格式不正确",
          "phone" => phone
        }

        response_map = %{
          "mainId" => @main_proto_reg_login,
          "subId" => @reg_login_sc_request_phone_verification_code_p,
          "data" => response_data
        }

        Logger.warning("📱 [PHONE_VERIFICATION] 手机号格式错误: #{phone}")
        {:reply, response_map, state}

      true ->
        # 调用验证码发送服务
        case Cypridina.Communications.VerificationCode.generate_and_send(%{
               phone_number: phone,
               code_type: type,
               ip_address: ip_address
             }) do
          {:ok, _verification_code} ->
            response_data = %{
              "status" => 0,
              "message" => "验证码已发送",
              "phone" => phone,
              "type" => type,
              "siteid" => siteid
            }

            response_map = %{
              "mainId" => @main_proto_reg_login,
              "subId" => @reg_login_sc_request_phone_verification_code_p,
              "data" => response_data
            }

            Logger.info("📱 [PHONE_VERIFICATION] 验证码发送成功 - 手机号: #{phone}, 类型: #{type}")
            {:reply, response_map, state}

          {:error, reason} ->
            # 安全地转换错误信息为字符串
            reason_str =
              case reason do
                %Ash.Error.Invalid{} -> Exception.message(reason)
                %Ash.Error.Unknown{} -> Exception.message(reason)
                _ -> to_string(reason)
              end

            response_data = %{
              "status" => 2,
              "message" => "验证码发送失败: #{reason_str}",
              "phone" => phone,
              "type" => type,
              "siteid" => siteid
            }

            response_map = %{
              "mainId" => @main_proto_reg_login,
              "subId" => @reg_login_sc_request_phone_verification_code_p,
              "data" => response_data
            }

            Logger.error("📱 [PHONE_VERIFICATION] 验证码发送失败 - 手机号: #{phone}, 原因: #{reason_str}")
            {:reply, response_map, state}
        end
    end
  end

  # ==================== 顶号机制处理 ====================

  # 处理重复登录（顶号机制）
  defp handle_duplicate_login(user_id, current_state) do
    # 获取当前会话ID
    current_session_id = Map.get(current_state, :session_id)

    Logger.info("🔄 [DUPLICATE_LOGIN] 检查用户重复登录 - 用户ID: #{user_id}, 当前会话: #{current_session_id}")

    # 查找该用户的其他在线会话
    case find_user_online_sessions(user_id, current_session_id) do
      [] ->
        Logger.info("🔄 [DUPLICATE_LOGIN] 无其他在线会话")
        :ok

      other_sessions ->
        Logger.info("🔄 [DUPLICATE_LOGIN] 发现 #{length(other_sessions)} 个其他在线会话，执行顶号")

        # 向其他会话发送被挤下线消息
        Enum.each(other_sessions, fn session_id ->
          send_other_login_message(session_id, user_id)
        end)

        # 记录顶号事件
        Logger.info("🔄 [DUPLICATE_LOGIN] 顶号完成 - 用户ID: #{user_id}, 被挤下线会话数: #{length(other_sessions)}")

        # 返回顶号信息，用于在登录响应中包含
        {:kicked_others, length(other_sessions)}
    end
  end

  # 查找用户的其他在线会话
  defp find_user_online_sessions(user_id, exclude_session_id) do
    # 使用Phoenix Presence查找用户的其他在线会话
    Teen.UserPresence.get_user_sessions(user_id, exclude_session_id)
  end

  # 向指定会话发送被挤下线消息 (SC_OTHER_LOGIN_P)
  defp send_other_login_message(session_id, user_id) do
    Logger.info("📤 [KICK_MESSAGE] 向会话发送被挤下线消息 - 会话: #{session_id}, 用户: #{user_id}")

    # 构建被挤下线消息
    kick_message = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_other_login_p,
      "data" => %{
        "code" => 0,
        "msg" => "你的账号在别处登录，你已经被挤下线",
        "user_id" => user_id,
        "timestamp" => System.system_time(:millisecond)
      }
    }

    # 通过Phoenix.PubSub发送消息到指定会话
    topic = "session:#{session_id}"

    case CypridinaWeb.Endpoint.broadcast(topic, "kick_message", kick_message) do
      :ok ->
        Logger.info("📤 [KICK_MESSAGE] 被挤下线消息发送成功 - 会话: #{session_id}")

      {:error, reason} ->
        Logger.error("📤 [KICK_MESSAGE] 被挤下线消息发送失败 - 会话: #{session_id}, 原因: #{inspect(reason)}")
    end
  end

  # 构建登录其他设备消息 (SC_LOGIN_OTHER_P)
  defp build_login_other_message(kicked_count) do
    %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_login_other_p,
      "data" => %{
        "code" => 0,
        "msg" => "你的账号在别处登录，你把它挤下线",
        "kicked_count" => kicked_count,
        "timestamp" => System.system_time(:millisecond)
      }
    }
  end

  # ==================== 私有辅助函数 ====================

  # 更新用户资料
  defp update_user_profile(user_id, attrs) do
    try do
      IO.puts("更新用户资料: user_id=#{user_id}, attrs=#{inspect(attrs)}")

      # 先尝试获取现有的用户资料
      case Cypridina.Accounts.UserProfile.get_by_user_id(%{user_id: user_id}) do
        {:ok, [profile]} ->
          IO.puts("找到现有用户资料，正在更新: #{inspect(profile)}")
          # 如果存在，则更新
          result = Cypridina.Accounts.UserProfile.update_profile(profile, attrs)
          IO.puts("更新结果: #{inspect(result)}")
          result

        {:ok, []} ->
          IO.puts("未找到用户资料，正在创建新的")
          # 如果不存在，则创建新的资料
          attrs_with_user_id = Map.put(attrs, :user_id, user_id)
          result = Cypridina.Accounts.UserProfile.create_for_user(attrs_with_user_id)
          IO.puts("创建结果: #{inspect(result)}")
          result

        {:error, reason} ->
          IO.puts("读取用户资料失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      error ->
        IO.puts("更新用户资料时发生异常: #{inspect(error)}")
        {:error, error}
    end
  end

  # 生成头像上传URL
  defp generate_avatar_upload_url(user_id, file_extension, content_type) do
    Cypridina.Services.UploadService.generate_presigned_upload_url(user_id, file_extension, content_type)
  end

  # 直接上传头像文件
  defp upload_avatar_file(user_id, file_data, file_extension, content_type) do
    case Cypridina.Services.UploadService.upload_avatar_from_base64(user_id, file_data, file_extension) do
      {:ok, urls} ->
        {:ok, urls.original}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # 根据文件扩展名获取MIME类型
  defp get_content_type_by_extension(extension) do
    case String.downcase(extension) do
      "jpg" -> "image/jpeg"
      "jpeg" -> "image/jpeg"
      "png" -> "image/png"
      "gif" -> "image/gif"
      "webp" -> "image/webp"
      _ -> "application/octet-stream"
    end
  end

  # 从base64数据上传头像
  defp upload_avatar_from_base64(user_id, base64_data, file_extension) do
    try do
      # 解码base64数据
      case Base.decode64(base64_data) do
        {:ok, binary_data} ->
          # 生成文件名
          timestamp = System.system_time(:millisecond)
          filename = "avatar_#{user_id}_#{timestamp}.#{file_extension}"

          # 获取内容类型
          content_type = get_content_type_by_extension(file_extension)

          # 调用现有的上传函数
          upload_avatar_file(user_id, binary_data, file_extension, content_type)

        :error ->
          Logger.error("Base64解码失败 - 用户ID: #{user_id}")
          {:error, "无效的base64数据"}
      end
    rescue
      error ->
        Logger.error("上传头像异常 - 用户ID: #{user_id}, 错误: #{inspect(error)}")
        {:error, "上传头像失败"}
    end
  end

  # 获取支付通知回调URL
  defp get_payment_notify_url do
    base_url = get_base_url()
    "#{base_url}/api/payment/notify"
  end

  # 获取支付返回URL
  defp get_payment_return_url do
    base_url = get_base_url()
    "#{base_url}/payment/success"
  end

  # 获取基础URL
  defp get_base_url do
    # 从配置中获取基础URL，如果没有配置则使用默认值
    Application.get_env(:cypridina, :payment)[:base_url] ||
      Application.get_env(:cypridina, CypridinaWeb.Endpoint)[:url][:host] ||
      "http://localhost:4000"
  end
end

defmodule Cypridina.Protocol.SystemHandlers do
  @moduledoc """
  cypridina项目的系统协议处理函数模块
  处理验证码、系统状态、游戏版本等系统级协议
  参考IndiaGameServer的处理逻辑
  """

  require Logger

  # ==================== RegLogin 系统协议处理 ====================

  @doc """
  处理请求验证码 (RegLogin.CS_REQUEST_VERCODE_P)
  """
  def handle_request_vercode(%{main_id: 0, sub_id: 41} = message, state) do
    Logger.info("请求验证码: #{inspect(message.data)}")

    data = message.data || %{}
    site_id = Map.get(data, "siteid", 1)

    # 模拟验证码生成
    verification_code = %{
      "code_id" => "vercode_#{:rand.uniform(100_000)}",
      "image_data" =>
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
      # 5分钟过期
      "expire_time" => System.system_time(:millisecond) + 300_000,
      "site_id" => site_id
    }

    response_data = %{
      "status" => 0,
      "verification_code" => verification_code,
      "message" => "验证码生成成功"
    }

    response_map = %{
      "mainId" => 0,
      "subId" => 42,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理响应验证码 (RegLogin.CS_RESPONSE_VERCODE_P)
  """
  def handle_response_vercode(%{main_id: 0, sub_id: 43} = message, state) do
    Logger.info("响应验证码: #{inspect(message.data)}")

    data = message.data || %{}
    code_id = Map.get(data, "code_id", "")
    user_input = Map.get(data, "user_input", "")

    # 模拟验证码验证
    # 简单验证：4位数字
    is_correct = String.length(user_input) == 4

    response_data = %{
      "status" => if(is_correct, do: 0, else: 1),
      "code_id" => code_id,
      "verified" => is_correct,
      "message" => if(is_correct, do: "验证码验证成功", else: "验证码错误")
    }

    # 根据验证结果返回不同的子协议
    sub_id = if is_correct, do: 44, else: 45

    response_map = %{
      "mainId" => 0,
      "subId" => sub_id,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理请求游戏版本号列表 (RegLogin.CS_REQUEST_GAMEVERSIONS_P)
  """
  def handle_request_game_versions(%{main_id: 0, sub_id: 51} = message, state) do
    Logger.info("请求游戏版本号列表: #{inspect(message.data)}")

    # 模拟游戏版本列表
    game_versions = [
      %{
        "game_id" => 1001,
        "game_name" => "Teen Patti",
        "version" => "1.5.0",
        "min_version" => "1.4.0",
        "download_url" => "https://example.com/games/teenpatti.zip",
        # 15MB
        "file_size" => 15_728_640,
        "md5" => "d41d8cd98f00b204e9800998ecf8427e",
        "force_update" => false,
        "enabled" => true
      },
      %{
        "game_id" => 1002,
        "game_name" => "Rummy",
        "version" => "1.3.2",
        "min_version" => "1.3.0",
        "download_url" => "https://example.com/games/rummy.zip",
        # 12MB
        "file_size" => 12_582_912,
        "md5" => "098f6bcd4621d373cade4e832627b4f6",
        "force_update" => false,
        "enabled" => true
      },
      %{
        "game_id" => 1003,
        "game_name" => "Poker",
        "version" => "2.0.1",
        "min_version" => "2.0.0",
        "download_url" => "https://example.com/games/poker.zip",
        # 20MB
        "file_size" => 20_971_520,
        "md5" => "5d41402abc4b2a76b9719d911017c592",
        "force_update" => true,
        "enabled" => true
      }
    ]

    response_data = %{
      "status" => 0,
      "game_versions" => game_versions,
      "total_count" => length(game_versions),
      "server_time" => System.system_time(:millisecond)
    }

    response_map = %{
      "mainId" => 0,
      "subId" => 52,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== 其他系统协议处理 ====================

  @doc """
  处理服务器停机维护通知 (RegLogin.SC_SERVER_STOP_P)
  """
  def send_server_stop_notice(state, maintenance_info \\ %{}) do
    Logger.info("发送服务器停机维护通知")

    default_info = %{
      # 1小时后开始维护
      "maintenance_start" => System.system_time(:millisecond) + 3_600_000,
      # 维护2小时
      "maintenance_duration" => 7_200_000,
      "reason" => "系统升级维护",
      "compensation" => [
        %{"type" => "money", "amount" => 5000},
        %{"type" => "item", "item_id" => 1, "count" => 3}
      ]
    }

    maintenance_data = Map.merge(default_info, maintenance_info)

    response_data = %{
      "status" => 0,
      "maintenance_info" => maintenance_data,
      "message" => "服务器即将进入维护状态，请提前做好准备"
    }

    response_map = %{
      "mainId" => 0,
      "subId" => 12,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理其他地方登录通知 (RegLogin.SC_OHTER_LOGIN_P)
  """
  def send_other_login_notice(state, login_info \\ %{}) do
    Logger.info("发送其他地方登录通知")

    default_info = %{
      "login_ip" => "*************",
      "login_time" => System.system_time(:millisecond),
      "device_info" => "Android 手机",
      "location" => "北京市"
    }

    login_data = Map.merge(default_info, login_info)

    response_data = %{
      "status" => 0,
      "login_info" => login_data,
      "message" => "您的账号在其他地方登录，您已被挤下线"
    }

    response_map = %{
      "mainId" => 0,
      "subId" => 10,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理更新保存密码随机数 (RegLogin.SC_UPDATE_SAVE_RANDOM_P)
  """
  def send_update_save_random(state) do
    Logger.info("发送更新保存密码随机数")

    response_data = %{
      "status" => 0,
      "random_code" => "random_#{:rand.uniform(1_000_000)}",
      # 24小时过期
      "expire_time" => System.system_time(:millisecond) + 86_400_000,
      "message" => "密码随机数已更新"
    }

    response_map = %{
      "mainId" => 0,
      "subId" => 13,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 私有辅助函数
end

defmodule Cypridina.Protocol.ProtocolHandlers do
  @moduledoc """
  cypridina项目的协议处理函数模块
  实现IndiaGameClient中Protocol.ts定义的所有协议处理
  参考IndiaGameServer的处理逻辑
  """

  require Logger

  # ==================== FindPsw 协议处理 ====================

  @doc """
  处理找回密码请求 (FindPsw.CS_FINDPSW_P)
  """
  def handle_find_password(%{main_id: 1, sub_id: 0} = message, state) do
    Logger.info("找回密码请求: #{inspect(message.data)}")

    data = message.data || %{}
    username = Map.get(data, "username", "")

    # 模拟找回密码验证
    response_data =
      cond do
        String.length(username) < 3 ->
          %{
            "status" => 1,
            "message" => "用户名不能少于3个字符"
          }

        true ->
          %{
            "status" => 0,
            "message" => "找回密码请求已发送",
            "username" => username,
            "reset_token" => "reset_#{:rand.uniform(100_000)}"
          }
      end

    response_map = %{
      "mainId" => 1,
      "subId" => 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理请求手机验证码 (FindPsw.CS_FINDPSW_REQUEST_CODE_P)
  """
  def handle_find_password_request_code(%{main_id: 1, sub_id: 2} = message, state) do
    Logger.info("请求手机验证码: #{inspect(message.data)}")

    data = message.data || %{}
    phone = Map.get(data, "phone", "")

    response_data = %{
      "status" => 0,
      "message" => "验证码已发送",
      "phone" => phone,
      "code_id" => "code_#{:rand.uniform(100_000)}"
    }

    response_map = %{
      "mainId" => 1,
      "subId" => 3,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理输入密保答案 (FindPsw.CS_FINDPSW_CRYPT_P)
  """
  def handle_find_password_crypt(%{main_id: 1, sub_id: 4} = message, state) do
    Logger.info("输入密保答案: #{inspect(message.data)}")

    data = message.data || %{}
    answer = Map.get(data, "answer", "")

    # 模拟密保验证
    is_correct = String.length(answer) > 0

    response_data = %{
      "status" => if(is_correct, do: 0, else: 1),
      "message" => if(is_correct, do: "密保验证成功", else: "密保答案错误"),
      "verified" => is_correct
    }

    response_map = %{
      "mainId" => 1,
      "subId" => 6,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理输入手机验证码 (FindPsw.CS_FINDPSW_PHONECODE_P)
  """
  def handle_find_password_phone_code(%{main_id: 1, sub_id: 5} = message, state) do
    Logger.info("输入手机验证码: #{inspect(message.data)}")

    data = message.data || %{}
    code = Map.get(data, "code", "")

    # 模拟验证码验证
    is_correct = String.length(code) == 6

    response_data = %{
      "status" => if(is_correct, do: 0, else: 1),
      "message" => if(is_correct, do: "验证码验证成功", else: "验证码错误"),
      "verified" => is_correct
    }

    response_map = %{
      "mainId" => 1,
      "subId" => 6,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理设置新密码 (FindPsw.CS_FINDPSW_SET_NEW_PSW_P)
  """
  def handle_find_password_set_new_password(%{main_id: 1, sub_id: 7} = message, state) do
    Logger.info("设置新密码: #{inspect(message.data)}")

    data = message.data || %{}
    new_password = Map.get(data, "new_password", "")

    response_data =
      cond do
        String.length(new_password) < 6 ->
          %{
            "status" => 1,
            "message" => "密码长度不能少于6个字符"
          }

        true ->
          %{
            "status" => 0,
            "message" => "密码重置成功"
          }
      end

    response_map = %{
      "mainId" => 1,
      "subId" => 8,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== NoticeManager 协议处理 ====================

  @doc """
  处理发送公告请求 (NoticeManager.CS_SEND_NOTICE_P)
  """
  def handle_send_notice(%{main_id: 15, sub_id: 1} = message, state) do
    Logger.info("发送公告请求: #{inspect(message.data)}")

    data = message.data || %{}
    notice_content = Map.get(data, "content", "")
    notice_type = Map.get(data, "type", 1)

    response_data = %{
      "status" => 0,
      "message" => "公告发送成功",
      "notice_id" => "notice_#{:rand.uniform(100_000)}",
      "content" => notice_content,
      "type" => notice_type
    }

    response_map = %{
      "mainId" => 15,
      "subId" => 2,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理请求发送公告所需信息 (NoticeManager.CS_REQUEST_NOTICE_NEED_P)
  """
  def handle_request_notice_need(%{main_id: 15, sub_id: 3} = message, state) do
    Logger.info("请求发送公告所需信息: #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "max_length" => 500,
      "notice_types" => [
        %{"id" => 1, "name" => "系统公告"},
        %{"id" => 2, "name" => "活动公告"},
        %{"id" => 3, "name" => "维护公告"}
      ],
      "user_level_required" => 5
    }

    response_map = %{
      "mainId" => 15,
      "subId" => 4,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理请求系统公告内容 (NoticeManager.CD_REQUEST_SYSTEM_NOTICE_P)
  """
  def handle_request_system_notice(%{main_id: 15, sub_id: 5} = message, state) do
    Logger.info("请求系统公告内容: #{inspect(message.data)}")

    # 模拟系统公告列表
    notices = [
      %{
        "id" => 1,
        "title" => "系统维护公告",
        "content" => "系统将于今晚22:00-24:00进行维护，请提前做好准备。",
        "type" => 3,
        "priority" => 1,
        "start_time" => System.system_time(:millisecond),
        "end_time" => System.system_time(:millisecond) + 86_400_000
      },
      %{
        "id" => 2,
        "title" => "新版本更新",
        "content" => "游戏已更新至v2.1.0版本，新增多项功能。",
        "type" => 1,
        "priority" => 2,
        "start_time" => System.system_time(:millisecond) - 3_600_000,
        "end_time" => System.system_time(:millisecond) + 86_400_000 * 7
      }
    ]

    response_data = %{
      "status" => 0,
      "notices" => notices,
      "total_count" => length(notices)
    }

    response_map = %{
      "mainId" => 15,
      "subId" => 6,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== MailManager 协议处理 ====================

  @doc """
  处理请求邮件内容 (MailManager.CS_REQUEST_MAIL_INFO_P)
  """
  def handle_request_mail_info(%{main_id: 14, sub_id: 0} = message, state) do
    Logger.info("请求邮件内容: #{inspect(message.data)}")

    data = message.data || %{}
    mail_id = Map.get(data, "mail_id", 0)

    # 模拟邮件详情
    mail_info = %{
      "id" => mail_id,
      "title" => "系统邮件",
      "sender" => "系统管理员",
      "content" => "这是一封系统邮件，包含重要信息。",
      "send_time" => System.system_time(:millisecond) - 3_600_000,
      "is_read" => false,
      "has_attachment" => true,
      "attachments" => [
        %{"type" => "money", "amount" => 1000},
        %{"type" => "item", "item_id" => 1, "count" => 5}
      ]
    }

    response_data = %{
      "status" => 0,
      "mail_info" => mail_info
    }

    response_map = %{
      "mainId" => 14,
      "subId" => 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理设置邮件为已读 (MailManager.CS_MAIL_SET_READ_P)
  """
  def handle_mail_set_read(%{main_id: 14, sub_id: 2} = message, state) do
    Logger.info("设置邮件为已读: #{inspect(message.data)}")

    data = message.data || %{}
    mail_id = Map.get(data, "mail_id", 0)

    response_data = %{
      "status" => 0,
      "mail_id" => mail_id,
      "message" => "邮件已标记为已读"
    }

    # 由于这是设置操作，不需要特定的响应协议，使用通用响应
    response_map = %{
      "mainId" => 14,
      "subId" => 2,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理删除邮件 (MailManager.CS_DEL_MAIL_INFO_P)
  """
  def handle_delete_mail(%{main_id: 14, sub_id: 3} = message, state) do
    Logger.info("删除邮件: #{inspect(message.data)}")

    data = message.data || %{}
    mail_id = Map.get(data, "mail_id", 0)

    response_data = %{
      "status" => 0,
      "mail_id" => mail_id,
      "message" => "邮件删除成功"
    }

    response_map = %{
      "mainId" => 14,
      "subId" => 3,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理请求邮件列表 (MailManager.CS_REQUEST_MAILLIST_P)
  """
  def handle_request_mail_list(%{main_id: 14, sub_id: 5} = message, state) do
    Logger.info("请求邮件列表: #{inspect(message.data)}")

    data = message.data || %{}
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 10)

    # 模拟邮件列表
    mail_list = [
      %{
        "id" => 1,
        "title" => "欢迎来到游戏",
        "sender" => "系统",
        "send_time" => System.system_time(:millisecond) - 86_400_000,
        "is_read" => false,
        "has_attachment" => true
      },
      %{
        "id" => 2,
        "title" => "每日签到奖励",
        "sender" => "系统",
        "send_time" => System.system_time(:millisecond) - 3_600_000,
        "is_read" => true,
        "has_attachment" => false
      },
      %{
        "id" => 3,
        "title" => "活动通知",
        "sender" => "活动管理员",
        "send_time" => System.system_time(:millisecond) - 7_200_000,
        "is_read" => false,
        "has_attachment" => true
      }
    ]

    response_data = %{
      "status" => 0,
      "mail_list" => mail_list,
      "total_count" => length(mail_list),
      "page" => page,
      "page_size" => page_size,
      "unread_count" => Enum.count(mail_list, fn mail -> !mail["is_read"] end)
    }

    response_map = %{
      "mainId" => 14,
      "subId" => 6,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理请求新邮件数量 (MailManager.CS_REQUEST_NEW_MAIL_COUNT_P)
  """
  def handle_request_new_mail_count(%{main_id: 14, sub_id: 7} = message, state) do
    Logger.info("请求新邮件数量: #{inspect(message.data)}")

    # 模拟新邮件数量
    new_mail_count = :rand.uniform(5)

    response_data = %{
      "status" => 0,
      "new_mail_count" => new_mail_count,
      "total_mail_count" => new_mail_count + :rand.uniform(10)
    }

    response_map = %{
      "mainId" => 14,
      "subId" => 8,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== Rank 协议处理 ====================

  @doc """
  处理获取排行榜信息 (Rank.CS_RANK_DATA)
  """
  def handle_rank_data(%{main_id: 40, sub_id: 0} = message, state) do
    Logger.info("获取排行榜信息: #{inspect(message.data)}")

    data = message.data || %{}
    # 1-金币排行, 2-胜率排行, 3-等级排行
    rank_type = Map.get(data, "type", 1)

    # 模拟排行榜数据
    rank_list = [
      %{
        "rank" => 1,
        "user_id" => "user_001",
        "nickname" => "排行榜第一",
        "avatar" => "avatar1.jpg",
        "score" => 1_000_000,
        "level" => 50
      },
      %{
        "rank" => 2,
        "user_id" => "user_002",
        "nickname" => "排行榜第二",
        "avatar" => "avatar2.jpg",
        "score" => 800_000,
        "level" => 45
      },
      %{
        "rank" => 3,
        "user_id" => "user_003",
        "nickname" => "排行榜第三",
        "avatar" => "avatar3.jpg",
        "score" => 600_000,
        "level" => 40
      }
    ]

    response_data = %{
      "status" => 0,
      "rank_type" => rank_type,
      "rank_list" => rank_list,
      "my_rank" => :rand.uniform(100),
      "my_score" => :rand.uniform(100_000)
    }

    response_map = %{
      "mainId" => 40,
      "subId" => 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理获取自己的排行榜数据 (Rank.CS_SELF_RANK_DATA_P)
  """
  def handle_self_rank_data(%{main_id: 40, sub_id: 4} = message, state) do
    Logger.info("获取自己的排行榜数据: #{inspect(message.data)}")

    data = message.data || %{}
    rank_type = Map.get(data, "type", 1)

    response_data = %{
      "status" => 0,
      "rank_type" => rank_type,
      "my_rank" => :rand.uniform(100),
      "my_score" => :rand.uniform(100_000),
      "total_players" => 10000,
      # -10到+10的排名变化
      "rank_change" => :rand.uniform(20) - 10
    }

    response_map = %{
      "mainId" => 40,
      "subId" => 5,
      "data" => response_data
    }

    {:reply, response_map, state}
  end
end


defmodule Cypridina.Protocol.ExtendedHandlers do
  @moduledoc """
  cypridina项目的扩展协议处理函数模块
  处理QMAgent、Task、HallActivity、LongHu等协议
  参考IndiaGameServer的处理逻辑
  """

  require Logger

  alias Cypridina.Utils.StringUtils

  # ==================== QMAgent 协议处理 ====================

  @doc """
  处理获得推广佣金信息 (QMAgent.CS_AGENT_PROMOTIONDATA)
  """
  def handle_agent_promotion_data(%{main_id: 41, sub_id: 0} = message, state) do
    Logger.info("获得推广佣金信息: #{inspect(message.data)}")

    # 模拟代理推广数据
    promotion_data = %{
      "total_commission" => 15000.50,
      "this_month_commission" => 3200.00,
      "today_commission" => 150.00,
      "total_invites" => 45,
      "active_invites" => 32,
      "commission_rate" => 0.15,
      "next_level_requirement" => 100,
      "current_level" => 2
    }

    response_data = %{
      "status" => 0,
      "promotion_data" => promotion_data,
      "msg" => "获取推广数据成功"
    }

    response_map = %{
      "mainId" => 41,
      "subId" => 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理领取佣金 (QMAgent.CS_AGENT_GETMONEY)
  """
  def handle_agent_get_money(%{main_id: 41, sub_id: 2} = message, state) do
    Logger.info("领取佣金: #{inspect(message.data)}")

    data = message.data || %{}
    amount = Map.get(data, "amount", 0)

    response_data =
      cond do
        amount <= 0 ->
          %{
            "status" => 1,
            "msg" => "领取金额必须大于0"
          }

        amount > 10000 ->
          %{
            "status" => 2,
            "msg" => "单次领取金额不能超过10000"
          }

        true ->
          %{
            "status" => 0,
            "amount" => amount,
            "remaining_commission" => 15000.50 - amount,
            "msg" => "佣金领取成功"
          }
      end

    response_map = %{
      "mainId" => 41,
      "subId" => 3,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理佣金明细 (QMAgent.CS_AGENT_MONEYDETAIL)
  """
  def handle_agent_money_detail(%{main_id: 41, sub_id: 4} = message, state) do
    Logger.info("佣金明细: #{inspect(message.data)}")

    data = message.data || %{}
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 10)

    # 模拟佣金明细
    money_details = [
      %{
        "id" => 1,
        # 1-推广佣金, 2-活跃佣金
        "type" => 1,
        "amount" => 150.00,
        "from_user" => "user_123",
        "from_nickname" => "玩家123",
        "time" => System.system_time(:millisecond) - 3_600_000,
        "description" => "推广佣金"
      },
      %{
        "id" => 2,
        "type" => 2,
        "amount" => 80.00,
        "from_user" => "user_456",
        "from_nickname" => "玩家456",
        "time" => System.system_time(:millisecond) - 7_200_000,
        "description" => "活跃佣金"
      }
    ]

    response_data = %{
      "status" => 0,
      "money_details" => money_details,
      "page" => page,
      "page_size" => page_size,
      "total_count" => length(money_details)
    }

    response_map = %{
      "mainId" => 41,
      "subId" => 5,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理我的团队 (QMAgent.CS_AGENT_MYTEAM)
  """
  def handle_agent_my_team(%{main_id: 41, sub_id: 6} = message, state) do
    Logger.info("我的团队: #{inspect(message.data)}")

    # 模拟团队数据
    team_members = [
      %{
        "user_id" => "user_001",
        "nickname" => "团队成员1",
        "level" => 15,
        "total_recharge" => 5000.00,
        "commission_generated" => 750.00,
        "join_time" => System.system_time(:millisecond) - 86_400_000 * 30,
        "last_active" => System.system_time(:millisecond) - 3_600_000,
        # 1-活跃, 0-不活跃
        "status" => 1
      },
      %{
        "user_id" => "user_002",
        "nickname" => "团队成员2",
        "level" => 8,
        "total_recharge" => 2000.00,
        "commission_generated" => 300.00,
        "join_time" => System.system_time(:millisecond) - 86_400_000 * 15,
        "last_active" => System.system_time(:millisecond) - 86_400_000,
        "status" => 0
      }
    ]

    response_data = %{
      "status" => 0,
      "team_members" => team_members,
      "total_members" => length(team_members),
      "active_members" => Enum.count(team_members, fn member -> member["status"] == 1 end),
      "total_commission" =>
        Enum.reduce(team_members, 0, fn member, acc -> acc + member["commission_generated"] end)
    }

    response_map = %{
      "mainId" => 41,
      "subId" => 7,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== Task 协议处理 ====================

  @doc """
  处理更新任务列表信息 (Task.CS_UPDATE_TASK_LIST)
  """
  def handle_update_task_list(%{main_id: 42, sub_id: 0} = message, state) do
    Logger.info("更新任务列表信息: #{inspect(message.data)}")

    # 模拟任务列表
    task_list = [
      %{
        "id" => 1,
        "name" => "每日登录",
        "description" => "每日登录游戏获得奖励",
        # 1-每日任务, 2-周任务, 3-成就任务
        "type" => 1,
        "progress" => 1,
        "target" => 1,
        # 0-未完成, 1-已完成, 2-已领取
        "status" => 2,
        "rewards" => [
          %{"type" => "money", "amount" => 1000},
          %{"type" => "exp", "amount" => 100}
        ]
      },
      %{
        "id" => 2,
        "name" => "游戏5局",
        "description" => "完成5局游戏",
        "type" => 1,
        "progress" => 3,
        "target" => 5,
        "status" => 0,
        "rewards" => [
          %{"type" => "money", "amount" => 2000}
        ]
      },
      %{
        "id" => 3,
        "name" => "胜利3局",
        "description" => "获得3局胜利",
        "type" => 1,
        "progress" => 1,
        "target" => 3,
        "status" => 0,
        "rewards" => [
          %{"type" => "money", "amount" => 3000},
          %{"type" => "item", "item_id" => 1, "count" => 1}
        ]
      }
    ]

    response_data = %{
      "status" => 0,
      "task_list" => task_list,
      "total_tasks" => length(task_list),
      "completed_tasks" => Enum.count(task_list, fn task -> task["status"] >= 1 end)
    }

    response_map = %{
      "mainId" => 42,
      "subId" => 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理领取任务奖励 (Task.CS_GET_TASK_REWARD)
  """
  def handle_get_task_reward(%{main_id: 42, sub_id: 2} = message, state) do
    Logger.info("领取任务奖励: #{inspect(message.data)}")

    data = message.data || %{}
    task_id = Map.get(data, "task_id", 0)

    response_data =
      cond do
        task_id <= 0 ->
          %{
            "status" => 1,
            "msg" => "无效的任务ID"
          }

        true ->
          # 模拟任务奖励
          rewards = [
            %{"type" => "money", "amount" => 1000},
            %{"type" => "exp", "amount" => 100}
          ]

          %{
            "status" => 0,
            "task_id" => task_id,
            "rewards" => rewards,
            "msg" => "任务奖励领取成功"
          }
      end

    response_map = %{
      "mainId" => 42,
      "subId" => 3,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理获取比赛列表信息 (Task.CS_GET_MATCH_LIST)
  """
  def handle_get_match_list(%{main_id: 42, sub_id: 11} = message, state) do
    Logger.info("获取比赛列表信息: #{inspect(message.data)}")

    # 模拟比赛列表
    match_list = [
      %{
        "id" => 1,
        "name" => "每日金币赛",
        "description" => "每日金币排行赛",
        # 1-金币赛, 2-积分赛
        "type" => 1,
        "start_time" => System.system_time(:millisecond),
        "end_time" => System.system_time(:millisecond) + 86_400_000,
        "entry_fee" => 1000,
        "max_players" => 100,
        "current_players" => 45,
        # 0-未开始, 1-进行中, 2-已结束
        "status" => 1,
        "rewards" => [
          %{"rank" => 1, "reward" => [%{"type" => "money", "amount" => 50000}]},
          %{"rank" => 2, "reward" => [%{"type" => "money", "amount" => 30000}]},
          %{"rank" => 3, "reward" => [%{"type" => "money", "amount" => 20000}]}
        ]
      },
      %{
        "id" => 2,
        "name" => "周末大奖赛",
        "description" => "周末特别大奖赛",
        "type" => 2,
        "start_time" => System.system_time(:millisecond) + 86_400_000,
        "end_time" => System.system_time(:millisecond) + 86_400_000 * 3,
        "entry_fee" => 5000,
        "max_players" => 500,
        "current_players" => 0,
        "status" => 0,
        "rewards" => [
          %{"rank" => 1, "reward" => [%{"type" => "money", "amount" => 500_000}]},
          %{"rank" => 2, "reward" => [%{"type" => "money", "amount" => 300_000}]},
          %{"rank" => 3, "reward" => [%{"type" => "money", "amount" => 200_000}]}
        ]
      }
    ]

    response_data = %{
      "status" => 0,
      "match_list" => match_list,
      "total_matches" => length(match_list)
    }

    response_map = %{
      "mainId" => 42,
      "subId" => 12,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理获取比赛排名和奖励 (Task.CS_GET_MATCH_RANK_REWARD)
  """
  def handle_get_match_rank_reward(%{main_id: 42, sub_id: 13} = message, state) do
    Logger.info("获取比赛排名和奖励: #{inspect(message.data)}")

    data = message.data || %{}
    match_id = Map.get(data, "match_id", 0)

    # 模拟比赛排名
    rank_list = [
      %{
        "rank" => 1,
        "user_id" => "user_001",
        "nickname" => "比赛冠军",
        "score" => 100_000,
        "reward_claimed" => true
      },
      %{
        "rank" => 2,
        "user_id" => "user_002",
        "nickname" => "比赛亚军",
        "score" => 80000,
        "reward_claimed" => false
      },
      %{
        "rank" => 3,
        "user_id" => state.user_id,
        "nickname" => "我的昵称",
        "score" => 60000,
        "reward_claimed" => false
      }
    ]

    my_rank = Enum.find_index(rank_list, fn player -> player["user_id"] == state.user_id end)
    my_rank = if my_rank, do: my_rank + 1, else: nil

    response_data = %{
      "status" => 0,
      "match_id" => match_id,
      "rank_list" => rank_list,
      "my_rank" => my_rank,
      "my_score" => if(my_rank, do: 60000, else: 0),
      "can_claim_reward" => my_rank != nil and my_rank <= 10
    }

    response_map = %{
      "mainId" => 42,
      "subId" => 14,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== HallActivity 协议处理 ====================

  @doc """
  处理请求登录活动信息 (HallActivity.CS_LOGINCASH_INFO_P)
  """
  def handle_login_cash_info(%{main_id: 101, sub_id: 0} = message, state) do
    Logger.info("请求登录活动信息: #{inspect(message.data)}")

    # 模拟登录活动信息
    activity_info = %{
      "activity_id" => 1,
      "activity_name" => "七日登录活动",
      "description" => "连续登录7天获得丰厚奖励",
      "start_time" => System.system_time(:millisecond) - 86_400_000,
      "end_time" => System.system_time(:millisecond) + 86_400_000 * 6,
      "current_day" => 2,
      "max_days" => 7,
      "daily_rewards" => [
        %{"day" => 1, "reward" => [%{"type" => "money", "amount" => 1000}], "claimed" => true},
        %{"day" => 2, "reward" => [%{"type" => "money", "amount" => 2000}], "claimed" => false},
        %{"day" => 3, "reward" => [%{"type" => "money", "amount" => 3000}], "claimed" => false},
        %{"day" => 4, "reward" => [%{"type" => "money", "amount" => 5000}], "claimed" => false},
        %{"day" => 5, "reward" => [%{"type" => "money", "amount" => 8000}], "claimed" => false},
        %{"day" => 6, "reward" => [%{"type" => "money", "amount" => 10000}], "claimed" => false},
        %{"day" => 7, "reward" => [%{"type" => "money", "amount" => 20000}], "claimed" => false}
      ],
      "can_claim_today" => true
    }

    response_data = %{
      "status" => 0,
      "activity_info" => activity_info
    }

    response_map = %{
      "mainId" => 101,
      "subId" => 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理领取登录活动奖励 (HallActivity.CS_FETCH_LOGINCASH_AWARD_P)
  """
  def handle_fetch_login_cash_award(%{main_id: 101, sub_id: 2} = message, state) do
    Logger.info("领取登录活动奖励: #{inspect(message.data)}")

    data = message.data || %{}
    # 0-登录, 1-充值, 2-游戏局数, 3-转盘, 4-游戏赢分, 10-完成
    fetch_type = Map.get(data, "fetchtype", 0)
    ip = Map.get(data, "ip", "")

    response_data =
      case fetch_type do
        # 登录奖励
        0 ->
          %{
            "status" => 0,
            "fetchaward" => 2000,
            "msg" => "登录奖励领取成功",
            "next_day" => 3
          }

        # 充值奖励
        1 ->
          %{
            "status" => 0,
            "fetchaward" => 5000,
            "msg" => "充值奖励领取成功"
          }

        # 游戏局数奖励
        2 ->
          %{
            "status" => 0,
            "fetchaward" => 1000,
            "msg" => "游戏局数奖励领取成功"
          }

        _ ->
          %{
            "status" => 1,
            "msg" => "无效的奖励类型"
          }
      end

    response_map = %{
      "mainId" => 101,
      "subId" => 3,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理获取用户金币信息 (HallActivity.CS_GET_USER_MONEY_P)
  """
  def handle_get_user_money(%{main_id: 101, sub_id: 4} = message, state) do
    Logger.info("🎮 [GET_USER_MONEY] 获取用户金币信息: #{inspect(message.data)}")

    # 获取用户真实积分
    real_points = Cypridina.Accounts.get_user_points(state.user_id)

    # 构建符合客户端期望的用户金币信息
    # 客户端期望的字段名称（小写）
    user_info = %{
      # 奖金金币 (暂时使用真实积分的一部分)
      "bonusmoney" => div(real_points, 10),
      # 奖金现金 (暂时使用真实积分的一部分)
      "bonuscashmoney" => div(real_points, 5),
      # 当前金币 (使用真实积分)
      "money" => real_points,
      # 今日赢取金币 (暂时设为0，后续可从游戏记录计算)
      "winningmoney" => 0,
      # VIP等级 (暂时根据积分计算)
      "vip" => calculate_vip_level(real_points)
    }

    # 按照客户端期望的格式返回，包含code和msg字段
    response_data = %{
      # 成功状态码（客户端期望）
      "code" => 0,
      # 成功消息（客户端期望）
      "msg" => "获取成功",
      # 客户端期望的user字段
      "user" => user_info
    }

    response_map = %{
      "mainId" => 101,
      "subId" => 5,
      "data" => response_data
    }

    Logger.info("🎮 [GET_USER_MONEY] 返回用户金币信息: #{inspect(response_data)}")

    {:reply, response_map, state}
  end

  @doc """
  处理领取用户积分信息 (HallActivity.CS_FETCH_USER_BONUS_P)
  """
  def handle_fetch_user_bonus(%{main_id: 101, sub_id: 6} = message, state) do
    Logger.info("领取用户积分信息: #{inspect(message.data)}")

    data = message.data || %{}
    fetch_amount = Map.get(data, "fetchamount", 0)
    ip = Map.get(data, "ip", "")

    response_data =
      cond do
        fetch_amount <= 0 ->
          %{
            "status" => 1,
            "msg" => "领取积分必须大于0"
          }

        fetch_amount > 10000 ->
          %{
            "status" => 2,
            "msg" => "单次领取积分不能超过10000"
          }

        true ->
          %{
            "status" => 0,
            "fetch_amount" => fetch_amount,
            "remaining_bonus" => 50000 - fetch_amount,
            "msg" => "积分领取成功"
          }
      end

    response_map = %{
      "mainId" => 101,
      "subId" => 7,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理请求7日签到活动信息 (HallActivity.CS_GET_SEVEN_DAYS_P)
  """
  def handle_get_seven_days(%{main_id: 101, sub_id: 8} = message, state) do
    Logger.info("请求7日签到活动信息: #{inspect(message.data)}")

    # 模拟7日签到活动信息
    seven_days_info = %{
      "activity_id" => 2,
      "activity_name" => "七日签到豪礼",
      "description" => "连续签到7天，奖励递增",
      "current_day" => 3,
      "consecutive_days" => 3,
      "sign_today" => false,
      "sign_records" => [
        %{"day" => 1, "signed" => true, "reward" => [%{"type" => "money", "amount" => 1000}]},
        %{"day" => 2, "signed" => true, "reward" => [%{"type" => "money", "amount" => 2000}]},
        %{"day" => 3, "signed" => false, "reward" => [%{"type" => "money", "amount" => 3000}]},
        %{"day" => 4, "signed" => false, "reward" => [%{"type" => "money", "amount" => 5000}]},
        %{"day" => 5, "signed" => false, "reward" => [%{"type" => "money", "amount" => 8000}]},
        %{"day" => 6, "signed" => false, "reward" => [%{"type" => "money", "amount" => 12000}]},
        %{"day" => 7, "signed" => false, "reward" => [%{"type" => "money", "amount" => 20000}]}
      ],
      "can_sign_today" => true,
      "next_reset_time" => System.system_time(:millisecond) + 86_400_000
    }

    response_data = %{
      "status" => 0,
      "seven_days_info" => seven_days_info
    }

    response_map = %{
      "mainId" => 101,
      "subId" => 9,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理绑定手机号 (HallActivity.CS_BIND_PHONE_P)
  """
  def handle_bind_phone(%{main_id: 101, sub_id: 44} = message, state) do
    Logger.info("📱 [BIND_PHONE] 绑定手机号请求: #{inspect(message.data)}")

    data = message.data || %{}
    user_id = state.current_user.id

    normalized_phone =
      Map.get(data, "phone", "")
      |> StringUtils.normalize_phone()
      |> StringUtils.add_default_country_code(91)

    checkcode = Map.get(data, "checkcode", "")

    # 验证输入参数
    cond do
      String.length(normalized_phone) == 0 ->
        response_data = %{
          "status" => 1,
          "code" => 1,
          "msg" => "手机号不能为空"
        }

        response_map = %{
          "mainId" => 101,
          "subId" => 45,
          "data" => response_data
        }

        {:reply, response_map, state}

      not StringUtils.valid_phone?(normalized_phone) ->
        response_data = %{
          "status" => 6,
          "code" => 1,
          "msg" => "手机号格式不正确"
        }

        response_map = %{
          "mainId" => 101,
          "subId" => 45,
          "data" => response_data
        }

        {:reply, response_map, state}

      String.length(checkcode) == 0 ->
        response_data = %{
          "status" => 2,
          "code" => 1,
          "msg" => "验证码不能为空"
        }

        response_map = %{
          "mainId" => 101,
          "subId" => 45,
          "data" => response_data
        }

        {:reply, response_map, state}

      true ->
        # 验证验证码
        case Cypridina.Communications.VerificationCode.verify_code(%{
               phone_number: normalized_phone,
               code: checkcode,
               code_type: 0
             }) do
          {:ok, true} ->
            # 验证码正确，绑定手机号
            case bind_phone_to_user(user_id, normalized_phone) do
              {:ok, _user} ->
                Logger.info("📱 [BIND_PHONE] 手机号绑定成功 - 用户ID: #{user_id}, 手机号: #{normalized_phone}")

                response_data = %{
                  "status" => 0,
                  "msg" => "手机号绑定成功",
                  "phone" => normalized_phone
                }

                response_map = %{
                  "mainId" => 101,
                  "subId" => 45,
                  "data" => response_data
                }

                {:reply, response_map, state}

              {:error, reason} ->
                Logger.error(
                  "📱 [BIND_PHONE] 手机号绑定失败 - 用户ID: #{user_id}, 手机号: #{normalized_phone}, 原因: #{inspect(reason)}"
                )

                response_data = %{
                  "status" => 3,
                  "code" => 1,
                  "msg" => "手机号绑定失败}"
                }

                response_map = %{
                  "mainId" => 101,
                  "subId" => 45,
                  "data" => response_data
                }

                {:reply, response_map, state}
            end

          {:ok, false} ->
            Logger.warning("📱 [BIND_PHONE] 验证码错误 - 手机号: #{normalized_phone}, 验证码: #{checkcode}")

            response_data = %{
              "status" => 4,
              "code" => 1,
              "msg" => "验证码错误或已过期"
            }

            response_map = %{
              "mainId" => 101,
              "subId" => 45,
              "data" => response_data
            }

            {:reply, response_map, state}

          {:error, reason} ->
            Logger.error(
              "📱 [BIND_PHONE] 验证码验证失败 - 手机号: #{normalized_phone}, 原因: #{inspect(reason)}"
            )

            response_data = %{
              "status" => 5,
              "code" => 1,
              "msg" => "验证码验证失败}"
            }

            response_map = %{
              "mainId" => 101,
              "subId" => 45,
              "data" => response_data
            }

            {:reply, response_map, state}
        end
    end
  end

  # 私有辅助函数

  # 绑定手机号到用户
  defp bind_phone_to_user(user_id, phone_number) do
    case Cypridina.Accounts.User.bind_phone(user_id, %{
           phone: phone_number,
           verification_code: "verified"
         }) do
      {:ok, user} ->
        {:ok, user}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # 根据积分计算VIP等级
  defp calculate_vip_level(points) do
    cond do
      points >= 1_000_000 -> 5
      points >= 500_000 -> 4
      points >= 100_000 -> 3
      points >= 50_000 -> 2
      points >= 10_000 -> 1
      true -> 0
    end
  end
end
