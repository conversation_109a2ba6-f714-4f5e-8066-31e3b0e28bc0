defmodule Teen.Live.ExchangeConfigLive do
  @moduledoc """
  兑换配置管理页面

  提供兑换配置的创建、查看、编辑和管理功能
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.PaymentSystem.ExchangeConfig,


    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "兑换配置"

  @impl Backpex.LiveResource
  def plural_name, do: "兑换配置"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      _ -> false
    end
  end

  @impl Backpex.LiveResource
  def fields do
    %{
      config_name: %{
        module: Backpex.Fields.Text,
        label: "配置名称",
        searchable: true,
        help_text: "兑换配置的名称"
      },
      exchange_rate: %{
        module: Backpex.Fields.Number,
        label: "兑换比例",
        help_text: "金币兑换现金的比例（金币:现金）"
      },
      min_amount: %{
        module: Backpex.Fields.Number,
        label: "最小兑换金额",
        help_text: "最小兑换金额（分）"
      },
      max_amount: %{
        module: Backpex.Fields.Number,
        label: "最大兑换金额",
        help_text: "最大兑换金额（分）"
      },
      daily_limit: %{
        module: Backpex.Fields.Number,
        label: "每日限额",
        help_text: "每日兑换限额（分）"
      },
      fee_rate: %{
        module: Backpex.Fields.Number,
        label: "手续费率",
        help_text: "兑换手续费率（百分比）"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"禁用", 0},
          {"启用", 1}
        ],
        render: fn assigns ->
          assigns = assigns
            |> assign(:status_class, (if assigns.value == 1, do: "badge-success", else: "badge-error"))
            |> assign(:status_text, (if assigns.value == 1, do: "启用", else: "禁用"))

          ~H"""
          <span class={"badge #{@status_class}"}><%= @status_text %></span>
          """
        end
      },
      sort_order: %{
        module: Backpex.Fields.Number,
        label: "排序",
        help_text: "数字越小排序越靠前"
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "描述",
        help_text: "配置的详细描述"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def filters do
    [
      status: %{
        module: Teen.Filters.StatusSelect
      }
    ]
  end

  @impl Backpex.LiveResource
  def resource_actions do
    [
      enable_config: %{
        module: Teen.ResourceActions.EnableConfig,
        label: "启用配置",
        icon: "hero-check-circle",
        confirm_label: "确认启用",
        confirm_text: "确定要启用选中的配置吗？",
        fields: []
      },
      disable_config: %{
        module: Teen.ResourceActions.DisableConfig,
        label: "禁用配置",
        icon: "hero-x-circle",
        confirm_label: "确认禁用",
        confirm_text: "确定要禁用选中的配置吗？",
        fields: []
      }
    ]
  end

  def item_actions do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        only: [:row, :index, :show]
      }
    ]
  end

  def render_resource_slot(assigns, :index_header) do
    ~H"""
    <div class="bg-base-100 p-6 rounded-lg shadow-sm mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-base-content">兑换配置管理</h2>
          <p class="text-base-content/70 mt-1">管理金币兑换现金的配置规则</p>
        </div>
        <div class="stats shadow">
          <div class="stat">
            <div class="stat-title">总配置数</div>
            <div class="stat-value text-primary">0</div>
          </div>
          <div class="stat">
            <div class="stat-title">启用中</div>
            <div class="stat-value text-success">0</div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  def render_resource_slot(assigns, :index_footer) do
    ~H"""
    <div class="bg-base-100 p-4 rounded-lg shadow-sm mt-6">
      <div class="text-sm text-base-content/70">
        <p><strong>操作说明：</strong></p>
        <ul class="list-disc list-inside mt-2 space-y-1">
          <li>兑换比例决定了金币与现金的转换关系</li>
          <li>最小/最大金额限制单次兑换的范围</li>
          <li>每日限额控制用户每天的兑换总额</li>
          <li>手续费率会从兑换金额中扣除</li>
        </ul>
      </div>
    </div>
    """
  end

  def render_resource_slot(assigns, :form_footer) do
    ~H"""
    <div class="bg-base-100 p-4 rounded-lg shadow-sm mt-6">
      <div class="text-sm text-base-content/70">
        <p><strong>配置说明：</strong></p>
        <ul class="list-disc list-inside mt-2 space-y-1">
          <li>兑换比例格式：1000表示1000金币兑换1元现金</li>
          <li>金额单位为分，100分=1元</li>
          <li>手续费率为百分比，5表示5%</li>
        </ul>
      </div>
    </div>
    """
  end

  def render_resource_slot(_assigns, _slot), do: nil
end
