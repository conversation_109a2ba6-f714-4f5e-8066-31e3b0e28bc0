defmodule Teen.Live.Filters.ChannelIdFilter do
  @moduledoc """
  渠道ID过滤器

  用于在渠道管理页面中按渠道ID过滤渠道
  """

  use Backpex.Filter
  import Phoenix.Component
  import CypridinaWeb.CoreComponents

  @impl Backpex.Filter
  def label, do: "渠道ID"

  @impl Backpex.Filter
  def type, do: :text

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <div>
      <.input
        field={@form[:value]}
        type="text"
        placeholder="输入渠道ID"
      />
    </div>
    """
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource) when is_nil(value) or value == "" do
    query
  end

  def query(query, value, _live_resource) do
    import Ash.Query

    query
    |> filter(contains(channel_id, ^value))
  end
end
