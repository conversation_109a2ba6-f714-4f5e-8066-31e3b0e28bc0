defmodule Teen.Live.Filters.DeviceOnlineFilter do
  @moduledoc """
  设备在线状态过滤器

  用于在设备管理页面中按在线状态过滤设备
  """

  use Backpex.Filter
  import Phoenix.Component
  import CypridinaWeb.CoreComponents

  @impl Backpex.Filter
  def label, do: "在线状态"

  @impl Backpex.Filter
  def type, do: :select

  @impl Backpex.Filter
  def options do
    [
      {"全部", nil},
      {"在线", true},
      {"离线", false}
    ]
  end

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <div>
      <.input
        field={@form[:value]}
        type="select"
        options={options()}
        prompt="选择状态"
      />
    </div>
    """
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource) when is_nil(value) or value == "" do
    query
  end

  def query(query, true, _live_resource) do
    import Ash.Query

    query
    |> filter(is_nil(last_offline_at))
  end

  def query(query, false, _live_resource) do
    import Ash.Query

    query
    |> filter(not is_nil(last_offline_at))
  end
end
