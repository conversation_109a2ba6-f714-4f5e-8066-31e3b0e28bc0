defmodule Teen.Live.Filters.StatusFilter do
  @moduledoc """
  状态过滤器

  用于过滤启用/禁用状态的活动
  """

  use Backpex.Filter

  @impl Backpex.Filter
  def label, do: "状态"

  @impl Backpex.Filter
  def type, do: :select

  @impl Backpex.Filter
  def options do
    [
      %{label: "全部", value: ""},
      %{label: "启用", value: "enabled"},
      %{label: "禁用", value: "disabled"}
    ]
  end

  @impl Backpex.Filter
  def render_form(_assigns) do
    # 简化实现，返回空字符串
    ""
  end

  @impl Backpex.Filter
  def render(_assigns) do
    # 简化实现，返回空字符串
    ""
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource, _socket) when is_nil(value) or value == "" do
    query
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource, _socket) do
    status_atom = String.to_existing_atom(value)
    import Ecto.Query
    where(query, [item], item.status == ^status_atom)
  end

  @impl Backpex.Filter
  def can?(assigns) do
    # 所有管理员都可以使用状态过滤器
    assigns.current_user.permission_level >= 1
  end

  defp get_status_label("enabled"), do: "启用"
  defp get_status_label("disabled"), do: "禁用"
  defp get_status_label(_), do: "全部"
end
