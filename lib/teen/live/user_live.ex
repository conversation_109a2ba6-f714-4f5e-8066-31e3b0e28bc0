defmodule Teen.Live.UserLive do
  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Cypridina.Accounts.User,
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "用户"

  @impl Backpex.LiveResource
  def plural_name, do: "用户列表"

  @impl Backpex.LiveResource
  def fields do
    %{
      numeric_id: %{
        module: Backpex.Fields.Number,
        label: "玩家ID",
        only: [:index, :show]
      },
      username: %{
        module: Backpex.Fields.Text,
        label: "用户名",
        searchable: true
      },
      # 密码字段 - 仅在创建时显示
      password: %{
        module: Backpex.Fields.Text,
        label: "密码",
        only: [:new],
        help_text: "密码长度至少6位"
      },
      # 确认密码字段 - 仅在创建时显示
      password_confirmation: %{
        module: Backpex.Fields.Text,
        label: "确认密码",
        only: [:new],
        help_text: "请再次输入密码"
      },
      email: %{
        module: Backpex.Fields.Text,
        label: "邮箱",
        searchable: true
      },
      phone: %{
        module: Backpex.Fields.Text,
        label: "手机号",
        searchable: true
      },
      permission_level: %{
        module: Backpex.Fields.Select,
        label: "权限级别",
        options: [
          {"普通用户", 0},
          {"管理员", 1},
          {"超级管理员", 2}
        ]
      },
      agent_level: %{
        module: Backpex.Fields.Number,
        label: "代理等级",
        help_text: "-1=不是代理，0=根代理，>0=下级代理",
        default: fn _assigns -> -1 end
      },
      # UserProfile 相关字段
      nickname: %{
        module: Backpex.Fields.Text,
        label: "昵称",
        only: [:new, :edit],
        help_text: "用户显示昵称，默认使用用户名"
      },
      gender: %{
        module: Backpex.Fields.Select,
        label: "性别",
        only: [:new, :edit],
        options: [
          {"未知", 0},
          {"男", 1},
          {"女", 2}
        ],
        default: fn _assigns -> 0 end
      },
      avatar_url: %{
        module: Backpex.Fields.Text,
        label: "头像URL",
        only: [:new, :edit],
        help_text: "自定义头像链接"
      },
      head_id: %{
        module: Backpex.Fields.Number,
        label: "预设头像ID",
        only: [:new, :edit],
        help_text: "客户端预设头像ID（1-999）",
        default: fn _assigns -> 1 end
      },
      confirmed_at: %{
        module: Backpex.Fields.DateTime,
        label: "邮箱确认时间",
        only: [:show]
      },
      phone_verified_at: %{
        module: Backpex.Fields.DateTime,
        label: "手机验证时间",
        only: [:show]
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        only: [:index, :show]
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        only: [:show]
      }
    }
  end

  @impl Backpex.LiveResource
  def return_to(_socket, _action, _item, _live_action, _params) do
    "/admin/users"
  end
end
