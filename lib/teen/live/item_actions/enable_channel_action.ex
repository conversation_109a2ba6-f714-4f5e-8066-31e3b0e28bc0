defmodule Teen.Live.ItemActions.EnableChannelAction do
  @moduledoc """
  启用渠道操作

  用于在渠道管理页面中启用指定的渠道
  """

  use Backpex.ItemAction
  import Phoenix.LiveView

  @impl Backpex.ItemAction
  def label(_assigns, _item) do "启用" end

  @impl Backpex.ItemAction
  def icon(_assigns, _item), do: "hero-check-circle"

  @impl Backpex.ItemAction
  def confirm_label(_assigns), do: "确认启用"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要启用这个渠道吗？"

  @impl Backpex.ItemAction
  def fields(), do: []

  @impl Backpex.ItemAction
  def handle(socket, items, _params) do
    socket =
      try do
        case items do
          [item] ->
            case Cypridina.Accounts.Channel.enable(item) do
              {:ok, _updated_item} ->
                socket
                |> put_flash(:info, "渠道已成功启用")

              {:error, _changeset} ->
                socket
                |> put_flash(:error, "启用渠道失败")
            end

          _ ->
            socket
            |> put_flash(:error, "只能对单个渠道执行启用操作")
        end
      rescue
        error ->
          socket
          |> put_flash(:error, "操作失败: #{inspect(error)}")
      end

    {:noreply, socket}
  end
end
