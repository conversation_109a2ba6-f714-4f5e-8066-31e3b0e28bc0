defmodule Teen.Live.UserDeviceLive do
  @moduledoc """
  用户设备管理 LiveView

  提供用户设备的管理功能，包括查看设备信息、设置设备离线等
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Cypridina.Accounts.UserDevice,
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "用户设备"

  @impl Backpex.LiveResource
  def plural_name, do: "用户设备"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    # 只有管理员可以管理用户设备
    case assigns.current_user do
      %{permission_level: level} when level >= 1 -> true
      _ -> false
    end
  end

  @impl Backpex.LiveResource
  def prepare_query(query, _params, _socket) do
    Ash.Query.load(query, :user)
  end

  @impl Backpex.LiveResource
  def fields do
    [
      id: %{
        module: Backpex.Fields.Text,
        label: "ID"
      },
      user_id: %{
        module: Backpex.Fields.Text,
        label: "用户ID"
      },
      user: %{
        module: Backpex.Fields.Text,
        label: "用户",
        render: fn assigns ->
          display_text = case assigns.value do
            %{username: username} when is_binary(username) ->
              username
            %{} = user ->
              # 如果用户对象存在但没有用户名，尝试其他字段
              Map.get(user, :username) || Map.get(user, :phone) || "未知用户"
            _ ->
              # 如果关联没有加载，显示用户ID
              user_id = Map.get(assigns.item, :user_id, "未知")
              "ID: #{user_id}"
          end

          assigns = assign(assigns, :display_text, display_text)

          ~H"""
          <span class={if String.starts_with?(@display_text, "ID:"), do: "text-gray-500", else: ""}>
            <%= @display_text %>
          </span>
          """
        end
      },
      device_id: %{
        module: Backpex.Fields.Text,
        label: "设备ID"
      },
      last_login_ip: %{
        module: Backpex.Fields.Text,
        label: "最后登录IP"
      },
      last_login_at: %{
        module: Backpex.Fields.DateTime,
        label: "最后登录时间"
      },
      last_offline_at: %{
        module: Backpex.Fields.DateTime,
        label: "最后离线时间"
      },
      is_online: %{
        module: Backpex.Fields.Boolean,
        label: "在线状态",
        render: fn assigns ->
          assigns =
            assigns
            |> assign(:status_class, (if assigns.value, do: "text-green-600", else: "text-gray-500"))
            |> assign(:status_text, (if assigns.value, do: "在线", else: "离线"))

          ~H"""
          <span class={@status_class}>
            <%= @status_text %>
          </span>
          """
        end
      },
      device_info: %{
        module: Backpex.Fields.Textarea,
        label: "设备信息"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间"
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间"
      }
    ]
  end

  @impl Backpex.LiveResource
  def item_actions do
    [
      set_offline: %{
        module: Teen.Live.ItemActions.SetDeviceOfflineAction
      }
    ]
  end

  @impl Backpex.LiveResource
  def resource_actions do
    []
  end
end
