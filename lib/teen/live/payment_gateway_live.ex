defmodule Teen.Live.PaymentGatewayLive do
  @moduledoc """
  支付网关管理页面

  提供支付网关的创建、查看、编辑和管理功能
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.PaymentSystem.PaymentGateway,


    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "支付网关"

  @impl Backpex.LiveResource
  def plural_name, do: "支付网关"

  @impl Backpex.LiveResource
  def fields do
    %{
      name: %{
        module: Backpex.Fields.Text,
        label: "网关名称",
        searchable: true,
        help_text: "支付网关的显示名称"
      },
      gateway_type: %{
        module: Backpex.Fields.Text,
        label: "网关类型",
        searchable: true,
        help_text: "网关的类型标识"
      },
      api_url: %{
        module: Backpex.Fields.Text,
        label: "API地址",
        help_text: "支付网关的API接口地址"
      },
      merchant_id: %{
        module: Backpex.Fields.Text,
        label: "商户ID",
        help_text: "在支付网关注册的商户标识"
      },
      api_key: %{
        module: Backpex.Fields.Text,
        label: "API密钥",
        help_text: "用于API调用的密钥"
      },
      secret_key: %{
        module: Backpex.Fields.Text,
        label: "密钥",
        help_text: "用于签名验证的密钥"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"禁用", 0},
          {"启用", 1}
        ],
        searchable: true
      },
      sort_order: %{
        module: Backpex.Fields.Number,
        label: "排序",
        help_text: "显示顺序，数字越小越靠前"
      },
      config_data: %{
        module: Backpex.Fields.Textarea,
        label: "配置数据",
        help_text: "额外的配置信息，JSON格式"
      },
      last_test_at: %{
        module: Backpex.Fields.DateTime,
        label: "最后测试时间",
        only: [:index, :show],
        help_text: "最后一次连接测试的时间"
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "描述",
        help_text: "网关的详细描述信息"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        only: [:index, :show]
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        only: [:show]
      }
    }
  end

  @impl Backpex.LiveResource
  def resource_actions do
    [
      enable_gateway: %{
        module: Teen.ResourceActions.EnableGateway
      },
      disable_gateway: %{
        module: Teen.ResourceActions.DisableGateway
      },
      test_connection: %{
        module: Teen.ResourceActions.TestGatewayConnection
      }
    ]
  end

  @impl Backpex.LiveResource
  def filters do
    [
      status: %{
        module: Teen.Filters.StatusSelect
      }
    ]
  end
end
