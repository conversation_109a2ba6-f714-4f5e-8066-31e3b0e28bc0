defmodule Teen.Live.ActivitySystem.BindingRewardLive do
  @moduledoc """
  绑定奖励管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.BindingReward
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "绑定奖励"

  @impl Backpex.LiveResource
  def plural_name, do: "绑定奖励"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true
      },
      title: %{
        module: Backpex.Fields.Text,
        label: "标题"
      },
      binding_type: %{
        module: Backpex.Fields.Select,
        label: "绑定类型",
        options: [
          {"手机号", "phone"},
          {"邮箱", "email"},
          {"银行卡", "bank_card"},
          {"身份证", "id_card"}
        ]
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "奖励金额"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", :coins},
          {"积分", :points},
          {"现金", :cash},
          {"转盘次数", :wheel_spins},
          {"道具", :items}
        ],
        default: fn _assigns -> :coins end
      },
      one_time_only: %{
        module: Backpex.Fields.Boolean,
        label: "仅限一次",
        default: fn _assigns -> true end
      },
      verification_required: %{
        module: Backpex.Fields.Boolean,
        label: "需要验证",
        default: fn _assigns -> true end
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "描述",
        placeholder: "请输入奖励描述（可选）"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"启用", :enabled},
          {"禁用", :disabled}
        ],
        default: fn _assigns -> :enabled end
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end

  @impl Backpex.LiveResource
  def filters do
    [
      binding_type: %{
        module: Backpex.Filters.Select,
        label: "绑定类型",
        options: [
          {"手机号", "phone"},
          {"邮箱", "email"},
          {"银行卡", "bank_card"},
          {"身份证", "id_card"}
        ]
      },
      status: %{
        module: Backpex.Filters.Select,
        label: "状态",
        options: [
          {"启用", "enabled"},
          {"禁用", "disabled"}
        ]
      }
    ]
  end
end
