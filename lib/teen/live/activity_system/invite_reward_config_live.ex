defmodule Teen.Live.ActivitySystem.InviteRewardConfigLive do
  @moduledoc """
  邀请奖励配置管理页面
  
  提供邀请奖励配置的独立管理功能，包括：
  - 查看所有奖励配置
  - 创建新的奖励配置
  - 编辑现有配置
  - 删除配置
  - 批量操作
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.InviteRewardConfig
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "邀请奖励配置"

  @impl Backpex.LiveResource
  def plural_name, do: "邀请奖励配置"

  @impl Backpex.LiveResource
  def query(query, _live_resource) do
    # 预加载活动信息
    Ash.Query.load(query, :activity)
  end

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:index, :show]
      },
      activity_id: %{
        module: Backpex.Fields.BelongsTo,
        label: "所属活动",
        display_field: :title,
        source: Teen.ActivitySystem.InviteCashActivity,
        searchable: true
      },
      round_number: %{
        module: Backpex.Fields.Number,
        label: "轮次",
        searchable: true
      },
      task_type: %{
        module: Backpex.Fields.Select,
        label: "任务类型",
        options: [
          {"邀请注册", :invite_register},
          {"邀请充值", :invite_recharge},
          {"邀请游戏", :invite_play},
          {"邀请留存", :invite_retention}
        ],
        searchable: true
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", :coins},
          {"积分", :points},
          {"现金", :cash},
          {"转盘次数", :wheel_spins},
          {"道具", :items}
        ],
        searchable: true
      },
      min_reward: %{
        module: Backpex.Fields.Number,
        label: "最小奖励（分）"
      },
      max_reward: %{
        module: Backpex.Fields.Number,
        label: "最大奖励（分）"
      },
      required_progress: %{
        module: Backpex.Fields.Number,
        label: "所需进度",
        default: fn _assigns -> 1 end
      },
      probability: %{
        module: Backpex.Fields.Number,
        label: "概率",
        default: fn _assigns -> 1.0 end
      },
      sort_order: %{
        module: Backpex.Fields.Number,
        label: "排序",
        default: fn _assigns -> 0 end
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true,
        only: [:index, :show]
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true,
        only: [:index, :show]
      }
    }
  end

  @impl Backpex.LiveResource
  def filters do
    [
      task_type: %{
        module: Teen.Filters.TaskTypeFilter,
        label: "任务类型"
      },
      reward_type: %{
        module: Teen.Filters.RewardTypeFilter,
        label: "奖励类型"
      },
      activity_id: %{
        module: Teen.Filters.ActivityIdFilter,
        label: "活动"
      }
    ]
  end

  @impl Backpex.LiveResource
  def resource_actions do
    [
      delete_by_activity: %{
        module: Teen.ResourceActions.DeleteByActivity,
        label: "批量删除",
        icon: "hero-trash",
        confirm_label: "确认批量删除",
        confirm_text: "此操作将删除选定活动的所有奖励配置，确定要继续吗？",
        fields: [
          activity_id: %{
            module: Backpex.Fields.BelongsTo,
            label: "选择活动",
            display_field: :title,
            source: Teen.ActivitySystem.InviteCashActivity,
            required: true
          }
        ]
      },
      bulk_update_probability: %{
        module: Teen.ResourceActions.BulkUpdateProbability,
        label: "批量更新概率",
        icon: "hero-calculator",
        confirm_label: "确认更新",
        confirm_text: "确定要批量更新选中配置的概率吗？",
        fields: [
          probability: %{
            module: Backpex.Fields.Number,
            label: "新概率",
            required: true
          }
        ]
      }
    ]
  end

  @impl Backpex.LiveResource
  def item_actions do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        label: "查看详情",
        icon: "hero-eye"
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        label: "编辑",
        icon: "hero-pencil-square"
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        label: "删除",
        icon: "hero-trash",
        confirm_label: "确认删除",
        confirm_text: "确定要删除此奖励配置吗？此操作不可撤销。"
      },
      duplicate: %{
        module: Teen.ItemActions.DuplicateConfig,
        label: "复制配置",
        icon: "hero-document-duplicate"
      }
    ]
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    # 基于用户权限的访问控制
    case action do
      :index -> true
      :show -> true
      :create -> has_permission?(assigns, :create_invite_config)
      :edit -> has_permission?(assigns, :edit_invite_config)
      :delete -> has_permission?(assigns, :delete_invite_config)
      _ -> true
    end
  end

  # 权限检查辅助函数
  defp has_permission?(assigns, permission) do
    # 这里可以根据实际的权限系统进行检查
    # 暂时返回 true，允许所有操作
    true
  end
end
