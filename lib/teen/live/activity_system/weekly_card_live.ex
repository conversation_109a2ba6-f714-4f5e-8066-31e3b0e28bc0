defmodule Teen.Live.ActivitySystem.WeeklyCardLive do
  @moduledoc """
  周卡活动管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.WeeklyCard
    ],
    layout: {Teen.Layouts, :admin}

  use Phoenix.Component
  import Phoenix.HTML.Form

  @impl Backpex.LiveResource
  def singular_name, do: "周卡活动"

  @impl Backpex.LiveResource
  def plural_name, do: "周卡活动"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "编号",
        readonly: true,
        only: [:index, :show]
      },
      title: %{
        module: Backpex.Fields.Text,
        label: "标题"
      },
      recharge_amount: %{
        module: Backpex.Fields.Number,
        label: "充值金额（分）"
      },
      total_reward: %{
        module: Backpex.Fields.Text,
        label: "奖励金额",
        readonly: true,
        only: [:index, :show],
        render: &__MODULE__.render_total_reward/1
      },
      initial_reward: %{
        module: Backpex.Fields.Number,
        label: "初始奖励（分）",
        default: fn _assigns -> 0 end
      },
      claim_days: %{
        module: Backpex.Fields.Number,
        label: "领取天数",
        default: fn _assigns -> 7 end
      },
      daily_reward: %{
        module: Backpex.Fields.Number,
        label: "每日领取（分）"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", :coins},
          {"积分", :points},
          {"现金", :cash},
          {"转盘次数", :wheel_spins},
          {"道具", :items}
        ],
        default: fn _assigns -> :coins end
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "修改时间",
        readonly: true,
        only: [:index, :show]
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"启用", :enabled},
          {"禁用", :disabled}
        ],
        default: fn _assigns -> :enabled end
      },
      business_logic_display: %{
        module: Backpex.Fields.Text,
        label: "业务逻辑",
        readonly: true,
        only: [:show],
        render: &__MODULE__.render_business_logic/1
      }
    }
  end

  @impl Backpex.LiveResource
  def filters do
    []
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end

  @doc """
  渲染奖励金额字段，显示每日领取金额×领取天数的计算结果
  """
  def render_total_reward(assigns) do
    total_amount = case assigns.item do
      %{daily_reward: daily, claim_days: days} when not is_nil(daily) and not is_nil(days) ->
        Decimal.mult(daily, Decimal.new(days))
        |> Decimal.to_string()
      _ -> "0"
    end

    assigns = assign(assigns, :total_amount, total_amount)

    ~H"""
    <div class={@live_action in [:index, :resource_action] && "truncate"}>
      <span class="text-sm text-gray-700"><%= @total_amount %> 分</span>
    </div>
    """
  end

  @doc """
  渲染业务逻辑说明字段
  """
  def render_business_logic(assigns) do
    logic_text = case assigns.item do
      %{initial_reward: initial, claim_days: days, daily_reward: daily, recharge_amount: recharge}
      when not is_nil(initial) and not is_nil(days) and not is_nil(daily) and not is_nil(recharge) ->
        if Decimal.equal?(initial, Decimal.new("0")) do
          "充值多少得多少，奖励金额分#{days}天返还（每日#{daily}分）"
        else
          "充值立即获得#{initial}分，另外#{days}天内每日可领取#{daily}分"
        end
      _ -> "未设置"
    end

    assigns = assign(assigns, :logic_text, logic_text)

    ~H"""
    <div class={@live_action in [:index, :resource_action] && "truncate"}>
      <span class="text-sm text-blue-600"><%= @logic_text %></span>
    </div>
    """
  end
end
