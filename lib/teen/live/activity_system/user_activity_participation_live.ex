defmodule Teen.Live.ActivitySystem.UserActivityParticipationLive do
  @moduledoc """
  用户活动参与记录管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.UserActivityParticipation
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "用户活动参与记录"

  @impl Backpex.LiveResource
  def plural_name, do: "用户活动参与记录"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true
      },
      user_id: %{
        module: Backpex.Fields.Text,
        label: "用户ID"
      },
      activity_type: %{
        module: Backpex.Fields.Select,
        label: "活动类型",
        options: [
          {"每日游戏任务", "daily_game_task"},
          {"周卡活动", "weekly_card"},
          {"七日登录", "seven_day_login"},
          {"VIP礼包", "vip_gift"},
          {"充值任务", "recharge_task"},
          {"转盘抽奖", "spin_wheel"},
          {"刮刮卡", "scratch_card"},
          {"首充礼包", "first_recharge"},
          {"亏损返利", "loss_rebate"},
          {"邀请奖励", "invite_reward"},
          {"绑定奖励", "binding_reward"},
          {"免费任务", "free_task"},
          {"CDKey", "cdkey"}
        ]
      },
      activity_id: %{
        module: Backpex.Fields.Text,
        label: "活动ID"
      },
      participation_status: %{
        module: Backpex.Fields.Select,
        label: "参与状态",
        options: [
          {"进行中", "in_progress"},
          {"已完成", "completed"},
          {"已过期", "expired"},
          {"已取消", "cancelled"}
        ]
      },
      progress_data: %{
        module: Backpex.Fields.Textarea,
        label: "进度数据(JSON)"
      },
      last_activity_at: %{
        module: Backpex.Fields.DateTime,
        label: "最后活动时间"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
