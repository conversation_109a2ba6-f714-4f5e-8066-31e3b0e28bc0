defmodule Teen.Live.ActivitySystem.FirstRechargeGiftLive do
  @moduledoc """
  首充礼包管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.FirstRechargeGift
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "首充礼包"

  @impl Backpex.LiveResource
  def plural_name, do: "首充礼包"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true
      },
      title: %{
        module: Backpex.Fields.Text,
        label: "标题"
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "活动描述"
      },
      task_type: %{
        module: Backpex.Fields.Select,
        label: "任务类型",
        options: [
          {"首次充值", :first_recharge},
          {"第二次充值", :second_recharge},
          {"第三次充值", :third_recharge},
          {"任意充值", :any_recharge}
        ],
        default: fn _assigns -> :first_recharge end
      },
      limit_days: %{
        module: Backpex.Fields.Number,
        label: "限制天数（注册天数）",
        default: fn _assigns -> 7 end
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "奖励金额（分）"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", :coins},
          {"积分", :points},
          {"现金", :cash},
          {"转盘次数", :wheel_spins},
          {"道具", :items}
        ],
        default: fn _assigns -> :coins end
      },
      start_date: %{
        module: Backpex.Fields.Date,
        label: "开始日期"
      },
      end_date: %{
        module: Backpex.Fields.Date,
        label: "结束日期"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"启用", :enabled},
          {"禁用", :disabled}
        ],
        default: fn _assigns -> :enabled end
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    # 基本权限控制
    case action do
      :index -> true
      :show -> true
      :new -> true
      :create -> true
      :edit -> true
      :update -> true
      :delete -> true
      _ -> false
    end
  end

  @impl Backpex.LiveResource
  def item_actions do
    [
      %{
        module: Backpex.ItemActions.Edit,
        label: "编辑"
      },
      %{
        module: Backpex.ItemActions.Show,
        label: "查看"
      },
      %{
        module: Backpex.ItemActions.Delete,
        label: "删除",
        confirm_label: "确认删除",
        confirm_text: "确定要删除这个首充礼包吗？此操作不可撤销。"
      }
    ]
  end




end
