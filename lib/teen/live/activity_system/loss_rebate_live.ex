defmodule Teen.Live.ActivitySystem.LossRebateLive do
  @moduledoc """
  亏损返利管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.LossRebateJar
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "亏损返利"

  @impl Backpex.LiveResource
  def plural_name, do: "亏损返利"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true
      },
      title: %{
        module: Backpex.Fields.Text,
        label: "标题"
      },
      task_type: %{
        module: Backpex.Fields.Select,
        label: "任务类型",
        options: [
          {"亏损返利", :loss_rebate},
          {"首次亏损", :first_loss},
          {"连续亏损", :consecutive_loss},
          {"大额亏损", :large_loss}
        ],
        default: fn _assigns -> :loss_rebate end
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "活动描述"
      },
      max_claims: %{
        module: Backpex.Fields.Number,
        label: "领取次数",
        default: fn _assigns -> 1 end
      },
      min_loss_amount: %{
        module: Backpex.Fields.Number,
        label: "最小亏损金额（分）"
      },
      rebate_percentage: %{
        module: Backpex.Fields.Number,
        label: "返利百分比(%)",
        default: fn _assigns -> 10 end
      },
      max_rebate_amount: %{
        module: Backpex.Fields.Number,
        label: "最大返利金额（分）"
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "奖励金额（分）"
      },
      calculation_period: %{
        module: Backpex.Fields.Select,
        label: "计算周期",
        options: [
          {"每日", :daily},
          {"每周", :weekly},
          {"每月", :monthly}
        ],
        default: fn _assigns -> :daily end
      },

      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", :coins},
          {"积分", :points},
          {"现金", :cash},
          {"转盘次数", :wheel_spins},
          {"道具", :items}
        ],
        default: fn _assigns -> :coins end
      },
      auto_distribute: %{
        module: Backpex.Fields.Boolean,
        label: "自动发放",
        default: fn _assigns -> false end
      },
      start_date: %{
        module: Backpex.Fields.Date,
        label: "开始日期"
      },
      end_date: %{
        module: Backpex.Fields.Date,
        label: "结束日期"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"启用", :enabled},
          {"禁用", :disabled}
        ],
        default: fn _assigns -> :enabled end
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
