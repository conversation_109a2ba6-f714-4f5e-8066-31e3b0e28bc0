defmodule Teen.Live.ActivitySystem.RechargeWheelLive do
  @moduledoc """
  充值转盘管理界面
  """
  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.RechargeWheel
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "充值转盘"

  @impl Backpex.LiveResource
  def plural_name, do: "充值转盘"

  @impl Backpex.LiveResource
  def fields do
    %{
      title: %{
        module: Backpex.Fields.Text,
        label: "转盘标题"
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "活动描述",
        except: [:index]
      },
      cumulative_recharge: %{
        module: Backpex.Fields.Number,
        label: "累计充值金额（分）"
      },
      wheel_spins: %{
        module: Backpex.Fields.Number,
        label: "转盘次数"
      },
      jackpot_pool: %{
        module: Backpex.Fields.Number,
        label: "奖金池（分）",
        render: &__MODULE__.render_jackpot_pool/1
      },
      start_date: %{
        module: Backpex.Fields.Date,
        label: "开始日期"
      },
      end_date: %{
        module: Backpex.Fields.Date,
        label: "结束日期"
      },
      activity_period: %{
        module: Backpex.Fields.Text,
        label: "活动期间",
        only: [:index, :show],
        render: &__MODULE__.render_activity_period/1
      },
      is_active: %{
        module: Backpex.Fields.Boolean,
        label: "当前有效",
        only: [:index, :show]
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"启用", "enabled"},
          {"禁用", "disabled"}
        ]
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        only: [:show]
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        only: [:show]
      }
    }
  end

  @doc """
  渲染奖金池字段
  """
  def render_jackpot_pool(assigns) do
    pool_text = case assigns.item do
      %{jackpot_pool: pool} when not is_nil(pool) ->
        formatted_amount = pool
        |> Decimal.to_string()
        |> format_currency()
        "#{formatted_amount}分"
      _ -> "0分"
    end

    assigns = assign(assigns, :pool_text, pool_text)

    ~H"""
    <div class={@live_action in [:index, :resource_action] && "truncate"}>
      <span class="text-sm text-yellow-600 font-medium"><%= @pool_text %></span>
    </div>
    """
  end

  @doc """
  渲染活动期间字段
  """
  def render_activity_period(assigns) do
    period_text = case assigns.item do
      %{start_date: start_date, end_date: end_date} ->
        case {start_date, end_date} do
          {nil, nil} -> "长期有效"
          {start_date, nil} -> "#{start_date}起长期有效"
          {nil, end_date} -> "至#{end_date}"
          {start_date, end_date} -> "#{start_date}至#{end_date}"
        end
      _ -> "未设置"
    end

    assigns = assign(assigns, :period_text, period_text)

    ~H"""
    <div class={@live_action in [:index, :resource_action] && "truncate"}>
      <span class="text-sm text-blue-600"><%= @period_text %></span>
    </div>
    """
  end

  # 格式化货币显示
  defp format_currency(amount_string) do
    case Integer.parse(amount_string) do
      {amount, ""} when amount >= 1000 ->
        # 大于等于1000时显示千分位
        amount
        |> Integer.to_string()
        |> String.reverse()
        |> String.replace(~r/(\d{3})(?=\d)/, "\\1,")
        |> String.reverse()
      _ ->
        amount_string
    end
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      _ -> false
    end
  end
end
