defmodule Teen.Live.ActivitySystem.RechargeTaskLive do
  @moduledoc """
  充值任务管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.RechargeTask
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "充值任务"

  @impl Backpex.LiveResource
  def plural_name, do: "充值任务"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "编号",
        readonly: true,
        only: [:index, :show]
      },
      title: %{
        module: Backpex.Fields.Text,
        label: "任务标题"
      },
      task_type: %{
        module: Backpex.Fields.Select,
        label: "任务类型",
        options: [
          {"单次充值", :single_recharge},
          {"累计充值", :cumulative_recharge},
          {"连续充值", :consecutive_recharge},
          {"首次充值", :first_recharge},
          {"每日充值", :daily_recharge}
        ],
        default: fn _assigns -> :single_recharge end
      },
      recharge_amount: %{
        module: Backpex.Fields.Number,
        label: "充值金额（分）"
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "奖励金额（分）"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", :coins},
          {"积分", :points},
          {"现金", :cash},
          {"转盘次数", :wheel_spins},
          {"道具", :items}
        ],
        default: fn _assigns -> :coins end
      },
      reward_rate: %{
        module: Backpex.Fields.Text,
        label: "奖励比例",
        only: [:index, :show],
        render: &__MODULE__.render_reward_rate/1
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "任务描述"
      },
      priority: %{
        module: Backpex.Fields.Number,
        label: "优先级"
      },
      max_claims: %{
        module: Backpex.Fields.Number,
        label: "最大领取次数"
      },
      start_date: %{
        module: Backpex.Fields.Date,
        label: "开始日期"
      },
      end_date: %{
        module: Backpex.Fields.Date,
        label: "结束日期"
      },
      activity_period: %{
        module: Backpex.Fields.Text,
        label: "活动期间",
        only: [:index, :show],
        render: &__MODULE__.render_activity_period/1
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"启用", :enabled},
          {"禁用", :disabled}
        ],
        default: fn _assigns -> :enabled end
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "修改时间",
        readonly: true,
        only: [:index, :show]
      }
    }
  end

  @doc """
  渲染奖励比例字段
  """
  def render_reward_rate(assigns) do
    rate_text = case assigns.item do
      %{reward_amount: reward_amount, recharge_amount: recharge_amount}
      when not is_nil(reward_amount) and not is_nil(recharge_amount) ->
        if Decimal.compare(recharge_amount, Decimal.new("0")) == :gt do
          rate = Decimal.div(reward_amount, recharge_amount)
                 |> Decimal.mult(Decimal.new("100"))
                 |> Decimal.round(2)
                 |> Decimal.to_string()
          "#{rate}%"
        else
          "0%"
        end
      _ -> "0%"
    end

    assigns = assign(assigns, :rate_text, rate_text)

    ~H"""
    <div class={@live_action in [:index, :resource_action] && "truncate"}>
      <span class="text-sm text-green-600 font-medium"><%= @rate_text %></span>
    </div>
    """
  end

  @doc """
  渲染活动期间字段
  """
  def render_activity_period(assigns) do
    period_text = case assigns.item do
      %{start_date: start_date, end_date: end_date} ->
        case {start_date, end_date} do
          {nil, nil} -> "长期有效"
          {start_date, nil} -> "#{start_date}起长期有效"
          {nil, end_date} -> "至#{end_date}"
          {start_date, end_date} -> "#{start_date}至#{end_date}"
        end
      _ -> "未设置"
    end

    assigns = assign(assigns, :period_text, period_text)

    ~H"""
    <div class={@live_action in [:index, :resource_action] && "truncate"}>
      <span class="text-sm text-gray-600"><%= @period_text %></span>
    </div>
    """
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
