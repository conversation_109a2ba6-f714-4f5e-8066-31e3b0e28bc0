defmodule Teen.Live.ActivitySystem.VipGiftLive do
  @moduledoc """
  VIP礼包管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.VipGift
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "VIP礼包"

  @impl Backpex.LiveResource
  def plural_name, do: "VIP礼包"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:index, :show]
      },
      title: %{
        module: Backpex.Fields.Text,
        label: "礼包标题"
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "描述"
      },
      vip_level: %{
        module: Backpex.Fields.Select,
        label: "VIP等级",
        options: fn _assigns ->
          # 动态获取所有启用的VIP等级
          case Teen.GameManagement.VipLevel.list_active_levels() do
            {:ok, vip_levels} ->
              # 将VIP等级转换为选项格式，按等级排序
              vip_levels
              |> Enum.sort_by(& &1.level)
              |> Enum.map(fn vip_level ->
                {"VIP#{vip_level.level} - #{vip_level.level_name}", vip_level.level}
              end)

            {:error, _} ->
              # 如果获取失败，返回默认选项
              [
                {"VIP0 - 普通用户", 0},
                {"VIP1 - 青铜VIP", 1},
                {"VIP2 - 白银VIP", 2},
                {"VIP3 - 黄金VIP", 3},
                {"VIP4 - 铂金VIP", 4},
                {"VIP5 - 钻石VIP", 5},
                {"VIP6 - 星耀VIP", 6},
                {"VIP7 - 王者VIP", 7},
                {"VIP8 - 荣耀VIP", 8},
                {"VIP9 - 传奇VIP", 9}
              ]
          end
        end,
        prompt: "选择VIP等级...",
        default: fn _assigns -> 1 end
      },
      task_type: %{
        module: Backpex.Fields.Select,
        label: "礼包类型",
        options: [
          {"每日登录礼包", :vip_daily_login},
          {"每周登录礼包", :vip_weekly_login},
          {"每月登录礼包", :vip_monthly_login},
          {"等级升级礼包", :vip_level_upgrade},
          {"生日专属礼包", :vip_birthday},
          {"周年庆典礼包", :vip_anniversary},
          {"充值返利礼包", :vip_recharge_bonus},
          {"特殊活动礼包", :vip_special_event}
        ],
        default: fn _assigns -> :vip_daily_login end
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "奖励金额（分）"
      },
      reward_frequency: %{
        module: Backpex.Fields.Number,
        label: "奖励频率（天数）",
        help_text: "必须为正数，空值表示一次性奖励"
      },
      max_claims: %{
        module: Backpex.Fields.Number,
        label: "领取限制次数",
        help_text: "必须为正数，空值表示无限制"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", :coins},
          {"积分", :points},
          {"现金", :cash},
          {"转盘次数", :wheel_spins},
          {"道具", :items}
        ],
        default: fn _assigns -> :coins end
      },
      start_date: %{
        module: Backpex.Fields.Date,
        label: "开始日期"
      },
      end_date: %{
        module: Backpex.Fields.Date,
        label: "结束日期"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"启用", :enabled},
          {"禁用", :disabled}
        ],
        default: fn _assigns -> :enabled end
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true,
        only: [:index, :show]
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true,
        only: [:index, :show]
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
