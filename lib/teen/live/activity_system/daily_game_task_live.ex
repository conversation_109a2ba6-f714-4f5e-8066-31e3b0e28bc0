defmodule Teen.Live.ActivitySystem.DailyGameTaskLive do
  @moduledoc """
  每日游戏任务管理页面

  提供游戏任务的CRUD操作界面，支持：
  - 创建和编辑游戏任务
  - 设置任务类型（游戏局数、游戏胜利）
  - 配置奖励金额和领取次数
  - 管理任务状态（启用/禁用）
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.GameTask
    ],
    layout: {Teen.Layouts, :admin}

  alias Teen.GameManagement
  alias Cypridina.RoomSystem.GameFactory

  use Phoenix.Component
  require Logger

  @impl Backpex.LiveResource
  def singular_name, do: "每日游戏任务"

  @impl Backpex.LiveResource
  def plural_name, do: "每日游戏任务"

  @impl Backpex.LiveResource
  def fields do
    [
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:index, :show]
      },
      task_name: %{
        module: Backpex.Fields.Text,
        label: "任务名称"
      },
      game_name: %{
        module: Backpex.Fields.Select,
        label: "游戏名称",
        options: &__MODULE__.get_game_options/1
      },
      game_id: %{
        module: Backpex.Fields.Text,
        label: "游戏ID",
        readonly: true,
        render: &__MODULE__.render_game_id/1,
        only: [:index, :show]
      },
      task_type: %{
        module: Backpex.Fields.Select,
        label: "任务类型",
        options: [
          {"游戏局数", "game_rounds"},
          {"胜利局数", "win_rounds"},
          {"充值金额", "recharge_amount"},
          {"游戏胜利", "game_wins"},
          {"完成任务", "task_completion"}
        ]
      },
      required_count: %{
        module: Backpex.Fields.Number,
        label: "所需局数",
        default: fn _assigns -> 1 end
      },
      max_claims: %{
        module: Backpex.Fields.Number,
        label: "每日最大领取次数",
        default: fn _assigns -> 1 end
      },
      reward_amount: %{
        module: Backpex.Fields.Text,
        label: "奖励金额（分）",
        placeholder: "请输入奖励金额，单位为分"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", "coins"},
          {"积分", "points"},
          {"现金", "cash"},
          {"转盘次数", "wheel_spins"},
          {"道具", "items"}

        ]
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"启用", :enabled},
          {"禁用", :disabled}
        ],
        default: fn _assigns -> :enabled end
      },
      start_date: %{
        module: Backpex.Fields.Date,
        label: "开始日期",
        except: [:index]
      },
      end_date: %{
        module: Backpex.Fields.Date,
        label: "结束日期",
        except: [:index]
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true,
        only: [:index, :show]
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true,
        only: [:index, :show]
      }
    ]
  end

  @impl Backpex.LiveResource
  def can?(_assigns, _action, _item) do
    # 允许所有操作，可以根据需要添加权限控制逻辑
    true
  end

  @impl Backpex.LiveResource
  def handle_assign_form(socket, item, params) do
    # 确保 game_id 与 game_name 保持一致
    updated_params =
      case Map.get(params, "game_name") do
        nil -> params
        game_name -> Map.put(params, "game_id", game_name)
      end

    {:ok, socket, item, updated_params}
  end

  @doc """
  获取游戏选项列表
  结合数据库中的游戏配置和游戏工厂中注册的游戏
  """
  def get_game_options(_item) do
    # 从数据库获取游戏配置
    db_games =
      case GameManagement.list_game_configs() do
        {:ok, games} when is_list(games) ->
          Enum.map(games, fn game ->
            display_name = Map.get(game, :display_name, "未知游戏")
            game_id = Map.get(game, :game_id, "unknown")
            {"#{display_name}（#{game_id}）", game_id}
          end)

        {:error, reason} ->
          require Logger
          Logger.warning("Failed to load game configs: #{inspect(reason)}")
          []

        _ ->
          []
      end

    # 从游戏工厂获取注册的游戏
    factory_games =
      try do
        case GameFactory.list_games() do
          games when is_list(games) ->
            Enum.map(games, fn game ->
              game_name = Map.get(game, :game_name, "未知游戏")
              game_type = Map.get(game, :game_type, :unknown)
              {game_name, Atom.to_string(game_type)}
            end)

          _ ->
            []
        end
      rescue
        error ->
          require Logger
          Logger.warning("Failed to load factory games: #{inspect(error)}")
          []
      end

    # 合并其他选项并去重排序
    other_games = (db_games ++ factory_games)
    |> Enum.uniq_by(fn {_name, value} -> value end)
    |> Enum.sort_by(fn {name, _value} -> name end)

    # 将"全部游戏"选项放在第一位
    [{"全部游戏", "all"}] ++ other_games
  end

  @doc """
  渲染游戏ID字段，显示实际的游戏ID值
  符合 Backpex 字段渲染函数的要求，接受 assigns 参数并返回 HEEX 模板
  """
  def render_game_id(assigns) do
    # 从 assigns.item 获取游戏ID，优先使用 game_id，如果没有则使用 game_name
    game_id =
      case assigns.item do
        %{game_id: id} when is_binary(id) and id != "" -> id
        %{game_name: name} when is_binary(name) and name != "" -> name
        _ -> "未设置"
      end

    # 将 game_id 赋值给 assigns，以便在模板中使用
    assigns = assign(assigns, :display_game_id, game_id)

    ~H"""
    <div class={@live_action in [:index, :resource_action] && "truncate"}>
      <span class="text-sm text-gray-700 font-mono"><%= @display_game_id %></span>
    </div>
    """
  end

  # ==================== 私有辅助函数 ====================

  @doc """
  验证游戏选项是否有效
  """
  defp validate_game_option({name, value}) when is_binary(name) and is_binary(value) do
    String.length(name) > 0 and String.length(value) > 0
  end

  defp validate_game_option(_), do: false

  @doc """
  格式化游戏显示名称
  """
  defp format_game_display_name(game) do
    case game do
      %{display_name: name, game_id: id} when is_binary(name) and is_binary(id) ->
        "#{name}（#{id}）"

      %{game_name: name} when is_binary(name) ->
        name

      _ ->
        "未知游戏"
    end
  end

  @doc """
  安全获取游戏属性
  """
  defp safe_get_game_attr(game, attr, default \\ "unknown") do
    case Map.get(game, attr) do
      nil -> default
      "" -> default
      value -> value
    end
  end
end
