defmodule Teen.Live.ActivitySystem.SevenDayLoginLive do
  @moduledoc """
  七天登录任务管理页面

  连续登录七天，可循环，如果中断了则重新从第一天开始领取
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.SevenDayTask
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "七天登录任务"

  @impl Backpex.LiveResource
  def plural_name, do: "七天登录任务"

  @impl Backpex.LiveResource
  def fields do
    [
      id: %{
        module: Backpex.Fields.Text,
        label: "编号",
        readonly: true,
        only: [:index, :show]
      },
      title: %{
        module: Backpex.Fields.Text,
        label: "任务标题"
      },
      task_type: %{
        module: Backpex.Fields.Select,
        label: "任务类型",
        options: [
          {"每日登录", :daily_login},
          {"连续登录", :consecutive_login},
          {"每周登录", :weekly_login},
          {"每月登录", :monthly_login}
        ],
        default: fn _assigns -> :daily_login end
      },
      day_number: %{
        module: Backpex.Fields.Number,
        label: "领取次数（第几次/天领取）"
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "奖励金额（分）"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", :coins},
          {"积分", :points},
          {"现金", :cash},
          {"转盘次数", :wheel_spins},
          {"道具", :items}
        ],
        default: fn _assigns -> :coins end
      },
      is_cyclic: %{
        module: Backpex.Fields.Select,
        label: "是否循环",
        options: [
          {"是", true},
          {"否", false}
        ],
        default: fn _assigns -> true end
      },
      is_special: %{
        module: Backpex.Fields.Boolean,
        label: "是否特殊奖励"
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "描述"
      },
      start_date: %{
        module: Backpex.Fields.Date,
        label: "开始日期"
      },
      end_date: %{
        module: Backpex.Fields.Date,
        label: "结束日期"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"启用", :enabled},
          {"禁用", :disabled}
        ],
        default: fn _assigns -> :enabled end
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "修改时间",
        readonly: true,
        only: [:index, :show]
      }
    ]
  end

  @impl Backpex.LiveResource
  def filters do
    []
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
