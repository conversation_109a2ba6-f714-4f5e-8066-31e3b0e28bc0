defmodule Teen.PromotionSystem.ShareConfig do
  @moduledoc """
  分享配置资源

  管理分享功能的配置参数，包括奖励规则、分享模板、平台设置等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    domain: Teen.PromotionSystem,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :config_key,
      :config_name,
      :config_type,
      :status,
      :updated_at,
      :inserted_at
    ]
  end

  postgres do
    table "share_configs"
    repo Cypridina.Repo
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [
        :config_key,
        :config_name,
        :config_type,
        :config_value,
        :description,
        :status,
        :sort_order
      ]
    end

    update :update do
      accept [:config_name, :config_value, :description, :status, :sort_order]
    end

    update :activate do
      accept []

      change fn changeset, _context ->
        Ash.Changeset.change_attribute(changeset, :status, 1)
      end
    end

    update :deactivate do
      accept []

      change fn changeset, _context ->
        Ash.Changeset.change_attribute(changeset, :status, 0)
      end
    end

    update :update_value do
      accept [:config_value]
    end
  end

  validations do
    validate match(:config_type, ~r/^(reward|template|platform|general)$/) do
      message "配置类型必须是：reward, template, platform, general 之一"
    end

    validate present(:config_key) do
      message "配置键名不能为空"
    end

    validate present(:config_name) do
      message "配置名称不能为空"
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :config_key, :string do
      allow_nil? false
      public? true
      description "配置键名"
      constraints max_length: 100
    end

    attribute :config_name, :string do
      allow_nil? false
      public? true
      description "配置名称"
      constraints max_length: 200
    end

    attribute :config_type, :string do
      allow_nil? false
      public? true
      description "配置类型：reward=奖励规则，template=分享模板，platform=平台设置，general=通用设置"
      constraints max_length: 20
    end

    attribute :config_value, :map do
      allow_nil? false
      public? true
      description "配置值（JSON格式）"
      default %{}
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "配置描述"
      constraints max_length: 500
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0=禁用，1=启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :sort_order, :integer do
      allow_nil? false
      public? true
      description "排序顺序"
      default 0
      constraints min: 0
    end

    timestamps()
  end

  calculations do
    calculate :is_active, :boolean, expr(status == 1)

    calculate :config_type_name, :string do
      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.config_type do
            "reward" -> "奖励规则"
            "template" -> "分享模板"
            "platform" -> "平台设置"
            "general" -> "通用设置"
            _ -> "其他配置"
          end
        end)
      end
    end
  end

  identities do
    identity :unique_config_key, [:config_key]
  end

  # 配置管理功能
  def get_config_by_key(config_key) do
    # 根据键名获取配置
    {:ok, "获取配置功能待实现"}
  end

  def get_configs_by_type(config_type) do
    # 根据类型获取配置列表
    {:ok, "获取配置列表功能待实现"}
  end

  def get_active_configs do
    # 获取所有启用的配置
    {:ok, "获取启用配置功能待实现"}
  end

  # 预定义配置模板
  def default_configs do
    [
      # 奖励规则配置
      %{
        config_key: "share_reward_rules",
        config_name: "分享奖励规则",
        config_type: "reward",
        config_value: %{
          # 分享奖励
          "share_reward" => 1.0,
          # 查看奖励
          "view_reward" => 0.1,
          # 点击奖励
          "click_reward" => 0.5,
          # 转化奖励
          "conversion_reward" => 10.0,
          # 每日奖励上限
          "daily_limit" => 100,
          # 最小提现金额
          "min_payout" => 10.0
        },
        description: "分享功能的奖励规则配置"
      },

      # 分享模板配置
      %{
        config_key: "share_templates",
        config_name: "分享模板",
        config_type: "template",
        config_value: %{
          "game_template" => %{
            "title" => "快来一起玩游戏吧！",
            "content" => "发现了一个超好玩的游戏，一起来体验吧！",
            "image" => "/images/game_share.jpg"
          },
          "promotion_template" => %{
            "title" => "限时优惠活动",
            "content" => "错过就没有了，赶紧来参与吧！",
            "image" => "/images/promotion_share.jpg"
          }
        },
        description: "各种分享场景的模板配置"
      },

      # 平台设置配置
      %{
        config_key: "platform_settings",
        config_name: "平台设置",
        config_type: "platform",
        config_value: %{
          "wechat" => %{
            "enabled" => true,
            "app_id" => "",
            "app_secret" => ""
          },
          "qq" => %{
            "enabled" => true,
            "app_id" => "",
            "app_key" => ""
          },
          "weibo" => %{
            "enabled" => false,
            "app_key" => "",
            "app_secret" => ""
          }
        },
        description: "各个分享平台的配置参数"
      },

      # 通用设置配置
      %{
        config_key: "general_settings",
        config_name: "通用设置",
        config_type: "general",
        config_value: %{
          "share_enabled" => true,
          "auto_settlement" => true,
          # 每月结算日
          "settlement_day" => 1,
          # 每日最大分享次数
          "max_shares_per_day" => 50,
          # 分享冷却时间（秒）
          "share_cooldown" => 300
        },
        description: "分享功能的通用设置"
      }
    ]
  end
end
