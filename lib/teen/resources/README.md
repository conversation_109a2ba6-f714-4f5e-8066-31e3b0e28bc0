# Teen 后台功能模块 Ash 资源定义

本文档描述了基于思维导图定义的后台功能模块对应的 Ash 资源结构。

## 🎯 项目完成状态

✅ **已完成**：
1. **资源定义完成** - 所有核心资源已定义
2. **业务逻辑集成** - 业务逻辑已集成到对应的domain文件中
3. **代码结构优化** - 便于调用和查找

⏳ **待完成**：
1. **数据库迁移生成** - 需要解决构建锁定问题后执行 `mix ash.codegen teen_backend_resources`

## 已定义的域（Domains）

### 1. Teen.CustomerService - 客服管理域
**路径**: `lib/teen/customer_service.ex`

包含以下资源：
- `CustomerChat` - 客服聊天记录
- `UserQuestion` - 用户问题
- `ExchangeOrder` - 兑换订单 ✅
- `PaymentOrder` - 支付订单
- `SensitiveWord` - 敏感字管理 ✅
- `UserTag` - 用户标签
- `ResetLog` - 重置日志
- `VerificationCode` - 验证码查询
- `ComplaintRecord` - 投诉记录
- `AvatarAudit` - 头像审核
- `GameComplaint` - 牌局投诉
- `CustomerServiceConfig` - 客服配置
- `CommonQuestion` - 常见问题

**已实现资源**:
- ✅ `CustomerChat` - 客服聊天记录
- ✅ `UserQuestion` - 用户问题管理
- ✅ `ExchangeOrder` - 兑换订单管理
- ✅ `SensitiveWord` - 敏感字管理
- ✅ `UserTag` - 用户标签管理
- ✅ `VerificationCode` - 验证码管理

**已实现业务逻辑**:
- ✅ `process_customer_chat/1` - 处理客服聊天（含敏感词过滤）
- ✅ `filter_sensitive_words/1` - 敏感词过滤
- ✅ `batch_reply_messages/2` - 批量回复消息
- ✅ `audit_exchange_order/4` - 审核兑换订单
- ✅ `batch_audit_orders/4` - 批量审核订单
- ✅ `calculate_exchange_fees/2` - 计算兑换费用
- ✅ `send_verification_code/2` - 发送验证码
- ✅ `verify_code/2` - 验证验证码

### 2. Teen.PaymentSystem - 支付系统域
**路径**: `lib/teen/payment_system.ex`

包含以下资源：
- `PaymentConfig` - 支付配置 ✅
- `ExchangeConfig` - 兑换配置
- `BankInfo` - 银行信息
- `PaymentChannel` - 支付渠道
- `PaymentGateway` - 支付网关
- `PaymentOrder` - 支付订单
- `PaymentLog` - 支付日志
- `RegionConfig` - 区域配置
- `AlipayBalance` - 支付宝余额查询

**已实现资源**:
- ✅ `PaymentConfig` - 支付配置管理
- ✅ `ExchangeConfig` - 兑换配置管理
- ✅ `PaymentGateway` - 支付网关管理

**已实现业务逻辑**:
- ✅ `get_available_payment_methods/2` - 获取可用支付方式
- ✅ `select_optimal_gateway/2` - 选择最优支付网关
- ✅ `calculate_payment_fees/2` - 计算支付费用
- ✅ `get_exchange_config/2` - 获取兑换配置
- ✅ `validate_exchange_eligibility/3` - 验证兑换资格
- ✅ `calculate_exchange_amount/2` - 计算兑换金额
- ✅ `update_gateway_status/2` - 更新网关状态
- ✅ `test_gateway_connection/1` - 测试网关连接
- ✅ `batch_update_config_status/2` - 批量更新配置状态

### 3. Teen.BanSystem - 封号管理域
**路径**: `lib/teen/ban_system.ex`

包含以下资源：
- `UserBan` - 用户封号记录 ✅
- `DeviceBan` - 设备封锁
- `IpBan` - IP封锁
- `BanRecord` - 封号记录
- `MuteRecord` - 禁言记录
- `KickRecord` - 踢人记录

**已实现资源**:
- ✅ `UserBan` - 用户封号管理

### 4. Teen.BlacklistSystem - 黑名单管理域
**路径**: `lib/teen/blacklist_system.ex`

包含以下资源：
- `PaymentBlacklist` - 支付黑名单 ✅
- `ExchangeBlacklist` - 兑换黑名单
- `BankCardBlacklist` - 银行卡黑名单
- `CheatBlacklist` - 作弊黑名单

**已实现资源**:
- ✅ `PaymentBlacklist` - 支付黑名单管理

### 5. Teen.AlertSystem - 报警系统域
**路径**: `lib/teen/alert_system.ex`

包含以下资源：
- `AlertRecord` - 报警记录 ✅
- `UserAlert` - 用户关注报警
- `AlertType` - 报警类型

**已实现资源**:
- ✅ `AlertRecord` - 报警记录管理

### 6. Teen.GameManagement - 游戏管理域
**路径**: `lib/teen/game_management.ex`

包含以下资源：
- `Platform` - 平台配置 ✅
- `GameConfig` - 游戏配置
- `ServerConfig` - 服务器配置
- `RobotConfig` - 机器人配置
- `ChannelPackage` - 渠道包
- `VipLevel` - VIP等级
- `SystemAccess` - 系统访问配置
- `VersionManagement` - 版本管理
- `RegionConfig` - 区域配置
- `RankingReward` - 排行榜奖励

**已实现资源**:
- ✅ `Platform` - 平台配置管理

### 7. Teen.ActivitySystem - 活动管理域
**路径**: `lib/teen/activity_system.ex`

包含以下资源：
- `SignInActivity` - 签到活动
- `CdkeyActivity` - CDKEY活动
- `LimitedGift` - 限时礼包
- `FreeBonus` - Free Bonus
- `FreeCash` - Free Cash
- `BankruptcyAssist` - 破产补助
- `TaskLevel` - 任务等级
- `LevelReward` - 等级奖励
- `RewardMultiplier` - 奖励倍率

### 8. Teen.Statistics - 统计分析域
**路径**: `lib/teen/statistics.ex`

包含以下资源：
- `SystemReport` - 系统报表
- `OnlineStats` - 在线统计
- `ChannelStats` - 渠道统计
- `UserStats` - 用户统计
- `CoinStats` - 金币统计
- `RetentionStats` - 留存率统计
- `PaymentStats` - 支付统计
- `LtvStats` - LTV统计
- `RobotStats` - 机器人统计

### 9. Teen.PromotionSystem - 推广系统域
**路径**: `lib/teen/promotion_system.ex`

包含以下资源：
- `Promoter` - 推广员
- `PromotionChannel` - 推广渠道
- `PromotionSettlement` - 推广结算
- `ShareManagement` - 分享管理
- `ShareSettlement` - 分享结算
- `ShareConfig` - 分成配置

### 10. Teen.SystemSettings - 系统设置域
**路径**: `lib/teen/system_settings.ex`

包含以下资源：
- `AdminUser` - 管理员用户
- `Role` - 角色管理
- `Permission` - 权限管理
- `OperationLog` - 操作日志
- `IpWhitelist` - IP白名单

## 资源特性

### 通用功能
所有资源都包含以下通用功能：
- 基本的 CRUD 操作（创建、读取、更新、删除）
- 管理后台集成（AshAdmin）
- 代码接口定义
- 数据验证和约束
- 时间戳（created_at, updated_at）

### 特殊功能
- **状态管理**: 大部分资源都有状态字段，支持启用/禁用操作
- **审核流程**: 兑换订单等资源支持审核状态管理
- **批量操作**: 支持批量处理和批量状态更新
- **关联关系**: 与用户系统（Cypridina.Accounts.User）的关联
- **搜索过滤**: 支持多种条件的搜索和过滤

## 下一步工作

1. **完善资源定义**: 为每个域补充完整的资源定义
2. **数据库迁移**: 使用 `mix ash.codegen` 生成数据库迁移文件
3. **业务逻辑**: 实现具体的业务逻辑和计算
4. **API接口**: 创建对应的API接口
5. **前端界面**: 开发管理后台界面

## 使用说明

### 生成数据库迁移
```bash
mix ash.codegen teen_backend_resources
```

### 运行迁移
```bash
mix ecto.migrate
```

### 访问管理后台
启动服务后访问: `http://localhost:4000/admin`

## 业务逻辑使用示例

### 客服管理
```elixir
# 处理客服聊天（自动过滤敏感词）
{:ok, chat} = Teen.CustomerService.process_customer_chat(%{
  "user_id" => user_id,
  "platform" => "web",
  "question" => "我想咨询充值问题"
})

# 批量回复消息
result = Teen.CustomerService.batch_reply_messages(
  [chat_id1, chat_id2],
  "感谢您的咨询，我们会尽快处理"
)

# 审核兑换订单
{:ok, order} = Teen.CustomerService.audit_exchange_order(
  order_id,
  auditor_id,
  :approve
)

# 计算兑换费用（VIP用户享受优惠）
fees = Teen.CustomerService.calculate_exchange_fees(
  Decimal.new("10000"),
  user_vip_level
)
```

### 支付系统
```elixir
# 获取用户可用的支付方式
{:ok, methods} = Teen.PaymentSystem.get_available_payment_methods(
  user_id,
  Decimal.new("5000")
)

# 选择最优支付网关
{:ok, gateway} = Teen.PaymentSystem.select_optimal_gateway(
  "alipay",
  Decimal.new("5000")
)

# 计算支付费用
fees = Teen.PaymentSystem.calculate_payment_fees(
  Decimal.new("5000"),
  payment_config
)

# 测试网关连接
{:ok, test_result} = Teen.PaymentSystem.test_gateway_connection(gateway_id)
```

### 游戏管理
```elixir
# 获取用户VIP信息
{:ok, vip_info} = Teen.GameManagement.get_user_vip_info(user_id)

# 计算VIP每日奖励
{:ok, bonus} = Teen.GameManagement.calculate_vip_daily_bonus(user_id)

# 选择合适的机器人
{:ok, robot} = Teen.GameManagement.select_robot_for_user(
  "poker",
  user_skill_level
)

# 获取平台配置
{:ok, config} = Teen.GameManagement.get_platform_config("platform_001")
```

### 活动系统
```elixir
# 处理用户签到
{:ok, result} = Teen.ActivitySystem.process_user_sign_in(user_id)

# 获取签到状态
{:ok, status} = Teen.ActivitySystem.get_user_sign_in_status(user_id)

# 使用CDKEY
{:ok, result} = Teen.ActivitySystem.use_cdkey(user_id, "ABCD1234EFGH")

# 批量生成CDKEY
{:ok, batch} = Teen.ActivitySystem.generate_cdkey_batch(
  "新年活动",
  1000,
  %{"coins" => "1000", "items" => ["道具1", "道具2"]}
)
```

### 封号管理
```elixir
# 封禁用户
{:ok, ban} = Teen.BanSystem.ban_user(
  user_id,
  1, # 账号封禁
  "违规操作",
  operator_id,
  24 # 24小时
)

# 检查用户是否被封禁
{:ok, is_banned, ban_info} = Teen.BanSystem.is_user_banned?(user_id)

# 解封用户
{:ok, updated_ban} = Teen.BanSystem.unban_user(
  ban_id,
  operator_id,
  "申诉成功"
)
```

### 统计分析
```elixir
# 生成日报
{:ok, report} = Teen.Statistics.generate_daily_report()

# 获取最新报表
{:ok, latest} = Teen.Statistics.get_latest_report("daily")

# 计算统计摘要
{:ok, summary} = Teen.Statistics.calculate_statistics_summary()
```

## 注意事项

1. **用户管理模块已忽略**: 根据要求，用户管理模块暂时忽略
2. **代理系统模块已忽略**: 根据要求，代理系统模块暂时忽略
3. **资源命名**: 所有资源都使用 `Teen.` 前缀，放置在 `lib/teen/` 目录下
4. **数据库表名**: 使用下划线命名法，如 `customer_chats`、`payment_configs` 等
5. **关联关系**: 主要与现有的 `Cypridina.Accounts.User` 建立关联
