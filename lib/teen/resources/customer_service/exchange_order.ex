defmodule Teen.CustomerService.ExchangeOrder do
  @moduledoc """
  兑换订单资源

  管理用户的兑换订单，包括审核状态、处理进度、支付信息等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.CustomerService,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :order_id,
      :user_id,
      :exchange_type,
      :exchange_amount,
      :audit_status,
      :progress_status,
      :inserted_at
    ]
  end

  postgres do
    table "exchange_orders"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_audit_status
    define :list_by_progress_status
    define :list_by_exchange_type
    define :list_by_user
    define :list_pending_orders
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_by_audit_status do
      argument :audit_status, :integer, allow_nil?: false
      filter expr(audit_status == ^arg(:audit_status))
    end

    read :list_by_progress_status do
      argument :progress_status, :integer, allow_nil?: false
      filter expr(progress_status == ^arg(:progress_status))
    end

    read :list_by_exchange_type do
      argument :exchange_type, :integer, allow_nil?: false
      filter expr(exchange_type == ^arg(:exchange_type))
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end

    read :list_pending_orders do
      filter expr(audit_status == 0 or progress_status == 0)
    end

    update :approve_order do
      argument :auditor_id, :uuid, allow_nil?: false
      change set_attribute(:audit_status, 1)
      change set_attribute(:auditor_id, arg(:auditor_id))
      change set_attribute(:audit_time, &DateTime.utc_now/0)
    end

    update :reject_order do
      argument :auditor_id, :uuid, allow_nil?: false
      argument :feedback, :string, allow_nil?: false
      change set_attribute(:audit_status, 2)
      change set_attribute(:auditor_id, arg(:auditor_id))
      change set_attribute(:audit_time, &DateTime.utc_now/0)
      change set_attribute(:feedback, arg(:feedback))
    end

    update :update_progress do
      argument :progress_status, :integer, allow_nil?: false
      change set_attribute(:progress_status, arg(:progress_status))
      change set_attribute(:process_time, &DateTime.utc_now/0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :order_id, :string do
      allow_nil? false
      public? true
      description "订单编号"
      constraints max_length: 50
    end

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :platform, :string do
      allow_nil? false
      public? true
      description "平台名称"
      constraints max_length: 50
    end

    attribute :exchange_type, :integer do
      allow_nil? false
      public? true
      description "兑换类型：0-游戏，1-推广"
      constraints min: 0, max: 1
    end

    attribute :exchange_amount, :decimal do
      allow_nil? false
      public? true
      description "兑换金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :current_coins, :decimal do
      allow_nil? false
      public? true
      description "当前金币"
      constraints min: Decimal.new("0")
    end

    attribute :tax_amount, :decimal do
      allow_nil? false
      public? true
      description "税收金额"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :total_recharge, :decimal do
      allow_nil? false
      public? true
      description "总充值金额"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :total_exchange, :decimal do
      allow_nil? false
      public? true
      description "总兑换金额"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :bank_info, :string do
      allow_nil? true
      public? true
      description "银行信息"
      constraints max_length: 500
    end

    attribute :alipay_info, :string do
      allow_nil? true
      public? true
      description "支付宝信息"
      constraints max_length: 200
    end

    attribute :audit_status, :integer do
      allow_nil? false
      public? true
      description "审核状态：0-未处理，1-审核通过，2-审核不通过"
      default 0
      constraints min: 0, max: 2
    end

    attribute :progress_status, :integer do
      allow_nil? false
      public? true
      description "进度状态：0-排队中，1-处理中，2-转账成功，3-转账失败，4-人工处理，5-锁定"
      default 0
      constraints min: 0, max: 5
    end

    attribute :result_status, :integer do
      allow_nil? false
      public? true
      description "处理结果：0-处理中，1-成功，2-失败"
      default 0
      constraints min: 0, max: 2
    end

    attribute :feedback, :string do
      allow_nil? true
      public? true
      description "反馈说明"
      constraints max_length: 1000
    end

    attribute :ip_address, :string do
      allow_nil? true
      public? true
      description "用户IP地址"
      constraints max_length: 45
    end

    attribute :auditor_id, :uuid do
      allow_nil? true
      public? true
      description "审核人ID"
    end

    attribute :audit_time, :utc_datetime do
      allow_nil? true
      public? true
      description "审核时间"
    end

    attribute :process_time, :utc_datetime do
      allow_nil? true
      public? true
      description "处理时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end

    belongs_to :auditor, Cypridina.Accounts.User do
      public? true
      source_attribute :auditor_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_order_id, [:order_id]
  end
end
