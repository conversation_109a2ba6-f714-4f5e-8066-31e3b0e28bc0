defmodule Teen.CustomerService.SensitiveWord do
  @moduledoc """
  敏感字管理资源

  管理系统中的敏感词汇，用于内容过滤和审核
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.CustomerService,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :keyword, :status, :inserted_at]
  end

  postgres do
    table "sensitive_words"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_words
    define :search_by_keyword
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_words do
      filter expr(status == 1)
    end

    read :search_by_keyword do
      argument :keyword, :string, allow_nil?: false
      filter expr(contains(keyword, ^arg(:keyword)))
    end

    update :activate do
      change set_attribute(:status, 1)
    end

    update :deactivate do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :keyword, :string do
      allow_nil? false
      public? true
      description "敏感词关键字"
      constraints max_length: 100
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :category, :string do
      allow_nil? true
      public? true
      description "分类"
      constraints max_length: 50
    end

    attribute :severity, :integer do
      allow_nil? false
      public? true
      description "严重程度：1-轻微，2-中等，3-严重"
      default 1
      constraints min: 1, max: 3
    end

    attribute :action_type, :integer do
      allow_nil? false
      public? true
      description "处理方式：1-替换，2-屏蔽，3-警告"
      default 2
      constraints min: 1, max: 3
    end

    attribute :replacement, :string do
      allow_nil? true
      public? true
      description "替换词"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "描述说明"
      constraints max_length: 500
    end

    timestamps()
  end

  identities do
    identity :unique_keyword, [:keyword]
  end
end
