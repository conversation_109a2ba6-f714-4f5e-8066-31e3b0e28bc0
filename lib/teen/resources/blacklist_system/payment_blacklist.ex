defmodule Teen.BlacklistSystem.PaymentBlacklist do
  @moduledoc """
  支付黑名单资源

  管理被禁止支付的用户列表
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.BlacklistSystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :user_id, :reason, :operator_id, :inserted_at]
  end

  postgres do
    table "payment_blacklists"
    repo Cypridina.Repo

    identity_wheres_to_sql unique_active_user_blacklist: "status = 1"
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_user
    define :add_to_blacklist
    define :remove_from_blacklist
    define :is_blacklisted
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end

    read :is_blacklisted do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id) and status == 1)
    end

    create :add_to_blacklist do
      argument :user_id, :uuid, allow_nil?: false
      argument :reason, :string, allow_nil?: false
      argument :operator_id, :uuid, allow_nil?: false

      change set_attribute(:user_id, arg(:user_id))
      change set_attribute(:reason, arg(:reason))
      change set_attribute(:operator_id, arg(:operator_id))
      change set_attribute(:status, 1)
    end

    update :remove_from_blacklist do
      argument :operator_id, :uuid, allow_nil?: false
      argument :remove_reason, :string, allow_nil?: true

      change set_attribute(:status, 0)
      change set_attribute(:removed_at, &DateTime.utc_now/0)
      change set_attribute(:remove_operator_id, arg(:operator_id))
      change set_attribute(:remove_reason, arg(:remove_reason))
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :reason, :string do
      allow_nil? false
      public? true
      description "加入黑名单原因"
      constraints max_length: 1000
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-已移除，1-生效中"
      default 1
      constraints min: 0, max: 1
    end

    attribute :operator_id, :uuid do
      allow_nil? false
      public? true
      description "操作员ID"
    end

    attribute :removed_at, :utc_datetime do
      allow_nil? true
      public? true
      description "移除时间"
    end

    attribute :remove_operator_id, :uuid do
      allow_nil? true
      public? true
      description "移除操作员ID"
    end

    attribute :remove_reason, :string do
      allow_nil? true
      public? true
      description "移除原因"
      constraints max_length: 1000
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end

    belongs_to :operator, Cypridina.Accounts.User do
      public? true
      source_attribute :operator_id
      destination_attribute :id
    end

    belongs_to :remove_operator, Cypridina.Accounts.User do
      public? true
      source_attribute :remove_operator_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_active_user_blacklist, [:user_id, :status], where: expr(status == 1)
  end
end
