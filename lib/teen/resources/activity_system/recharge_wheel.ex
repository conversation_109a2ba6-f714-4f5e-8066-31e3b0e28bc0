defmodule Teen.ActivitySystem.RechargeWheel do
  @moduledoc """
  充值转盘资源

  管理充值转盘活动配置
  转盘奖金池系统：用户累计充值达到指定金额后可获得转盘次数
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :description, :title, :cumulative_recharge, :wheel_spins, :jackpot_pool, :status, :start_date, :end_date, :updated_at]
  end

  postgres do
    table "recharge_wheels"
    repo Cypridina.Repo

    references do
      reference :activity, on_delete: :delete
    end
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_wheels
    define :get_by_recharge_amount
    define :enable_wheel
    define :disable_wheel
    define :get_current_wheel
    define :update_jackpot_pool
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:activity_id, :title, :description, :cumulative_recharge, :wheel_spins, :jackpot_pool, :start_date, :end_date, :status]

      change fn changeset, _context ->
        changeset = changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)

        # 只有在字段存在时才设置默认值
        changeset = if Ash.Changeset.get_attribute(changeset, :title) == nil do
          Ash.Changeset.change_attribute(changeset, :title, "充值转盘活动")
        else
          changeset
        end

        changeset = if Ash.Changeset.get_attribute(changeset, :jackpot_pool) == nil do
          Ash.Changeset.change_attribute(changeset, :jackpot_pool, Decimal.new("0"))
        else
          changeset
        end

        changeset
      end

      validate present(:cumulative_recharge), message: "累计充值金额不能为空"
      validate present(:title), message: "标题不能为空"
    end

    update :update do
      accept [:activity_id, :title, :description, :cumulative_recharge, :wheel_spins, :jackpot_pool, :start_date, :end_date, :status]
      require_atomic? false

      change fn changeset, _context ->
        # 确保必要字段有默认值
        changeset = if Ash.Changeset.changing_attribute?(changeset, :jackpot_pool) and
                       Ash.Changeset.get_attribute(changeset, :jackpot_pool) == nil do
          Ash.Changeset.change_attribute(changeset, :jackpot_pool, Decimal.new("0"))
        else
          changeset
        end

        # 确保 title 不为空
        changeset = if Ash.Changeset.changing_attribute?(changeset, :title) and
                       (Ash.Changeset.get_attribute(changeset, :title) == nil or
                        String.trim(Ash.Changeset.get_attribute(changeset, :title) || "") == "") do
          Ash.Changeset.add_error(changeset, :title, "标题不能为空")
        else
          changeset
        end

        changeset
      end

      validate present(:cumulative_recharge), message: "累计充值金额不能为空"
      validate present(:title), message: "标题不能为空"
    end

    read :list_active_wheels do
      filter expr(status == :enabled and (is_nil(start_date) or start_date <= ^Date.utc_today()) and (is_nil(end_date) or end_date >= ^Date.utc_today()))
    end

    read :get_by_recharge_amount do
      argument :recharge_amount, :decimal, allow_nil?: false
      filter expr(cumulative_recharge <= ^arg(:recharge_amount) and status == :enabled and (is_nil(start_date) or start_date <= ^Date.utc_today()) and (is_nil(end_date) or end_date >= ^Date.utc_today()))
    end

    read :get_current_wheel do
      filter expr(status == :enabled and (is_nil(start_date) or start_date <= ^Date.utc_today()) and (is_nil(end_date) or end_date >= ^Date.utc_today()))
      get? true
    end

    update :enable_wheel do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_wheel do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end

    update :update_jackpot_pool do
      accept [:jackpot_pool]
      require_atomic? false
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :activity_id, :uuid do
      allow_nil? true
      public? true
      description "关联的活动ID"
    end

    attribute :title, :string do
      allow_nil? false
      public? true
      description "转盘活动标题"
      constraints max_length: 100
      default "充值转盘活动"
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "转盘活动描述"
      constraints max_length: 500
    end

    attribute :cumulative_recharge, :decimal do
      allow_nil? false
      public? true
      description "累计充值金币（分）"
      constraints min: Decimal.new("0")
    end

    attribute :wheel_spins, :integer do
      allow_nil? false
      public? true
      description "转盘次数"
      constraints min: 1
      default 1
    end

    attribute :jackpot_pool, :decimal do
      allow_nil? false
      public? true
      description "奖金池金额（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "活动开始日期"
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "活动结束日期"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  relationships do
    belongs_to :activity, Teen.ActivitySystem.Activity do
      public? true
      source_attribute :activity_id
      destination_attribute :id
    end
  end

  calculations do
    calculate :is_active, :boolean, expr(
      status == :enabled and
      (is_nil(start_date) or start_date <= ^Date.utc_today()) and
      (is_nil(end_date) or end_date >= ^Date.utc_today())
    ) do
      description "是否当前有效"
    end

    calculate :activity_period, :string, expr(
      cond do
        is_nil(start_date) and is_nil(end_date) -> "长期有效"
        is_nil(start_date) -> fragment("'至' || ?", end_date)
        is_nil(end_date) -> fragment("? || '起长期有效'", start_date)
        true -> fragment("? || '至' || ?", start_date, end_date)
      end
    ) do
      description "活动期间"
    end

    calculate :jackpot_display, :string, expr(
      fragment("? || '分'", jackpot_pool)
    ) do
      description "奖金池显示"
    end
  end

  validations do
    validate compare(:start_date, less_than_or_equal_to: :end_date),
      message: "开始日期不能晚于结束日期",
      where: [present(:start_date), present(:end_date)]

    validate compare(:cumulative_recharge, greater_than: Decimal.new("0")),
      message: "累计充值金额必须大于0"

    validate compare(:wheel_spins, greater_than: 0),
      message: "转盘次数必须大于0"
  end

  identities do
    identity :unique_recharge_amount, [:cumulative_recharge]
    # 临时注释掉标题唯一性约束，直到迁移完成
    # identity :unique_title, [:title]
  end
end
