defmodule Teen.ActivitySystem.CdkeyActivity do
  @moduledoc """
  CDKEY活动资源

  管理CDKEY兑换码的生成、分发和使用
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :cdkey, :batch_name, :status, :used_at, :inserted_at]
  end

  postgres do
    table "cdkey_activities"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_batch
    define :list_unused_keys
    define :list_used_keys
    define :use_cdkey
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_by_batch do
      argument :batch_name, :string, allow_nil?: false
      filter expr(batch_name == ^arg(:batch_name))
    end

    read :list_unused_keys do
      filter expr(status == 0)
    end

    read :list_used_keys do
      filter expr(status == 1)
    end

    update :use_cdkey do
      argument :user_id, :uuid, allow_nil?: false
      change set_attribute(:status, 1)
      change set_attribute(:used_by_user_id, arg(:user_id))
      change set_attribute(:used_at, &DateTime.utc_now/0)
    end

    create :generate_batch do
      argument :batch_name, :string, allow_nil?: false
      argument :reward_config, :map, allow_nil?: false

      change fn changeset, _context ->
        batch_name = Ash.Changeset.get_argument(changeset, :batch_name)
        reward_config = Ash.Changeset.get_argument(changeset, :reward_config)

        # 生成唯一的CDKEY
        cdkey =
          :crypto.strong_rand_bytes(6)
          |> Base.encode32()
          |> String.slice(0, 12)
          |> String.upcase()

        changeset
        |> Ash.Changeset.change_attribute(:batch_name, batch_name)
        |> Ash.Changeset.change_attribute(:cdkey, cdkey)
        |> Ash.Changeset.change_attribute(:reward_config, reward_config)
        |> Ash.Changeset.change_attribute(:status, 0)
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :cdkey, :string do
      allow_nil? false
      public? true
      description "领取KEY"
      constraints max_length: 50
    end

    attribute :batch_name, :string do
      allow_nil? false
      public? true
      description "批次名称"
      constraints max_length: 100
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :cash, :items]
    end

    attribute :total_reward_amount, :decimal do
      allow_nil? false
      public? true
      description "总奖励金币（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :total_reward_count, :integer do
      allow_nil? false
      public? true
      description "总奖励数量"
      constraints min: 1
      default 1
    end

    attribute :claimed_count, :integer do
      allow_nil? false
      public? true
      description "已领取数量"
      default 0
      constraints min: 0
    end

    attribute :claimed_coins, :decimal do
      allow_nil? false
      public? true
      description "已领取金币（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :valid_days, :integer do
      allow_nil? false
      public? true
      description "有效天数"
      constraints min: 1
      default 30
    end

    attribute :min_vip_level, :integer do
      allow_nil? false
      public? true
      description "可领取用户VIP等级"
      constraints min: 0
      default 0
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    attribute :expires_at, :utc_datetime do
      allow_nil? true
      public? true
      description "过期时间"
    end

    attribute :used_by_user_id, :uuid do
      allow_nil? true
      public? true
      description "使用者用户ID"
    end

    attribute :used_at, :utc_datetime do
      allow_nil? true
      public? true
      description "使用时间"
    end

    attribute :usage_limit, :integer do
      allow_nil? false
      public? true
      description "使用次数限制"
      default 1
      constraints min: 1
    end

    attribute :used_count, :integer do
      allow_nil? false
      public? true
      description "已使用次数"
      default 0
      constraints min: 0
    end

    timestamps()
  end

  relationships do
    belongs_to :used_by_user, Cypridina.Accounts.User do
      public? true
      source_attribute :used_by_user_id
      destination_attribute :id
    end

    has_many :claim_records, Teen.ActivitySystem.CdkeyClaimRecord do
      public? true
      source_attribute :id
      destination_attribute :cdkey_id
    end
  end

  identities do
    identity :unique_cdkey, [:cdkey]
  end
end
