defmodule Teen.ActivitySystem.InviteCashActivity do
  @moduledoc """
  拼多多邀请提现（Free Cash）资源

  管理邀请活动配置
  包括奖励总金额、初始奖励范围等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :title, :description, :total_reward, :initial_min, :initial_max, :start_date, :end_date, :status, :updated_at]
  end

  postgres do
    table "invite_cash_activities"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :create_with_configs
    define :read
    define :get_with_configs
    define :list_with_configs
    define :list_active_with_configs
    define :update
    define :update_with_configs
    define :destroy
    define :list_active_activities
    define :enable_activity
    define :disable_activity
    define :list_by_status
    define :list_by_date_range
    define :get_by_title
    define :list_current_active
  end

  actions do
    defaults [:read]

    destroy :destroy do
      primary? true
      require_atomic? false

      # 添加验证，确保活动可以被删除
      validate fn changeset, _context ->
        activity_id = changeset.data.id
        activity_title = changeset.data.title || "未知活动"

        # 检查活动状态，如果是启用状态，建议先禁用
        if changeset.data.status == :enabled do
          {:error, "活动「#{activity_title}」当前处于启用状态，请先禁用后再删除"}
        else
          :ok
        end
      end

      # 使用 before_action 钩子来处理关联数据的清理
      change before_action(fn changeset, _context ->
        require Logger

        activity_id = changeset.data.id
        activity_title = changeset.data.title || "未知活动"

        Logger.info("开始删除活动: ID=#{activity_id}, 标题=#{activity_title}")

        # 使用事务来确保数据一致性
        case delete_related_configs(activity_id, activity_title) do
          :ok ->
            Logger.info("关联数据清理完成，继续删除活动本身")
            changeset
          {:error, reason} ->
            Logger.error("清理关联数据失败: #{reason}")
            Ash.Changeset.add_error(changeset, "删除失败: #{reason}")
        end
      end)
    end

    create :create do
      primary? true
      accept [:title, :description, :total_reward, :initial_min, :initial_max, :start_date, :end_date, :status]

      validate present(:title), message: "标题不能为空"
      validate present(:total_reward), message: "奖励总金额不能为空"
      validate present(:initial_min), message: "初始最小值不能为空"
      validate present(:initial_max), message: "初始最大值不能为空"

      validate compare(:total_reward, greater_than: 0), message: "奖励总金额必须大于0"
      validate compare(:initial_min, greater_than_or_equal_to: 0), message: "初始最小值不能为负数"
      validate compare(:initial_max, greater_than_or_equal_to: 0), message: "初始最大值不能为负数"

      # 验证初始值范围
      validate fn changeset, _context ->
        initial_min = Ash.Changeset.get_attribute(changeset, :initial_min)
        initial_max = Ash.Changeset.get_attribute(changeset, :initial_max)

        cond do
          is_nil(initial_min) or is_nil(initial_max) -> :ok
          initial_min > initial_max ->
            {:error, field: :initial_max, message: "初始最小值不能大于初始最大值"}
          true -> :ok
        end
      end

      # 验证日期范围
      validate fn changeset, _context ->
        start_date = Ash.Changeset.get_attribute(changeset, :start_date)
        end_date = Ash.Changeset.get_attribute(changeset, :end_date)

        cond do
          is_nil(start_date) and is_nil(end_date) -> :ok
          is_nil(start_date) or is_nil(end_date) -> :ok
          Date.compare(start_date, end_date) == :gt ->
            {:error, field: :end_date, message: "开始日期不能晚于结束日期"}
          true -> :ok
        end
      end

      # 设置默认值
      change fn changeset, _context ->
        changeset
        |> maybe_set_default(:status, :enabled)
      end
    end

    update :update do
      primary? true
      accept [:title, :description, :total_reward, :initial_min, :initial_max, :start_date, :end_date, :status]
      require_atomic? false

      validate present(:title), message: "标题不能为空"
      validate present(:total_reward), message: "奖励总金额不能为空"
      validate present(:initial_min), message: "初始最小值不能为空"
      validate present(:initial_max), message: "初始最大值不能为空"

      validate compare(:total_reward, greater_than: 0), message: "奖励总金额必须大于0"
      validate compare(:initial_min, greater_than_or_equal_to: 0), message: "初始最小值不能为负数"
      validate compare(:initial_max, greater_than_or_equal_to: 0), message: "初始最大值不能为负数"

      # 验证初始值范围
      validate fn changeset, _context ->
        initial_min = Ash.Changeset.get_attribute(changeset, :initial_min)
        initial_max = Ash.Changeset.get_attribute(changeset, :initial_max)

        cond do
          is_nil(initial_min) or is_nil(initial_max) -> :ok
          initial_min > initial_max ->
            {:error, field: :initial_max, message: "初始最小值不能大于初始最大值"}
          true -> :ok
        end
      end

      # 验证日期范围
      validate fn changeset, _context ->
        start_date = Ash.Changeset.get_attribute(changeset, :start_date)
        end_date = Ash.Changeset.get_attribute(changeset, :end_date)

        cond do
          is_nil(start_date) and is_nil(end_date) -> :ok
          is_nil(start_date) or is_nil(end_date) -> :ok
          Date.compare(start_date, end_date) == :gt ->
            {:error, field: :end_date, message: "开始日期不能晚于结束日期"}
          true -> :ok
        end
      end
    end

    create :create_with_configs do
      accept [:title, :description, :total_reward, :initial_min, :initial_max, :start_date, :end_date, :status]

      argument :invite_reward_configs, {:array, :map} do
        allow_nil? true
        description "奖励配置列表"
      end

      # 添加基础验证
      validate present(:title), message: "标题不能为空"
      validate present(:total_reward), message: "奖励总金额不能为空"
      validate present(:initial_min), message: "初始最小值不能为空"
      validate present(:initial_max), message: "初始最大值不能为空"

      validate compare(:total_reward, greater_than: 0), message: "奖励总金额必须大于0"
      validate compare(:initial_min, greater_than_or_equal_to: 0), message: "初始最小值不能为负数"
      validate compare(:initial_max, greater_than_or_equal_to: 0), message: "初始最大值不能为负数"

      # 验证初始值范围
      validate fn changeset, _context ->
        initial_min = Ash.Changeset.get_attribute(changeset, :initial_min)
        initial_max = Ash.Changeset.get_attribute(changeset, :initial_max)

        cond do
          is_nil(initial_min) or is_nil(initial_max) -> :ok
          initial_min > initial_max ->
            {:error, field: :initial_max, message: "初始最小值不能大于初始最大值"}
          true -> :ok
        end
      end

      # 验证日期范围
      validate fn changeset, _context ->
        start_date = Ash.Changeset.get_attribute(changeset, :start_date)
        end_date = Ash.Changeset.get_attribute(changeset, :end_date)

        cond do
          is_nil(start_date) and is_nil(end_date) -> :ok
          is_nil(start_date) or is_nil(end_date) -> :ok
          Date.compare(start_date, end_date) == :gt ->
            {:error, field: :end_date, message: "开始日期不能晚于结束日期"}
          true -> :ok
        end
      end

      # 设置默认值
      change fn changeset, _context ->
        changeset
        |> maybe_set_default(:status, :enabled)
      end

      # 管理关联的奖励配置
      change manage_relationship(:invite_reward_configs,
        type: :direct_control,
        on_missing: :destroy,
        on_match: :update,
        on_no_match: :create
      )
    end

    update :update_with_configs do
      accept [:title, :description, :total_reward, :initial_min, :initial_max, :start_date, :end_date, :status]
      require_atomic? false

      argument :invite_reward_configs, {:array, :map} do
        allow_nil? true
        description "奖励配置列表"
      end

      # 添加验证
      validate numericality(:total_reward, greater_than: 0)
      validate numericality(:initial_min, greater_than_or_equal_to: 0)
      validate numericality(:initial_max, greater_than_or_equal_to: 0)

      # 验证初始值范围
      validate fn changeset, _context ->
        initial_min = Ash.Changeset.get_attribute(changeset, :initial_min)
        initial_max = Ash.Changeset.get_attribute(changeset, :initial_max)

        cond do
          is_nil(initial_min) or is_nil(initial_max) -> :ok
          initial_min > initial_max ->
            {:error, field: :initial_max, message: "初始最小值不能大于初始最大值"}
          true -> :ok
        end
      end

      # 验证日期范围
      validate fn changeset, _context ->
        start_date = Ash.Changeset.get_attribute(changeset, :start_date)
        end_date = Ash.Changeset.get_attribute(changeset, :end_date)

        cond do
          is_nil(start_date) and is_nil(end_date) -> :ok
          is_nil(start_date) or is_nil(end_date) -> :ok
          Date.compare(start_date, end_date) == :gt ->
            {:error, field: :end_date, message: "开始日期不能晚于结束日期"}
          true -> :ok
        end
      end

      # 管理关联的奖励配置
      change manage_relationship(:invite_reward_configs,
        type: :direct_control,
        on_missing: :destroy,
        on_match: :update,
        on_no_match: :create
      )
    end

    read :list_active_activities do
      filter expr(status == :enabled)
    end

    read :get_with_configs do
      get? true
      prepare build(load: [:invite_reward_configs])
    end

    read :list_with_configs do
      prepare build(load: [:invite_reward_configs])
      prepare build(sort: [inserted_at: :desc])
    end

    read :list_active_with_configs do
      filter expr(status == :enabled)
      prepare build(load: [:invite_reward_configs])
      prepare build(sort: [inserted_at: :desc])
    end

    update :enable_activity do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_activity do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end

    # 添加更多有用的读取操作
    read :list_by_status do
      argument :status, :atom, allow_nil?: false
      filter expr(status == ^arg(:status))
      prepare build(sort: [inserted_at: :desc])
    end

    read :list_by_date_range do
      argument :start_date, :date, allow_nil?: true
      argument :end_date, :date, allow_nil?: true

      filter expr(
        (is_nil(^arg(:start_date)) or start_date >= ^arg(:start_date)) and
        (is_nil(^arg(:end_date)) or end_date <= ^arg(:end_date))
      )
      prepare build(sort: [start_date: :asc])
    end

    read :get_by_title do
      argument :title, :string, allow_nil?: false
      filter expr(title == ^arg(:title))
      get? true
    end

    read :list_current_active do
      filter expr(
        status == :enabled and
        (is_nil(start_date) or start_date <= ^Date.utc_today()) and
        (is_nil(end_date) or end_date >= ^Date.utc_today())
      )
      prepare build(sort: [inserted_at: :desc])
    end
  end



  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "标题"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "活动描述"
      constraints max_length: 500
    end

    attribute :total_reward, :decimal do
      allow_nil? false
      public? true
      description "奖励总金额（分）"
      constraints min: 0
    end

    attribute :initial_min, :decimal do
      allow_nil? false
      public? true
      description "初始最小值（分）"
      constraints min: 0
    end

    attribute :initial_max, :decimal do
      allow_nil? false
      public? true
      description "初始最大值（分）"
      constraints min: 0
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "开始日期"
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "结束日期"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  validations do
    validate present(:title), message: "标题不能为空"
    validate present(:total_reward), message: "奖励总金额不能为空"
    validate present(:initial_min), message: "初始最小值不能为空"
    validate present(:initial_max), message: "初始最大值不能为空"
    validate compare(:total_reward, greater_than: 0), message: "奖励总金额必须大于0"
    validate compare(:initial_min, greater_than_or_equal_to: 0), message: "初始最小值不能为负数"
    validate compare(:initial_max, greater_than_or_equal_to: 0), message: "初始最大值不能为负数"
    validate one_of(:status, [:enabled, :disabled])
  end

  # 私有辅助函数
  defp maybe_set_default(changeset, field, default_value) do
    if Ash.Changeset.changing_attribute?(changeset, field) do
      changeset
    else
      Ash.Changeset.change_attribute(changeset, field, default_value)
    end
  end

  # 私有辅助函数：删除关联的奖励配置
  defp delete_related_configs(activity_id, activity_title) do
    require Logger

    try do
      # 查找关联的奖励配置
      case Teen.ActivitySystem.InviteRewardConfig.list_by_activity(%{activity_id: activity_id}) do
        {:ok, configs} ->
          config_count = length(configs)
          Logger.info("找到 #{config_count} 个关联的奖励配置")

          if config_count > 0 do
            Logger.info("开始删除活动「#{activity_title}」的 #{config_count} 个奖励配置")

            # 批量删除配置，使用 Enum.reduce 来收集错误
            result = Enum.reduce_while(configs, :ok, fn config, _acc ->
              case Teen.ActivitySystem.InviteRewardConfig.destroy(config) do
                {:ok, _} ->
                  Logger.debug("成功删除奖励配置: #{config.id}")
                  {:cont, :ok}
                {:error, error} ->
                  Logger.error("删除奖励配置 #{config.id} 失败: #{inspect(error)}")
                  {:halt, {:error, "删除奖励配置失败: #{inspect(error)}"}}
              end
            end)

            case result do
              :ok ->
                Logger.info("成功删除活动「#{activity_title}」的所有 #{config_count} 个奖励配置")
                :ok
              error ->
                error
            end
          else
            Logger.info("活动「#{activity_title}」没有关联的奖励配置")
            :ok
          end

        {:error, reason} ->
          Logger.error("查询关联奖励配置失败: #{inspect(reason)}")
          {:error, "查询关联数据失败: #{inspect(reason)}"}
      end
    rescue
      error ->
        Logger.error("删除关联数据时发生异常: #{inspect(error)}")
        {:error, "删除关联数据时发生异常: #{Exception.message(error)}"}
    end
  end

  relationships do
    has_many :invite_reward_configs, Teen.ActivitySystem.InviteRewardConfig do
      public? true
      source_attribute :id
      destination_attribute :activity_id
    end
  end



end
