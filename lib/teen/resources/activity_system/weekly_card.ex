defmodule Teen.ActivitySystem.WeeklyCard do
  @moduledoc """
  周卡任务资源

  管理周卡配置，用户充值后可获得初始奖励和每日领取奖励

  示例：
  - 充值500：初始奖励10，每日领取50（共7天）
  - 充值1000：初始奖励30，每日领取80
  - 充值5000：初始奖励180，每日领取150
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :title, :recharge_amount, :total_reward, :initial_reward, :claim_days, :daily_reward, :reward_type, :updated_at, :status]
  end

  postgres do
    table "weekly_cards"
    repo Cy<PERSON>ridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_cards
    define :list_by_recharge_amount
    define :enable_card
    define :disable_card
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:title, :description, :task_type, :recharge_amount, :initial_reward, :daily_reward, :claim_days, :reward_type, :start_date, :end_date, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
        |> Ash.Changeset.change_attribute(:claim_days, 7)
        |> Ash.Changeset.change_attribute(:reward_type, :coins)
        |> Ash.Changeset.change_attribute(:task_type, :weekly_card)
      end
    end

    update :update do
      accept [:title, :description, :task_type, :recharge_amount, :initial_reward, :daily_reward, :claim_days, :reward_type, :start_date, :end_date, :status]
      require_atomic? false
    end

    read :list_active_cards do
      filter expr(status == :enabled)
    end

    read :list_by_recharge_amount do
      argument :recharge_amount, :decimal, allow_nil?: false
      filter expr(recharge_amount <= ^arg(:recharge_amount) and status == :enabled)
    end

    update :enable_card do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_card do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "周卡标题"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "周卡描述"
      constraints max_length: 500
    end

    attribute :task_type, :atom do
      allow_nil? false
      public? true
      description "任务类型"
      constraints one_of: [:weekly_card, :monthly_card, :season_card, :annual_card]
      default :weekly_card
    end

    attribute :recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "充值金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :initial_reward, :decimal do
      allow_nil? false
      public? true
      description "初始奖励（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :daily_reward, :decimal do
      allow_nil? false
      public? true
      description "每日领取奖励（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :claim_days, :integer do
      allow_nil? false
      public? true
      description "可领取天数"
      constraints min: 1
      default 7
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :points, :cash, :wheel_spins, :items]
      default :coins
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "开始日期"
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "结束日期"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  calculations do
    calculate :total_reward, :decimal do
      public? true
      description "奖励金额（每日领取金额 × 领取天数）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          # 奖励金额 = 每日领取金额 × 领取天数
          Decimal.mult(record.daily_reward, Decimal.new(record.claim_days))
        end)
      end
    end

    calculate :total_benefit, :decimal do
      public? true
      description "总收益（初始奖励 + 奖励金额）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          daily_total = Decimal.mult(record.daily_reward, Decimal.new(record.claim_days))
          Decimal.add(record.initial_reward, daily_total)
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :reward_summary, :string do
      public? true
      description "奖励摘要"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if Decimal.equal?(record.initial_reward, Decimal.new("0")) do
            "充值#{record.recharge_amount}分，分#{record.claim_days}天返还，每日#{record.daily_reward}分"
          else
            "初始#{record.initial_reward}分，每日#{record.daily_reward}分×#{record.claim_days}天"
          end
        end)
      end
    end

    calculate :business_logic_display, :string do
      public? true
      description "业务逻辑说明"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if Decimal.equal?(record.initial_reward, Decimal.new("0")) do
            "充值多少得多少，奖励金额分#{record.claim_days}天返还"
          else
            "充值立即获得#{record.initial_reward}分，另外#{record.claim_days}天内每日可领取#{record.daily_reward}分"
          end
        end)
      end
    end

    calculate :reward_type_display, :string do
      public? true
      description "奖励类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.reward_type do
            :coins -> "金币"
            :points -> "积分"
            :cash -> "现金"
            :wheel_spins -> "转盘次数"
            :items -> "道具"
            _ -> "未知"
          end
        end)
      end
    end
  end

  validations do
    validate compare(:recharge_amount, greater_than: Decimal.new("0")), message: "充值金额必须大于0"
    validate compare(:initial_reward, greater_than_or_equal_to: Decimal.new("0")), message: "初始奖励不能为负数"
    validate compare(:daily_reward, greater_than_or_equal_to: Decimal.new("0")), message: "每日奖励不能为负数"
    validate compare(:claim_days, greater_than: 0), message: "领取天数必须大于0"
  end

  identities do
    identity :unique_recharge_amount, [:recharge_amount]
  end
end
