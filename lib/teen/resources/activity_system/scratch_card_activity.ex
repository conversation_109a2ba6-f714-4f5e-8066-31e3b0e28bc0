defmodule Teen.ActivitySystem.ScratchCardActivity do
  @moduledoc """
  30刮刮卡任务资源（多级任务系统）

  管理30刮刮卡活动的主配置
  包含活动编号、标题、可领取数量、奖励概率等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :activity_title, :claimable_count, :reward_probability, :status, :updated_at]
  end

  postgres do
    table "scratch_card_activities"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_activities
    define :enable_activity
    define :disable_activity
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:activity_title, :claimable_count, :reward_probability, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
        |> Ash.Changeset.change_attribute(:claimable_count, 30)
      end
    end

    read :list_active_activities do
      filter expr(status == :enabled)
    end

    update :enable_activity do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_activity do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :activity_title, :string do
      allow_nil? false
      public? true
      description "活动标题"
      constraints max_length: 100
    end

    attribute :claimable_count, :integer do
      allow_nil? false
      public? true
      description "可领取数量"
      default 30
      constraints min: 1
    end

    attribute :reward_probability, :decimal do
      allow_nil? false
      public? true
      description "奖励概率（%）"
      constraints min: Decimal.new("0"), max: Decimal.new("100")
      default Decimal.new("10")
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  relationships do
    has_many :task_rounds, Teen.ActivitySystem.ScratchCardTaskRound do
      public? true
      source_attribute :id
      destination_attribute :activity_id
    end

    has_many :task_levels, Teen.ActivitySystem.ScratchCardTaskLevel do
      public? true
      source_attribute :id
      destination_attribute :activity_id
    end
  end
end
