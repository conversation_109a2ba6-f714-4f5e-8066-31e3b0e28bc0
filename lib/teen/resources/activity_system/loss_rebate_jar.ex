defmodule Teen.ActivitySystem.LossRebateJar do
  @moduledoc """
  输钱返利金罐子资源

  管理输钱返利配置
  规则：每日领取前一天损失的指定百分比
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :title, :task_type, :description, :min_loss_amount, :rebate_percentage, :max_rebate_amount, :reward_amount, :reward_type, :status, :start_date, :end_date, :updated_at]
  end

  postgres do
    table "loss_rebate_jars"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_jars
    define :get_rebate_config
    define :enable_jar
    define :disable_jar
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:title, :task_type, :description, :max_claims, :min_loss_amount, :rebate_percentage, :max_rebate_amount, :reward_amount, :calculation_period, :reward_type, :auto_distribute, :start_date, :end_date, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
        |> Ash.Changeset.change_attribute(:task_type, :loss_rebate)
        |> Ash.Changeset.change_attribute(:reward_type, :coins)
        |> Ash.Changeset.change_attribute(:calculation_period, :daily)
        |> Ash.Changeset.change_attribute(:max_claims, 1)
        |> Ash.Changeset.change_attribute(:auto_distribute, false)
      end
    end

    update :update do
      accept [:title, :task_type, :description, :max_claims, :min_loss_amount, :rebate_percentage, :max_rebate_amount, :reward_amount, :calculation_period, :reward_type, :auto_distribute, :start_date, :end_date, :status]
    end

    read :list_active_jars do
      filter expr(status == :enabled)
    end

    read :get_rebate_config do
      get? true
      filter expr(status == :enabled)
    end

    update :enable_jar do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_jar do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "标题"
      constraints max_length: 100
    end

    attribute :task_type, :atom do
      allow_nil? false
      public? true
      description "任务类型"
      constraints one_of: [:loss_rebate, :first_loss, :consecutive_loss, :large_loss]
      default :loss_rebate
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "活动描述"
      constraints max_length: 500
    end

    attribute :max_claims, :integer do
      allow_nil? false
      public? true
      description "领取次数"
      constraints min: 1
      default 1
    end

    attribute :min_loss_amount, :decimal do
      allow_nil? false
      public? true
      description "最小亏损金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :rebate_percentage, :decimal do
      allow_nil? false
      public? true
      description "返利百分比(%)"
      constraints min: Decimal.new("0"), max: Decimal.new("100")
      default Decimal.new("10")
    end

    attribute :max_rebate_amount, :decimal do
      allow_nil? false
      public? true
      description "最大返利金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :reward_amount, :decimal do
      allow_nil? true
      public? true
      description "奖励金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :calculation_period, :atom do
      allow_nil? true
      public? true
      description "计算周期"
      constraints one_of: [:daily, :weekly, :monthly]
      default :daily
    end



    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :points, :cash, :wheel_spins, :items]
      default :coins
    end

    attribute :auto_distribute, :boolean do
      allow_nil? false
      public? true
      description "自动发放"
      default false
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "开始日期"
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "结束日期"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end
end
