defmodule Teen.ActivitySystem.SevenDayTask do
  @moduledoc """
  七天登录任务资源

  管理连续登录7天的任务配置
  规则：连续登录七天，可循环，如果中断了则重新从第一天开始领取
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :title,
      :day_display,
      :task_type_display,
      :reward_amount_display,
      :reward_type_display,
      :cyclic_display,
      :special_display,
      :status_display,
      :start_date,
      :end_date,
      :updated_at
    ]
  end

  postgres do
    table "seven_day_tasks"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_tasks
    define :list_by_day
    define :enable_task
    define :disable_task

    define :get_day_reward
    define :list_active_and_enabled_tasks
    define :list_special_rewards
    define :list_by_reward_type
    define :list_ordered_by_day
    define :get_current_cycle_tasks
    define :list_by_date_range
    define :list_active_cycle_tasks
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:title, :task_type, :day_number, :reward_amount, :reward_type, :is_cyclic, :is_special, :description, :start_date, :end_date, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
        |> Ash.Changeset.change_attribute(:is_cyclic, true)
        |> Ash.Changeset.change_attribute(:is_special, false)
        |> Ash.Changeset.change_attribute(:reward_type, :coins)
        |> Ash.Changeset.change_attribute(:task_type, :daily_login)
      end
    end

    update :update do
      accept [:title, :task_type, :day_number, :reward_amount, :reward_type, :is_cyclic, :is_special, :description, :start_date, :end_date, :status]
      require_atomic? false
    end

    read :list_active_tasks do
      filter expr(status == :enabled)
    end

    read :list_by_day do
      argument :day_number, :integer, allow_nil?: false
      filter expr(day_number == ^arg(:day_number) and status == :enabled)
    end

    read :get_day_reward do
      argument :day_number, :integer, allow_nil?: false
      get? true
      filter expr(day_number == ^arg(:day_number) and status == :enabled)
    end

    update :enable_task do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_task do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end



    read :list_active_and_enabled_tasks do
      filter expr(status == :enabled)
    end

    read :list_special_rewards do
      filter expr(is_special == true and status == :enabled)
    end

    read :list_by_reward_type do
      argument :reward_type, :atom, allow_nil?: false
      filter expr(reward_type == ^arg(:reward_type) and status == :enabled)
    end

    read :list_ordered_by_day do
      filter expr(status == :enabled)
      prepare build(sort: [day_number: :asc])
    end

    read :get_current_cycle_tasks do
      filter expr(status == :enabled and is_cyclic == true)
      prepare build(sort: [day_number: :asc])
    end

    read :list_by_date_range do
      argument :start_date, :date, allow_nil?: true
      argument :end_date, :date, allow_nil?: true

      filter expr(
        status == :enabled and
        (is_nil(^arg(:start_date)) or is_nil(start_date) or start_date >= ^arg(:start_date)) and
        (is_nil(^arg(:end_date)) or is_nil(end_date) or end_date <= ^arg(:end_date))
      )
      prepare build(sort: [day_number: :asc])
    end

    read :list_active_cycle_tasks do
      filter expr(status == :enabled and is_cyclic == true)
      prepare build(sort: [day_number: :asc])
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? true
      public? true
      description "任务标题"
      constraints max_length: 100
    end

    attribute :task_type, :atom do
      allow_nil? false
      public? true
      description "任务类型"
      constraints one_of: [:daily_login, :consecutive_login, :weekly_login, :monthly_login]
      default :daily_login
    end

    attribute :day_number, :integer do
      allow_nil? false
      public? true
      description "领取次数（第几次/天领取）"
      constraints min: 1, max: 7
    end

    attribute :reward_amount, :integer do
      allow_nil? false
      public? true
      description "奖励金额（以分为单位）"
      constraints min: 0
      default 0
    end

    attribute :is_cyclic, :boolean do
      allow_nil? false
      public? true
      description "是否循环"
      default true
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :points, :cash, :wheel_spins, :items]
      default :coins
    end

    attribute :is_special, :boolean do
      allow_nil? false
      public? true
      description "是否特殊奖励"
      default false
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "描述"
      constraints max_length: 500
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "开始日期"
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "结束日期"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  calculations do
    calculate :day_display, :string do
      public? true
      description "天数显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          "第#{record.day_number}天"
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
          end
        end)
      end
    end

    calculate :cyclic_display, :string do
      public? true
      description "循环状态显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if record.is_cyclic, do: "是", else: "否"
        end)
      end
    end

    calculate :reward_type_display, :string do
      public? true
      description "奖励类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.reward_type do
            :coins -> "金币"
            :points -> "积分"
            :cash -> "现金"
            :wheel_spins -> "转盘次数"
            :items -> "道具"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :special_display, :string do
      public? true
      description "特殊奖励状态显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if record.is_special, do: "是", else: "否"
        end)
      end
    end

    calculate :task_type_display, :string do
      public? true
      description "任务类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.task_type do
            :daily_login -> "每日登录"
            :consecutive_login -> "连续登录"
            :weekly_login -> "每周登录"
            :monthly_login -> "每月登录"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :reward_amount_display, :string do
      public? true
      description "奖励金额显示（转换为元）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.reward_type do
            :coins -> "#{record.reward_amount}金币"
            :points -> "#{record.reward_amount}积分"
            :cash -> "¥#{Float.round(record.reward_amount / 100, 2)}"
            :wheel_spins -> "#{record.reward_amount}次"
            :items -> "#{record.reward_amount}个"
            _ -> "#{record.reward_amount}"
          end
        end)
      end
    end
  end

  validations do
    validate compare(:day_number, greater_than: 0, less_than_or_equal_to: 7),
      message: "天数必须在1-7之间"

    validate compare(:reward_amount, greater_than_or_equal_to: 0),
      message: "奖励金额不能为负数"

    # 验证日期范围的合理性
    validate fn changeset, _context ->
      start_date = Ash.Changeset.get_attribute(changeset, :start_date)
      end_date = Ash.Changeset.get_attribute(changeset, :end_date)

      cond do
        is_nil(start_date) or is_nil(end_date) -> :ok
        Date.compare(start_date, end_date) == :gt ->
          {:error, field: :end_date, message: "结束日期不能早于开始日期"}
        true -> :ok
      end
    end
  end

  identities do
    identity :unique_day_number, [:day_number]
  end
end
