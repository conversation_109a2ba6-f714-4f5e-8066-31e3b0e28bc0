defmodule Teen.ActivitySystem.ScratchCardLevelReward do
  @moduledoc """
  刮刮卡等级奖励资源

  管理刮刮卡活动等级的奖励
  包括等级、奖励类型、奖励金额、概率、状态等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :reward_config_id, :level, :reward_type, :reward_amount, :probability, :sort_order, :status, :updated_at]
  end

  postgres do
    table "scratch_card_level_rewards"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_config_and_level
    define :get_random_reward
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:reward_config_id, :level, :reward_type, :reward_amount, :probability, :sort_order, :status]
    end

    read :list_by_config_and_level do
      argument :reward_config_id, :uuid, allow_nil?: false
      argument :level, :integer, allow_nil?: false
      filter expr(reward_config_id == ^arg(:reward_config_id) and level == ^arg(:level))
      prepare build(sort: [:sort_order])
    end

    read :get_random_reward do
      argument :reward_config_id, :uuid, allow_nil?: false
      argument :level, :integer, allow_nil?: false
      filter expr(reward_config_id == ^arg(:reward_config_id) and level == ^arg(:level) and status == :active)
      prepare build(sort: [:sort_order])
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :level, :integer do
      allow_nil? false
      public? true
      description "等级"
      constraints min: 1
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :cash]
      default :coins
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :probability, :decimal do
      allow_nil? false
      public? true
      description "概率（%）"
      constraints min: Decimal.new("0"), max: Decimal.new("100")
      default Decimal.new("0")
    end

    attribute :sort_order, :integer do
      allow_nil? false
      public? true
      description "排序号"
      default 0
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:active, :inactive]
      default :active
    end

    timestamps()
  end

  relationships do
    belongs_to :reward_config, Teen.ActivitySystem.ScratchCardActivity do
      public? true
      source_attribute :reward_config_id
      destination_attribute :id
    end
  end
end
