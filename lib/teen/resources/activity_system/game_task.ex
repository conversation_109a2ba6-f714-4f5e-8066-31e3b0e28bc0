defmodule Teen.ActivitySystem.GameTask do
  @moduledoc """
  游戏任务资源

  管理每日游戏任务配置，包括：
  - 游戏局数任务
  - 游戏赢的局数任务
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :title, :game_name, :task_type, :required_count, :max_claims, :reward_amount, :status, :reward_type, :start_date, :end_date, :updated_at]
  end

  postgres do
    table "game_tasks"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_tasks
    define :list_by_game
    define :enable_task
    define :disable_task
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:title, :description, :task_name, :game_id, :game_name, :task_type, :required_count, :max_claims, :reward_amount, :status, :reward_type, :start_date, :end_date]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
        |> Ash.Changeset.change_attribute(:reward_type, :coins)
        |> ensure_game_id_matches_game_name()
      end
    end

    update :update do
      accept [:title, :description, :task_name, :game_id, :game_name, :task_type, :required_count, :max_claims, :reward_amount, :status, :reward_type, :start_date, :end_date]
      require_atomic? false

      change fn changeset, _context ->
        changeset
        |> ensure_game_id_matches_game_name()
      end
    end

    read :list_active_tasks do
      filter expr(status == :enabled)
    end

    read :list_by_game do
      argument :game_id, :string, allow_nil?: false
      filter expr(game_id == ^arg(:game_id) and status == :enabled)
    end

    update :enable_task do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_task do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? true
      public? true
      description "任务标题"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "任务描述"
      constraints max_length: 500
    end

    attribute :task_name, :string do
      allow_nil? false
      public? true
      description "任务名称"
      constraints max_length: 100
    end

    attribute :game_id, :string do
      allow_nil? false
      public? true
      description "游戏ID"
      constraints max_length: 50
    end

    attribute :game_name, :string do
      allow_nil? false
      public? true
      description "游戏名称"
      constraints max_length: 100
      default "all"
    end

    attribute :task_type, :atom do
      allow_nil? false
      public? true
      description "任务类型"
      constraints one_of: [:game_rounds, :win_rounds, :recharge_amount, :game_wins, :task_completion]
    end
    
    attribute :required_count, :integer do
      allow_nil? false
      public? true
      description "所需局数"
      constraints min: 1
      default 1
    end

    attribute :max_claims, :integer do
      allow_nil? false
      public? true
      description "每日最大领取次数"
      constraints min: 1
      default 1
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :points, :cash, :wheel_spins, :items]
      default :coins
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "开始日期"
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "结束日期"
    end

    timestamps()
  end

  calculations do
    calculate :task_type_display, :string do
      public? true
      description "任务类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.task_type do
            :game_rounds -> "游戏局数"
            :win_rounds -> "胜利局数"
            :recharge_amount -> "充值金额"
            :game_wins -> "游戏胜利"
            :task_completion -> "完成任务"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :reward_type_display, :string do
      public? true
      description "奖励类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.reward_type do
            :coins -> "金币"
            :points -> "积分"
            :cash -> "现金"
            :wheel_spins -> "转盘次数"
            :items -> "道具"
            _ -> "未设置"
          end
        end)
      end
    end
  end

  validations do
    validate compare(:required_count, greater_than: 0), message: "所需局数必须大于0"
    validate compare(:max_claims, greater_than: 0), message: "最大领取次数必须大于0"
    validate compare(:reward_amount, greater_than_or_equal_to: Decimal.new("0")), message: "奖励金额不能为负数"

    validate fn changeset, _context ->
      start_date = Ash.Changeset.get_attribute(changeset, :start_date)
      end_date = Ash.Changeset.get_attribute(changeset, :end_date)

      cond do
        is_nil(start_date) or is_nil(end_date) ->
          :ok

        Date.compare(start_date, end_date) == :gt ->
          {:error, field: :end_date, message: "结束日期不能早于开始日期"}

        true ->
          :ok
      end
    end
  end

  identities do
    identity :unique_game_task, [:game_id, :task_type]
  end

  # ==================== 私有辅助函数 ====================

  defp ensure_game_id_matches_game_name(changeset) do
    case Ash.Changeset.get_attribute(changeset, :game_name) do
      nil -> changeset
      game_name -> Ash.Changeset.change_attribute(changeset, :game_id, game_name)
    end
  end
end
