defmodule Teen.ActivitySystem.VipGift do
  @moduledoc """
  VIP礼包资源

  管理不同VIP等级的奖励配置
  包括每日奖励、每周奖励、每月奖励
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :title,
      :vip_level_display,
      :task_type_display,
      :primary_reward_display,
      :reward_type_display,
      :value_tier,
      :status_display,
      :start_date,
      :end_date,
      :updated_at
    ]
  end

  postgres do
    table "vip_gifts"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_gifts
    define :get_by_vip_level
    define :enable_gift
    define :disable_gift
    define :list_by_task_type
    define :list_by_reward_type
    define :list_by_date_range
    define :list_ordered_by_vip_level
    define :get_vip_gifts_for_level_range
    define :list_high_value_gifts
    define :batch_enable_gifts
    define :batch_disable_gifts
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:title, :description, :task_type, :vip_level, :reward_amount, :reward_frequency, :max_claims, :reward_type, :start_date, :end_date, :status]

      change fn changeset, _context ->
        # 设置默认值（不覆盖用户输入的值）
        changeset = changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
        |> Ash.Changeset.change_attribute(:reward_type, :coins)

        # 如果没有提供 task_type，设置默认值
        changeset = if Ash.Changeset.get_attribute(changeset, :task_type) == nil do
          Ash.Changeset.change_attribute(changeset, :task_type, :vip_daily_login)
        else
          changeset
        end

        # 只有在用户没有明确设置 reward_frequency 和 max_claims 时，才根据任务类型设置默认值
        task_type = Ash.Changeset.get_attribute(changeset, :task_type)
        user_frequency = Ash.Changeset.get_attribute(changeset, :reward_frequency)
        user_max_claims = Ash.Changeset.get_attribute(changeset, :max_claims)

        {default_frequency, default_max_claims} = case task_type do
          :vip_daily_login -> {1, nil}        # 每天可领取，无限制
          :vip_weekly_login -> {7, nil}       # 每周可领取，无限制
          :vip_monthly_login -> {30, nil}     # 每月可领取，无限制
          :vip_level_upgrade -> {nil, 1}      # 一次性，只能领取1次
          :vip_birthday -> {365, 1}           # 每年生日，只能领取1次
          :vip_anniversary -> {365, nil}      # 年周年，无限制
          :vip_recharge_bonus -> {nil, nil}   # 根据充值触发，无限制
          :vip_special_event -> {nil, nil}    # 特殊活动，根据活动规则
          _ -> {nil, nil}
        end

        # 只有在用户没有设置时才使用默认值
        changeset = if is_nil(user_frequency) do
          Ash.Changeset.change_attribute(changeset, :reward_frequency, default_frequency)
        else
          changeset
        end

        changeset = if is_nil(user_max_claims) do
          Ash.Changeset.change_attribute(changeset, :max_claims, default_max_claims)
        else
          changeset
        end

        changeset
      end

      validate present(:vip_level), message: "VIP等级不能为空"
      validate present(:title), message: "标题不能为空"
    end

    update :update do
      accept [:title, :description, :task_type, :vip_level, :reward_amount, :reward_frequency, :max_claims, :reward_type, :start_date, :end_date, :status]
      require_atomic? false

      change fn changeset, _context ->
        # 只有在 task_type 发生变化且用户没有明确设置 reward_frequency 和 max_claims 时，才更新默认值
        task_type = Ash.Changeset.get_attribute(changeset, :task_type)
        user_frequency = Ash.Changeset.get_attribute(changeset, :reward_frequency)
        user_max_claims = Ash.Changeset.get_attribute(changeset, :max_claims)

        # 检查是否有 task_type 变化
        task_type_changed = Ash.Changeset.changing_attribute?(changeset, :task_type)

        if task_type && task_type_changed do
          {default_frequency, default_max_claims} = case task_type do
            :vip_daily_login -> {1, nil}
            :vip_weekly_login -> {7, nil}
            :vip_monthly_login -> {30, nil}
            :vip_level_upgrade -> {nil, 1}
            :vip_birthday -> {365, 1}
            :vip_anniversary -> {365, nil}
            :vip_recharge_bonus -> {nil, nil}
            :vip_special_event -> {nil, nil}
            _ -> {nil, nil}
          end

          # 只有在用户没有明确设置时才使用默认值
          changeset = if is_nil(user_frequency) && !Ash.Changeset.changing_attribute?(changeset, :reward_frequency) do
            Ash.Changeset.change_attribute(changeset, :reward_frequency, default_frequency)
          else
            changeset
          end

          changeset = if is_nil(user_max_claims) && !Ash.Changeset.changing_attribute?(changeset, :max_claims) do
            Ash.Changeset.change_attribute(changeset, :max_claims, default_max_claims)
          else
            changeset
          end

          changeset
        else
          changeset
        end
      end
    end

    read :list_active_gifts do
      filter expr(status == :enabled)
    end

    read :get_by_vip_level do
      argument :vip_level, :integer, allow_nil?: false
      get? true
      filter expr(vip_level == ^arg(:vip_level) and status == :enabled)
    end

    update :enable_gift do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_gift do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end

    read :list_by_task_type do
      argument :task_type, :atom, allow_nil?: false
      filter expr(task_type == ^arg(:task_type) and status == :enabled)
      prepare build(sort: [vip_level: :asc])
    end

    read :list_by_reward_type do
      argument :reward_type, :atom, allow_nil?: false
      filter expr(reward_type == ^arg(:reward_type) and status == :enabled)
      prepare build(sort: [vip_level: :asc])
    end

    read :list_by_date_range do
      argument :start_date, :date, allow_nil?: true
      argument :end_date, :date, allow_nil?: true

      filter expr(
        status == :enabled and
        (is_nil(^arg(:start_date)) or is_nil(start_date) or start_date >= ^arg(:start_date)) and
        (is_nil(^arg(:end_date)) or is_nil(end_date) or end_date <= ^arg(:end_date))
      )
      prepare build(sort: [vip_level: :asc])
    end

    read :list_ordered_by_vip_level do
      filter expr(status == :enabled)
      prepare build(sort: [vip_level: :asc])
    end

    read :get_vip_gifts_for_level_range do
      argument :min_level, :integer, allow_nil?: false
      argument :max_level, :integer, allow_nil?: false

      filter expr(
        status == :enabled and
        vip_level >= ^arg(:min_level) and
        vip_level <= ^arg(:max_level)
      )
      prepare build(sort: [vip_level: :asc])
    end

    read :list_high_value_gifts do
      argument :min_value, :integer, allow_nil?: false

      filter expr(status == :enabled)
      prepare build(sort: [vip_level: :asc])

      # 这个查询需要在应用层进一步过滤，因为 total_monthly_value 是计算字段
    end

    update :batch_enable_gifts do
      argument :vip_levels, {:array, :integer}, allow_nil?: false
      require_atomic? false

      filter expr(vip_level in ^arg(:vip_levels))
      change set_attribute(:status, :enabled)
    end

    update :batch_disable_gifts do
      argument :vip_levels, {:array, :integer}, allow_nil?: false
      require_atomic? false

      filter expr(vip_level in ^arg(:vip_levels))
      change set_attribute(:status, :disabled)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? true
      public? true
      description "VIP礼包标题"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "VIP礼包描述"
      constraints max_length: 500
    end

    attribute :task_type, :atom do
      allow_nil? false
      public? true
      description "VIP礼包类型（定义礼包的发放频率和触发条件）"
      constraints one_of: [
        :vip_daily_login,      # VIP每日登录礼包
        :vip_weekly_login,     # VIP每周登录礼包
        :vip_monthly_login,    # VIP每月登录礼包
        :vip_level_upgrade,    # VIP等级升级礼包
        :vip_birthday,         # VIP生日礼包
        :vip_anniversary,      # VIP周年礼包
        :vip_recharge_bonus,   # VIP充值返利礼包
        :vip_special_event     # VIP特殊活动礼包
      ]
      default :vip_daily_login
    end

    attribute :vip_level, :integer do
      allow_nil? false
      public? true
      description "VIP等级"
      constraints min: 0
      default 1
    end

    attribute :reward_amount, :integer do
      allow_nil? false
      public? true
      description "奖励金额（以分为单位）"
      constraints min: 0
      default 0
    end

    attribute :reward_frequency, :integer do
      allow_nil? true
      public? true
      description "奖励发放频率（天数，空值表示一次性奖励）"
      constraints min: 1
      default nil
    end

    attribute :max_claims, :integer do
      allow_nil? true
      public? true
      description "最大领取次数（空值表示无限制）"
      constraints min: 1
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :points, :cash, :wheel_spins, :items]
      default :coins
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "开始日期"
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "结束日期"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  calculations do
    calculate :vip_level_display, :string do
      public? true
      description "VIP等级显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          "VIP#{record.vip_level}"
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :task_type_display, :string do
      public? true
      description "任务类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.task_type do
            :vip_daily_login -> "每日登录礼包"
            :vip_weekly_login -> "每周登录礼包"
            :vip_monthly_login -> "每月登录礼包"
            :vip_level_upgrade -> "等级升级礼包"
            :vip_birthday -> "生日专属礼包"
            :vip_anniversary -> "周年庆典礼包"
            :vip_recharge_bonus -> "充值返利礼包"
            :vip_special_event -> "特殊活动礼包"
            _ -> "未知类型"
          end
        end)
      end
    end

    calculate :reward_type_display, :string do
      public? true
      description "奖励类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.reward_type do
            :coins -> "金币"
            :points -> "积分"
            :cash -> "现金"
            :wheel_spins -> "转盘次数"
            :items -> "道具"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :reward_amount_display, :string do
      public? true
      description "奖励金额显示（转换为元）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.reward_type do
            :coins -> "#{record.reward_amount}金币"
            :points -> "#{record.reward_amount}积分"
            :cash -> "¥#{Float.round(record.reward_amount / 100, 2)}"
            :wheel_spins -> "#{record.reward_amount}次"
            :items -> "#{record.reward_amount}个"
            _ -> "#{record.reward_amount}"
          end
        end)
      end
    end

    calculate :total_monthly_value, :integer do
      public? true
      description "月度总价值（基于奖励频率计算）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.reward_frequency do
            1 -> record.reward_amount * 30    # 每日奖励 × 30天
            7 -> record.reward_amount * 4     # 每周奖励 × 4周
            30 -> record.reward_amount        # 每月奖励
            365 -> div(record.reward_amount, 12)  # 每年奖励 ÷ 12个月
            nil -> 0                          # 一次性奖励不计入月度价值
            n when is_integer(n) -> div(record.reward_amount * 30, n)  # 自定义频率
            _ -> 0
          end
        end)
      end
    end

    calculate :reward_summary, :string do
      public? true
      description "奖励摘要"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if record.reward_amount > 0 do
            frequency_part = case record.reward_frequency do
              1 -> "每日"
              7 -> "每周"
              30 -> "每月"
              365 -> "每年"
              nil -> "一次性"
              n when is_integer(n) -> "每#{n}天"
              _ -> ""
            end

            claims_part = case record.max_claims do
              nil -> "无限制"
              1 -> "限1次"
              n when is_integer(n) and n > 0 -> "限#{n}次"
              _ -> "无限制"
            end

            "#{frequency_part}#{record.reward_amount}（#{claims_part}）"
          else
            "无奖励"
          end
        end)
      end
    end

    calculate :primary_reward_display, :string do
      public? true
      description "主要奖励显示（根据任务类型和频率）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if record.reward_amount > 0 do
            frequency_text = case record.reward_frequency do
              1 -> "每日"
              7 -> "每周"
              30 -> "每月"
              365 -> "每年"
              nil -> "一次性"
              n when is_integer(n) -> "每#{n}天"
              _ -> ""
            end

            claim_limit_text = case record.max_claims do
              nil -> ""
              1 -> "（限1次）"
              n when is_integer(n) and n > 0 -> "（限#{n}次）"
              _ -> ""
            end

            "#{frequency_text}#{record.reward_amount}#{claim_limit_text}"
          else
            "无奖励"
          end
        end)
      end
    end

    calculate :value_tier, :string do
      public? true
      description "价值等级（基于月度总价值）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          # 计算月度总价值
          total_value = case record.reward_frequency do
            1 -> record.reward_amount * 30    # 每日奖励 × 30天
            7 -> record.reward_amount * 4     # 每周奖励 × 4周
            30 -> record.reward_amount        # 每月奖励
            365 -> div(record.reward_amount, 12)  # 每年奖励 ÷ 12个月
            nil -> 0                          # 一次性奖励不计入月度价值
            n when is_integer(n) -> div(record.reward_amount * 30, n)  # 自定义频率
            _ -> 0
          end

          cond do
            total_value >= 100000 -> "超高价值"  # 1000元以上
            total_value >= 50000 -> "高价值"    # 500-1000元
            total_value >= 10000 -> "中等价值"  # 100-500元
            total_value >= 1000 -> "低价值"     # 10-100元
            total_value > 0 -> "基础价值"       # 0-10元
            true -> "无价值"
          end
        end)
      end
    end
  end

  validations do
    validate compare(:vip_level, greater_than_or_equal_to: 0),
      message: "VIP等级不能为负数"

    validate compare(:reward_amount, greater_than_or_equal_to: 0),
      message: "奖励金额不能为负数"

    validate compare(:reward_frequency, greater_than_or_equal_to: 1),
      message: "奖励频率必须为正数",
      where: [present(:reward_frequency)]

    validate compare(:max_claims, greater_than_or_equal_to: 1),
      message: "最大领取次数必须为正数",
      where: [present(:max_claims)]

    # 验证日期范围的合理性
    validate fn changeset, _context ->
      start_date = Ash.Changeset.get_attribute(changeset, :start_date)
      end_date = Ash.Changeset.get_attribute(changeset, :end_date)

      cond do
        is_nil(start_date) or is_nil(end_date) -> :ok
        Date.compare(start_date, end_date) == :gt ->
          {:error, field: :end_date, message: "结束日期不能早于开始日期"}
        true -> :ok
      end
    end
  end

  # 移除 vip_level 的唯一性约束，因为同一个 VIP 等级可以有多个礼包
  # identities do
  #   identity :unique_vip_level, [:vip_level]
  # end
end
