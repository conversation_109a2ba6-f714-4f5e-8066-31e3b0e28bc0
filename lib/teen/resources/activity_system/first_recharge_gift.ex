defmodule Teen.ActivitySystem.FirstRechargeGift do
  @moduledoc """
  首充礼包资源（Sale）

  管理新手福利首充礼包配置
  根据用户注册天数限制，提供首充奖励
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :title, :task_type, :description, :limit_days, :reward_amount, :reward_type, :start_date, :end_date, :status, :updated_at]
  end

  postgres do
    table "first_recharge_gifts"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_gifts
    define :list_all_gifts
    define :get_by_user_days
    define :get_by_task_type
    define :get_current_active_gifts
    define :enable_gift
    define :disable_gift
    define :batch_enable_gifts
    define :batch_disable_gifts
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:title, :description, :task_type, :limit_days, :reward_amount, :reward_type, :start_date, :end_date, :status]

      validate present(:title), message: "标题不能为空"
      validate present(:reward_amount), message: "奖励金额不能为空"
      validate present(:limit_days), message: "限制天数不能为空"

      validate compare(:reward_amount, greater_than: 0), message: "奖励金额必须大于0"
      validate compare(:limit_days, greater_than: 0), message: "限制天数必须大于0"
      validate compare(:limit_days, less_than_or_equal_to: 365), message: "限制天数不能超过365天"

      # 验证日期逻辑
      validate fn changeset, _context ->
        start_date = Ash.Changeset.get_attribute(changeset, :start_date)
        end_date = Ash.Changeset.get_attribute(changeset, :end_date)

        cond do
          is_nil(start_date) and is_nil(end_date) -> :ok
          is_nil(start_date) or is_nil(end_date) -> :ok
          Date.compare(start_date, end_date) == :gt ->
            {:error, field: :end_date, message: "结束日期不能早于开始日期"}
          true -> :ok
        end
      end

      # 设置默认值（仅在用户未提供时）
      change fn changeset, _context ->
        changeset
        |> maybe_set_default(:status, :enabled)
        |> maybe_set_default(:reward_type, :coins)
        |> maybe_set_default(:task_type, :first_recharge)
      end
    end

    update :update do
      accept [:title, :description, :task_type, :limit_days, :reward_amount, :reward_type, :start_date, :end_date, :status]
      require_atomic? false

      validate present(:title), message: "标题不能为空"
      validate present(:reward_amount), message: "奖励金额不能为空"
      validate present(:limit_days), message: "限制天数不能为空"

      validate compare(:reward_amount, greater_than: 0), message: "奖励金额必须大于0"
      validate compare(:limit_days, greater_than: 0), message: "限制天数必须大于0"
      validate compare(:limit_days, less_than_or_equal_to: 365), message: "限制天数不能超过365天"

      # 验证日期逻辑
      validate fn changeset, _context ->
        start_date = Ash.Changeset.get_attribute(changeset, :start_date)
        end_date = Ash.Changeset.get_attribute(changeset, :end_date)

        cond do
          is_nil(start_date) and is_nil(end_date) -> :ok
          is_nil(start_date) or is_nil(end_date) -> :ok
          Date.compare(start_date, end_date) == :gt ->
            {:error, field: :end_date, message: "结束日期不能早于开始日期"}
          true -> :ok
        end
      end
    end

    read :list_active_gifts do
      filter expr(status == :enabled)
      prepare build(sort: [inserted_at: :desc])
    end

    read :list_all_gifts do
      prepare build(sort: [inserted_at: :desc])
    end

    read :get_by_user_days do
      argument :user_days, :integer, allow_nil?: false
      filter expr(limit_days >= ^arg(:user_days) and status == :enabled)
      prepare build(sort: [reward_amount: :desc])
    end

    read :get_by_task_type do
      argument :task_type, :atom, allow_nil?: false
      filter expr(task_type == ^arg(:task_type) and status == :enabled)
      prepare build(sort: [reward_amount: :desc])
    end

    read :get_current_active_gifts do
      filter expr(
        status == :enabled and
        (is_nil(start_date) or start_date <= ^Date.utc_today()) and
        (is_nil(end_date) or end_date >= ^Date.utc_today())
      )
      prepare build(sort: [reward_amount: :desc])
    end

    update :enable_gift do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_gift do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end

    update :batch_enable_gifts do
      require_atomic? false
      argument :gift_ids, {:array, :uuid}, allow_nil?: false
      filter expr(id in ^arg(:gift_ids))
      change set_attribute(:status, :enabled)
    end

    update :batch_disable_gifts do
      require_atomic? false
      argument :gift_ids, {:array, :uuid}, allow_nil?: false
      filter expr(id in ^arg(:gift_ids))
      change set_attribute(:status, :disabled)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "标题"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "活动描述"
      constraints max_length: 500
    end

    attribute :task_type, :atom do
      allow_nil? false
      public? true
      description "任务类型"
      constraints one_of: [:first_recharge, :second_recharge, :third_recharge, :any_recharge]
      default :first_recharge
    end

    attribute :limit_days, :integer do
      allow_nil? false
      public? true
      description "限制天数（注册天数）"
      constraints min: 1, max: 365
      default 7
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :points, :cash, :wheel_spins, :items]
      default :coins
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "开始日期"
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "结束日期"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  calculations do
    calculate :is_active, :boolean do
      public? true
      description "是否当前激活"

      calculation fn records, _context ->
        today = Date.utc_today()

        Enum.map(records, fn record ->
          record.status == :enabled and
          (is_nil(record.start_date) or Date.compare(record.start_date, today) != :gt) and
          (is_nil(record.end_date) or Date.compare(record.end_date, today) != :lt)
        end)
      end
    end

    calculate :reward_amount_yuan, :decimal do
      public? true
      description "奖励金额（元）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          Decimal.div(record.reward_amount, 100)
        end)
      end
    end

    calculate :task_type_display, :string do
      public? true
      description "任务类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.task_type do
            :first_recharge -> "首次充值"
            :second_recharge -> "第二次充值"
            :third_recharge -> "第三次充值"
            :any_recharge -> "任意充值"
            _ -> to_string(record.task_type)
          end
        end)
      end
    end

    calculate :reward_type_display, :string do
      public? true
      description "奖励类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.reward_type do
            :coins -> "金币"
            :points -> "积分"
            :cash -> "现金"
            :wheel_spins -> "转盘次数"
            :items -> "道具"
            _ -> to_string(record.reward_type)
          end
        end)
      end
    end
  end

  # 私有辅助函数
  defp maybe_set_default(changeset, field, default_value) do
    if Ash.Changeset.changing_attribute?(changeset, field) do
      changeset
    else
      Ash.Changeset.change_attribute(changeset, field, default_value)
    end
  end
end
