defmodule Teen.ActivitySystem.BindingReward do
  @moduledoc """
  手机、邮箱绑定奖励资源

  管理绑定奖励配置
  用户绑定手机或邮箱后可获得奖励
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :title, :binding_type, :reward_amount, :reward_type, :one_time_only, :status, :updated_at]
  end

  postgres do
    table "binding_rewards"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_rewards
    define :get_by_binding_type
    define :enable_reward
    define :disable_reward
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:title, :binding_type, :task_type, :reward_amount, :reward_type, :one_time_only, :verification_required, :description, :start_date, :end_date, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
        |> Ash.Changeset.change_attribute(:task_type, :binding_verification)
      end
    end

    update :update do
      accept [:title, :binding_type, :task_type, :reward_amount, :reward_type, :one_time_only, :verification_required, :description, :start_date, :end_date, :status]
      require_atomic? false
    end

    read :list_active_rewards do
      filter expr(status == :enabled)
    end

    read :get_by_binding_type do
      argument :binding_type, :atom, allow_nil?: false
      get? true
      filter expr(binding_type == ^arg(:binding_type) and status == :enabled)
    end

    update :enable_reward do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_reward do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "标题"
      constraints max_length: 100
    end

    attribute :binding_type, :atom do
      allow_nil? false
      public? true
      description "绑定类型"
      constraints one_of: [:phone, :email, :bank_card, :id_card]
    end

    attribute :task_type, :atom do
      allow_nil? false
      public? true
      description "任务类型"
      constraints one_of: [:binding_verification, :binding_completion, :binding_upgrade, :binding_maintenance]
      default :binding_verification
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :points, :cash, :wheel_spins, :items]
      default :coins
    end

    attribute :one_time_only, :boolean do
      allow_nil? false
      public? true
      description "仅限一次"
      default true
    end

    attribute :verification_required, :boolean do
      allow_nil? false
      public? true
      description "需要验证"
      default true
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "描述"
      constraints max_length: 500
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "开始日期"
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "结束日期"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  calculations do
    calculate :binding_type_display, :string do
      public? true
      description "绑定类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.binding_type do
            :phone -> "手机号"
            :email -> "邮箱"
            :bank_card -> "银行卡"
            :id_card -> "身份证"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :reward_type_display, :string do
      public? true
      description "奖励类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.reward_type do
            :coins -> "金币"
            :points -> "积分"
            :cash -> "现金"
            :wheel_spins -> "转盘次数"
            :items -> "道具"
            _ -> "未设置"
          end
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end
  end

  identities do
    identity :unique_binding_type, [:binding_type]
  end

  validations do
    validate compare(:reward_amount, greater_than_or_equal_to: Decimal.new("0")), message: "奖励金额不能为负数"
  end
end
