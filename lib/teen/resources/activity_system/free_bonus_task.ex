defmodule Teen.ActivitySystem.FreeBonusTask do
  @moduledoc """
  免费任务（Free bonus）资源

  管理分享任务配置
  包括分享次数、游戏要求、需要赢金币、提现次数等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :title, :share_count, :game_id, :required_win_coins, :withdraw_count, :status, :updated_at]
  end

  postgres do
    table "free_bonus_tasks"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_tasks
    define :get_by_game
    define :enable_task
    define :disable_task
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:title, :description, :task_type, :reward_amount, :reward_type, :share_count, :game_id, :game_name, :required_win_coins, :withdraw_count, :start_date, :end_date, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
        |> Ash.Changeset.change_attribute(:task_type, :share_task)
        |> Ash.Changeset.change_attribute(:reward_type, :coins)
      end
    end

    update :update do
      accept [:title, :description, :task_type, :reward_amount, :reward_type, :share_count, :game_id, :game_name, :required_win_coins, :withdraw_count, :start_date, :end_date, :status]
      require_atomic? false
    end

    read :list_active_tasks do
      filter expr(status == :enabled)
    end

    read :get_by_game do
      argument :game_id, :string, allow_nil?: false
      filter expr(game_id == ^arg(:game_id) and status == :enabled)
    end

    update :enable_task do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_task do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "标题"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "任务描述"
      constraints max_length: 500
    end

    attribute :task_type, :atom do
      allow_nil? false
      public? true
      description "任务类型"
      constraints one_of: [:share_task, :game_task, :win_task, :withdraw_task]
      default :share_task
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :points, :cash, :wheel_spins, :items]
      default :coins
    end

    attribute :share_count, :integer do
      allow_nil? false
      public? true
      description "分享次数"
      constraints min: 1
      default 1
    end

    attribute :game_id, :string do
      allow_nil? true
      public? true
      description "游戏ID"
      constraints max_length: 50
    end

    attribute :game_name, :string do
      allow_nil? true
      public? true
      description "游戏名称"
      constraints max_length: 100
    end

    attribute :required_win_coins, :decimal do
      allow_nil? false
      public? true
      description "需要赢金币（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :withdraw_count, :integer do
      allow_nil? false
      public? true
      description "提现次数"
      constraints min: 1
      default 1
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "开始日期"
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "结束日期"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end
end
