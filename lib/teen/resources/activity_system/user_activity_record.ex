defmodule Teen.ActivitySystem.UserActivityRecord do
  @moduledoc """
  用户活动记录资源

  记录用户参与各种活动的情况，包括：
  - 参与时间
  - 活动类型
  - 活动ID
  - 参与数据
  - 奖励领取状态
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :user_id, :activity_type, :activity_id, :reward_claimed, :claimed_at, :inserted_at]
  end

  postgres do
    table "user_activity_records"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_user
    define :list_by_activity
    define :list_unclaimed_rewards
    define :claim_reward
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:user_id, :activity_type, :activity_id, :participation_data, :reward_claimed]
      
      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:reward_claimed, false)
      end
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end

    read :list_by_activity do
      argument :activity_type, :string, allow_nil?: false
      argument :activity_id, :uuid, allow_nil?: false
      filter expr(activity_type == ^arg(:activity_type) and activity_id == ^arg(:activity_id))
    end

    read :list_unclaimed_rewards do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id) and reward_claimed == false)
    end

    update :claim_reward do
      require_atomic? false
      
      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:reward_claimed, true)
        |> Ash.Changeset.change_attribute(:claimed_at, DateTime.utc_now())
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :activity_type, :string do
      allow_nil? false
      public? true
      description "活动类型"
      constraints max_length: 50
    end

    attribute :activity_id, :uuid do
      allow_nil? false
      public? true
      description "活动ID"
    end

    attribute :participation_data, :map do
      allow_nil? true
      public? true
      description "参与数据（JSON格式）"
      default %{}
    end

    attribute :reward_claimed, :boolean do
      allow_nil? false
      public? true
      description "奖励是否已领取"
      default false
    end

    attribute :claimed_at, :utc_datetime do
      allow_nil? true
      public? true
      description "奖励领取时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end

  calculations do
    calculate :activity_type_display, :string do
      public? true
      description "活动类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.activity_type do
            "game_task" -> "游戏任务"
            "weekly_card" -> "周卡任务"
            "seven_day_task" -> "七次任务"
            "vip_gift" -> "VIP礼包"
            "recharge_task" -> "充值任务"
            "recharge_wheel" -> "充值转盘"
            "scratch_card" -> "刮刮卡任务"
            "first_recharge_gift" -> "首充礼包"
            "loss_refund" -> "输钱返利"
            "invite_cash" -> "邀请提现"
            "binding_reward" -> "绑定奖励"
            "free_bonus" -> "免费任务"
            "cdkey_activity" -> "CDKey活动"
            _ -> record.activity_type
          end
        end)
      end
    end

    calculate :reward_status_display, :string do
      public? true
      description "奖励状态显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if record.reward_claimed, do: "已领取", else: "未领取"
        end)
      end
    end

    calculate :participation_summary, :string do
      public? true
      description "参与情况摘要"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          data = record.participation_data || %{}
          
          case record.activity_type do
            "game_task" ->
              "完成#{data["completed_count"] || 0}局"
            "weekly_card" ->
              "第#{data["day"] || 1}天"
            "seven_day_task" ->
              "连续登录#{data["consecutive_days"] || 1}天"
            _ ->
              "已参与"
          end
        end)
      end
    end
  end

  validations do
    validate present(:user_id), message: "用户ID不能为空"
    validate present(:activity_type), message: "活动类型不能为空"
    validate present(:activity_id), message: "活动ID不能为空"
  end

  identities do
    identity :unique_user_activity, [:user_id, :activity_type, :activity_id]
  end
end
