defmodule Teen.ActivitySystem.RechargeTask do
  @moduledoc """
  充值任务资源

  管理充值奖励配置
  规则：充值X金额奖励Y金币/积分/现金等
  支持多种奖励类型：金币、积分、现金、转盘次数、道具
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :title, :task_type, :recharge_amount, :reward_amount, :reward_type, :priority, :start_date, :end_date, :status, :updated_at]
  end

  postgres do
    table "recharge_tasks"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_tasks
    define :list_by_amount_range
    define :list_by_reward_type
    define :list_by_task_type
    define :get_best_reward_for_amount
    define :get_reward_for_amount
    define :enable_task
    define :disable_task
    define :batch_enable_tasks
    define :batch_disable_tasks
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:title, :description, :task_type, :recharge_amount, :reward_amount, :reward_type, :priority, :max_claims, :start_date, :end_date, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
        |> Ash.Changeset.change_attribute(:reward_type, :coins)
        |> Ash.Changeset.change_attribute(:task_type, :single_recharge)
        |> Ash.Changeset.change_attribute(:priority, 0)
      end
    end

    update :update do
      accept [:title, :description, :task_type, :recharge_amount, :reward_amount, :reward_type, :priority, :max_claims, :start_date, :end_date, :status]
      require_atomic? false
    end

    read :list_active_tasks do
      filter expr(status == :enabled)
    end

    read :list_by_amount_range do
      argument :min_amount, :decimal, allow_nil?: false
      argument :max_amount, :decimal, allow_nil?: false
      filter expr(recharge_amount >= ^arg(:min_amount) and recharge_amount <= ^arg(:max_amount) and status == :enabled)
    end

    read :list_by_reward_type do
      argument :reward_type, :atom, allow_nil?: false
      filter expr(reward_type == ^arg(:reward_type) and status == :enabled)
    end

    read :list_by_task_type do
      argument :task_type, :atom, allow_nil?: false
      filter expr(task_type == ^arg(:task_type) and status == :enabled)
    end

    read :get_best_reward_for_amount do
      argument :recharge_amount, :decimal, allow_nil?: false
      filter expr(recharge_amount <= ^arg(:recharge_amount) and status == :enabled)
      prepare build(sort: [reward_amount: :desc], limit: 1)
    end

    read :get_reward_for_amount do
      argument :recharge_amount, :decimal, allow_nil?: false
      filter expr(recharge_amount <= ^arg(:recharge_amount) and status == :enabled)
    end

    update :enable_task do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_task do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end

    update :batch_enable_tasks do
      require_atomic? false
      argument :task_ids, {:array, :uuid}, allow_nil?: false
      filter expr(id in ^arg(:task_ids))
      change set_attribute(:status, :enabled)
    end

    update :batch_disable_tasks do
      require_atomic? false
      argument :task_ids, {:array, :uuid}, allow_nil?: false
      filter expr(id in ^arg(:task_ids))
      change set_attribute(:status, :disabled)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "充值金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :points, :cash, :wheel_spins, :items]
      default :coins
    end

    attribute :title, :string do
      allow_nil? true
      public? true
      description "任务标题"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "任务描述"
      constraints max_length: 500
    end

    attribute :task_type, :atom do
      allow_nil? false
      public? true
      description "任务类型"
      constraints one_of: [:single_recharge, :cumulative_recharge, :consecutive_recharge, :first_recharge, :daily_recharge]
      default :single_recharge
    end

    attribute :priority, :integer do
      allow_nil? false
      public? true
      description "优先级（数字越大优先级越高）"
      default 0
    end

    attribute :max_claims, :integer do
      allow_nil? true
      public? true
      description "最大领取次数（null表示无限制）"
      constraints min: 1
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "开始日期"
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "结束日期"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  calculations do
    calculate :reward_rate, :decimal do
      public? true
      description "奖励比例（奖励金额/充值金额）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if Decimal.compare(record.recharge_amount, Decimal.new("0")) == :gt do
            Decimal.div(record.reward_amount, record.recharge_amount)
            |> Decimal.mult(Decimal.new("100"))
            |> Decimal.round(2)
          else
            Decimal.new("0")
          end
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :task_type_display, :string do
      public? true
      description "任务类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.task_type do
            :single_recharge -> "单次充值"
            :cumulative_recharge -> "累计充值"
            :consecutive_recharge -> "连续充值"
            :first_recharge -> "首次充值"
            :daily_recharge -> "每日充值"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :reward_type_display, :string do
      public? true
      description "奖励类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.reward_type do
            :coins -> "金币"
            :points -> "积分"
            :cash -> "现金"
            :wheel_spins -> "转盘次数"
            :items -> "道具"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :task_description, :string do
      public? true
      description "任务描述"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          recharge_yuan = Decimal.div(record.recharge_amount, Decimal.new("100"))
          reward_yuan = Decimal.div(record.reward_amount, Decimal.new("100"))

          task_type_name = case record.task_type do
            :single_recharge -> "单次充值"
            :cumulative_recharge -> "累计充值"
            :consecutive_recharge -> "连续充值"
            :first_recharge -> "首次充值"
            :daily_recharge -> "每日充值"
            _ -> "充值"
          end

          reward_type_name = case record.reward_type do
            :coins -> "金币"
            :points -> "积分"
            :cash -> "现金"
            :wheel_spins -> "转盘次数"
            :items -> "道具"
            _ -> "奖励"
          end

          "#{task_type_name}#{recharge_yuan}元奖励#{reward_yuan}#{reward_type_name}"
        end)
      end
    end

    calculate :is_active, :boolean do
      public? true
      description "是否在活动期间"

      calculation fn records, _context ->
        today = Date.utc_today()
        Enum.map(records, fn record ->
          start_valid = is_nil(record.start_date) or Date.compare(today, record.start_date) != :lt
          end_valid = is_nil(record.end_date) or Date.compare(today, record.end_date) != :gt
          start_valid and end_valid and record.status == :enabled
        end)
      end
    end

    calculate :activity_period, :string do
      public? true
      description "活动期间"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case {record.start_date, record.end_date} do
            {nil, nil} -> "长期有效"
            {start_date, nil} -> "#{start_date}起长期有效"
            {nil, end_date} -> "至#{end_date}"
            {start_date, end_date} -> "#{start_date}至#{end_date}"
          end
        end)
      end
    end

    calculate :efficiency_score, :decimal do
      public? true
      description "效率评分（奖励率×优先级）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if Decimal.compare(record.recharge_amount, Decimal.new("0")) == :gt do
            reward_rate = Decimal.div(record.reward_amount, record.recharge_amount)
            priority_decimal = Decimal.new(record.priority)
            Decimal.mult(reward_rate, priority_decimal)
            |> Decimal.round(4)
          else
            Decimal.new("0")
          end
        end)
      end
    end
  end

  validations do
    validate compare(:recharge_amount, greater_than: Decimal.new("0")), message: "充值金额必须大于0"
    validate compare(:reward_amount, greater_than_or_equal_to: Decimal.new("0")), message: "奖励金额不能为负数"
    validate compare(:priority, greater_than_or_equal_to: 0), message: "优先级不能为负数"

    validate attribute_in(:reward_type, [:coins, :points, :cash, :wheel_spins, :items]), message: "奖励类型必须是有效的类型之一"

    validate fn changeset, _context ->
      start_date = Ash.Changeset.get_attribute(changeset, :start_date)
      end_date = Ash.Changeset.get_attribute(changeset, :end_date)

      case {start_date, end_date} do
        {nil, _} -> :ok
        {_, nil} -> :ok
        {start_date, end_date} when is_struct(start_date, Date) and is_struct(end_date, Date) ->
          if Date.compare(start_date, end_date) == :gt do
            {:error, field: :end_date, message: "结束日期不能早于开始日期"}
          else
            :ok
          end
        _ -> :ok
      end
    end
  end

  identities do
    identity :unique_recharge_amount_and_type, [:recharge_amount, :reward_type]
  end

  preparations do
    prepare build(sort: [priority: :desc, recharge_amount: :asc])
  end
end
