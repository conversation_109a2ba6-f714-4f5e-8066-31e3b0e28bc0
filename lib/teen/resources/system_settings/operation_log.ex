defmodule Teen.SystemSettings.OperationLog do
  @moduledoc """
  操作日志资源

  记录管理员的操作日志，包括登录、数据修改等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.SystemSettings,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :admin_user_id,
      :operation_type,
      :module,
      :description,
      :ip_address,
      :inserted_at
    ]
  end

  postgres do
    table "operation_logs"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :list_by_admin
    define :list_by_type
    define :list_by_module
    define :list_by_date_range
  end

  actions do
    defaults [:create, :read]

    read :list_by_admin do
      argument :admin_user_id, :uuid, allow_nil?: false
      filter expr(admin_user_id == ^arg(:admin_user_id))
      prepare build(sort: [inserted_at: :desc])
    end

    read :list_by_type do
      argument :operation_type, :string, allow_nil?: false
      filter expr(operation_type == ^arg(:operation_type))
      prepare build(sort: [inserted_at: :desc])
    end

    read :list_by_module do
      argument :module, :string, allow_nil?: false
      filter expr(module == ^arg(:module))
      prepare build(sort: [inserted_at: :desc])
    end

    read :list_by_date_range do
      argument :start_date, :utc_datetime, allow_nil?: false
      argument :end_date, :utc_datetime, allow_nil?: false
      filter expr(inserted_at >= ^arg(:start_date) and inserted_at <= ^arg(:end_date))
      prepare build(sort: [inserted_at: :desc])
    end

    create :log_operation do
      accept [
        :admin_user_id,
        :operation_type,
        :module,
        :description,
        :ip_address,
        :user_agent,
        :request_data,
        :response_data
      ]
    end

    create :log_login do
      accept [:admin_user_id, :ip_address, :user_agent, :login_result]
      change set_attribute(:operation_type, "login")
      change set_attribute(:module, "auth")
    end

    create :log_logout do
      accept [:admin_user_id, :ip_address]
      change set_attribute(:operation_type, "logout")
      change set_attribute(:module, "auth")
      change set_attribute(:description, "管理员退出登录")
    end
  end

  validations do
    validate present(:login_result) do
      where attribute_equals(:operation_type, "login")
      message "登录操作必须提供登录结果"
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :admin_user_id, :uuid do
      allow_nil? false
      public? true
      description "管理员用户ID"
    end

    attribute :operation_type, :string do
      allow_nil? false
      public? true
      description "操作类型：login=登录，logout=退出，create=创建，update=更新，delete=删除，view=查看"
      constraints max_length: 20
    end

    attribute :module, :string do
      allow_nil? false
      public? true
      description "操作模块"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? false
      public? true
      description "操作描述"
      constraints max_length: 500
    end

    attribute :ip_address, :string do
      allow_nil? true
      public? true
      description "操作IP地址"
      constraints max_length: 45
    end

    attribute :user_agent, :string do
      allow_nil? true
      public? true
      description "用户代理"
      constraints max_length: 500
    end

    attribute :request_data, :map do
      allow_nil? true
      public? true
      description "请求数据"
      default %{}
    end

    attribute :response_data, :map do
      allow_nil? true
      public? true
      description "响应数据"
      default %{}
    end

    attribute :execution_time, :integer do
      allow_nil? true
      public? true
      description "执行时间（毫秒）"
    end

    attribute :login_result, :string do
      allow_nil? true
      public? true
      description "登录结果：success=成功，failed=失败"
      constraints max_length: 20
    end

    attribute :error_message, :string do
      allow_nil? true
      public? true
      description "错误信息"
      constraints max_length: 1000
    end

    timestamps()
  end

  relationships do
    belongs_to :admin_user, Teen.SystemSettings.AdminUser do
      attribute_writable? true
    end
  end

  calculations do
    calculate :is_login_operation, :boolean, expr(operation_type == "login")

    calculate :is_successful_login,
              :boolean,
              expr(operation_type == "login" and login_result == "success")

    calculate :is_failed_login,
              :boolean,
              expr(operation_type == "login" and login_result == "failed")

    calculate :operation_date, :date, expr(fragment("DATE(?)", inserted_at))
  end
end
