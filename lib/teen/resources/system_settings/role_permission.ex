defmodule Teen.SystemSettings.RolePermission do
  @moduledoc """
  角色权限关联资源

  管理角色和权限的多对多关系
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.SystemSettings,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :role_id, :permission_id, :granted_by, :inserted_at]
  end

  postgres do
    table "role_permissions"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :destroy
    define :list_by_role
    define :list_by_permission
    define :grant_permission
    define :revoke_permission
  end

  actions do
    defaults [:create, :read, :destroy]

    read :list_by_role do
      argument :role_id, :uuid, allow_nil?: false
      filter expr(role_id == ^arg(:role_id))
    end

    read :list_by_permission do
      argument :permission_id, :uuid, allow_nil?: false
      filter expr(permission_id == ^arg(:permission_id))
    end

    create :grant_permission do
      accept [:role_id, :permission_id, :granted_by]
      description "为角色授予权限"
    end

    destroy :revoke_permission do
      description "撤销角色权限"
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :role_id, :uuid do
      allow_nil? false
      public? true
      description "角色ID"
    end

    attribute :permission_id, :uuid do
      allow_nil? false
      public? true
      description "权限ID"
    end

    attribute :granted_by, :uuid do
      allow_nil? true
      public? true
      description "授权者ID"
    end

    attribute :granted_at, :utc_datetime do
      allow_nil? false
      public? true
      description "授权时间"
      default &DateTime.utc_now/0
    end

    timestamps()
  end

  relationships do
    belongs_to :role, Teen.SystemSettings.Role do
      attribute_writable? true
    end

    belongs_to :permission, Teen.SystemSettings.Permission do
      attribute_writable? true
    end

    belongs_to :granter, Teen.SystemSettings.AdminUser do
      source_attribute :granted_by
      attribute_writable? true
    end
  end

  identities do
    identity :unique_role_permission, [:role_id, :permission_id]
  end
end
