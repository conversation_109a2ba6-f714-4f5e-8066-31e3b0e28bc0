defmodule Teen.SystemSettings.IpWhitelist do
  @moduledoc """
  IP白名单资源

  管理允许访问后台的IP地址白名单
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.SystemSettings,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :ip_address,
      :ip_range,
      :type,
      :status,
      :description,
      :created_by,
      :inserted_at
    ]
  end

  postgres do
    table "ip_whitelists"
    repo Cypridina.Repo

    identity_wheres_to_sql unique_ip_address: "type = 'single'",
                           unique_ip_range: "type = 'range'"
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_status
    define :list_by_type
    define :check_ip_allowed
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_by_status do
      argument :status, :integer, allow_nil?: false
      filter expr(status == ^arg(:status))
    end

    read :list_by_type do
      argument :type, :string, allow_nil?: false
      filter expr(type == ^arg(:type))
    end

    read :check_ip_allowed do
      argument :ip_address, :string, allow_nil?: false
      description "检查IP是否在白名单中"

      filter expr(
               status == 1 and
                 (ip_address == ^arg(:ip_address) or
                    fragment("? <<= ?", ^arg(:ip_address), ip_range))
             )
    end

    update :activate do
      accept []
      change set_attribute(:status, 1)
      change set_attribute(:updated_at, &DateTime.utc_now/0)
    end

    update :deactivate do
      accept []
      change set_attribute(:status, 0)
      change set_attribute(:updated_at, &DateTime.utc_now/0)
    end

    create :add_single_ip do
      accept [:ip_address, :description, :created_by]
      change set_attribute(:type, "single")
      change set_attribute(:status, 1)
    end

    create :add_ip_range do
      accept [:ip_range, :description, :created_by]
      change set_attribute(:type, "range")
      change set_attribute(:status, 1)
    end
  end

  validations do
    validate present(:ip_address) do
      where type: [equal_to: "single"]
      message "单个IP类型必须提供IP地址"
    end

    validate present(:ip_range) do
      where type: [equal_to: "range"]
      message "IP段类型必须提供IP地址段"
    end

    validate match(:ip_address, ~r/^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/) do
      where present(:ip_address)
      message "IP地址格式不正确"
    end

    validate match(:ip_range, ~r/^(?:[0-9]{1,3}\.){3}[0-9]{1,3}\/[0-9]{1,2}$/) do
      where present(:ip_range)
      message "IP地址段格式不正确（应为CIDR格式，如：***********/24）"
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :ip_address, :string do
      allow_nil? true
      public? true
      description "单个IP地址"
      constraints max_length: 45
    end

    attribute :ip_range, :string do
      allow_nil? true
      public? true
      description "IP地址段（CIDR格式）"
      constraints max_length: 50
    end

    attribute :type, :string do
      allow_nil? false
      public? true
      description "类型：single=单个IP，range=IP段"
      constraints max_length: 20
      default "single"
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0=禁用，1=启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "描述信息"
      constraints max_length: 500
    end

    attribute :created_by, :uuid do
      allow_nil? true
      public? true
      description "创建者ID"
    end

    attribute :last_used_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后使用时间"
    end

    attribute :use_count, :integer do
      allow_nil? false
      public? true
      description "使用次数"
      default 0
    end

    timestamps()
  end

  relationships do
    belongs_to :creator, Teen.SystemSettings.AdminUser do
      source_attribute :created_by
      attribute_writable? true
    end
  end

  calculations do
    calculate :is_active, :boolean, expr(status == 1)
    calculate :is_single_ip, :boolean, expr(type == "single")
    calculate :is_ip_range, :boolean, expr(type == "range")

    calculate :display_ip,
              :string,
              expr(
                if type == "single" do
                  ip_address
                else
                  ip_range
                end
              )
  end

  identities do
    identity :unique_ip_address, [:ip_address, :type] do
      where expr(type == "single")
    end

    identity :unique_ip_range, [:ip_range, :type] do
      where expr(type == "range")
    end
  end
end
