defmodule Teen.PaymentSystem.PaymentOrder do
  @moduledoc """
  支付订单管理资源

  管理支付订单的状态、回调处理等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    domain: Teen.PaymentSystem,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :order_id, :user_id, :amount, :status, :created_at]
  end

  postgres do
    table "payment_orders"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :get_by_order_id, args: [:order_id]
    define :handle_callback
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:order_id, :user_id, :amount, :currency, :channel_id, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:created_at, DateTime.utc_now())
      end
    end

    update :update do
      primary? true
      accept [
        :status,
        :external_order_id,
        :payment_url,
        :error_message,
        :callback_data,
        :callback_at
      ]
    end

    read :get_by_order_id do
      argument :order_id, :string, allow_nil?: false
      get? true
      filter expr(order_id == ^arg(:order_id))
    end

    action :handle_callback, :struct do
      argument :order_id, :string, allow_nil?: false
      argument :status, :string, allow_nil?: false
      argument :amount, :decimal, allow_nil?: true
      argument :external_order_id, :string, allow_nil?: true
      argument :callback_data, :map, allow_nil?: true

      run fn input, _context ->
        order_id = input.arguments.order_id
        status = input.arguments.status
        amount = input.arguments.amount
        external_order_id = input.arguments.external_order_id
        callback_data = input.arguments.callback_data

        case get_by_order_id(order_id) do
          {:ok, payment_order} ->
            # 验证回调数据的有效性
            case verify_callback(payment_order, callback_data) do
              :ok ->
                # 更新订单状态
                updated_order =
                  Ash.update!(payment_order, %{
                    status: status,
                    external_order_id: external_order_id || payment_order.external_order_id,
                    callback_data: callback_data,
                    callback_at: DateTime.utc_now()
                  })

                # 如果支付成功，处理业务逻辑
                if status == "success" do
                  handle_payment_success(updated_order)
                end

                {:ok, updated_order}

              {:error, reason} ->
                {:error, reason}
            end

          {:error, _} ->
            {:error, "订单不存在"}
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :order_id, :string do
      allow_nil? false
      public? true
      description "内部订单ID"
      constraints max_length: 50
    end

    attribute :external_order_id, :string do
      allow_nil? true
      public? true
      description "外部支付平台订单ID"
      constraints max_length: 100
    end

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :amount, :decimal do
      allow_nil? false
      public? true
      description "支付金额"
      constraints precision: 10, scale: 2
    end

    attribute :currency, :string do
      allow_nil? false
      public? true
      description "货币类型"
      default "CNY"
      constraints max_length: 10
    end

    attribute :channel_id, :string do
      allow_nil? false
      public? true
      description "支付通道ID"
      constraints max_length: 20
    end

    attribute :status, :string do
      allow_nil? false
      public? true
      description "订单状态：pending, created, success, failed, cancelled"
      default "pending"
    end

    attribute :payment_url, :string do
      allow_nil? true
      public? true
      description "支付链接"
      constraints max_length: 500
    end

    attribute :error_message, :string do
      allow_nil? true
      public? true
      description "错误信息"
      constraints max_length: 500
    end

    attribute :callback_data, :map do
      allow_nil? true
      public? true
      description "回调数据"
    end

    attribute :callback_at, :utc_datetime do
      allow_nil? true
      public? true
      description "回调时间"
    end

    attribute :created_at, :utc_datetime do
      allow_nil? false
      public? true
      description "创建时间"
      default &DateTime.utc_now/0
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      attribute_writable? true
    end
  end

  identities do
    identity :unique_order_id, [:order_id]
  end

  # 私有函数

  defp verify_callback(payment_order, callback_data) do
    # 这里应该验证回调数据的签名等安全性
    # 简化实现，实际应该根据支付平台的要求进行验证
    case callback_data do
      %{"sign" => sign} when is_binary(sign) ->
        # 验证签名逻辑
        :ok

      _ ->
        # 如果没有签名数据，也允许通过（用于测试）
        :ok
    end
  end

  defp handle_payment_success(payment_order) do
    # 支付成功后的业务逻辑处理
    # 例如：给用户账户充值积分
    # 将支付金额转换为积分（1元 = 100积分）
    points = payment_order.amount |> Decimal.mult(100) |> Decimal.to_integer()
    # 给用户充值积分
    Cypridina.Accounts.add_points(payment_order.user_id, points,
      transaction_type: :recharge,
      description: "充值积分",
      metadata: %{
        "operation" => "recharge",
        "order_id" => payment_order.order_id,
        "amount" => Decimal.to_string(payment_order.amount),
        "currency" => payment_order.currency
      }
    )
  end
end
