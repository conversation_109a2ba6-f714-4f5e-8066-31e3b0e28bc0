defmodule Teen.PaymentSystem.PaymentService do
  @moduledoc """
  支付服务资源

  包装支付网关API，使用混合配置方案处理支付流程
  """
  require Logger
  alias Teen.PaymentSystem.GatewaySelector

  use Ash.Resource,
    otp_app: :cypridina,
    domain: Teen.PaymentSystem,
    data_layer: Ash.DataLayer.Ets,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :order_id, :amount, :status, :created_at]
  end

  code_interface do
    define :create_order
    define :query_order
  end

  actions do
    defaults [:read]

    action :create_order, :struct do
      argument :user_id, :uuid, allow_nil?: false
      argument :amount, :decimal, allow_nil?: false
      argument :currency, :string, allow_nil?: false, default: "CNY"
      argument :channel_id, :string, allow_nil?: false, default: "3021"
      argument :notify_url, :string, allow_nil?: true
      argument :return_url, :string, allow_nil?: true

      run fn input, _context ->
        user_id = input.arguments.user_id
        amount = input.arguments.amount
        currency = input.arguments.currency
        channel_id = input.arguments.channel_id
        notify_url = input.arguments.notify_url
        return_url = input.arguments.return_url

        # 选择最佳支付网关
        case GatewaySelector.select_recharge_gateway(amount, currency, user_id) do
          {:ok, gateway_config} ->
            # 生成订单ID
            order_id = generate_order_id()

            # 创建支付订单记录
            case Teen.PaymentSystem.PaymentOrder.create(%{
                   order_id: order_id,
                   user_id: user_id,
                   amount: amount,
                   currency: currency,
                   channel_id: gateway_config.channel_id,
                   status: "pending"
                 }) do
              {:ok, payment_order} ->
                # 调用支付API
                case create_payment_request(payment_order, gateway_config, notify_url, return_url) do
                  {:ok, response} ->
                    # 更新订单状态
                    Ash.update!(payment_order, %{
                      external_order_id: response["orderId"],
                      payment_url: response["payUrl"],
                      status: "created"
                    })

                    {:ok,
                     %{
                       id: Ash.UUID.generate(),
                       order_id: order_id,
                       amount: amount,
                       currency: currency,
                       payment_url: response["payUrl"],
                       status: "created",
                       gateway_name: gateway_config.name,
                       created_at: DateTime.utc_now()
                     }}

                  {:error, reason} ->
                    # 更新订单状态为失败
                    Ash.update!(payment_order, %{status: "failed", error_message: reason})
                    {:error, reason}
                end

              {:error, reason} ->
                {:error, reason}
            end

          {:error, reason} ->
            {:error, reason}
        end
      end
    end

    action :query_order, :struct do
      argument :order_id, :string, allow_nil?: false

      run fn input, _context ->
        order_id = input.arguments.order_id

        case query_payment_request(order_id) do
          {:ok, response} ->
            # 更新本地订单状态
            case Teen.PaymentSystem.PaymentOrder.get_by_order_id(order_id) do
              {:ok, payment_order} ->
                new_status = map_external_status(response["status"])
                Ash.update!(payment_order, %{status: new_status})

                {:ok,
                 %{
                   id: Ash.UUID.generate(),
                   order_id: order_id,
                   status: new_status,
                   external_status: response["status"],
                   amount: response["amount"],
                   created_at: DateTime.utc_now()
                 }}

              {:error, _} ->
                {:error, "订单不存在"}
            end

          {:error, reason} ->
            {:error, reason}
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :order_id, :string do
      allow_nil? false
      public? true
      description "订单ID"
    end

    attribute :amount, :decimal do
      allow_nil? false
      public? true
      description "支付金额"
    end

    attribute :currency, :string do
      allow_nil? false
      public? true
      description "货币类型"
      default "CNY"
    end

    attribute :status, :string do
      allow_nil? false
      public? true
      description "支付状态"
    end

    attribute :payment_url, :string do
      allow_nil? true
      public? true
      description "支付链接"
    end

    attribute :created_at, :utc_datetime do
      allow_nil? false
      public? true
      description "创建时间"
      default &DateTime.utc_now/0
    end
  end

  # 私有函数

  defp generate_order_id do
    timestamp = DateTime.utc_now() |> DateTime.to_unix(:millisecond)
    random = :rand.uniform(9999) |> Integer.to_string() |> String.pad_leading(4, "0")
    "CYP#{timestamp}#{random}"
  end

  defp create_payment_request(payment_order, gateway_config, notify_url, return_url) do
    # 在开发环境中返回模拟响应
    if Mix.env() == :dev do
      create_mock_payment_response(payment_order, gateway_config)
    else
      create_real_payment_request(payment_order, gateway_config, notify_url, return_url)
    end
  end

  defp create_mock_payment_response(payment_order, gateway_config) do
    Logger.info("💰 [PAYMENT] 开发环境 - 返回模拟支付响应")
    Logger.info("💰 [PAYMENT] 订单: #{payment_order.order_id}, 金额: #{payment_order.amount}, 网关: #{gateway_config.name}")

    mock_response = %{
      "orderId" => payment_order.order_id,
      "payUrl" => "https://mock-payment.example.com/pay/#{payment_order.order_id}",
      "externalOrderId" => "MOCK_#{payment_order.order_id}",
      "status" => "created"
    }

    {:ok, mock_response}
  end

  defp create_real_payment_request(payment_order, gateway_config, notify_url, return_url) do
    # 将金额转换为分（整数字符串），支付网关可能期望字符串格式的整数
    amount_in_cents = payment_order.amount |> Decimal.mult(100) |> Decimal.to_integer() |> to_string()

    params = %{
      "merchantId" => gateway_config.merchant_id,
      "orderId" => payment_order.order_id,
      "amount" => amount_in_cents,
      "currency" => payment_order.currency,
      "channelId" => gateway_config.channel_id,
      "notifyUrl" => notify_url || gateway_config.notify_url,
      "returnUrl" => return_url || gateway_config.return_url,
      "timestamp" => DateTime.utc_now() |> DateTime.to_unix(:millisecond) |> to_string()
    }

    # 生成签名
    sign = generate_payment_signature(params, gateway_config.secret_key)
    params_with_sign = Map.put(params, "sign", sign)

    # 添加调试日志
    Logger.info("💰 [PAYMENT] 发送支付请求参数: #{inspect(params_with_sign)}")
    Logger.info("💰 [PAYMENT] 使用网关: #{gateway_config.name} (#{gateway_config.channel_id})")

    headers = [
      {"Content-Type", "application/json"},
      {"Accept", "application/json"}
    ]

    create_url = GatewaySelector.get_create_order_url(gateway_config)
    timeout = (gateway_config.timeout_seconds || 30) * 1000

    case Req.post(create_url,
           json: params_with_sign,
           headers: headers,
           receive_timeout: timeout
         ) do
      {:ok, %{status: 200, body: response}} ->
        case response do
          %{"code" => "0000", "data" => data} -> {:ok, data}
          %{"code" => code, "msg" => msg} -> {:error, "支付创建失败: #{code} - #{msg}"}
          _ -> {:error, "支付创建失败: 未知错误"}
        end

      {:ok, %{status: status}} ->
        {:error, "支付创建失败: HTTP #{status}"}

      {:error, reason} ->
        {:error, "支付创建失败: #{inspect(reason)}"}
    end
  end

  defp query_payment_request(order_id) do
    # 在开发环境中返回模拟响应
    if Mix.env() == :dev do
      query_mock_payment_response(order_id)
    else
      query_real_payment_request(order_id)
    end
  end

  defp query_mock_payment_response(order_id) do
    Logger.info("💰 [PAYMENT] 开发环境 - 返回模拟查询响应")
    Logger.info("💰 [PAYMENT] 查询订单: #{order_id}")

    # 根据订单ID查找本地订单状态
    case Teen.PaymentSystem.PaymentOrder.get_by_order_id(order_id) do
      {:ok, payment_order} ->
        mock_response = %{
          "orderId" => order_id,
          "status" => "1",  # 1 表示成功
          "amount" => payment_order.amount |> Decimal.mult(100) |> Decimal.to_integer() |> to_string(),
          "currency" => payment_order.currency,
          "externalOrderId" => "MOCK_#{order_id}",
          "paidAt" => DateTime.utc_now() |> DateTime.to_unix(:millisecond) |> to_string()
        }

        {:ok, mock_response}

      {:error, reason} ->
        {:error, "订单不存在: #{reason}"}
    end
  end

  defp query_real_payment_request(order_id) do
    # 根据订单ID查找对应的网关配置
    case Teen.PaymentSystem.PaymentOrder.get_by_order_id(order_id) do
      {:ok, payment_order} ->
        case GatewaySelector.select_recharge_gateway(payment_order.amount, payment_order.currency) do
          {:ok, gateway_config} ->
            params = %{
              "merchantId" => gateway_config.merchant_id,
              "orderId" => order_id,
              "timestamp" => DateTime.utc_now() |> DateTime.to_unix(:millisecond) |> to_string()
            }

            # 生成签名
            sign = generate_payment_signature(params, gateway_config.secret_key)
            params_with_sign = Map.put(params, "sign", sign)

            headers = [
              {"Content-Type", "application/json"},
              {"Accept", "application/json"}
            ]

            query_url = GatewaySelector.get_query_order_url(gateway_config)
            timeout = (gateway_config.timeout_seconds || 30) * 1000

            case Req.post(query_url,
                   json: params_with_sign,
                   headers: headers,
                   receive_timeout: timeout
                 ) do
              {:ok, %{status: 200, body: response}} ->
                case response do
                  %{"code" => "0000", "data" => data} -> {:ok, data}
                  %{"code" => code, "msg" => msg} -> {:error, "订单查询失败: #{code} - #{msg}"}
                  _ -> {:error, "订单查询失败: 未知错误"}
                end

              {:ok, %{status: status}} ->
                {:error, "订单查询失败: HTTP #{status}"}

              {:error, reason} ->
                {:error, "订单查询失败: #{inspect(reason)}"}
            end

          {:error, reason} ->
            {:error, "查询失败: #{reason}"}
        end

      {:error, reason} ->
        {:error, "订单不存在: #{reason}"}
    end
  end



  defp generate_payment_signature(params, secret_key) do
    # 按照字母顺序排序参数
    sorted_params =
      params
      |> Enum.sort_by(fn {key, _} -> key end)
      |> Enum.map(fn {key, value} -> "#{key}=#{value}" end)
      |> Enum.join("&")

    # 添加密钥
    sign_string = "#{sorted_params}&key=#{secret_key}"

    # MD5签名
    :crypto.hash(:md5, sign_string)
    |> Base.encode16(case: :upper)
  end

  defp map_external_status(external_status) do
    case external_status do
      "0" -> "pending"
      "1" -> "success"
      "2" -> "failed"
      _ -> "unknown"
    end
  end
end
