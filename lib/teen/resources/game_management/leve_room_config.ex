defmodule Teen.GameManagement.LeveRoomConfig do
  @moduledoc """
  房间配置资源 - 对应gamelist数据结构
  管理游戏房间的服务器信息、入场条件等配置
  """
  use Ash.Resource,
    domain: Teen.GameManagement,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "room_configs"
    repo <PERSON><PERSON><PERSON><PERSON>.Repo
  end

  attributes do
    uuid_primary_key :id

    attribute :game_config_id, :uuid do
      allow_nil? false
      description "关联的游戏配置ID"
    end

    attribute :game_id, :integer do
      allow_nil? false
      description "游戏ID"
    end

    attribute :server_id, :integer do
      allow_nil? false
      description "服务器ID"
    end

    attribute :server_ip, :string do
      allow_nil? false
      description "服务器IP地址"
    end

    attribute :port, :integer do
      allow_nil? false
      description "服务器端口"
    end

    attribute :order_id, :integer do
      allow_nil? false
      description "房间排序ID"
    end

    attribute :min_bet, :integer do
      allow_nil? false
      description "底分/最小下注金额"
    end

    attribute :entry_fee, :integer do
      allow_nil? false
      description "入场金币要求"
    end

    attribute :max_bet, :integer do
      allow_nil? false
      default 0
      description "封顶金币(0表示无限制)"
    end

    attribute :max_players, :integer do
      allow_nil? false
      default 6
      description "最大玩家数量"
    end

    attribute :bundle_name, :string do
      allow_nil? false
      description "游戏包名，对应前端bundleName"
    end

    attribute :is_enabled, :boolean do
      allow_nil? false
      default true
      description "是否启用"
    end

    attribute :basic_config, :map do
      allow_nil? true
      default %{}
      description "基础配置JSON - 存储房间的基础设置（已废弃，使用unified_config）"
    end

    attribute :gameplay_config, :map do
      allow_nil? true
      default %{}
      description "玩法配置JSON - 存储游戏的玩法规则和参数（已废弃，使用unified_config）"
    end

    attribute :unified_config, :map do
      allow_nil? true
      default %{}
      description "统一配置JSON - 包含所有游戏配置（房间设置+玩法配置）"
    end

    attribute :created_by, :uuid do
      description "创建者用户ID"
    end

    attribute :updated_by, :uuid do
      description "更新者用户ID"
    end

    create_timestamp :inserted_at
    update_timestamp :updated_at
  end

  relationships do
    belongs_to :game_config, Teen.GameManagement.ManageGameConfig do
      source_attribute :game_config_id
      destination_attribute :id
    end



    belongs_to :creator, Cypridina.Accounts.User do
      source_attribute :created_by
      destination_attribute :id
    end

    belongs_to :updater, Cypridina.Accounts.User do
      source_attribute :updated_by
      destination_attribute :id
    end
  end

  actions do
    defaults [:read]

    create :create do
      accept [:game_config_id, :game_id, :server_id, :server_ip, :port, :order_id,
              :min_bet, :entry_fee, :max_bet, :max_players, :bundle_name, :is_enabled,
              :basic_config, :gameplay_config, :unified_config, :created_by]
    end

    update :update do
      accept [:server_ip, :port, :order_id, :min_bet, :entry_fee, :max_bet,
              :max_players, :bundle_name, :is_enabled, :basic_config, :gameplay_config, :unified_config, :updated_by]
    end

    update :enable do
      accept []
      change set_attribute(:is_enabled, true)
      change set_attribute(:updated_by, arg(:actor_id))
    end

    update :disable do
      accept []
      change set_attribute(:is_enabled, false)
      change set_attribute(:updated_by, arg(:actor_id))
    end

    destroy :destroy

    read :get_by_game_id do
      argument :game_id, :integer, allow_nil?: false
      filter expr(game_id == ^arg(:game_id))
    end

    read :list_enabled do
      filter expr(is_enabled == true)
    end

    read :list_by_game_config do
      argument :game_config_id, :uuid, allow_nil?: false
      filter expr(game_config_id == ^arg(:game_config_id))
    end

    read :get_by_game_and_server do
      argument :game_id, :integer, allow_nil?: false
      argument :server_id, :integer, allow_nil?: false
      filter expr(game_id == ^arg(:game_id) and server_id == ^arg(:server_id))
    end

    read :list_enabled_by_game do
      argument :game_id, :integer, allow_nil?: false
      filter expr(game_id == ^arg(:game_id) and is_enabled == true)
    end
  end

  validations do
    validate present([:game_config_id, :game_id, :server_id, :server_ip, :port,
                     :order_id, :min_bet, :entry_fee, :bundle_name])
    validate numericality(:port, greater_than: 0, less_than: 65536)
    validate numericality(:min_bet, greater_than: 0)
    validate numericality(:entry_fee, greater_than_or_equal_to: 0)
    validate numericality(:max_players, greater_than: 0)
  end

  identities do
    identity :unique_game_server, [:game_id, :server_id]
  end

  code_interface do
    domain Teen.GameManagement
    define :create, action: :create
    define :update, action: :update
    define :enable, action: :enable
    define :disable, action: :disable
    define :destroy, action: :destroy
    define :get_by_id, action: :read, get_by: [:id]
    define :get_by_game_id, action: :get_by_game_id
    define :get_by_server_id, action: :read, get_by: [:game_id, :server_id]
    define :list_enabled, action: :list_enabled
    define :list_by_game_config, action: :list_by_game_config
    define :get_by_game_and_server, action: :get_by_game_and_server
    define :list_enabled_by_game, action: :list_enabled_by_game
  end

  preparations do
    prepare build(sort: [order_id: :asc, server_id: :asc])
  end
end
