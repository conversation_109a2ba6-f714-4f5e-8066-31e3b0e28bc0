<Backpex.HTML.Layout.app_shell fluid={@fluid?}>
  <:topbar>
    <Backpex.HTML.Layout.topbar_branding />
    <Backpex.HTML.Layout.theme_selector
      socket={@socket}
      themes={[
        {"Light", "light"},
        {"Dark", "dark"},
        {"Cyberpunk", "cyberpunk"}
      ]}
    />
    <!-- 用户信息显示 -->
    <div class="flex items-center gap-4 mr-4">
      <!-- 用户信息文本 -->
      <div class="hidden md:flex flex-col items-end text-sm">
        <span class="font-medium text-base-content">
          <%= if assigns[:current_user] && @current_user.username do %>
            <%= @current_user.username %>
          <% else %>
            未知用户
          <% end %>
        </span>
        <span class="text-xs text-base-content/60">
          <%= if assigns[:current_user] && @current_user.role_name do %>
            <%= @current_user.role_name %>
          <% else %>
            普通用户
          <% end %>
        </span>
      </div>

      <!-- 用户头像和下拉菜单 -->
      <Backpex.HTML.Layout.topbar_dropdown class="">
        <:label>
          <label tabindex="0" class="btn btn-ghost flex items-center gap-2 px-3">
            <!-- 用户头像 -->
            <div class="avatar placeholder">
              <div class="bg-primary text-primary-content rounded-full w-8 h-8">
                <span class="text-xs font-bold">
                  <%= if assigns[:current_user] && @current_user.username do %>
                    <%= String.first(to_string(@current_user.username)) %>
                  <% else %>
                    U
                  <% end %>
                </span>
              </div>
            </div>
            <!-- 移动端显示用户名 -->
            <div class="md:hidden flex flex-col items-start">
              <span class="text-sm font-medium">
                <%= if assigns[:current_user] && @current_user.username do %>
                  <%= @current_user.username %>
                <% else %>
                  未知用户
                <% end %>
              </span>
              <span class="text-xs opacity-60">
                <%= if assigns[:current_user] && @current_user.role_name do %>
                  <%= @current_user.role_name %>
                <% else %>
                  普通用户
                <% end %>
              </span>
            </div>
            <.icon name="hero-chevron-down" class="size-4" />
          </label>
        </:label>
        <!-- 个人信息菜单项 -->
        <li>
          <.link navigate="/admin/profile" class="flex justify-between hover:bg-base-200">
            <span class="flex items-center gap-2">
              <.icon name="hero-user-circle" class="size-4" />
              个人信息
            </span>
          </.link>
        </li>
        <!-- 分隔线 -->
        <li><hr class="my-1" /></li>
        <!-- 退出登录 -->
        <li>
          <.link navigate={~p"/sign-out"} class="text-error flex justify-between hover:bg-base-200">
            <span class="flex items-center gap-2">
              <.icon name="hero-arrow-right-on-rectangle" class="size-4" />
              退出登录
            </span>
          </.link>
        </li>
      </Backpex.HTML.Layout.topbar_dropdown>
    </div>
  </:topbar>
  <:sidebar>
    <Backpex.HTML.Layout.sidebar_section id="user-management">
      <:label>用户管理</:label>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/users"}>
        <.icon name="hero-user" class="size-5" /> 用户列表
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/profile">
        <.icon name="hero-user-circle" class="size-5" /> 个人信息
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/user-management">
        <.icon name="hero-users" class="size-5" /> 用户管理
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/subordinate-management">
        <.icon name="hero-user-group" class="size-5" /> 下线管理
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/user-bans"}>
        <.icon name="hero-no-symbol" class="size-5" /> 封禁管理
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/user-devices"}>
        <.icon name="hero-device-phone-mobile" class="size-5" /> 设备管理
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>

    <Backpex.HTML.Layout.sidebar_section id="activity-system">
      <:label>活动系统管理</:label>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/daily-game-tasks">
        <.icon name="hero-calendar-days" class="size-5" /> 每日游戏任务
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/weekly-cards">
        <.icon name="hero-credit-card" class="size-5" /> 周卡活动
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/seven-day-logins">
        <.icon name="hero-calendar" class="size-5" /> 七日登录奖励
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/vip-gifts">
        <.icon name="hero-star" class="size-5" /> VIP礼包
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/recharge-tasks">
        <.icon name="hero-banknotes" class="size-5" /> 充值任务
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/recharge-wheels">
        <.icon name="hero-arrow-path" class="size-5" /> 转盘抽奖
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/scratch-card-activities">
        <.icon name="hero-ticket" class="size-5" /> 刮刮卡活动
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/first-recharge-gifts">
        <.icon name="hero-gift" class="size-5" /> 首充礼包
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/loss-rebates">
        <.icon name="hero-arrow-uturn-left" class="size-5" /> 亏损返利
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/invite-cash-activities">
        <.icon name="hero-user-plus" class="size-5" /> 邀请奖励活动
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/binding-rewards">
        <.icon name="hero-link" class="size-5" /> 绑定奖励
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/free-bonus-tasks">
        <.icon name="hero-hand-raised" class="size-5" /> 免费任务
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/cdkey-rewards">
        <.icon name="hero-key" class="size-5" /> CDKey奖励
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>

    <Backpex.HTML.Layout.sidebar_section id="activity-records">
      <:label>活动记录管理</:label>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/user-activity-participations">
        <.icon name="hero-user-circle" class="size-5" /> 用户活动参与记录
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/reward-claim-records">
        <.icon name="hero-clipboard-document-list" class="size-5" /> 奖励领取记录
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>

    <Backpex.HTML.Layout.sidebar_section id="game-management">
      <:label>游戏管理</:label>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/games"}>
        <.icon name="hero-puzzle-piece" class="size-5" /> 游戏列表
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/game-rooms">
        <.icon name="hero-building-office" class="size-5" /> 游戏房间
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/game-statistics">
        <.icon name="hero-chart-bar" class="size-5" /> 游戏统计
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>
  
    <Backpex.HTML.Layout.sidebar_section id="payment-system">
      <:label>支付系统</:label>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/payment-configs"}>
        <.icon name="hero-credit-card" class="size-5" /> 支付配置
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/payment-gateways"}>
        <.icon name="hero-server" class="size-5" /> 支付网关
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/exchange-configs"}>
        <.icon name="hero-arrow-path-rounded-square" class="size-5" /> 兑换配置
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/bank-configs"}>
        <.icon name="hero-building-library" class="size-5" /> 银行配置
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/user-bank-cards"}>
        <.icon name="hero-identification" class="size-5" /> 用户银行卡
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/payment-orders"}>
        <.icon name="hero-document-text" class="size-5" /> 支付订单
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>

    <Backpex.HTML.Layout.sidebar_section id="system">
      <:label>系统</:label>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/system-config">
        <.icon name="hero-cog-6-tooth" class="size-5" /> 系统配置
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/channels">
        <.icon name="hero-tv" class="size-5" /> 渠道管理
      </Backpex.HTML.Layout.sidebar_item>
      <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate="/admin/logs">
        <.icon name="hero-document-text" class="size-5" /> 系统日志
      </Backpex.HTML.Layout.sidebar_item>
    </Backpex.HTML.Layout.sidebar_section>
  </:sidebar>
  <Backpex.HTML.Layout.flash_messages flash={@flash} />
  <%= @inner_content %>
</Backpex.HTML.Layout.app_shell>