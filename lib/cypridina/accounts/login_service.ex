defmodule <PERSON>pridina.Accounts.LoginService do
  @moduledoc """
  登录服务模块

  处理用户登录验证、设备记录、封禁检查等功能
  """

  alias <PERSON>pridina.Accounts.{User, UserDevice}
  alias Teen.BanSystem

  require Logger

  @doc """
  处理用户登录

  参数:
  - user_id: 用户ID
  - device_id: 设备ID
  - login_ip: 登录IP地址
  - device_info: 设备信息（可选）

  返回:
  - {:ok, user} - 登录成功
  - {:error, reason} - 登录失败
  """
  def handle_user_login(user_id, device_id, login_ip, device_info \\ nil) do
    with {:ok, user} <- get_user(user_id),
         :ok <- check_user_ban(user_id),
         :ok <- check_ip_ban(login_ip),
         :ok <- check_device_ban(device_id),
         {:ok, _device} <- record_device_login(user_id, device_id, login_ip, device_info),
         {:ok, updated_user} <- set_user_online(user) do
      Logger.info("用户登录成功", %{
        user_id: user_id,
        device_id: device_id,
        login_ip: login_ip
      })

      {:ok, updated_user}
    else
      {:error, :user_banned, ban_info} ->
        Logger.warning("用户被封禁尝试登录", %{
          user_id: user_id,
          ban_info: ban_info,
          device_id: device_id,
          login_ip: login_ip
        })
        {:error, :user_banned, ban_info}

      {:error, :ip_banned} ->
        Logger.warning("被封禁IP尝试登录", %{
          user_id: user_id,
          login_ip: login_ip,
          device_id: device_id
        })
        {:error, :ip_banned}

      {:error, :device_banned} ->
        Logger.warning("被封禁设备尝试登录", %{
          user_id: user_id,
          device_id: device_id,
          login_ip: login_ip
        })
        {:error, :device_banned}

      {:error, reason} = error ->
        Logger.error("用户登录失败", %{
          user_id: user_id,
          device_id: device_id,
          login_ip: login_ip,
          reason: reason
        })
        error
    end
  end

  @doc """
  处理用户登出
  """
  def handle_user_logout(user_id, device_id) do
    with {:ok, user} <- get_user(user_id),
         {:ok, _device} <- set_device_offline(user_id, device_id),
         {:ok, updated_user} <- set_user_offline(user) do
      Logger.info("用户登出成功", %{
        user_id: user_id,
        device_id: device_id
      })

      {:ok, updated_user}
    else
      {:error, reason} = error ->
        Logger.error("用户登出失败", %{
          user_id: user_id,
          device_id: device_id,
          reason: reason
        })
        error
    end
  end

  @doc """
  检查用户是否被封禁
  """
  def check_user_ban(user_id) do
    case BanSystem.is_user_banned?(user_id) do
      {:ok, false} ->
        :ok

      {:ok, true, ban_info} ->
        {:error, :user_banned, ban_info}

      {:error, reason} ->
        Logger.error("检查用户封禁状态失败", %{
          user_id: user_id,
          reason: reason
        })
        # 如果检查失败，为了安全起见，允许登录但记录错误
        :ok
    end
  end

  @doc """
  检查IP是否被封禁
  """
  def check_ip_ban(ip_address) do
    # 查询IP封禁记录
    case query_ip_ban(ip_address) do
      {:ok, false} ->
        :ok

      {:ok, true} ->
        {:error, :ip_banned}

      {:error, reason} ->
        Logger.error("检查IP封禁状态失败", %{
          ip_address: ip_address,
          reason: reason
        })
        # 如果检查失败，为了安全起见，允许登录但记录错误
        :ok
    end
  end

  @doc """
  检查设备是否被封禁
  """
  def check_device_ban(device_id) do
    # 查询设备封禁记录
    case query_device_ban(device_id) do
      {:ok, false} ->
        :ok

      {:ok, true} ->
        {:error, :device_banned}

      {:error, reason} ->
        Logger.error("检查设备封禁状态失败", %{
          device_id: device_id,
          reason: reason
        })
        # 如果检查失败，为了安全起见，允许登录但记录错误
        :ok
    end
  end

  # 私有函数

  defp get_user(user_id) do
    try do
      case Ash.get(User, user_id) do
        {:ok, user} when not is_nil(user) -> {:ok, user}
        {:ok, nil} -> {:error, :user_not_found}
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        {:error, error}
    end
  end

  defp record_device_login(user_id, device_id, login_ip, _device_info) do
    # 尝试查找现有设备记录
    try do
      case UserDevice.get_by_user_and_device(%{user_id: user_id, device_id: device_id}) do
        {:ok, device} when not is_nil(device) ->
          # 更新现有设备的登录信息
          device
          |> Ash.Changeset.for_update(:update_login_info, %{login_ip: login_ip})
          |> Ash.update()

        {:ok, nil} ->
          # 创建新的设备记录
          UserDevice
          |> Ash.Changeset.for_create(:record_login, %{
            user_id: user_id,
            device_id: device_id,
            login_ip: login_ip
          })
          |> Ash.create()

        {:error, reason} ->
          {:error, reason}
      end
    rescue
      error ->
        {:error, error}
    end
  end

  defp set_device_offline(user_id, device_id) do
    try do
      case UserDevice.get_by_user_and_device(%{user_id: user_id, device_id: device_id}) do
        {:ok, device} when not is_nil(device) ->
          device
          |> Ash.Changeset.for_update(:set_offline, %{})
          |> Ash.update()

        {:ok, nil} ->
          Logger.warning("设置设备离线失败，设备不存在", %{
            user_id: user_id,
            device_id: device_id
          })
          # 设备不存在不算错误
          {:ok, nil}

        {:error, reason} ->
          Logger.warning("设置设备离线失败", %{
            user_id: user_id,
            device_id: device_id,
            reason: reason
          })
          # 设备查询失败不算错误
          {:ok, nil}
      end
    rescue
      error ->
        Logger.warning("设置设备离线异常", %{
          user_id: user_id,
          device_id: device_id,
          error: error
        })
        # 异常不算错误
        {:ok, nil}
    end
  end

  defp set_user_online(user) do
    user
    |> Ash.Changeset.for_update(:set_online, %{})
    |> Ash.update()
  end

  defp set_user_offline(user) do
    user
    |> Ash.Changeset.for_update(:set_offline, %{})
    |> Ash.update()
  end

  defp query_ip_ban(ip_address) do
    # 查询IP封禁记录（ban_type: 3）
    case BanSystem.UserBan.list_active_bans() do
      {:ok, bans} ->
        ip_banned = Enum.any?(bans, fn ban ->
          ban.ban_type == 3 and ban.ip_address == ip_address
        end)
        {:ok, ip_banned}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp query_device_ban(_device_id) do
    # 查询设备封禁记录（ban_type: 2）
    # 这里需要根据实际的设备封禁表结构来实现
    # 暂时返回false，表示设备未被封禁
    {:ok, false}
  end
end
