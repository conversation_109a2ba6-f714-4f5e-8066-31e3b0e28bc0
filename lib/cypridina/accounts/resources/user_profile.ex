defmodule Cypridina.Accounts.UserProfile do
  @moduledoc """
  用户资料资源

  管理用户的个人资料信息，包括性别、头像、昵称等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    domain: Cypridina.Accounts,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :user_id, :nickname, :gender, :avatar_url, :updated_at]
  end

  postgres do
    table "user_profiles"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :create_for_user
    define :create_initial_profile
    define :read
    define :update
    define :destroy
    define :get_by_user_id
    define :update_profile
  end

  actions do
    defaults [:read, :update, :destroy, create: :*]

    read :get_by_user_id do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end

    update :update_profile do
      argument :nickname, :string, allow_nil?: true
      argument :gender, :integer, allow_nil?: true
      argument :avatar_url, :string, allow_nil?: true
      argument :head_id, :integer, allow_nil?: true

      require_atomic? false

      change fn changeset, _context ->
        changeset
        |> maybe_set_attribute(:nickname, :nickname)
        |> maybe_set_attribute(:gender, :gender)
        |> maybe_set_attribute(:avatar_url, :avatar_url)
        |> maybe_set_attribute(:head_id, :head_id)
      end
    end

    create :create_for_user do
      argument :user_id, :uuid, allow_nil?: false
      argument :nickname, :string, allow_nil?: true
      argument :gender, :integer, allow_nil?: true
      argument :avatar_url, :string, allow_nil?: true
      argument :head_id, :integer, allow_nil?: true

      change set_attribute(:user_id, arg(:user_id))

      change fn changeset, _context ->
        changeset
        |> maybe_set_attribute(:nickname, :nickname)
        |> maybe_set_attribute(:gender, :gender)
        |> maybe_set_attribute(:avatar_url, :avatar_url)
        |> maybe_set_attribute(:head_id, :head_id)
      end
    end

    create :create_initial_profile do
      description "为新用户创建初始资料"
      accept [:user_id]

      argument :user_id, :uuid, allow_nil?: false
      argument :username, :string, allow_nil?: false

      change set_attribute(:user_id, arg(:user_id))
      # 使用用户名作为初始昵称
      change set_attribute(:nickname, arg(:username))
      # 性别设为未知
      change set_attribute(:gender, 0)
      # 头像URL为空
      change set_attribute(:avatar_url, nil)
      # 头像ID设为1
      change set_attribute(:head_id, 1)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :nickname, :string do
      allow_nil? true
      public? true
      description "用户昵称"
      constraints max_length: 50
    end

    attribute :gender, :integer do
      allow_nil? true
      public? true
      description "性别：0-未知，1-男，2-女"
      constraints min: 0, max: 2
    end

    attribute :avatar_url, :string do
      allow_nil? true
      public? true
      description "自定义头像URL"
      constraints max_length: 500
    end

    attribute :head_id, :integer do
      allow_nil? true
      public? true
      description "预设头像ID（客户端本地图片）"
      constraints min: 1, max: 999
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_user_profile, [:user_id]
  end

  # 私有辅助函数
  defp maybe_set_attribute(changeset, arg_name, attr_name) do
    case Ash.Changeset.fetch_argument(changeset, arg_name) do
      {:ok, value} -> Ash.Changeset.change_attribute(changeset, attr_name, value)
      :error -> changeset
    end
  end
end
