defmodule Cypridina.Accounts.Channel do
  @moduledoc """
  登录渠道资源

  管理用户登录渠道，包括渠道ID、渠道包名、渠道配置等
  用于区分不同的客户端版本和渠道来源
  """

  use Ash.Resource,
    otp_app: :cypridina,
    domain: Cypridina.Accounts,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :channel_id,
      :channel_name,
      :package_name,
      :status,
      :user_count,
      :inserted_at,
      :updated_at
    ]
  end

  postgres do
    table "channels"
    repo Cypridina.Repo

    # 为身份约束配置 SQL 条件
    identity_wheres_to_sql [
      unique_package_name: "package_name IS NOT NULL"
    ]
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      primary? true
      accept [
        :channel_id,
        :channel_name,
        :package_name,
        :description,
        :config,
        :status
      ]
    end

    update :update do
      accept [
        :channel_name,
        :package_name,
        :description,
        :config,
        :status
      ]
    end

    # 启用渠道
    update :enable do
      accept []
      change set_attribute(:status, 1)
    end

    # 禁用渠道
    update :disable do
      accept []
      change set_attribute(:status, 0)
    end

    # 根据渠道ID查找
    read :by_channel_id do
      argument :channel_id, :string, allow_nil?: false
      filter expr(channel_id == ^arg(:channel_id))
    end

    # 获取活跃渠道
    read :active do
      filter expr(status == 1)
    end
  end

  attributes do
    uuid_primary_key :id

    # 渠道数字ID - 对应登录协议中的siteid
    attribute :channel_id, :string do
      allow_nil? false
      public? true
      description "渠道ID，对应登录协议中的siteid，如501、503等"
    end

    # 渠道名称
    attribute :channel_name, :string do
      allow_nil? false
      public? true
      description "渠道名称，如'51TeenPatti'、'官方渠道'等"
      constraints max_length: 100
    end

    # 渠道包名
    attribute :package_name, :string do
      allow_nil? true
      public? true
      description "渠道包名，如'com.teenpatti.channel501'等"
      constraints max_length: 200
    end

    # 渠道描述
    attribute :description, :string do
      allow_nil? true
      public? true
      description "渠道描述信息"
      constraints max_length: 500
    end

    # 渠道配置（JSON格式）
    attribute :config, :map do
      allow_nil? true
      public? true
      description "渠道特定配置，如游戏列表、服务器地址等"
      default %{}
    end

    # 渠道状态
    attribute :status, :integer do
      allow_nil? false
      public? true
      description "渠道状态：0=禁用，1=启用"
      default 1
      constraints min: 0, max: 1
    end

    timestamps()
  end

  calculations do
    # 是否活跃
    calculate :is_active, :boolean, expr(status == 1)

    # 渠道状态名称
    calculate :status_name, :string do
      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            1 -> "启用"
            0 -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end

    # 用户数量统计
    calculate :user_count, :integer do
      calculation fn records, _context ->
        Enum.map(records, fn record ->
          # 这里可以添加实际的用户统计逻辑
          # 暂时返回0，后续可以通过关联查询实现
          0
        end)
      end
    end

    # 渠道显示名称
    calculate :display_name, :string do
      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if record.channel_name && String.length(record.channel_name) > 0 do
            "#{record.channel_name}(#{record.channel_id})"
          else
            "渠道#{record.channel_id}"
          end
        end)
      end
    end
  end

  identities do
    # 渠道ID唯一性约束
    identity :unique_channel_id, [:channel_id]

    # 包名唯一性约束（如果有包名的话）
    identity :unique_package_name, [:package_name], where: expr(not is_nil(package_name))
  end

  relationships do
    # 关联到用户（一个渠道可以有多个用户）
    has_many :users, Cypridina.Accounts.User do
      source_attribute :channel_id
      destination_attribute :channel_id
    end
  end
end
