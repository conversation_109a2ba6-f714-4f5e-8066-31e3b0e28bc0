defmodule Cypridina.BalanceCache do
  @moduledoc """
  账户余额的 ETS 缓存服务

  支持多种账户类型的余额缓存，提供高性能的余额查询和原子更新：
  - 用户账户：通过 user_id 查询用户余额
  - 系统账户：通过 system_account_type 查询系统账户余额
  - 游戏账户：通过 game_id 查询单个游戏账户余额
  - 原子性余额增减操作，确保并发安全
  - 自动缓存过期和清理
  - 通过 Ash.Notifier 订阅余额变更，自动更新缓存
  - 缓存统计和监控

  ## 基础使用示例

      # 获取用户余额
      {:ok, balance} = BalanceCache.get_user_balance("user_id")

      # 获取系统账户余额
      {:ok, balance} = BalanceCache.get_system_balance(:main)

      # 获取游戏账户余额
      {:ok, balance} = BalanceCache.get_game_balance("game_id")

      # 设置余额
      BalanceCache.set_balance("user:user_id", 1000)
      BalanceCache.set_balance("system:main", 50000)
      BalanceCache.set_balance("game:game_id", 2000)

  ## 原子更新示例

      # 原子性增加用户余额
      {:ok, new_balance} = BalanceCache.add_user_balance("user_123", 100)

      # 原子性减少用户余额
      {:ok, new_balance} = BalanceCache.subtract_user_balance("user_123", 50)

      # 原子性增加系统账户余额
      {:ok, new_balance} = BalanceCache.add_system_balance(:main, 1000)

      # 原子性减少系统账户余额
      {:ok, new_balance} = BalanceCache.subtract_system_balance(:main, 500)

      # 原子性增加游戏账户余额
      {:ok, new_balance} = BalanceCache.add_game_balance("game_456", 200)

      # 原子性减少游戏账户余额
      {:ok, new_balance} = BalanceCache.subtract_game_balance("game_456", 100)

      # 通用原子更新（使用完整缓存键）
      {:ok, new_balance} = BalanceCache.add_balance("user:user_123", 100)
      {:ok, new_balance} = BalanceCache.subtract_balance("user:user_123", 50)

  ## 缓存管理示例

      # 清除余额缓存
      BalanceCache.invalidate_balance("user:user_id")

      # 检查缓存状态
      BalanceCache.stats()

      # 清除所有缓存
      BalanceCache.clear_all()

  ## 错误处理

      # 处理余额不足的情况
      case BalanceCache.subtract_user_balance("user_123", 1000) do
        {:ok, balance} ->
          # 成功减少余额
          IO.puts("新余额: \#{balance}")
        {:error, :insufficient_balance} ->
          # 余额不足
          IO.puts("余额不足")
        {:error, reason} ->
          # 其他错误
          IO.puts("操作失败: \#{reason}")
      end

  """
  use GenServer
  require Logger
  require Ash.Query
  import Ash.Expr

  alias Cypridina.Accounts.AccountIdentifier

  @table_name :balance_cache
  # 默认缓存1小时
  @ttl :timer.hours(1)
  # 每5分钟清理过期数据
  @cleanup_interval :timer.minutes(5)

  # Client API

  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  # ========== 用户账户相关API ==========

  @doc """
  获取用户余额，优先从缓存读取
  """
  def get_user_balance(user_id) when is_binary(user_id) do
    cache_key = build_cache_key(:user, user_id)

    case lookup(cache_key) do
      {:ok, balance} ->
        {:ok, balance}

      :not_found ->
        # 从Ledger系统加载并缓存
        load_and_cache_user_balance(user_id)
    end
  end

  def get_user_balance(user_id) when is_integer(user_id) do
    get_user_balance(to_string(user_id))
  end

  def get_user_balance(nil), do: {:error, :invalid_user_id}

  @doc """
  设置用户余额到缓存
  """
  def set_user_balance(user_id, balance) when is_binary(user_id) and is_integer(balance) do
    cache_key = build_cache_key(:user, user_id)
    GenServer.cast(__MODULE__, {:set_balance, cache_key, balance})
  end

  def set_user_balance(user_id, balance) when is_integer(user_id) and is_integer(balance) do
    set_user_balance(to_string(user_id), balance)
  end

  @doc """
  原子性增加用户余额，返回新的余额

  ## 参数
  - `user_id`: 用户ID
  - `amount`: 增加的金额（必须为正数）

  ## 返回值
  - `{:ok, new_balance}`: 成功，返回新的余额
  - `{:error, reason}`: 失败，返回错误原因

  ## 示例
      {:ok, new_balance} = BalanceCache.add_user_balance("user_123", 100)
  """
  def add_user_balance(user_id, amount)
      when is_binary(user_id) and is_integer(amount) and amount > 0 do
    cache_key = build_cache_key(:user, user_id)
    GenServer.call(__MODULE__, {:add_balance, cache_key, amount})
  end

  def add_user_balance(user_id, amount)
      when is_integer(user_id) and is_integer(amount) and amount > 0 do
    add_user_balance(to_string(user_id), amount)
  end

  def add_user_balance(_user_id, amount) when amount <= 0 do
    {:error, :invalid_amount}
  end

  @doc """
  原子性减少用户余额，返回新的余额

  ## 参数
  - `user_id`: 用户ID
  - `amount`: 减少的金额（必须为正数）

  ## 返回值
  - `{:ok, new_balance}`: 成功，返回新的余额
  - `{:error, reason}`: 失败，返回错误原因

  ## 示例
      {:ok, new_balance} = BalanceCache.subtract_user_balance("user_123", 50)
  """
  def subtract_user_balance(user_id, amount)
      when is_binary(user_id) and is_integer(amount) and amount > 0 do
    cache_key = build_cache_key(:user, user_id)
    GenServer.call(__MODULE__, {:subtract_balance, cache_key, amount})
  end

  def subtract_user_balance(user_id, amount)
      when is_integer(user_id) and is_integer(amount) and amount > 0 do
    subtract_user_balance(to_string(user_id), amount)
  end

  def subtract_user_balance(_user_id, amount) when amount <= 0 do
    {:error, :invalid_amount}
  end

  @doc """
  清除指定用户的余额缓存
  """
  def invalidate_user_balance(user_id) when is_binary(user_id) do
    cache_key = build_cache_key(:user, user_id)
    GenServer.cast(__MODULE__, {:invalidate, cache_key})
  end

  def invalidate_user_balance(user_id) when is_integer(user_id) do
    invalidate_user_balance(to_string(user_id))
  end

  # ========== 系统账户相关API ==========

  @doc """
  获取系统账户余额，优先从缓存读取
  """
  def get_system_balance(system_account_type) when is_atom(system_account_type) do
    cache_key = build_cache_key(:system, system_account_type)

    case lookup(cache_key) do
      {:ok, balance} ->
        {:ok, balance}

      :not_found ->
        # 从Ledger系统加载并缓存
        load_and_cache_system_balance(system_account_type)
    end
  end

  @doc """
  设置系统账户余额到缓存
  """
  def set_system_balance(system_account_type, balance)
      when is_atom(system_account_type) and is_integer(balance) do
    cache_key = build_cache_key(:system, system_account_type)
    GenServer.cast(__MODULE__, {:set_balance, cache_key, balance})
  end

  @doc """
  原子性增加系统账户余额，返回新的余额

  ## 参数
  - `system_account_type`: 系统账户类型
  - `amount`: 增加的金额（必须为正数）

  ## 返回值
  - `{:ok, new_balance}`: 成功，返回新的余额
  - `{:error, reason}`: 失败，返回错误原因

  ## 示例
      {:ok, new_balance} = BalanceCache.add_system_balance(:main, 1000)
  """
  def add_system_balance(system_account_type, amount)
      when is_atom(system_account_type) and is_integer(amount) and amount > 0 do
    cache_key = build_cache_key(:system, system_account_type)
    GenServer.call(__MODULE__, {:add_balance, cache_key, amount})
  end

  def add_system_balance(_system_account_type, amount) when amount <= 0 do
    {:error, :invalid_amount}
  end

  @doc """
  原子性减少系统账户余额，返回新的余额

  ## 参数
  - `system_account_type`: 系统账户类型
  - `amount`: 减少的金额（必须为正数）

  ## 返回值
  - `{:ok, new_balance}`: 成功，返回新的余额
  - `{:error, reason}`: 失败，返回错误原因

  ## 示例
      {:ok, new_balance} = BalanceCache.subtract_system_balance(:main, 500)
  """
  def subtract_system_balance(system_account_type, amount)
      when is_atom(system_account_type) and is_integer(amount) and amount > 0 do
    cache_key = build_cache_key(:system, system_account_type)
    GenServer.call(__MODULE__, {:subtract_balance, cache_key, amount})
  end

  def subtract_system_balance(_system_account_type, amount) when amount <= 0 do
    {:error, :invalid_amount}
  end

  @doc """
  清除指定系统账户的余额缓存
  """
  def invalidate_system_balance(system_account_type) when is_atom(system_account_type) do
    cache_key = build_cache_key(:system, system_account_type)
    GenServer.cast(__MODULE__, {:invalidate, cache_key})
  end

  # ========== 游戏账户相关API ==========

  @doc """
  获取游戏账户余额，优先从缓存读取
  """
  def get_game_balance(game_id) when is_binary(game_id) do
    cache_key = build_cache_key(:game, game_id)

    case lookup(cache_key) do
      {:ok, balance} ->
        {:ok, balance}

      :not_found ->
        # 从Ledger系统加载并缓存
        load_and_cache_game_balance(game_id)
    end
  end

  def get_game_balance(game_id) when is_integer(game_id) do
    get_game_balance(to_string(game_id))
  end

  @doc """
  设置游戏账户余额到缓存
  """
  def set_game_balance(game_id, balance) when is_binary(game_id) and is_integer(balance) do
    cache_key = build_cache_key(:game, game_id)
    GenServer.cast(__MODULE__, {:set_balance, cache_key, balance})
  end

  def set_game_balance(game_id, balance) when is_integer(game_id) and is_integer(balance) do
    set_game_balance(to_string(game_id), balance)
  end

  @doc """
  原子性增加游戏账户余额，返回新的余额

  ## 参数
  - `game_id`: 游戏ID
  - `amount`: 增加的金额（必须为正数）

  ## 返回值
  - `{:ok, new_balance}`: 成功，返回新的余额
  - `{:error, reason}`: 失败，返回错误原因

  ## 示例
      {:ok, new_balance} = BalanceCache.add_game_balance("game_456", 200)
  """
  def add_game_balance(game_id, amount)
      when is_binary(game_id) and is_integer(amount) and amount > 0 do
    cache_key = build_cache_key(:game, game_id)
    GenServer.call(__MODULE__, {:add_balance, cache_key, amount})
  end

  def add_game_balance(game_id, amount)
      when is_integer(game_id) and is_integer(amount) and amount > 0 do
    add_game_balance(to_string(game_id), amount)
  end

  def add_game_balance(_game_id, amount) when amount <= 0 do
    {:error, :invalid_amount}
  end

  @doc """
  原子性减少游戏账户余额，返回新的余额

  ## 参数
  - `game_id`: 游戏ID
  - `amount`: 减少的金额（必须为正数）

  ## 返回值
  - `{:ok, new_balance}`: 成功，返回新的余额
  - `{:error, reason}`: 失败，返回错误原因

  ## 示例
      {:ok, new_balance} = BalanceCache.subtract_game_balance("game_456", 100)
  """
  def subtract_game_balance(game_id, amount)
      when is_binary(game_id) and is_integer(amount) and amount > 0 do
    cache_key = build_cache_key(:game, game_id)
    GenServer.call(__MODULE__, {:subtract_balance, cache_key, amount})
  end

  def subtract_game_balance(game_id, amount)
      when is_integer(game_id) and is_integer(amount) and amount > 0 do
    subtract_game_balance(to_string(game_id), amount)
  end

  def subtract_game_balance(_game_id, amount) when amount <= 0 do
    {:error, :invalid_amount}
  end

  @doc """
  清除指定游戏账户的余额缓存
  """
  def invalidate_game_balance(game_id) when is_binary(game_id) do
    cache_key = build_cache_key(:game, game_id)
    GenServer.cast(__MODULE__, {:invalidate, cache_key})
  end

  def invalidate_game_balance(game_id) when is_integer(game_id) do
    invalidate_game_balance(to_string(game_id))
  end

  # ========== 通用API ==========

  @doc """
  通用设置余额方法，使用完整的缓存键
  """
  def set_balance(cache_key, balance) when is_binary(cache_key) and is_integer(balance) do
    GenServer.cast(__MODULE__, {:set_balance, cache_key, balance})
  end

  @doc """
  通用原子性增加余额方法，使用完整的缓存键

  ## 参数
  - `cache_key`: 完整的缓存键（如 "user:user_123"）
  - `amount`: 增加的金额（必须为正数）

  ## 返回值
  - `{:ok, new_balance}`: 成功，返回新的余额
  - `{:error, reason}`: 失败，返回错误原因

  ## 示例
      {:ok, new_balance} = BalanceCache.add_balance("user:user_123", 100)
  """
  def add_balance(cache_key, amount)
      when is_binary(cache_key) and is_integer(amount) and amount > 0 do
    GenServer.call(__MODULE__, {:add_balance, cache_key, amount})
  end

  def add_balance(_cache_key, amount) when amount <= 0 do
    {:error, :invalid_amount}
  end

  @doc """
  通用原子性减少余额方法，使用完整的缓存键

  ## 参数
  - `cache_key`: 完整的缓存键（如 "user:user_123"）
  - `amount`: 减少的金额（必须为正数）

  ## 返回值
  - `{:ok, new_balance}`: 成功，返回新的余额
  - `{:error, reason}`: 失败，返回错误原因

  ## 示例
      {:ok, new_balance} = BalanceCache.subtract_balance("user:user_123", 50)
  """
  def subtract_balance(cache_key, amount)
      when is_binary(cache_key) and is_integer(amount) and amount > 0 do
    GenServer.call(__MODULE__, {:subtract_balance, cache_key, amount})
  end

  def subtract_balance(_cache_key, amount) when amount <= 0 do
    {:error, :invalid_amount}
  end

  @doc """
  批量获取多个identifier对应的account_id

  通过缓存机制快速获取多个账户标识符对应的account_id，
  支持用户账户、系统账户和游戏账户的混合查询。

  ## 参数
  - `identifiers`: 账户标识符列表，格式如：
    - `["user:XAA:user_123", "system:XAA:main", "game:XAA:game_456"]`

  ## 返回值
  - `{:ok, [account_id | nil]}`: 成功时返回account_id列表，按输入顺序排列，不存在的用nil填充
  - `{:error, reason}`: 失败时返回错误原因

  ## 示例
      identifiers = [
        "user:XAA:user_123",
        "system:XAA:main",
        "user:XAA:non_existent"
      ]

      {:ok, account_ids} = BalanceCache.get_account_ids_by_identifiers(identifiers)
      # => {:ok, [
      #      "********-89ab-cdef-0123-456789abcdef",  # user_123的account_id
      #      "fedcba98-7654-3210-fedc-ba9876543210",  # main系统账户的account_id
      #      nil                                       # 不存在的账户用nil填充
      #    ]}

  ## 性能特性
  - 使用ETS缓存提高查询性能
  - 支持批量查询，减少数据库访问
  - 自动缓存account_id映射关系
  - 缓存过期时间与余额缓存一致
  - 保持输入顺序，不存在的账户用nil填充
  """
  def get_account_ids_by_identifiers(identifiers) when is_list(identifiers) do
    # try do
    # 过滤有效的标识符，同时保持原始位置信息
    indexed_identifiers =
      identifiers
      |> Enum.with_index()
      |> Enum.filter(fn {identifier, _index} -> is_binary(identifier) end)

    if indexed_identifiers == [] do
      # 如果没有有效的标识符，返回与输入长度相同的nil列表
      result_list = List.duplicate(nil, length(identifiers))
      {:ok, result_list}
    else
      # 提取有效的标识符
      valid_identifiers = Enum.map(indexed_identifiers, fn {identifier, _index} -> identifier end)

      # 分离缓存命中和未命中的标识符
      {cached_results, missing_identifiers} =
        Enum.split_with(valid_identifiers, &account_id_cached?/1)

      # 获取缓存的结果
      cached_map =
        cached_results
        |> Enum.map(fn identifier ->
          case lookup_account_id(identifier) do
            {:ok, account_id} -> {identifier, account_id}
            _ -> nil
          end
        end)
        |> Enum.reject(&is_nil/1)
        |> Map.new()

      # 批量查询未缓存的account_id
      missing_map =
        if missing_identifiers != [] do
          batch_load_and_cache_account_ids(missing_identifiers)
        else
          %{}
        end

      # 合并结果
      result_map = Map.merge(cached_map, missing_map)

      # 按原始顺序构建结果列表，不存在的用nil填充
      result_list =
        Enum.map(identifiers, fn identifier ->
          if is_binary(identifier) do
            Map.get(result_map, identifier)
          else
            nil
          end
        end)

      {:ok, result_list}
    end

    # rescue
    #   error ->
    #     Logger.warning("Failed to get account IDs by identifiers: #{inspect(error)}")
    #     {:error, :batch_query_failed}
    # end
  end

  @doc """
  通用清除余额缓存方法，使用完整的缓存键
  """
  def invalidate_balance(cache_key) when is_binary(cache_key) do
    GenServer.cast(__MODULE__, {:invalidate, cache_key})
  end

  @doc """
  清除所有缓存
  """
  def clear_all do
    GenServer.call(__MODULE__, :clear_all)
  end

  @doc """
  获取缓存统计信息
  """
  def stats do
    GenServer.call(__MODULE__, :stats)
  end

  @doc """
  检查用户余额是否在缓存中
  """
  def cached?(user_id) when is_binary(user_id) do
    cache_key = build_cache_key(:user, user_id)
    cached_by_key?(cache_key)
  end

  def cached?(user_id) when is_integer(user_id), do: cached?(to_string(user_id))
  def cached?(nil), do: false

  @doc """
  检查系统账户余额是否在缓存中
  """
  def system_cached?(system_account_type) when is_atom(system_account_type) do
    cache_key = build_cache_key(:system, system_account_type)
    cached_by_key?(cache_key)
  end

  @doc """
  检查游戏账户余额是否在缓存中
  """
  def game_cached?(game_id) when is_binary(game_id) do
    cache_key = build_cache_key(:game, game_id)
    cached_by_key?(cache_key)
  end

  def game_cached?(game_id) when is_integer(game_id), do: game_cached?(to_string(game_id))

  @doc """
  通用检查缓存方法，使用完整的缓存键
  """
  def cached_by_key?(cache_key) when is_binary(cache_key) do
    case :ets.lookup(@table_name, cache_key) do
      [{^cache_key, _balance, expires_at}] ->
        current_time = System.system_time(:second)
        expires_at > current_time

      [] ->
        false
    end
  end

  # Server callbacks

  def init(opts) do
    # 创建 ETS 表
    table =
      :ets.new(@table_name, [
        :set,
        :public,
        :named_table,
        read_concurrency: true,
        write_concurrency: true
      ])

    # 订阅 Ash 通知
    subscribe_to_notifications()

    # 定期清理过期缓存
    schedule_cleanup()

    state = %{
      table: table,
      ttl: opts[:ttl] || @ttl,
      stats: %{
        hits: 0,
        misses: 0,
        evictions: 0
      }
    }

    {:ok, state}
  end

  def handle_cast({:set_balance, cache_key, balance}, state) do
    cache_balance(cache_key, balance)
    {:noreply, state}
  end

  def handle_cast({:invalidate, cache_key}, state) do
    :ets.delete(@table_name, cache_key)
    Logger.debug("Invalidated balance cache for #{cache_key}")
    {:noreply, state}
  end

  def handle_call(:clear_all, _from, state) do
    :ets.delete_all_objects(@table_name)
    Logger.info("Cleared all balance cache")

    {:reply, :ok, %{state | stats: %{hits: 0, misses: 0, evictions: 0}}}
  end

  def handle_call(:stats, _from, state) do
    cache_size = :ets.info(@table_name, :size)

    stats =
      Map.merge(state.stats, %{
        cache_size: cache_size,
        hit_rate: calculate_hit_rate(state.stats)
      })

    {:reply, stats, state}
  end

  def handle_call({:add_balance, cache_key, amount}, _from, state) do
    result = atomic_update_balance(cache_key, amount, :add)
    {:reply, result, state}
  end

  def handle_call({:subtract_balance, cache_key, amount}, _from, state) do
    result = atomic_update_balance(cache_key, amount, :subtract)
    {:reply, result, state}
  end

  # 处理 Phoenix.Socket.Broadcast 广播的 Ash 通知
  def handle_info(
        %{topic: topic, event: event, payload: notification} = broadcast,
        state
      )
      when is_map(broadcast) do
    case topic do
      "balance" ->
        handle_balance_notification(notification)
        {:noreply, state}

      # 处理特定账户的余额通知 "balance:account_id"
      "balance:" <> _account_id ->
        handle_balance_notification(notification)
        {:noreply, state}

      _ ->
        # 忽略其他主题的消息
        {:noreply, state}
    end
  end

  # 定期清理过期缓存
  def handle_info(:cleanup, state) do
    evicted = cleanup_expired()

    new_stats = update_in(state.stats, [:evictions], &(&1 + evicted))

    schedule_cleanup()
    {:noreply, %{state | stats: new_stats}}
  end

  # 处理统计更新
  def handle_info({:update_stats, :hit}, state) do
    new_state = update_in(state, [:stats, :hits], &(&1 + 1))
    {:noreply, new_state}
  end

  def handle_info({:update_stats, :miss}, state) do
    new_state = update_in(state, [:stats, :misses], &(&1 + 1))
    {:noreply, new_state}
  end

  # 私有函数

  # ========== 缓存键构建 ==========

  defp build_cache_key(:user, user_id), do: AccountIdentifier.build_cache_key(:user, user_id)

  defp build_cache_key(:system, system_account_type),
    do: AccountIdentifier.build_cache_key(:system, system_account_type)

  defp build_cache_key(:game, game_id), do: AccountIdentifier.build_cache_key(:game, game_id)

  # ========== 缓存查找 ==========

  defp lookup(cache_key) do
    case :ets.lookup(@table_name, cache_key) do
      [{^cache_key, balance, expires_at}] ->
        current_time = System.system_time(:second)

        if expires_at > current_time do
          update_stats(:hit)
          {:ok, balance}
        else
          # 过期了，删除
          :ets.delete(@table_name, cache_key)
          update_stats(:miss)
          :not_found
        end

      [] ->
        update_stats(:miss)
        :not_found
    end
  end

  # ========== 余额加载和缓存 ==========

  defp load_and_cache_user_balance(user_id) do
    # 从Ledger系统获取最新余额
    balance_integer = Cypridina.Ledger.get_user_balance(user_id, :XAA)

    # 缓存余额
    cache_key = build_cache_key(:user, user_id)
    cache_balance(cache_key, balance_integer)

    {:ok, balance_integer}
  end

  defp load_and_cache_system_balance(system_account_type) do
    try do
      # 从Ledger系统获取系统账户余额
      account = Cypridina.Ledger.get_system_account(system_account_type)

      balance =
        account
        |> Ash.load!(:balance_as_of)
        |> Map.get(:balance_as_of)

      balance_integer = Money.to_decimal(balance) |> Decimal.to_integer()

      # 缓存余额
      cache_key = build_cache_key(:system, system_account_type)
      cache_balance(cache_key, balance_integer)

      {:ok, balance_integer}
    rescue
      error ->
        Logger.warning(
          "Failed to load balance for system account #{system_account_type}: #{inspect(error)}"
        )

        {:error, :load_failed}
    end
  end

  defp load_and_cache_game_balance(game_id) do
    try do
      # 使用Ledger模块获取游戏账户余额（自动创建账户）
      balance_integer = Cypridina.Ledger.get_game_balance(game_id)

      # 缓存余额
      cache_key = build_cache_key(:game, game_id)
      cache_balance(cache_key, balance_integer)

      {:ok, balance_integer}
    rescue
      error ->
        Logger.error("Failed to load balance for game #{game_id}: #{inspect(error)}")
        {:error, :load_failed}
    end
  end

  defp cache_balance(cache_key, balance) do
    # 使用 System.system_time 来避免时区问题
    expires_at = System.system_time(:second) + div(@ttl, 1000)
    :ets.insert(@table_name, {cache_key, balance, expires_at})
    Logger.debug("Cached balance #{balance} for #{cache_key}")
  end

  defp subscribe_to_notifications do
    # 订阅余额资源的变更通知
    # 在测试环境中，Endpoint可能没有启动，所以需要处理异常
    try do
      CypridinaWeb.Endpoint.subscribe("balance")
      Logger.info("BalanceCache subscribed to Balance notifications")
    rescue
      error ->
        Logger.warning("Failed to subscribe to Balance notifications: #{inspect(error)}")
        # 在测试环境中这是正常的，不需要崩溃
        :ok
    end
  end

  defp handle_balance_notification(notification) do
    case notification do
      %{resource: Cypridina.Ledger.Balance, action: :upsert_balance, data: balance} ->
        # 同步处理余额更新通知，确保按顺序处理
        update_balance_from_notification(balance)

      _ ->
        # 忽略其他通知
        :ok
    end
  end

  defp update_balance_from_notification(balance) do
    # 加载账户信息以获取账户类型和相关ID
    balance_with_account = Ash.load!(balance, [:account])
    account = balance_with_account.account
    balance_integer = Money.to_decimal(balance.balance) |> Decimal.to_integer()

    # 根据账户类型更新不同的缓存
    case account.account_type do
      :user when not is_nil(account.user_id) ->
        # 用户账户
        cache_key = build_cache_key(:user, account.user_id)
        cache_balance(cache_key, balance_integer)

        Logger.debug(
          "Updated balance cache for user #{account.user_id} with new balance: #{balance_integer}"
        )

      :system when not is_nil(account.system_account_type) ->
        # 系统账户
        cache_key = build_cache_key(:system, account.system_account_type)
        cache_balance(cache_key, balance_integer)

        Logger.debug(
          "Updated balance cache for system account #{account.system_account_type} with new balance: #{balance_integer}"
        )

      :game ->
        # 游戏账户 - 从identifier中提取game_id
        case extract_game_id_from_identifier(account.identifier) do
          {:ok, game_id} ->
            cache_key = build_cache_key(:game, game_id)
            cache_balance(cache_key, balance_integer)

            Logger.debug(
              "Updated balance cache for game #{game_id} with new balance: #{balance_integer}"
            )

          {:error, _} ->
            Logger.warning("Failed to extract game_id from identifier: #{account.identifier}")
        end

      _ ->
        Logger.debug("Ignored balance notification for account type: #{account.account_type}")
    end
  end

  # 从账户标识符中提取游戏ID
  defp extract_game_id_from_identifier(identifier) do
    AccountIdentifier.extract_game_id(identifier)
  end

  defp cleanup_expired do
    current_time = System.system_time(:second)

    # 使用 foldl 遍历表，找出过期的条目
    expired =
      :ets.foldl(
        fn {cache_key, _balance, expires_at}, acc ->
          if expires_at <= current_time do
            [cache_key | acc]
          else
            acc
          end
        end,
        [],
        @table_name
      )

    # 删除过期条目
    Enum.each(expired, fn cache_key ->
      :ets.delete(@table_name, cache_key)
    end)

    length(expired)
  end

  defp schedule_cleanup do
    Process.send_after(self(), :cleanup, @cleanup_interval)
  end

  defp update_stats(type) do
    case Process.whereis(__MODULE__) do
      nil -> :ok
      pid -> send(pid, {:update_stats, type})
    end
  end

  defp calculate_hit_rate(%{hits: hits, misses: misses}) do
    total = hits + misses

    if total > 0 do
      Float.round(hits / total * 100, 2)
    else
      0.0
    end
  end

  # ========== 原子更新操作 ==========

  @doc """
  原子性更新余额的核心实现

  使用 ETS 的原子操作来确保并发安全性。如果缓存中不存在该键，
  会先尝试从 Ledger 系统加载当前余额，然后执行更新操作。

  ## 参数
  - `cache_key`: 缓存键
  - `amount`: 变更金额
  - `operation`: 操作类型 (:add 或 :subtract)

  ## 返回值
  - `{:ok, new_balance}`: 成功，返回新的余额
  - `{:error, reason}`: 失败，返回错误原因
  """
  defp atomic_update_balance(cache_key, amount, operation) do
    current_time = System.system_time(:second)
    expires_at = current_time + div(@ttl, 1000)

    # 检查缓存是否存在
    case :ets.lookup(@table_name, cache_key) do
      [{^cache_key, _balance, _expires_at}] ->
        # 缓存存在，直接执行原子操作
        new_balance =
          case operation do
            :add -> :ets.update_counter(@table_name, cache_key, {2, amount})
            :subtract -> :ets.update_counter(@table_name, cache_key, {2, -amount})
          end
        {:ok, new_balance}

      [] ->
        # 缓存不存在，从数据库加载
        case load_balance_from_db(cache_key) do
          {:ok, db_balance} ->
            # 从数据库加载成功，执行操作并缓存结果
            new_balance =
              case operation do
                :add -> db_balance + amount
                :subtract -> db_balance - amount
              end

            # 缓存新余额
            cache_balance(cache_key, new_balance)
            {:ok, new_balance}

          {:error, reason} ->
            {:error, reason}
        end
    end
  end

  # 根据缓存键从数据库加载余额
  defp load_balance_from_db(cache_key) do
    cond do
      String.starts_with?(cache_key, "user:") ->
        user_id = String.replace_prefix(cache_key, "user:", "")
        load_and_cache_user_balance(user_id)

      String.starts_with?(cache_key, "system:") ->
        system_type = String.replace_prefix(cache_key, "system:", "") |> String.to_atom()
        load_and_cache_system_balance(system_type)

      String.starts_with?(cache_key, "game:") ->
        game_id = String.replace_prefix(cache_key, "game:", "")
        load_and_cache_game_balance(game_id)

      true ->
        {:error, :invalid_cache_key}
    end
  end

  # ========== Account ID 缓存相关函数 ==========

  # 检查account_id是否在缓存中
  defp account_id_cached?(identifier) when is_binary(identifier) do
    account_id_cache_key = "account_id:#{identifier}"
    cached_by_key?(account_id_cache_key)
  end

  # 从缓存中查找account_id
  defp lookup_account_id(identifier) when is_binary(identifier) do
    account_id_cache_key = "account_id:#{identifier}"

    case :ets.lookup(@table_name, account_id_cache_key) do
      [{^account_id_cache_key, account_id, expires_at}] ->
        current_time = System.system_time(:second)

        if expires_at > current_time do
          {:ok, account_id}
        else
          # 过期了，删除
          :ets.delete(@table_name, account_id_cache_key)
          :not_found
        end

      [] ->
        :not_found
    end
  end

  # 逐个调用Ledger方法获取并缓存account_id
  defp batch_load_and_cache_account_ids(identifiers) when is_list(identifiers) do
    identifiers
    |> Enum.map(&get_account_by_identifier/1)
    |> Enum.reject(&is_nil/1)
    |> Map.new()
  end

  # 根据标识符调用对应的Ledger方法获取账户
  defp get_account_by_identifier(identifier) when is_binary(identifier) do
    case Cypridina.Accounts.AccountIdentifier.parse(identifier) do
      {:ok, %{type: :user, currency: currency, id: user_id}} ->
        get_user_account_by_ledger(user_id, currency, identifier)

      {:ok, %{type: :system, currency: currency, account_type: account_type}} ->
        get_system_account_by_ledger(account_type, currency, identifier)

      {:ok, %{type: :game, currency: currency, id: game_id}} ->
        get_game_account_by_ledger(game_id, currency, identifier)

      {:error, reason} ->
        Logger.warning("Invalid identifier #{identifier}: #{inspect(reason)}")
        nil
    end
  end

  # 调用Ledger.get_user_account获取用户账户
  defp get_user_account_by_ledger(user_id, currency, identifier) do
    case Cypridina.Ledger.get_user_account(user_id, currency) do
      {:ok, account} ->
        # 缓存account_id
        cache_account_id(identifier, account.id)
        {identifier, account.id}

      {:error, _reason} ->
        Logger.debug("User account not found for identifier: #{identifier}")
        nil
    end
  end

  # 调用Ledger.get_system_account获取系统账户
  defp get_system_account_by_ledger(account_type, currency, identifier) do
    account = Cypridina.Ledger.get_system_account(account_type, currency)
    # 缓存account_id
    cache_account_id(identifier, account.id)
    {identifier, account.id}
  end

  # 调用Ledger.get_game_account获取游戏账户
  defp get_game_account_by_ledger(game_id, currency, identifier) do
    try do
      account = Cypridina.Ledger.get_game_account(game_id, currency)
      # 缓存account_id
      cache_account_id(identifier, account.id)
      {identifier, account.id}
    rescue
      error ->
        Logger.warning(
          "Failed to get game account for identifier #{identifier}: #{inspect(error)}"
        )

        nil
    end
  end

  # 缓存account_id
  defp cache_account_id(identifier, account_id)
       when is_binary(identifier) and is_binary(account_id) do
    account_id_cache_key = "account_id:#{identifier}"
    expires_at = System.system_time(:second) + div(@ttl, 1000)
    :ets.insert(@table_name, {account_id_cache_key, account_id, expires_at})
  end
end
