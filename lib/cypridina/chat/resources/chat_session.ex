defmodule Cypridina.Chat.ChatSession do
  @moduledoc """
  聊天会话资源

  管理用户间的聊天会话，支持一对一聊天和群聊功能。
  包括会话创建、参与者管理、会话状态等功能。
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Cypridina.Chat,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :session_type, :title, :status, :creator_id, :last_message_at, :inserted_at]
  end

  postgres do
    table "chat_sessions"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_user
    define :list_active_sessions
    define :get_session_with_participants
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    # 根据用户ID查询会话
    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false

      prepare fn query, _context ->
        require Ash.Query
        import Ash.Expr

        user_id = Ash.Query.get_argument(query, :user_id)

        # 通过参与者关系查询用户的会话
        Ash.Query.filter(query, expr(
          exists(participants, user_id == ^user_id and status == :active)
        ))
      end

      prepare build(load: [:participants, :last_message])
      prepare build(sort: [last_message_at: :desc])
    end

    # 查询活跃会话
    read :list_active_sessions do
      filter expr(status == :active)
      prepare build(load: [:participants, :last_message])
      prepare build(sort: [last_message_at: :desc])
    end

    # 获取会话及其参与者
    read :get_session_with_participants do
      get? true
      prepare build(load: [:participants, :messages, :last_message])
    end

    # 创建一对一聊天会话
    create :create_private_session do
      argument :participant_ids, {:array, :uuid}, allow_nil?: false
      argument :creator_id, :uuid, allow_nil?: false

      validate fn changeset, _context ->
        participant_ids = Ash.Changeset.get_argument(changeset, :participant_ids)

        if length(participant_ids) != 2 do
          {:error, "私聊会话必须包含2个参与者"}
        else
          :ok
        end
      end

      change set_attribute(:session_type, :private)
      change set_attribute(:status, :active)
      change set_attribute(:creator_id, arg(:creator_id))

      change fn changeset, _context ->
        participant_ids = Ash.Changeset.get_argument(changeset, :participant_ids)
        title = "私聊会话"
        Ash.Changeset.change_attribute(changeset, :title, title)
      end
    end

    # 创建群聊会话
    create :create_group_session do
      argument :participant_ids, {:array, :uuid}, allow_nil?: false
      argument :creator_id, :uuid, allow_nil?: false
      argument :title, :string, allow_nil?: false

      validate fn changeset, _context ->
        participant_ids = Ash.Changeset.get_argument(changeset, :participant_ids)

        if length(participant_ids) < 2 do
          {:error, "群聊会话至少需要2个参与者"}
        else
          :ok
        end
      end

      change set_attribute(:session_type, :group)
      change set_attribute(:status, :active)
      change set_attribute(:creator_id, arg(:creator_id))
      change set_attribute(:title, arg(:title))
    end

    # 更新最后消息时间
    update :update_last_message_time do
      accept []
      change set_attribute(:last_message_at, &DateTime.utc_now/0)
    end

    # 归档会话
    update :archive_session do
      accept []
      change set_attribute(:status, :archived)
    end

    # 删除会话
    update :delete_session do
      accept []
      change set_attribute(:status, :deleted)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :session_type, :atom do
      allow_nil? false
      public? true
      description "会话类型：private-私聊，group-群聊"
      constraints one_of: [:private, :group]
    end

    attribute :title, :string do
      allow_nil? true
      public? true
      description "会话标题（群聊时使用）"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "会话描述"
      constraints max_length: 500
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "会话状态：active-活跃，archived-归档，deleted-已删除"
      default :active
      constraints one_of: [:active, :archived, :deleted]
    end

    attribute :creator_id, :uuid do
      allow_nil? false
      public? true
      description "会话创建者ID"
    end

    attribute :last_message_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后消息时间"
    end

    attribute :message_count, :integer do
      allow_nil? false
      public? true
      description "消息总数"
      default 0
    end

    attribute :settings, :map do
      allow_nil? true
      public? true
      description "会话设置（JSON格式）"
      default %{}
    end

    timestamps()
  end

  relationships do
    belongs_to :creator, Cypridina.Accounts.User do
      public? true
      source_attribute :creator_id
      destination_attribute :id
    end

    has_many :participants, Cypridina.Chat.ChatParticipant do
      public? true
      source_attribute :id
      destination_attribute :session_id
    end

    has_many :messages, Cypridina.Chat.ChatMessage do
      public? true
      source_attribute :id
      destination_attribute :session_id
    end

    has_one :last_message, Cypridina.Chat.ChatMessage do
      public? true
      source_attribute :id
      destination_attribute :session_id
      sort inserted_at: :desc
    end
  end

  calculations do
    calculate :participant_count, :integer, expr(count(participants, query: [filter: expr(status == :active)]))

    calculate :unread_count, :integer do
      argument :user_id, :uuid, allow_nil?: false

      calculation fn records, %{user_id: user_id} ->
        # 计算用户在此会话中的未读消息数
        Enum.map(records, fn session ->
          # 这里需要实现具体的未读消息计算逻辑
          0
        end)
      end
    end
  end

  identities do
    # 确保私聊会话的唯一性（通过参与者组合）
    identity :unique_private_session, [:session_type], where: expr(session_type == :private)
  end

  validations do
    validate present([:session_type, :creator_id])

    validate fn changeset, _context ->
      session_type = Ash.Changeset.get_attribute(changeset, :session_type)
      title = Ash.Changeset.get_attribute(changeset, :title)

      case session_type do
        :group when is_nil(title) or title == "" ->
          {:error, field: :title, message: "群聊会话必须有标题"}
        _ ->
          :ok
      end
    end
  end
end
