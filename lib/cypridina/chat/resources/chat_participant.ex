defmodule Cypridina.Chat.ChatParticipant do
  @moduledoc """
  聊天参与者资源

  管理聊天会话中的参与者信息和权限。
  包括参与者加入、退出、权限管理等功能。
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Cypridina.Chat,
    extensions: [AshAdmin.Resource]

  require Ash.Query
  import Ash.Expr

  admin do
    table_columns [:id, :session_id, :user_id, :role, :status, :joined_at, :last_read_at]
  end

  postgres do
    table "chat_participants"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_session
    define :list_by_user
    define :create_initial_participant
    define :add_participant
    define :remove_participant
    define :update_last_read
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    # 根据会话ID查询参与者
    read :list_by_session do
      argument :session_id, :uuid, allow_nil?: false

      filter expr(session_id == ^arg(:session_id) and status == :active)
      prepare build(load: [:user])
      prepare build(sort: [joined_at: :asc])
    end

    # 根据用户ID查询参与的会话
    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false

      filter expr(user_id == ^arg(:user_id) and status == :active)
      prepare build(load: [:session, :user])
      prepare build(sort: [joined_at: :desc])
    end

    # 初始创建参与者（用于新会话创建时，无权限验证）
    create :create_initial_participant do
      argument :session_id, :uuid, allow_nil?: false
      argument :user_id, :uuid, allow_nil?: false
      argument :role, :atom, default: :member
      argument :added_by, :uuid, allow_nil?: true

      change set_attribute(:session_id, arg(:session_id))
      change set_attribute(:user_id, arg(:user_id))
      change set_attribute(:role, arg(:role))
      change set_attribute(:added_by, arg(:added_by))
      change set_attribute(:status, :active)
      change set_attribute(:joined_at, &DateTime.utc_now/0)
    end

    # 添加参与者（用于向现有会话添加新参与者，有权限验证）
    create :add_participant do
      argument :session_id, :uuid, allow_nil?: false
      argument :user_id, :uuid, allow_nil?: false
      argument :role, :atom, default: :member
      argument :added_by, :uuid, allow_nil?: false

      change set_attribute(:session_id, arg(:session_id))
      change set_attribute(:user_id, arg(:user_id))
      change set_attribute(:role, arg(:role))
      change set_attribute(:added_by, arg(:added_by))
      change set_attribute(:status, :active)
      change set_attribute(:joined_at, &DateTime.utc_now/0)

      # 验证权限：只有管理员或群主可以添加成员
      validate fn changeset, _context ->
        session_id = Ash.Changeset.get_argument(changeset, :session_id)
        added_by = Ash.Changeset.get_argument(changeset, :added_by)

        # 检查添加者是否有权限
        case Cypridina.Chat.ChatParticipant
             |> Ash.Query.filter(expr(session_id == ^session_id and user_id == ^added_by))
             |> Ash.read_one() do
          {:ok, %{role: role}} when role in [:admin, :owner] -> :ok
          {:ok, _} -> {:error, "没有权限添加参与者"}
          _ -> {:error, "添加者不在会话中"}
        end
      end
    end

    # 移除参与者
    update :remove_participant do
      argument :removed_by, :uuid, allow_nil?: false

      accept []
      change set_attribute(:status, :removed)
      change set_attribute(:left_at, &DateTime.utc_now/0)

      # 验证权限：只有管理员、群主或本人可以移除
      validate fn changeset, _context ->
        participant = changeset.data
        removed_by = Ash.Changeset.get_argument(changeset, :removed_by)

        cond do
          participant.user_id == removed_by ->
            # 用户自己退出
            :ok
          true ->
            # 检查移除者是否有权限
            case Cypridina.Chat.ChatParticipant
                 |> Ash.Query.filter(expr(session_id == ^participant.session_id and user_id == ^removed_by))
                 |> Ash.read_one() do
              {:ok, %{role: role}} when role in [:admin, :owner] -> :ok
              {:ok, _} -> {:error, "没有权限移除参与者"}
              _ -> {:error, "移除者不在会话中"}
            end
        end
      end
    end

    # 更新最后阅读时间
    update :update_last_read do
      accept []
      change fn changeset, _context ->
        Ash.Changeset.change_attribute(changeset, :last_read_at, DateTime.utc_now())
      end
    end

    # 更新参与者角色
    update :update_role do
      argument :new_role, :atom, allow_nil?: false
      argument :updated_by, :uuid, allow_nil?: false

      accept []
      change set_attribute(:role, arg(:new_role))

      # 验证权限：只有群主可以更改角色
      validate fn changeset, _context ->
        participant = changeset.data
        updated_by = Ash.Changeset.get_argument(changeset, :updated_by)

        case Cypridina.Chat.ChatParticipant
             |> Ash.Query.filter(expr(session_id == ^participant.session_id and user_id == ^updated_by))
             |> Ash.read_one() do
          {:ok, %{role: :owner}} -> :ok
          {:ok, _} -> {:error, "只有群主可以更改成员角色"}
          _ -> {:error, "操作者不在会话中"}
        end
      end
    end

    # 静音/取消静音
    update :toggle_mute do
      accept []

      change fn changeset, _context ->
        current_muted = changeset.data.is_muted
        Ash.Changeset.change_attribute(changeset, :is_muted, !current_muted)
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :session_id, :uuid do
      allow_nil? false
      public? true
      description "所属会话ID"
    end

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "参与者用户ID"
    end

    attribute :role, :atom do
      allow_nil? false
      public? true
      description "参与者角色：owner-群主，admin-管理员，member-普通成员"
      default :member
      constraints one_of: [:owner, :admin, :member]
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "参与状态：active-活跃，removed-已移除，left-已退出"
      default :active
      constraints one_of: [:active, :removed, :left]
    end

    attribute :joined_at, :utc_datetime do
      allow_nil? false
      public? true
      description "加入时间"
      default &DateTime.utc_now/0
    end

    attribute :left_at, :utc_datetime do
      allow_nil? true
      public? true
      description "离开时间"
    end

    attribute :last_read_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后阅读时间"
    end

    attribute :added_by, :uuid do
      allow_nil? true
      public? true
      description "添加者ID"
    end

    attribute :is_muted, :boolean do
      allow_nil? false
      public? true
      description "是否静音"
      default false
    end

    attribute :notification_settings, :map do
      allow_nil? true
      public? true
      description "通知设置（JSON格式）"
      default %{}
    end

    timestamps()
  end

  relationships do
    belongs_to :session, Cypridina.Chat.ChatSession do
      public? true
      source_attribute :session_id
      destination_attribute :id
    end

    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end

    belongs_to :added_by_user, Cypridina.Accounts.User do
      public? true
      source_attribute :added_by
      destination_attribute :id
    end
  end

  calculations do
    # 计算未读消息数量
    calculate :unread_count, :integer do
      calculation fn records, _context ->
        Enum.map(records, fn participant ->
          # 计算该参与者在会话中的未读消息数
          last_read_at = participant.last_read_at || participant.joined_at

          case Cypridina.Chat.ChatMessage
               |> Ash.Query.filter(expr(
                 session_id == ^participant.session_id and
                 sender_id != ^participant.user_id and
                 inserted_at > ^last_read_at
               ))
               |> Ash.read() do
            {:ok, messages} -> length(messages)
            _ -> 0
          end
        end)
      end
    end

    # 检查是否为会话管理员
    calculate :is_admin, :boolean, expr(role in [:owner, :admin])
  end

  identities do
    # 确保用户在同一会话中只能有一个活跃的参与记录
    identity :unique_active_participant, [:session_id, :user_id],
      where: expr(status == :active)
  end

  validations do
    validate present([:session_id, :user_id, :role, :status])

    # 验证群主唯一性
    validate fn changeset, _context ->
      role = Ash.Changeset.get_attribute(changeset, :role)
      session_id = Ash.Changeset.get_attribute(changeset, :session_id)

      if role == :owner and session_id do
        case Cypridina.Chat.ChatParticipant
             |> Ash.Query.filter(expr(session_id == ^session_id and role == :owner and status == :active))
             |> Ash.read() do
          {:ok, []} -> :ok
          {:ok, [existing]} ->
            if existing.id != changeset.data.id do
              {:error, field: :role, message: "每个会话只能有一个群主"}
            else
              :ok
            end
          _ -> {:error, field: :role, message: "检查群主唯一性时出错"}
        end
      else
        :ok
      end
    end
  end
end
