defmodule Cypridina.Chat.ChatService do
  @moduledoc """
  聊天服务模块

  提供聊天系统的核心业务逻辑，包括：
  - 创建聊天会话
  - 发送消息
  - 管理参与者
  - 消息状态更新
  """

  require Logger
  require Ash.Query
  import Ash.Expr

  alias Cypridina.Chat.{ChatSession, ChatMessage, ChatParticipant, MessageReadReceipt}
  alias Cypridina.Uploaders.ChatFile
  alias Cypridina.Services.UploadService

  @doc """
  创建或获取一对一聊天会话
  """
  def create_or_get_private_session(user1_id, user2_id) do
    # 检查是否已存在私聊会话
    case find_existing_private_session(user1_id, user2_id) do
      {:ok, session} ->
        {:ok, session}

      {:error, :not_found} ->
        # 创建新的私聊会话
        with {:ok, session} <- create_private_session(user1_id, user2_id),
             {:ok, _} <- add_participants_to_session(session.id, [user1_id, user2_id], user1_id) do
          {:ok, session}
        end
    end
  end

  @doc """
  创建群聊会话
  """
  def create_group_session(creator_id, participant_ids, title, description \\ nil) do
    # 确保创建者在参与者列表中
    all_participants = [creator_id | participant_ids] |> Enum.uniq()

    with {:ok, session} <- ChatSession
                           |> Ash.Changeset.for_create(:create_group_session, %{
                             participant_ids: all_participants,
                             creator_id: creator_id,
                             title: title
                           })
                           |> Ash.Changeset.change_attribute(:description, description)
                           |> Ash.create(),
         {:ok, _} <- add_participants_to_session(session.id, all_participants, creator_id) do
      {:ok, session}
    end
  end

  @doc """
  发送消息
  """
  def send_message(session_id, sender_id, content, opts \\ []) do
    message_type = Keyword.get(opts, :message_type, :text)
    reply_to_id = Keyword.get(opts, :reply_to_id)
    attachments = Keyword.get(opts, :attachments, [])

    # 验证发送者是否在会话中
    with {:ok, _participant} <- verify_participant_in_session(session_id, sender_id),
         {:ok, message} <- ChatMessage
                           |> Ash.Changeset.for_create(:send_message, %{
                             session_id: session_id,
                             sender_id: sender_id,
                             content: content,
                             message_type: message_type,
                             reply_to_id: reply_to_id,
                             attachments: attachments
                           })
                           |> Ash.create() do

      # 更新会话的最后消息时间
      update_session_last_message_time(session_id)

      # 广播消息到会话频道
      broadcast_message(message)

      {:ok, message}
    end
  end

  @doc """
  上传聊天文件并发送消息
  """
  def send_file_message(session_id, sender_id, file, opts \\ []) do
    reply_to_id = Keyword.get(opts, :reply_to_id)
    caption = Keyword.get(opts, :caption, "")

    # 验证发送者是否在会话中
    with {:ok, _participant} <- verify_participant_in_session(session_id, sender_id),
         {:ok, file_info} <- upload_chat_file(session_id, sender_id, file) do

      # 根据文件类型确定消息类型
      message_type = determine_message_type(file_info.file_type)

      # 构建附件信息
      attachments = [%{
        file_name: file_info.original_name,
        file_size: file_info.file_size,
        file_type: file_info.file_type,
        file_url: file_info.file_url,
        thumb_url: file_info.thumb_url,
        mime_type: file_info.mime_type
      }]

      # 发送消息
      send_message(session_id, sender_id, caption, [
        message_type: message_type,
        reply_to_id: reply_to_id,
        attachments: attachments
      ])
    end
  end

  @doc """
  上传聊天文件
  """
  def upload_chat_file(session_id, sender_id, file) do
    scope = %{session_id: session_id, sender_id: sender_id}

    try do
      case ChatFile.store({file, scope}) do
        {:ok, filename} ->
          file_type = ChatFile.get_file_type({file, scope})

          file_info = %{
            filename: filename,
            original_name: file.filename || file.file_name,
            file_size: file.size || file.file_size,
            file_type: file_type,
            file_url: ChatFile.url({filename, scope}, :original),
            thumb_url: if(ChatFile.is_image?({file, scope}), do: ChatFile.url({filename, scope}, :thumb)),
            mime_type: MIME.from_path(file.filename || file.file_name)
          }

          Logger.info("聊天文件上传成功 - 会话: #{session_id}, 用户: #{sender_id}, 文件: #{file_info.original_name}")
          {:ok, file_info}

        {:error, reason} ->
          Logger.error("聊天文件上传失败 - 会话: #{session_id}, 用户: #{sender_id}, 原因: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      error ->
        Logger.error("聊天文件上传异常 - 会话: #{session_id}, 用户: #{sender_id}, 异常: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  从Base64数据上传聊天文件
  """
  def upload_chat_file_from_base64(session_id, sender_id, base64_data, filename, opts \\ []) do
    file_extension = Keyword.get(opts, :file_extension, Path.extname(filename))

    try do
      # 处理data URL格式，提取纯base64数据
      clean_base64_data = case String.contains?(base64_data, ",") do
        true ->
          base64_data |> String.split(",", parts: 2) |> List.last()
        false ->
          base64_data
      end

      # 解码base64数据
      binary_data = Base.decode64!(clean_base64_data)

      # 创建临时文件
      temp_file = create_temp_file(binary_data, filename)

      # 上传文件
      result = upload_chat_file(session_id, sender_id, temp_file)

      # 清理临时文件
      File.rm!(temp_file.path)

      result
    rescue
      error ->
        Logger.error("Base64聊天文件上传异常 - 会话: #{session_id}, 用户: #{sender_id}, 异常: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  生成聊天文件上传的预签名URL
  """
  def generate_chat_file_upload_url(session_id, sender_id, filename, content_type) do
    try do
      # 验证发送者是否在会话中
      with {:ok, _participant} <- verify_participant_in_session(session_id, sender_id) do
        # 生成文件路径
        timestamp = System.system_time(:millisecond)
        file_extension = Path.extname(filename)
        file_path = "chat_files/#{session_id}/#{sender_id}/#{timestamp}#{file_extension}"

        # 生成预签名URL
        config = ExAws.Config.new(:s3)
        bucket = get_bucket()

        case ExAws.S3.presigned_url(config, :put, bucket, file_path,
               expires_in: 3600,
               query_params: [{"Content-Type", content_type}]) do
          {:ok, upload_url} ->
            Logger.info("聊天文件预签名URL生成成功 - 会话: #{session_id}, 用户: #{sender_id}, 路径: #{file_path}")
            {:ok, upload_url, file_path}

          {:error, reason} ->
            Logger.error("聊天文件预签名URL生成失败 - 会话: #{session_id}, 用户: #{sender_id}, 原因: #{inspect(reason)}")
            {:error, reason}
        end
      end
    rescue
      error ->
        Logger.error("聊天文件预签名URL生成异常 - 会话: #{session_id}, 用户: #{sender_id}, 异常: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  标记消息为已读
  """
  def mark_message_as_read(message_id, user_id) do
    MessageReadReceipt
    |> Ash.Changeset.for_create(:mark_message_read, %{
      message_id: message_id,
      user_id: user_id
    })
    |> Ash.create()
  end

  @doc """
  批量标记会话消息为已读
  """
  def mark_session_messages_as_read(session_id, user_id, up_to_message_id \\ nil) do
    # 更新参与者的最后阅读时间
    with {:ok, participant} <- get_participant(session_id, user_id),
         {:ok, updated_participant} <- participant
                     |> Ash.Changeset.for_update(:update_last_read, %{})
                     |> Ash.update() do

      # 标记未读消息为已读
      result = mark_unread_messages_as_read(session_id, user_id, up_to_message_id)

      # 广播已读通知
      broadcast_read_receipt(session_id, user_id, updated_participant.last_read_at)

      result
    end
  end

  @doc """
  添加参与者到会话
  """
  def add_participant_to_session(session_id, user_id, added_by_id, role \\ :member) do
    ChatParticipant
    |> Ash.Changeset.for_create(:add_participant, %{
      session_id: session_id,
      user_id: user_id,
      role: role,
      added_by: added_by_id
    })
    |> Ash.create()
  end

  @doc """
  从会话中移除参与者
  """
  def remove_participant_from_session(session_id, user_id, removed_by_id) do
    with {:ok, participant} <- get_participant(session_id, user_id) do
      participant
      |> Ash.Changeset.for_update(:remove_participant, %{removed_by: removed_by_id})
      |> Ash.update()
    end
  end

  @doc """
  获取用户的聊天会话列表
  """
  def list_user_sessions(user_id, opts \\ []) do
    limit = Keyword.get(opts, :limit, 20)
    offset = Keyword.get(opts, :offset, 0)

    ChatSession
    |> Ash.Query.for_read(:list_by_user, %{user_id: user_id})
    |> Ash.Query.limit(limit)
    |> Ash.Query.offset(offset)
    |> Ash.read()
  end

  @doc """
  获取会话消息列表
  """
  def list_session_messages(session_id, user_id, opts \\ []) do
    limit = Keyword.get(opts, :limit, 50)
    offset = Keyword.get(opts, :offset, 0)

    # 验证用户是否在会话中
    with {:ok, _participant} <- verify_participant_in_session(session_id, user_id) do
      ChatMessage
      |> Ash.Query.for_read(:list_by_session, %{
        session_id: session_id,
        limit: limit,
        offset: offset
      })
      |> Ash.read()
    end
  end

  @doc """
  获取用户的未读消息数量
  """
  def get_unread_message_count(user_id, session_id \\ nil) do
    ChatMessage
    |> Ash.Query.for_read(:list_unread_messages, %{
      user_id: user_id,
      session_id: session_id
    })
    |> Ash.read()
    |> case do
      {:ok, messages} -> {:ok, length(messages)}
      error -> error
    end
  end

  # 私有函数

  defp find_existing_private_session(user1_id, user2_id) do
    # 查找两个用户之间的私聊会话
    # 这需要通过参与者关系来查询
    case ChatSession
         |> Ash.Query.filter(expr(session_type == :private))
         |> Ash.Query.load(:participants)
         |> Ash.read() do
      {:ok, sessions} ->
        # 查找包含这两个用户的私聊会话
        session = Enum.find(sessions, fn session ->
          participant_ids = Enum.map(session.participants, & &1.user_id)
          Enum.sort([user1_id, user2_id]) == Enum.sort(participant_ids)
        end)

        if session do
          {:ok, session}
        else
          {:error, :not_found}
        end

      error ->
        error
    end
  end

  defp create_private_session(user1_id, user2_id) do
    ChatSession
    |> Ash.Changeset.for_create(:create_private_session, %{
      participant_ids: [user1_id, user2_id],
      creator_id: user1_id
    })
    |> Ash.create()
  end

  defp add_participants_to_session(session_id, participant_ids, added_by_id) do
    Logger.info("💬 [CHAT] 开始添加参与者 - 会话: #{session_id}, 参与者: #{inspect(participant_ids)}, 添加者: #{added_by_id}")

    results = Enum.map(participant_ids, fn user_id ->
      role = if user_id == added_by_id, do: :owner, else: :member

      Logger.info("💬 [CHAT] 添加参与者 - 用户: #{user_id}, 角色: #{role}")

      # 对于新会话的初始参与者，使用create_initial_participant action
      # 这个action没有权限验证，专门用于会话创建时添加初始参与者
      attrs = %{
        session_id: session_id,
        user_id: user_id,
        role: role,
        added_by: added_by_id
      }

      Logger.info("💬 [CHAT] 创建参与者记录 - 属性: #{inspect(attrs)}")

      result = ChatParticipant
      |> Ash.Changeset.for_create(:create_initial_participant, attrs)
      |> Ash.create()

      case result do
        {:ok, participant} ->
          Logger.info("💬 [CHAT] 参与者添加成功 - ID: #{participant.id}")
        {:error, reason} ->
          Logger.error("💬 [CHAT] 参与者添加失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
      end

      result
    end)

    # 检查是否所有参与者都添加成功
    if Enum.all?(results, &match?({:ok, _}, &1)) do
      Logger.info("💬 [CHAT] 所有参与者添加成功")
      {:ok, :success}
    else
      failed_results = Enum.filter(results, &match?({:error, _}, &1))
      Logger.error("💬 [CHAT] 添加参与者失败: #{inspect(failed_results)}")
      {:error, :failed_to_add_participants}
    end
  end

  defp verify_participant_in_session(session_id, user_id) do
    ChatParticipant
    |> Ash.Query.filter(expr(session_id == ^session_id and user_id == ^user_id and status == :active))
    |> Ash.read_one()
    |> case do
      {:ok, participant} when not is_nil(participant) -> {:ok, participant}
      {:ok, nil} -> {:error, :not_participant}
      error -> error
    end
  end

  defp get_participant(session_id, user_id) do
    ChatParticipant
    |> Ash.Query.filter(expr(session_id == ^session_id and user_id == ^user_id))
    |> Ash.read_one()
  end

  defp update_session_last_message_time(session_id) do
    case ChatSession |> Ash.get(session_id) do
      {:ok, session} ->
        session
        |> Ash.Changeset.for_update(:update_last_message_time, %{})
        |> Ash.update()

      error ->
        Logger.error("Failed to update session last message time: #{inspect(error)}")
        error
    end
  end

  defp mark_unread_messages_as_read(session_id, user_id, up_to_message_id) do
    # 获取未读消息
    query = ChatMessage
    |> Ash.Query.filter(expr(
      session_id == ^session_id and
      sender_id != ^user_id
    ))

    query = if up_to_message_id do
      case ChatMessage |> Ash.get(up_to_message_id) do
        {:ok, message} ->
          Ash.Query.filter(query, expr(inserted_at <= ^message.inserted_at))
        _ ->
          query
      end
    else
      query
    end

    case Ash.read(query) do
      {:ok, messages} ->
        # 为每条消息创建已读回执
        Enum.each(messages, fn message ->
          MessageReadReceipt
          |> Ash.Changeset.for_create(:mark_message_read, %{
            message_id: message.id,
            user_id: user_id
          })
          |> Ash.create()
        end)

      error ->
        Logger.error("Failed to mark messages as read: #{inspect(error)}")
        error
    end
  end

  defp broadcast_message(message) do
    # 通过 Phoenix.PubSub 广播消息
    Phoenix.PubSub.broadcast(
      Cypridina.PubSub,
      "chat_session:#{message.session_id}",
      {:new_message, message}
    )
  end

  # 根据文件类型确定消息类型
  defp determine_message_type(file_type) do
    case file_type do
      :image -> :image
      :audio -> :audio
      :video -> :video
      _ -> :file
    end
  end

  # 创建临时文件
  defp create_temp_file(binary_data, filename) do
    temp_dir = System.tmp_dir!()
    temp_path = Path.join(temp_dir, "chat_upload_#{System.system_time(:millisecond)}_#{filename}")

    File.write!(temp_path, binary_data)

    %{
      path: temp_path,
      filename: filename,
      file_name: filename,
      size: byte_size(binary_data),
      file_size: byte_size(binary_data)
    }
  end

  # 获取存储桶名称
  defp get_bucket do
    Application.get_env(:waffle, :bucket, "cypridina")
  end

  # 广播已读回执
  defp broadcast_read_receipt(session_id, user_id, read_at) do
    Logger.info("📖 [READ] 广播已读回执 - 会话: #{session_id}, 用户: #{user_id}")

    Phoenix.PubSub.broadcast(
      Cypridina.PubSub,
      "chat_session:#{session_id}",
      {:read_receipt, %{
        session_id: session_id,
        user_id: user_id,
        read_at: read_at
      }}
    )
  end
end
