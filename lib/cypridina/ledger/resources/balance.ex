defmodule Cypridina.Ledger.Balance do
  @moduledoc """
  余额快照资源

  记录每次转账后的账户余额，支持：
  - 历史时点余额查询
  - 余额变化追踪
  - 审计和对账
  """

  use Ash.Resource,
    domain: Cypridina.Ledger,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshDoubleEntry.Balance, AshAdmin.Resource],
    notifiers: [Ash.Notifier.PubSub]

  balance do
    # 配置相关资源
    transfer_resource Cypridina.Ledger.Transfer
    account_resource Cypridina.Ledger.Account
  end

  admin do
    table_columns [:id, :account, :transfer, :balance]
  end

  postgres do
    table "ledger_balances"
    repo Cypridina.Repo

    references do
      reference :transfer, on_delete: :delete
    end
  end

  code_interface do
    define :read
    define :upsert_balance, action: :upsert_balance
  end

  actions do
    # ash_double_entry会自动添加read和upsert_balance actions
    create :upsert_balance do
      accept [:balance, :account_id, :transfer_id]
      upsert? true
      upsert_identity :unique_references
    end

    # 自定义读取操作
    read :read do
      primary? true
      # 配置keyset分页用于流式处理
      pagination keyset?: true, required?: false
    end

    update :adjust_balance do
      argument :from_account_id, :uuid_v7, allow_nil?: false
      argument :to_account_id, :uuid_v7, allow_nil?: false
      argument :delta, :money, allow_nil?: false
      argument :transfer_id, AshDoubleEntry.ULID, allow_nil?: false

      change filter expr(
                      account_id in [^arg(:from_account_id), ^arg(:to_account_id)] and
                        transfer_id > ^arg(:transfer_id)
                    )

      change {AshDoubleEntry.Balance.Changes.AdjustBalance, can_add_money?: true}
    end

    # 按账户查询余额历史
    read :by_account do
      argument :account_id, :uuid, allow_nil?: false
      filter account_id: arg(:account_id)
      pagination offset?: true, keyset?: true, countable: true
    end

    # 按用户查询余额历史（通过identifier）
    read :by_user do
      argument :user_id, :string, allow_nil?: false
      argument :currency, :atom, allow_nil?: false, default: :XAA

      prepare fn query, _context ->
        user_id = Ash.Query.get_argument(query, :user_id)
        currency = Ash.Query.get_argument(query, :currency)
        user_identifier = "user:#{currency}:#{user_id}"

        # 首先获取用户账户
        case Cypridina.Ledger.Account.get_by_identifier(user_identifier) do
          {:ok, account} ->
            account_id = account.id
            Ash.Query.filter(query, account_id: account_id)
          {:error, _} ->
            # 如果账户不存在，返回空结果
            Ash.Query.filter(query, account_id: nil)
        end
      end

      prepare build(load: [:account])
    end

    # 获取指定时间点的余额
    read :balance_at_time do
      argument :account_id, :uuid, allow_nil?: false
      argument :timestamp, :utc_datetime_usec, allow_nil?: false

      filter account_id: arg(:account_id)

      get? true
    end

    # 获取最新余额
    read :latest_balance do
      argument :account_id, :uuid, allow_nil?: false
      filter account_id: arg(:account_id)
      get? true
    end
  end

  # 添加 pub_sub 通知，当余额更新时通知相关组件
  pub_sub do
    module CypridinaWeb.Endpoint
    prefix "balance"

    # 发布余额更新事件，使用 publish 来发布自定义动作的事件
    # 这将发布到 "balance" 主题
    publish :upsert_balance, ["balance"]

    # 也发布标准的 create/update 事件
    # publish_all :create, [[:account_id, nil]]
    # publish_all :update, [[:account_id, nil]]
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :balance, :money do
      constraints storage_type: :money_with_currency
      description "账户余额"
    end

    attribute :inserted_at, :utc_datetime_usec do
      allow_nil? false
      default &DateTime.utc_now/0
      writable? false
      description "创建时间"
    end

    attribute :updated_at, :utc_datetime_usec do
      allow_nil? false
      default &DateTime.utc_now/0
      update_default &DateTime.utc_now/0
      description "更新时间"
    end
  end

  relationships do
    belongs_to :transfer, Cypridina.Ledger.Transfer do
      attribute_type AshDoubleEntry.ULID
      allow_nil? false
      attribute_writable? true
    end

    belongs_to :account, Cypridina.Ledger.Account do
      allow_nil? false
      attribute_writable? true
    end
  end

  identities do
    identity :unique_references, [:account_id, :transfer_id]
  end
end
