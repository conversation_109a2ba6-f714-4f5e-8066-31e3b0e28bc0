defmodule Cypridina.Chat do
  @moduledoc """
  聊天系统领域模块

  提供用户间聊天功能，包括：
  - 一对一聊天
  - 群聊功能
  - 消息发送和接收
  - 消息状态管理
  - 实时通知
  """

  use Ash.Domain,
    otp_app: :cypridina

  resources do
    resource Cypridina.Chat.ChatSession
    resource Cypridina.Chat.ChatMessage
    resource Cypridina.Chat.ChatParticipant
    resource Cypridina.Chat.MessageReadReceipt
  end

  authorization do
    authorize :by_default
  end
end
