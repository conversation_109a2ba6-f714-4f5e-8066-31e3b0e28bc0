defmodule CypridinaWeb.Components.Charts do
  @moduledoc """
  图表相关的Phoenix Components
  """
  use Phoenix.Component

  @doc """
  渲染选手名次走势图组件

  ## 示例

      <.ranking_chart
        id="ranking-chart"
        races={@recent_races}
        animals={@animals}
        title="选手名次走势图 (最近5场)"
      />
  """
  attr :id, :string, required: true, doc: "图表的唯一ID"
  attr :races, :list, required: true, doc: "近期比赛数据列表"
  attr :animals, :list, required: true, doc: "选手列表"
  attr :title, :string, default: "选手名次走势图", doc: "图表标题"
  attr :class, :string, default: "", doc: "图表容器的额外CSS类"

  def ranking_chart(assigns) do
    ~H"""
    <div class={"ranking-chart-container #{@class}"}>
      <div class="chart-title">
        <h3>{@title}</h3>
      </div>

      <div class="chart-wrapper">
        <div
          id={@id}
          phx-hook="RacingChart"
          data-chart-data={Jason.encode!(@races)}
          data-animals={Jason.encode!(@animals)}
          phx-update="ignore"
        >
        </div>
      </div>
    </div>
    """
  end
end
