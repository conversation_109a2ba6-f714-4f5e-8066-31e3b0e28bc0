<!DOCTYPE html>
<html lang="en" data-theme={assigns[:theme] || "light"} class="h-full">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content={get_csrf_token()} />
    <.live_title default="动物运动会" suffix="">
      {assigns[:page_title]}
    </.live_title>
    <link phx-track-static rel="stylesheet" href={~p"/assets/css/app.css"} />
    <!-- FontAwesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script defer phx-track-static type="text/javascript" src={~p"/assets/js/app.js"}>
    </script>

    <script>
      (() => {
        const setTheme = (theme) => {
          if (theme === "system") {
            localStorage.removeItem("phx:theme");
            document.documentElement.setAttribute("data-theme", "light");
          } else {
            localStorage.setItem("phx:theme", theme);
            document.documentElement.setAttribute("data-theme", theme);
          }
        };
        setTheme(localStorage.getItem("phx:theme") || "light");
        window.addEventListener("storage", (e) => e.key === "phx:theme" && setTheme(e.newValue || "light"));
        window.addEventListener("phx:set-theme", ({ detail: { theme } }) => setTheme(theme));
      })();
    </script>
  </head>

  <body class="h-full">
    {@inner_content}
  </body>
</html>
