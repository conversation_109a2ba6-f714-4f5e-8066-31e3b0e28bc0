defmodule CypridinaWeb.Plugs.AdminAuth do
  @moduledoc """
  管理员权限验证插件
  基于新的权限级别系统：
  - 0: 普通用户
  - 1: 管理员
  - 2: 超级管理员
  """

  import Plug.Conn
  import Phoenix.Controller

  alias Cypridina.Accounts

  @doc """
  检查用户是否具有指定权限级别
  """
  def init(opts), do: opts

  def call(conn, opts) do
    # 默认需要管理员权限
    required_level = Keyword.get(opts, :permission_level, 1)

    case get_current_user(conn) do
      {:ok, user} ->
        if Accounts.has_permission_level?(user, required_level) do
          conn
        else
          level_name = Accounts.permission_level_name(required_level)
          handle_unauthorized(conn, opts, "您没有 #{level_name} 权限")
        end

      {:error, _reason} ->
        handle_unauthorized(conn, opts, "请先登录")
    end
  end

  @doc """
  要求管理员权限的便捷函数
  """
  def require_admin(conn, _opts) do
    call(conn, permission_level: 1)
  end

  @doc """
  要求超级管理员权限的便捷函数
  """
  def require_super_admin(conn, _opts) do
    call(conn, permission_level: 2)
  end

  @doc """
  检查权限但不阻止请求，只是在conn中设置权限信息
  """
  def check_permission_level(conn, opts) do
    required_level = Keyword.get(opts, :permission_level, 1)

    case get_current_user(conn) do
      {:ok, user} ->
        has_permission = Accounts.has_permission_level?(user, required_level)

        conn
        |> assign(:current_user, user)
        |> assign(:has_required_permission, has_permission)
        |> assign(:user_permission_level, user.permission_level)

      {:error, _reason} ->
        conn
        |> assign(:current_user, nil)
        |> assign(:has_required_permission, false)
        |> assign(:user_permission_level, 0)
    end
  end

  @doc """
  API版本的权限检查，返回JSON错误
  """
  def check_api_permission_level(conn, opts) do
    required_level = Keyword.get(opts, :permission_level, 1)

    case get_current_user(conn) do
      {:ok, user} ->
        if Accounts.has_permission_level?(user, required_level) do
          {:ok, user}
        else
          level_name = Accounts.permission_level_name(required_level)
          {:error, :forbidden, "您没有 #{level_name} 权限"}
        end

      {:error, reason} ->
        {:error, :unauthorized, "认证失败: #{reason}"}
    end
  end

  # 私有函数
  defp get_current_user(conn) do
    case conn.assigns[:current_user] do
      nil ->
        # 尝试从session或token获取用户
        case get_session(conn, :user_token) do
          nil ->
            {:error, "未找到用户令牌"}

          token ->
            case AshAuthentication.subject_to_user(token, Cypridina.Accounts.User) do
              {:ok, user} -> {:ok, user}
              {:error, reason} -> {:error, reason}
            end
        end

      user ->
        {:ok, user}
    end
  end

  defp handle_unauthorized(conn, opts, message) do
    case Keyword.get(opts, :format, :html) do
      :json ->
        conn
        |> put_status(:forbidden)
        |> json(%{error: message})
        |> halt()

      :html ->
        conn
        |> put_flash(:error, message)
        |> redirect(to: "/auth/sign_in")
        |> halt()
    end
  end
end
