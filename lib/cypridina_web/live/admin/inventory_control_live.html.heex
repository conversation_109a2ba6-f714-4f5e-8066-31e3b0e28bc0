<!-- 钱包波动仪表盘 -->
<div class="min-h-screen bg-gray-50">
  <!-- 顶部导航 -->
  <div class="bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-4">
        <div class="flex items-center space-x-4">
          <h1 class="text-2xl font-bold text-gray-900">钱包波动仪表盘</h1>
          <button
            phx-click="refresh"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            刷新
          </button>
        </div>

        <!-- 时间范围切换 -->
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-500">时间范围:</span>
          <div class="flex rounded-md shadow-sm">
            <button
              phx-click="toggle_time_range"
              phx-value-range="minute"
              class={"px-3 py-2 text-sm font-medium border #{if @time_range == :minute, do: "bg-indigo-600 text-white border-indigo-600", else: "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"} rounded-l-md"}
            >
              分钟
            </button>
            <button
              phx-click="toggle_time_range"
              phx-value-range="hour"
              class={"px-3 py-2 text-sm font-medium border-t border-b #{if @time_range == :hour, do: "bg-indigo-600 text-white border-indigo-600", else: "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"}"}
            >
              小时
            </button>
            <button
              phx-click="toggle_time_range"
              phx-value-range="day"
              class={"px-3 py-2 text-sm font-medium border #{if @time_range == :day, do: "bg-indigo-600 text-white border-indigo-600", else: "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"} rounded-r-md"}
            >
              天
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 主要内容区域 -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- 总览卡片 -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-900">总钱包余额</h2>
        <div class="text-3xl font-bold text-gray-900">
          ¥<%= format_number(@total_balance) %>
        </div>
      </div>

      <!-- 总钱包波动曲线 -->
      <div class="h-64 bg-gray-50 rounded-lg p-4">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-sm font-medium text-gray-700">总钱包波动曲线</h3>
          <span class="text-xs text-gray-500">
            时间范围: <%= case @time_range do
              :minute -> "最近60分钟"
              :hour -> "最近24小时"
              :day -> "最近30天"
            end %>
          </span>
        </div>
        <div
          id="total-wallet-chart"
          phx-hook="MiniWalletChart"
          data-chart-data={Jason.encode!(convert_total_history_to_chart_data(@total_history))}
          data-time-range={@time_range}
          phx-update="ignore"
          class="w-full h-full"
        ></div>
      </div>
    </div>

    <!-- 游戏列表 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <%= for game <- @games do %>
        <div class="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer"
             phx-click="navigate_to_game"
             phx-value-game_id={game.id}>
          <div class="p-6">
            <!-- 游戏标题 -->
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900"><%= game.name %></h3>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <%= game.type %>
              </span>
            </div>

            <!-- 当前余额 -->
            <div class="mb-4">
              <div class="text-2xl font-bold text-gray-900">
                ¥<%= format_number(game.current_balance) %>
              </div>
              <div class="text-sm text-gray-500">
                基准: ¥<%= format_number(game.base_balance) %>
              </div>
            </div>

            <!-- 控制状态 -->
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500">状态:</span>
                <span class={"text-sm font-medium #{get_action_color(game.action)}"}>
                  <%= get_action_text(game.action) %>
                </span>
              </div>

              <%= if game.action_strength > 0 do %>
                <div class="text-sm text-gray-500">
                  强度: <%= Float.round(game.action_strength, 2) %>
                </div>
              <% end %>
            </div>

            <!-- 余额比例条 -->
            <div class="mt-4">
              <div class="flex justify-between text-xs text-gray-500 mb-1">
                <span>收分线</span>
                <span>中心线</span>
                <span>放分线</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <% percentage = calculate_inventory_percentage(game.current_balance, game.base_balance) %>
                <div class={"h-2 rounded-full #{get_inventory_bar_color(percentage)}"}
                     style={"width: #{min(100, percentage)}%"}>
                </div>
              </div>
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>80%</span>
                <span>100%</span>
                <span>120%</span>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <!-- 选中游戏的详细信息 -->
    <%= if @selected_game do %>
      <div class="mt-6 bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">
          游戏详情 - <%= Enum.find(@games, fn g -> g.id == String.to_integer(@selected_game) end).name %>
        </h2>

        <!-- 游戏波动曲线 -->
        <div class="h-64 bg-gray-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-sm font-medium text-gray-700">游戏钱包波动曲线</h3>
            <span class="text-xs text-gray-500">显示三线控制状态</span>
          </div>
          <div
            id="game-wallet-chart"
            phx-hook="MiniWalletChart"
            data-chart-data={Jason.encode!(convert_game_history_to_chart_data(@game_history))}
            data-time-range={@time_range}
            phx-update="ignore"
            class="w-full h-full"
          ></div>
        </div>
      </div>
    <% end %>
  </div>
</div>
