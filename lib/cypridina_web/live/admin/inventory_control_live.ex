defmodule CypridinaWeb.Live.Admin.InventoryControlLive do
  @moduledoc """
  钱包波动仪表盘

  实时监控游戏钱包余额变化，提供三线控制决策：
  - 实时波动曲线
  - 游戏钱包状态
  - 控制决策显示
  """

  use CypridinaWeb, :live_view
  require Logger

  alias Teen.Resources.Inventory.WalletControl

  @refresh_interval 3000  # 3秒刷新一次

  # ==================== LiveView 回调 ====================

  @impl true
  def mount(_params, _session, socket) do
    Logger.info("📊 [WALLET_DASHBOARD] 钱包仪表盘启动")

    if connected?(socket) do
      # 设置定时刷新
      Process.send_after(self(), :refresh_data, @refresh_interval)
    end

    socket =
      socket
      |> assign(:page_title, "钱包波动仪表盘")
      |> assign(:loading, true)
      |> assign(:games, [])
      |> assign(:selected_game, nil)
      |> assign(:time_range, :hour)
      |> assign(:total_balance, 0)
      |> assign(:total_history, [])
      |> assign(:game_history, [])
      |> load_initial_data()
      |> load_total_history(:hour)

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    selected_game = Map.get(params, "game_id")

    socket =
      socket
      |> assign(:selected_game, selected_game)
      |> load_game_data(selected_game)

    {:noreply, socket}
  end

  @impl true
  def handle_event("navigate_to_game", %{"game_id" => game_id}, socket) do
    {:noreply, push_navigate(socket, to: ~p"/admin/inventory_control/game/#{game_id}")}
  end

  @impl true
  def handle_event("refresh", _params, socket) do
    socket = load_initial_data(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_time_range", %{"range" => range}, socket) do
    time_range = String.to_atom(range)

    socket =
      socket
      |> assign(:time_range, time_range)
      |> load_total_history(time_range)
      |> load_game_data(socket.assigns.selected_game)

    {:noreply, socket}
  end

  @impl true
  def handle_info(:refresh_data, socket) do
    # 定时刷新数据
    socket =
      socket
      |> load_initial_data()
      |> load_total_history(socket.assigns.time_range)
      |> load_game_data(socket.assigns.selected_game)

    # 设置下次刷新
    Process.send_after(self(), :refresh_data, @refresh_interval)

    {:noreply, socket}
  end

  @impl true
  def handle_info(_msg, socket) do
    {:noreply, socket}
  end

  # ==================== 私有函数 ====================

  # 格式化数字显示
  defp format_number(number) when is_number(number) do
    Number.Delimit.number_to_delimited(number, precision: 0)
  end
  defp format_number(_), do: "0"

  # 获取动作文本
  defp get_action_text(:force_collect), do: "强制收分"
  defp get_action_text(:collect), do: "收分"
  defp get_action_text(:force_release), do: "强制放分"
  defp get_action_text(:release), do: "放分"
  defp get_action_text(:random), do: "随机"
  defp get_action_text(_), do: "未知"

  # 获取动作颜色
  defp get_action_color(:force_collect), do: "text-red-600"
  defp get_action_color(:collect), do: "text-orange-600"
  defp get_action_color(:force_release), do: "text-green-600"
  defp get_action_color(:release), do: "text-blue-600"
  defp get_action_color(:random), do: "text-gray-600"
  defp get_action_color(_), do: "text-gray-400"

  # 计算库存百分比
  defp calculate_inventory_percentage(current_balance, base_balance) when base_balance > 0 do
    (current_balance / base_balance) * 100
  end
  defp calculate_inventory_percentage(_, _), do: 0

  # 获取库存条颜色
  defp get_inventory_bar_color(percentage) when percentage <= 60, do: "bg-red-500"
  defp get_inventory_bar_color(percentage) when percentage <= 80, do: "bg-orange-500"
  defp get_inventory_bar_color(percentage) when percentage <= 120, do: "bg-green-500"
  defp get_inventory_bar_color(percentage) when percentage <= 150, do: "bg-blue-500"
  defp get_inventory_bar_color(_), do: "bg-purple-500"

  defp load_initial_data(socket) do
    # 获取所有游戏及其余额
    games = WalletControl.get_all_wallet_status()
    total_balance = WalletControl.get_total_balance()

    socket
    |> assign(:loading, false)
    |> assign(:games, games)
    |> assign(:total_balance, total_balance)
  end

  defp load_total_history(socket, time_range) do
    total_history = WalletControl.get_total_wallet_history(time_range)

    socket
    |> assign(:total_history, total_history)
  end

  defp load_game_data(socket, nil), do: socket
  defp load_game_data(socket, game_id) when is_binary(game_id) do
    load_game_data(socket, String.to_integer(game_id))
  end
  defp load_game_data(socket, game_id) when is_integer(game_id) do
    # 获取游戏历史数据
    game_history = WalletControl.get_wallet_history(game_id, socket.assigns.time_range)

    socket
    |> assign(:game_history, game_history)
  end



  defp format_number(nil), do: "N/A"
  defp format_number(num) when is_integer(num), do: to_string(num)
  defp format_number(num) when is_float(num) do
    num
    |> :erlang.float_to_binary(decimals: 2)
    |> String.replace(~r/\.?0+$/, "")
  end
  defp format_number(num), do: to_string(num)

  defp format_currency(nil), do: "¥0"
  defp format_currency(amount) when is_integer(amount) do
    "¥#{:erlang.float_to_binary(amount / 100, decimals: 2)}"
  end
  defp format_currency(amount) when is_float(amount) do
    "¥#{:erlang.float_to_binary(amount / 100, decimals: 2)}"
  end
  defp format_currency(amount), do: "¥#{amount}"

  defp get_status_color(:active), do: "text-green-600"
  defp get_status_color(:inactive), do: "text-gray-500"
  defp get_status_color(:error), do: "text-red-600"
  defp get_status_color(_), do: "text-gray-500"

  defp get_action_color(:force_release), do: "text-blue-600 font-bold"
  defp get_action_color(:release), do: "text-blue-500"
  defp get_action_color(:force_collect), do: "text-red-600 font-bold"
  defp get_action_color(:collect), do: "text-red-500"
  defp get_action_color(:random), do: "text-gray-600"
  defp get_action_color(_), do: "text-gray-500"

  defp get_action_text(:force_release), do: "强制放分"
  defp get_action_text(:release), do: "放分"
  defp get_action_text(:force_collect), do: "强制收分"
  defp get_action_text(:collect), do: "收分"
  defp get_action_text(:random), do: "随机"
  defp get_action_text(action), do: to_string(action)

  defp calculate_inventory_percentage(current, center_line) when is_number(current) and is_number(center_line) and center_line > 0 do
    percentage = (current / center_line) * 100
    min(200, max(0, percentage))  # 限制在0-200%范围内
  end
  defp calculate_inventory_percentage(_, _), do: 50

  defp get_inventory_bar_color(percentage) when percentage < 50, do: "bg-red-500"
  defp get_inventory_bar_color(percentage) when percentage < 80, do: "bg-yellow-500"
  defp get_inventory_bar_color(percentage) when percentage < 120, do: "bg-green-500"
  defp get_inventory_bar_color(percentage) when percentage < 150, do: "bg-yellow-500"
  defp get_inventory_bar_color(_), do: "bg-red-500"

  # 转换总历史数据为图表格式
  defp convert_total_history_to_chart_data(history_data) when is_list(history_data) and length(history_data) > 0 do
    # 提取钱包余额数据
    wallet_values = Enum.map(history_data, & &1.balance)

    # 生成时间标签
    time_labels = Enum.map(history_data, fn data_point ->
      DateTime.to_iso8601(data_point.timestamp)
    end)

    %{
      labels: time_labels,
      values: wallet_values
    }
  end

  defp convert_total_history_to_chart_data(_) do
    # 如果没有历史数据，返回空数据
    %{
      labels: [],
      values: []
    }
  end

  # 转换游戏历史数据为图表格式
  defp convert_game_history_to_chart_data(history_data) when is_list(history_data) and length(history_data) > 0 do
    # 提取钱包余额数据
    wallet_values = Enum.map(history_data, & &1.balance)

    # 生成时间标签
    time_labels = Enum.map(history_data, fn data_point ->
      DateTime.to_iso8601(data_point.timestamp)
    end)

    %{
      labels: time_labels,
      values: wallet_values
    }
  end

  defp convert_game_history_to_chart_data(_) do
    # 如果没有历史数据，返回空数据
    %{
      labels: [],
      values: []
    }
  end
end
