<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
  <!-- 顶部导航 -->
  <div class="bg-white/80 backdrop-blur-lg shadow-lg border-b border-white/20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div class="flex items-center">
          <button phx-click="go_back" class="mr-4 p-2 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-all duration-200">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
              <%= @game_info.name %> 钱包控制
            </h1>
            <p class="text-sm text-gray-500 mt-1">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 mr-2">
                ID: <%= @game_info.id %>
              </span>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                <%= @game_info.type %>
              </span>
            </p>
          </div>
        </div>

        <!-- 实时状态指示器 -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center bg-green-50 px-3 py-2 rounded-full">
            <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse mr-2 shadow-lg shadow-green-400/50"></div>
            <span class="text-sm font-medium text-green-700">实时监控中</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 钱包状态卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <!-- 当前余额 -->
      <div class="group bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 group-hover:shadow-blue-500/40 transition-all duration-300">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">当前余额</dt>
              <dd class="text-2xl font-bold text-gray-900 mt-1">
                ¥<%= Number.Delimit.number_to_delimited(@wallet_status.current_balance) %>
              </dd>
              <div class="text-xs text-gray-400 mt-1">实时更新</div>
            </dl>
          </div>
        </div>
      </div>

      <!-- 波动金额 -->
      <div class="group bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class={"w-12 h-12 rounded-xl flex items-center justify-center shadow-lg transition-all duration-300 #{if @wallet_status.fluctuation_amount >= 0, do: "bg-gradient-to-br from-emerald-500 to-green-600 shadow-emerald-500/25 group-hover:shadow-emerald-500/40", else: "bg-gradient-to-br from-red-500 to-rose-600 shadow-red-500/25 group-hover:shadow-red-500/40"}"}>
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <%= if @wallet_status.fluctuation_amount >= 0 do %>
                  <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                <% else %>
                  <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                <% end %>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">波动金额</dt>
              <dd class={"text-2xl font-bold mt-1 #{if @wallet_status.fluctuation_amount >= 0, do: "text-emerald-600", else: "text-red-600"}"}>
                <%= if @wallet_status.fluctuation_amount >= 0, do: "+", else: "" %>¥<%= Number.Delimit.number_to_delimited(@wallet_status.fluctuation_amount) %>
              </dd>
              <div class={"text-xs mt-1 #{if @wallet_status.fluctuation_amount >= 0, do: "text-emerald-500", else: "text-red-500"}"}>
                <%= if @wallet_status.fluctuation_amount >= 0, do: "盈利", else: "亏损" %>
              </div>
            </dl>
          </div>
        </div>
      </div>

      <!-- 波动百分比 -->
      <div class="group bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class={"w-12 h-12 rounded-xl flex items-center justify-center shadow-lg transition-all duration-300 #{if @wallet_status.fluctuation_percentage >= 0, do: "bg-gradient-to-br from-emerald-500 to-green-600 shadow-emerald-500/25 group-hover:shadow-emerald-500/40", else: "bg-gradient-to-br from-red-500 to-rose-600 shadow-red-500/25 group-hover:shadow-red-500/40"}"}>
              <span class="text-white text-lg font-bold">%</span>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">波动百分比</dt>
              <dd class={"text-2xl font-bold mt-1 #{if @wallet_status.fluctuation_percentage >= 0, do: "text-emerald-600", else: "text-red-600"}"}>
                <%= if @wallet_status.fluctuation_percentage >= 0, do: "+", else: "" %><%=
                  case @wallet_status.fluctuation_percentage do
                    val when is_float(val) -> Float.round(val, 2)
                    val when is_integer(val) -> val
                    val -> val
                  end
                %>%
              </dd>
              <div class={"text-xs mt-1 #{if @wallet_status.fluctuation_percentage >= 0, do: "text-emerald-500", else: "text-red-500"}"}>
                相对基准
              </div>
            </dl>
          </div>
        </div>
      </div>

      <!-- 控制模式 -->
      <div class="group bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class={"w-12 h-12 rounded-xl flex items-center justify-center shadow-lg transition-all duration-300 #{
              case @wallet_status.control_mode do
                :collect -> "bg-gradient-to-br from-orange-500 to-amber-600 shadow-orange-500/25 group-hover:shadow-orange-500/40"
                :release -> "bg-gradient-to-br from-cyan-500 to-blue-600 shadow-cyan-500/25 group-hover:shadow-cyan-500/40"
                _ -> "bg-gradient-to-br from-gray-500 to-slate-600 shadow-gray-500/25 group-hover:shadow-gray-500/40"
              end
            }"}>
              <%= case @wallet_status.control_mode do %>
                <% :collect -> %>
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                  </svg>
                <% :release -> %>
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M17 3a1 1 0 01-1 1H4a1 1 0 110-2h12a1 1 0 011 1zm-7.707 3.293a1 1 0 010 1.414L8 9.414V17a1 1 0 11-2 0V9.414L4.707 10.707a1 1 0 01-1.414-1.414l3-3a1 1 0 011.414 0l3 3z" clip-rule="evenodd"></path>
                  </svg>
                <% _ -> %>
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                  </svg>
              <% end %>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">控制模式</dt>
              <dd class={"text-2xl font-bold mt-1 #{
                case @wallet_status.control_mode do
                  :collect -> "text-orange-600"
                  :release -> "text-cyan-600"
                  _ -> "text-gray-600"
                end
              }"}>
                <%= case @wallet_status.control_mode do
                  :collect -> "收分模式"
                  :release -> "放分模式"
                  _ -> "正常模式"
                end %>
              </dd>
              <div class={"text-xs mt-1 #{
                case @wallet_status.control_mode do
                  :collect -> "text-orange-500"
                  :release -> "text-cyan-500"
                  _ -> "text-gray-500"
                end
              }"}>
                <%= case @wallet_status.control_mode do
                  :collect -> "正在收分"
                  :release -> "正在放分"
                  _ -> "自动控制"
                end %>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- 波动曲线图 -->
      <div class="lg:col-span-2">
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
          <div class="px-6 py-4 bg-gradient-to-r from-indigo-50 to-purple-50 border-b border-white/20">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">实时波动曲线</h3>
              </div>

              <!-- 时间范围切换 -->
              <div class="flex space-x-1 bg-white/60 backdrop-blur-sm rounded-xl p-1 shadow-lg">
                <%= for {range, label} <- [{"minute", "分钟"}, {"hour", "小时"}, {"day", "天"}] do %>
                  <button
                    phx-click="change_time_range"
                    phx-value-range={range}
                    class={"px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 #{
                      if @time_range == range do
                        "bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg shadow-indigo-500/25"
                      else
                        "text-gray-600 hover:text-indigo-600 hover:bg-white/80"
                      end
                    }"}
                  >
                    <%= label %>
                  </button>
                <% end %>
              </div>
            </div>
          </div>

          <div class="p-6">
            <!-- ApexCharts 图表容器 -->
            <div class="relative h-96 bg-gradient-to-br from-slate-50 to-blue-50 rounded-xl p-4">
              <div
                id="wallet-chart"
                phx-hook="WalletChart"
                data-chart-data={Jason.encode!(@chart_data)}
                data-time-range={@time_range}
                phx-update="ignore"
                class="w-full h-full"
              >
              </div>
            </div>


          </div>
        </div>
      </div>

      <!-- 配置面板 -->
      <div class="lg:col-span-1">
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
          <div class="px-6 py-4 bg-gradient-to-r from-emerald-50 to-teal-50 border-b border-white/20">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">钱包配置</h3>
              </div>
              <%= if not @editing_config do %>
                <button phx-click="edit_config" class="px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-600 text-white text-sm font-medium rounded-lg hover:from-emerald-600 hover:to-teal-700 transition-all duration-200 shadow-lg shadow-emerald-500/25">
                  编辑
                </button>
              <% end %>
            </div>
          </div>
          
          <div class="p-6">
            <%= if @editing_config do %>
              <!-- 编辑模式 -->
              <.form for={@config_form} phx-submit="save_config" class="space-y-4">

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    收分比例 (%)
                    <span class="text-xs text-gray-500 ml-2">当平台亏损时的调控强度</span>
                  </label>
                  <input
                    type="number"
                    name="config[collect_percentage]"
                    value={@wallet_config.collect_percentage}
                    min="5"
                    max="50"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                  <p class="mt-1 text-xs text-gray-500">
                    • 5-15%: 温和收分，保持玩家体验<br>
                    • 16-25%: 标准收分，平衡收益和体验<br>
                    • 26-35%: 积极收分，快速止损<br>
                    • 36-50%: 激进收分，最大化短期收益
                  </p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    放分比例 (%)
                    <span class="text-xs text-gray-500 ml-2">当平台盈利时的让利强度</span>
                  </label>
                  <input
                    type="number"
                    name="config[release_percentage]"
                    value={@wallet_config.release_percentage}
                    min="0"
                    max="35"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                  <p class="mt-1 text-xs text-gray-500">
                    • 0-5%: 保守让利，最大化平台收益<br>
                    • 6-15%: 标准让利，维持玩家粘性<br>
                    • 16-25%: 慷慨让利，提升玩家满意度<br>
                    • 26-35%: 大幅让利，拉新或活动期间
                  </p>
                </div>

                <!-- 运营策略建议 -->
                <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                  <h4 class="text-sm font-medium text-blue-800 mb-2">💡 运营策略建议</h4>
                  <ul class="text-xs text-blue-700 space-y-1">
                    <li>• <strong>新游戏推广</strong>: 收分15% + 放分20% (吸引玩家)</li>
                    <li>• <strong>稳定运营</strong>: 收分20% + 放分10% (平衡收益)</li>
                    <li>• <strong>收益优化</strong>: 收分30% + 放分5% (提升盈利)</li>
                    <li>• <strong>活动期间</strong>: 收分10% + 放分30% (大幅让利)</li>
                  </ul>
                </div>



                <div class="flex space-x-3 pt-4">
                  <button 
                    type="submit"
                    class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    保存
                  </button>
                  <button 
                    type="button"
                    phx-click="cancel_edit"
                    class="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                  >
                    取消
                  </button>
                </div>
              </.form>
            <% else %>
              <!-- 显示模式 -->
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    收分比例
                  </label>
                  <div class="text-lg font-semibold text-orange-600">
                    <%= @wallet_config.collect_percentage %>%
                  </div>
                  <div class="text-sm text-gray-500">
                    当平台亏损时的调控强度，数值越高平台收益越快
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    放分比例
                  </label>
                  <div class="text-lg font-semibold text-green-600">
                    <%= @wallet_config.release_percentage %>%
                  </div>
                  <div class="text-sm text-gray-500">
                    当平台盈利时的让利强度，数值越高玩家体验越好
                  </div>
                </div>


              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 自定义样式 -->
<style>
  /* 添加一些自定义动画和效果 */
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
    50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
  }

  .float-animation {
    animation: float 6s ease-in-out infinite;
  }

  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* 渐变文字效果 */
  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 玻璃态效果增强 */
  .glass-effect {
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* 卡片悬停效果 */
  .card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }
</style>

<!-- ApexCharts 将通过 Hook 处理 -->
