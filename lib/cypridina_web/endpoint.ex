defmodule <PERSON>pridinaWeb.Endpoint do
  require Logger
  use Phoenix.Endpoint, otp_app: :cypridina

  # The session will be stored in the cookie and signed,
  # this means its contents can be read but not tampered with.
  # Set :encryption_salt if you would also like to encrypt it.
  @session_options [
    store: :cookie,
    key: "_cypridina_key",
    signing_salt: "EGIhf4+I",
    same_site: "Lax"
  ]
  socket("/live", Phoenix.LiveView.Socket, websocket: [connect_info: [session: @session_options]])

  # 用户聊天WebSocket
  socket("/socket", CypridinaWeb.UserSocket,
    websocket: [connect_info: [session: @session_options]],
    longpoll: false
  )

  # 自定义 WebSocket 处理路由 - 直接使用 cowboy_websocket
  socket("/Teen", CypridinaWeb.PhoenixSocket,
    websocket: [
      connect_info: [:peer_data, :x_headers, :uri, session: @session_options],
      # 1MB
      max_frame_size: 1024 * 1024,
      # 增加超时时间到120秒，避免消息超时导致重连
      timeout: 120_000
      # serializer: [{Cypridina.MsgpackSerializer, "2.0.0"}]
    ],
    longpoll: false
  )

  # longpoll: [connect_info: [session: @session_options]]

  # Serve at "/" the static files from "priv/static" directory.
  #
  # When code reloading is disabled (e.g., in production),
  # the `gzip` option is enabled to serve compressed
  # static files generated by running `phx.digest`.
  plug(Plug.Static,
    at: "/",
    from: :cypridina,
    gzip: not code_reloading?,
    only: CypridinaWeb.static_paths()
  )

  if Code.ensure_loaded?(Tidewave) do
    plug Tidewave, allow_remote_access: true
  end

  # Code reloading can be explicitly enabled under the
  # :code_reloader configuration of your endpoint.
  if code_reloading? do
    socket("/phoenix/live_reload/socket", Phoenix.LiveReloader.Socket)

    plug AshAi.Mcp.Dev,
      # If using mcp-remote, and this issue is not fixed yet: https://github.com/geelen/mcp-remote/issues/66
      # You will need to set the `protocol_version_statement` to the
      # older version.
      protocol_version_statement: "2024-11-05",
      otp_app: :cypridina

    plug(Phoenix.LiveReloader)
    plug(Phoenix.CodeReloader)
    plug(Phoenix.Ecto.CheckRepoStatus, otp_app: :cypridina)
  end

  plug(Phoenix.LiveDashboard.RequestLogger,
    param_key: "request_logger",
    cookie_key: "request_logger"
  )

  plug(Plug.RequestId)
  plug(Plug.Telemetry, event_prefix: [:phoenix, :endpoint])

  plug(Plug.Parsers,
    parsers: [:urlencoded, :multipart, :json],
    pass: ["*/*"],
    json_decoder: Phoenix.json_library()
  )

  plug(Plug.MethodOverride)
  plug(Plug.Head)
  plug(Plug.Session, @session_options)

  plug(Corsica, origins: "*", allow_headers: :all)
  plug(CypridinaWeb.Router)
end
