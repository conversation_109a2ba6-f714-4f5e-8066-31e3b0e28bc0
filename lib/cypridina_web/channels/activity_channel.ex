defmodule CypridinaWeb.ActivityChannel do
  @moduledoc """
  活动系统WebSocket频道
  
  处理客户端的活动相关协议请求
  """

  use Phoenix.Channel
  require Logger
  alias Teen.Protocol.ProtocolRouter

  @doc """
  加入频道
  """
  def join("activity:" <> user_id, _params, socket) do
    Logger.info("🔌 [ACTIVITY_CHANNEL] 用户 #{user_id} 加入活动频道")
    
    socket = assign(socket, :user_id, user_id)
    {:ok, %{status: "connected", user_id: user_id}, socket}
  end

  @doc """
  处理协议消息
  """
  def handle_in("protocol", %{
    "main_protocol" => main_protocol,
    "sub_protocol" => sub_protocol,
    "data" => data
  }, socket) do
    user_id = socket.assigns.user_id
    
    Logger.info("📨 [ACTIVITY_CHANNEL] 收到协议消息: 主协议=#{main_protocol}, 子协议=#{sub_protocol}, 用户=#{user_id}")

    case ProtocolRouter.route_protocol(main_protocol, sub_protocol, data, user_id) do
      {:ok, response_sub_protocol, response_data} ->
        # 发送响应给客户端
        push(socket, "protocol_response", %{
          main_protocol: main_protocol,
          sub_protocol: response_sub_protocol,
          data: response_data
        })
        
        {:noreply, socket}

      {:error, reason} ->
        Logger.error("❌ [ACTIVITY_CHANNEL] 协议处理失败: #{inspect(reason)}")
        
        push(socket, "protocol_error", %{
          main_protocol: main_protocol,
          sub_protocol: sub_protocol,
          error: reason,
          message: "协议处理失败"
        })
        
        {:noreply, socket}
    end
  end

  @doc """
  处理活动数据请求
  """
  def handle_in("get_activities", %{"user_id" => user_id}, socket) do
    case Teen.ActivitySystem.ActivityService.get_user_available_activities(user_id) do
      {:ok, activities} ->
        push(socket, "activities_data", %{
          status: "success",
          data: activities
        })

      {:error, reason} ->
        push(socket, "activities_error", %{
          status: "error",
          reason: reason
        })
    end

    {:noreply, socket}
  end

  @doc """
  处理奖励领取请求
  """
  def handle_in("claim_reward", %{
    "user_id" => user_id,
    "activity_type" => activity_type,
    "activity_id" => activity_id
  } = params, socket) do
    reward_data = Map.get(params, "reward_data", %{})

    case Teen.ActivitySystem.ActivityService.claim_activity_reward(user_id, activity_type, activity_id, reward_data) do
      {:ok, record} ->
        push(socket, "reward_claimed", %{
          status: "success",
          record: %{
            id: record.id,
            reward_type: record.reward_type,
            reward_amount: record.reward_amount,
            claimed_at: record.claimed_at
          }
        })

      {:error, reason} ->
        push(socket, "reward_claim_error", %{
          status: "error",
          reason: reason
        })
    end

    {:noreply, socket}
  end

  @doc """
  处理转盘抽奖请求
  """
  def handle_in("spin_wheel", %{
    "user_id" => user_id,
    "wheel_id" => wheel_id
  }, socket) do
    case Teen.ActivitySystem.ActivityService.spin_wheel(user_id, wheel_id) do
      {:ok, result} ->
        push(socket, "wheel_result", %{
          status: "success",
          prize: result.prize,
          record_id: result.record.id
        })

      {:error, reason} ->
        push(socket, "wheel_error", %{
          status: "error",
          reason: reason
        })
    end

    {:noreply, socket}
  end

  @doc """
  处理刮刮卡请求
  """
  def handle_in("scratch_card", %{
    "user_id" => user_id,
    "activity_id" => activity_id,
    "level_id" => level_id
  }, socket) do
    case Teen.ActivitySystem.ActivityService.scratch_card(user_id, activity_id, level_id) do
      {:ok, result} ->
        push(socket, "scratch_result", %{
          status: "success",
          reward: result.reward,
          record_id: result.record.id
        })

      {:error, reason} ->
        push(socket, "scratch_error", %{
          status: "error",
          reason: reason
        })
    end

    {:noreply, socket}
  end

  @doc """
  处理用户活动进度更新
  """
  def handle_in("update_progress", %{
    "user_id" => user_id,
    "activity_type" => activity_type,
    "activity_id" => activity_id,
    "progress_data" => progress_data
  }, socket) do
    case Teen.ActivitySystem.ActivityService.update_user_activity_progress(user_id, activity_type, activity_id, progress_data) do
      {:ok, participation} ->
        push(socket, "progress_updated", %{
          status: "success",
          participation: %{
            id: participation.id,
            progress: participation.progress,
            completed: participation.completed
          }
        })

      {:error, reason} ->
        push(socket, "progress_error", %{
          status: "error",
          reason: reason
        })
    end

    {:noreply, socket}
  end

  @doc """
  处理获取用户奖励记录请求
  """
  def handle_in("get_reward_records", %{
    "user_id" => user_id
  } = params, socket) do
    activity_type = Map.get(params, "activity_type")
    
    records = Teen.ActivitySystem.ActivityService.get_user_reward_claim_records(user_id, activity_type)
    
    push(socket, "reward_records", %{
      status: "success",
      records: Enum.map(records, fn record ->
        %{
          id: record.id,
          activity_type: record.activity_type,
          activity_id: record.activity_id,
          reward_type: record.reward_type,
          reward_amount: record.reward_amount,
          claimed_at: record.claimed_at
        }
      end)
    })

    {:noreply, socket}
  end

  @doc """
  处理获取用户参与记录请求
  """
  def handle_in("get_participation_records", %{
    "user_id" => user_id
  } = params, socket) do
    activity_type = Map.get(params, "activity_type")
    
    participations = Teen.ActivitySystem.ActivityService.get_user_activity_participations(user_id, activity_type)
    
    push(socket, "participation_records", %{
      status: "success",
      participations: Enum.map(participations, fn participation ->
        %{
          id: participation.id,
          activity_type: participation.activity_type,
          activity_id: participation.activity_id,
          progress: participation.progress,
          completed: participation.completed,
          completed_at: participation.completed_at
        }
      end)
    })

    {:noreply, socket}
  end

  @doc """
  处理断开连接
  """
  def terminate(reason, socket) do
    user_id = socket.assigns[:user_id]
    Logger.info("🔌 [ACTIVITY_CHANNEL] 用户 #{user_id} 断开连接: #{inspect(reason)}")
    :ok
  end

  # 私有辅助函数

  defp broadcast_activity_update(user_id, activity_type, data) do
    CypridinaWeb.Endpoint.broadcast("activity:#{user_id}", "activity_update", %{
      activity_type: activity_type,
      data: data
    })
  end
end
