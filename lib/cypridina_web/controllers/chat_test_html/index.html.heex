<div class="container mx-auto p-6">
  <h1 class="text-3xl font-bold mb-6">聊天系统测试</h1>

  <!-- 创建聊天会话 -->
  <div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <h2 class="text-xl font-semibold mb-4">创建聊天会话</h2>
    
    <.form for={%{}} action={~p"/chat_test/create_session"} method="post" class="space-y-4">
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">用户1</label>
          <select name="user1_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">选择用户1</option>
            <%= for user <- @users do %>
              <option value={user.id}><%= user.username %> (ID: <%= user.id %>)</option>
            <% end %>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">用户2</label>
          <select name="user2_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">选择用户2</option>
            <%= for user <- @users do %>
              <option value={user.id}><%= user.username %> (ID: <%= user.id %>)</option>
            <% end %>
          </select>
        </div>
      </div>
      
      <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
        创建聊天会话
      </button>
    </.form>
  </div>

  <!-- 发送测试消息 -->
  <div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <h2 class="text-xl font-semibold mb-4">发送测试消息</h2>

    <.form for={%{}} action={~p"/chat_test/send_message"} method="post" class="space-y-4">
      <div class="grid grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">会话ID</label>
          <input type="text" name="session_id" placeholder="输入会话ID"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">发送者</label>
          <select name="sender_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">选择发送者</option>
            <%= for user <- @users do %>
              <option value={user.id}><%= user.username %> (ID: <%= user.id %>)</option>
            <% end %>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">消息内容</label>
          <input type="text" name="content" placeholder="输入消息内容"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
      </div>

      <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500">
        发送消息
      </button>
    </.form>
  </div>

  <!-- 文件上传测试 -->
  <div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <h2 class="text-xl font-semibold mb-4">文件上传测试</h2>

    <.form for={%{}} action={~p"/chat_test/upload_file"} method="post" enctype="multipart/form-data" class="space-y-4">
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">会话ID</label>
          <input type="text" name="session_id" placeholder="输入会话ID"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">发送者</label>
          <select name="sender_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">选择发送者</option>
            <%= for user <- @users do %>
              <option value={user.id}><%= user.username %> (ID: <%= user.id %>)</option>
            <% end %>
          </select>
        </div>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">选择文件</label>
        <input type="file" name="file"
               accept=".jpg,.jpeg,.png,.gif,.webp,.bmp,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.mp3,.wav,.aac,.ogg,.m4a,.mp4,.avi,.mov,.wmv,.flv,.webm,.zip,.rar,.7z"
               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        <p class="text-sm text-gray-500 mt-1">支持图片、文档、音频、视频、压缩包等格式，最大50MB</p>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">文件说明（可选）</label>
        <input type="text" name="caption" placeholder="输入文件说明"
               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
      </div>

      <button type="submit" class="bg-purple-500 text-white px-4 py-2 rounded-md hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-500">
        上传文件
      </button>
    </.form>
  </div>

  <!-- API测试工具 -->
  <div class="bg-white rounded-lg shadow-md p-6">
    <h2 class="text-xl font-semibold mb-4">API测试工具</h2>
    
    <div class="grid grid-cols-2 gap-6">
      <!-- 查询用户会话 -->
      <div>
        <h3 class="text-lg font-medium mb-3">查询用户会话</h3>
        <div class="space-y-3">
          <select id="user-select" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">选择用户</option>
            <%= for user <- @users do %>
              <option value={user.id}><%= user.username %> (ID: <%= user.id %>)</option>
            <% end %>
          </select>
          
          <button onclick="listSessions()" class="w-full bg-purple-500 text-white px-4 py-2 rounded-md hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-500">
            查询会话列表
          </button>
          
          <div id="sessions-result" class="mt-3 p-3 bg-gray-100 rounded-md min-h-[100px] overflow-auto">
            <p class="text-gray-500">会话列表将显示在这里...</p>
          </div>
        </div>
      </div>

      <!-- 查询会话消息 -->
      <div>
        <h3 class="text-lg font-medium mb-3">查询会话消息</h3>
        <div class="space-y-3">
          <input type="text" id="session-id-input" placeholder="输入会话ID" 
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          
          <select id="user-select-2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">选择用户</option>
            <%= for user <- @users do %>
              <option value={user.id}><%= user.username %> (ID: <%= user.id %>)</option>
            <% end %>
          </select>
          
          <button onclick="listMessages()" class="w-full bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500">
            查询消息列表
          </button>
          
          <div id="messages-result" class="mt-3 p-3 bg-gray-100 rounded-md min-h-[100px] overflow-auto">
            <p class="text-gray-500">消息列表将显示在这里...</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
async function listSessions() {
  const userId = document.getElementById('user-select').value;
  if (!userId) {
    alert('请选择用户');
    return;
  }
  
  try {
    const response = await fetch(`/chat_test/sessions/${userId}`);
    const data = await response.json();
    
    const resultDiv = document.getElementById('sessions-result');
    if (data.success) {
      resultDiv.innerHTML = `
        <h4 class="font-medium mb-2">会话列表 (${data.sessions.length}个):</h4>
        ${data.sessions.map(session => `
          <div class="border-b pb-2 mb-2">
            <p><strong>ID:</strong> ${session.id}</p>
            <p><strong>类型:</strong> ${session.type}</p>
            <p><strong>标题:</strong> ${session.title || '无'}</p>
            <p><strong>参与者数量:</strong> ${session.participant_count}</p>
            <p><strong>最后消息时间:</strong> ${session.last_message_at || '无'}</p>
          </div>
        `).join('')}
      `;
    } else {
      resultDiv.innerHTML = `<p class="text-red-500">错误: ${data.error}</p>`;
    }
  } catch (error) {
    document.getElementById('sessions-result').innerHTML = `<p class="text-red-500">请求失败: ${error.message}</p>`;
  }
}

async function listMessages() {
  const sessionId = document.getElementById('session-id-input').value;
  const userId = document.getElementById('user-select-2').value;
  
  if (!sessionId || !userId) {
    alert('请输入会话ID和选择用户');
    return;
  }
  
  try {
    const response = await fetch(`/chat_test/messages/${sessionId}/${userId}`);
    const data = await response.json();
    
    const resultDiv = document.getElementById('messages-result');
    if (data.success) {
      resultDiv.innerHTML = `
        <h4 class="font-medium mb-2">消息列表 (${data.messages.length}条):</h4>
        ${data.messages.map(message => `
          <div class="border-b pb-2 mb-2">
            <p><strong>ID:</strong> ${message.id}</p>
            <p><strong>发送者:</strong> ${message.sender_id}</p>
            <p><strong>内容:</strong> ${message.content}</p>
            <p><strong>类型:</strong> ${message.message_type}</p>
            <p><strong>时间:</strong> ${message.inserted_at}</p>
          </div>
        `).join('')}
      `;
    } else {
      resultDiv.innerHTML = `<p class="text-red-500">错误: ${data.error}</p>`;
    }
  } catch (error) {
    document.getElementById('messages-result').innerHTML = `<p class="text-red-500">请求失败: ${error.message}</p>`;
  }
}
</script>
