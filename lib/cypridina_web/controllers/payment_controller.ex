defmodule CypridinaWeb.PaymentController do
  @moduledoc """
  支付回调控制器

  处理支付网关的回调通知
  """

  use CypridinaWeb, :controller
  require Logger

  @doc """
  处理支付通知回调
  """
  def handle_notify(conn, params) do
    Logger.info("收到支付通知回调: #{inspect(params)}")

    case verify_and_process_payment_callback(params) do
      {:ok, _result} ->
        # 返回成功响应给支付网关
        conn
        |> put_status(200)
        |> json(%{"code" => "0000", "msg" => "success"})

      {:error, reason} ->
        Logger.error("支付回调处理失败: #{inspect(reason)}")

        conn
        |> put_status(400)
        |> json(%{"code" => "9999", "msg" => "failed", "error" => reason})
    end
  end

  @doc """
  处理支付回调（兼容旧接口）
  """
  def handle_callback(conn, params) do
    handle_notify(conn, params)
  end

  # 私有函数

  defp verify_and_process_payment_callback(params) do
    with {:ok, order_id} <- extract_order_id(params),
         {:ok, status} <- extract_payment_status(params),
         :ok <- verify_callback_signature(params),
         {:ok, result} <- process_payment_callback(order_id, status, params) do
      {:ok, result}
    else
      {:error, reason} -> {:error, reason}
      error -> {:error, "Unknown error: #{inspect(error)}"}
    end
  end

  defp extract_order_id(params) do
    case Map.get(params, "orderId") || Map.get(params, "order_id") do
      nil -> {:error, "Missing order_id"}
      order_id when is_binary(order_id) -> {:ok, order_id}
      _ -> {:error, "Invalid order_id format"}
    end
  end

  defp extract_payment_status(params) do
    case Map.get(params, "status") || Map.get(params, "trade_status") do
      nil -> {:error, "Missing payment status"}
      "1" -> {:ok, "success"}
      "0" -> {:ok, "pending"}
      "2" -> {:ok, "failed"}
      "TRADE_SUCCESS" -> {:ok, "success"}
      "TRADE_FINISHED" -> {:ok, "success"}
      "TRADE_CLOSED" -> {:ok, "failed"}
      status when is_binary(status) -> {:ok, status}
      _ -> {:error, "Invalid payment status format"}
    end
  end

  defp verify_callback_signature(params) do
    # 获取支付配置
    config = get_payment_config()
    
    # 提取签名
    received_sign = Map.get(params, "sign") || Map.get(params, "signature")
    
    if received_sign do
      # 生成期望的签名
      expected_sign = generate_callback_signature(params, config.secret_key)
      
      if received_sign == expected_sign do
        :ok
      else
        Logger.warning("支付回调签名验证失败: received=#{received_sign}, expected=#{expected_sign}")
        {:error, "Invalid signature"}
      end
    else
      Logger.warning("支付回调缺少签名")
      {:error, "Missing signature"}
    end
  end

  defp generate_callback_signature(params, secret_key) do
    # 排除签名字段
    filtered_params = Map.drop(params, ["sign", "signature"])
    
    # 按照字母顺序排序参数
    sorted_params =
      filtered_params
      |> Enum.sort_by(fn {key, _} -> key end)
      |> Enum.map(fn {key, value} -> "#{key}=#{value}" end)
      |> Enum.join("&")

    # 添加密钥
    sign_string = "#{sorted_params}&key=#{secret_key}"

    # MD5签名
    :crypto.hash(:md5, sign_string)
    |> Base.encode16(case: :upper)
  end

  defp process_payment_callback(order_id, status, callback_data) do
    # 提取金额信息
    amount = case Map.get(callback_data, "amount") do
      nil -> nil
      amount_str when is_binary(amount_str) -> 
        case Decimal.parse(amount_str) do
          {decimal, _} -> decimal
          :error -> nil
        end
      amount when is_number(amount) -> Decimal.new(amount)
      _ -> nil
    end

    # 提取外部订单ID
    external_order_id = Map.get(callback_data, "tradeNo") || Map.get(callback_data, "external_order_id")

    # 调用支付订单处理
    case Teen.PaymentSystem.PaymentOrder.handle_callback(%{
           order_id: order_id,
           status: status,
           amount: amount,
           external_order_id: external_order_id,
           callback_data: callback_data
         }) do
      {:ok, payment_order} ->
        Logger.info("支付回调处理成功: order_id=#{order_id}, status=#{status}")
        {:ok, payment_order}

      {:error, reason} ->
        Logger.error("支付回调处理失败: order_id=#{order_id}, reason=#{inspect(reason)}")
        {:error, reason}
    end
  end

  defp get_payment_config do
    %{
      secret_key:
        Application.get_env(:cypridina, :payment)[:secret_key] ||
          raise("Payment secret_key not configured")
    }
  end
end
