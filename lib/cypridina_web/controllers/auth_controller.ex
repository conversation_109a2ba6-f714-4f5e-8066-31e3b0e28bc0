defmodule CypridinaWeb.AuthController do
  use CypridinaWeb, :controller
  use AshAuthentication.Phoenix.Controller
  require Logger

  def success(conn, activity, user, _token) do
    return_to = get_session(conn, :return_to) || ~p"/app/racing_game"

    message =
      case activity do
        {:confirm_new_user, :confirm} -> "您的邮箱地址已成功确认"
        {:password, :reset} -> "您的密码已成功重置"
        _ -> "您已成功登录"
      end

    conn
    |> delete_session(:return_to)
    |> store_in_session(user)
    # If your resource has a different name, update the assign name here (i.e :current_admin)
    |> assign(:current_user, user)
    |> put_flash(:info, message)
    |> redirect(to: return_to)
  end

  def failure(conn, activity, reason) do
    message =
      case {activity, reason} do
        {_,
         %AshAuthentication.Errors.AuthenticationFailed{
           caused_by: %Ash.Error.Forbidden{
             errors: [%AshAuthentication.Errors.CannotConfirmUnconfirmedUser{}]
           }
         }} ->
          """
          您已经通过其他方式登录，但尚未确认您的账户。
          您可以使用我们发送给您的链接确认您的账户，或者通过重置密码来确认。
          """

        _ ->
          "用户名或密码不正确"
      end

    conn
    |> put_flash(:error, message)
    |> redirect(to: ~p"/sign-in")
  end

  def sign_out(conn, _params) do
    return_to = get_session(conn, :return_to) || ~p"/sign-in"

    conn
    |> clear_session(:cypridina)
    |> put_flash(:info, "您已成功登出")
    |> redirect(to: return_to)
  end
end
