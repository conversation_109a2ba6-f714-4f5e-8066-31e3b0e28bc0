<div class="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
  <div class="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
    <!-- 成功图标 -->
    <div class="mb-6">
      <div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
      </div>
    </div>

    <!-- 标题 -->
    <h1 class="text-2xl font-bold text-gray-900 mb-2">
      支付成功！
    </h1>

    <!-- 描述 -->
    <p class="text-gray-600 mb-6">
      您的支付已经成功处理，积分将在几分钟内到账。
    </p>

    <!-- 订单信息 -->
    <%= if @order_id do %>
      <div class="bg-gray-50 rounded-lg p-4 mb-6">
        <p class="text-sm text-gray-500 mb-1">订单号</p>
        <p class="text-lg font-mono text-gray-900"><%= @order_id %></p>
      </div>
    <% end %>

    <!-- 操作按钮 -->
    <div class="space-y-3">
      <button 
        onclick="window.close()" 
        class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
      >
        关闭窗口
      </button>
      
      <a 
        href="javascript:history.back()" 
        class="block w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors"
      >
        返回游戏
      </a>
    </div>

    <!-- 提示信息 -->
    <div class="mt-6 text-xs text-gray-500">
      <p>如有疑问，请联系客服</p>
    </div>
  </div>
</div>

<script>
  // 自动关闭窗口（5秒后）
  setTimeout(function() {
    if (window.opener) {
      window.close();
    }
  }, 5000);
</script>
