# 简单的充值转盘功能测试

defmodule SimpleWheelTest do
  def test_activity_period do
    IO.puts("=== 测试活动期间显示 ===")
    
    test_cases = [
      {nil, nil, "长期有效"},
      {"2024-01-01", nil, "2024-01-01起长期有效"},
      {nil, "2024-12-31", "至2024-12-31"},
      {"2024-01-01", "2024-12-31", "2024-01-01至2024-12-31"}
    ]
    
    Enum.each(test_cases, fn {start_date, end_date, expected} ->
      result = format_activity_period(start_date, end_date)
      IO.puts("#{inspect({start_date, end_date})} -> #{result}")
      IO.puts("✅ 匹配: #{result == expected}")
    end)
    
    IO.puts("")
  end

  def test_jackpot_formatting do
    IO.puts("=== 测试奖金池格式化 ===")
    
    amounts = ["0", "1000", "12345", "1000000"]
    
    Enum.each(amounts, fn amount ->
      formatted = format_currency(amount)
      IO.puts("#{amount}分 -> #{formatted}分")
    end)
    
    IO.puts("")
  end

  defp format_activity_period(start_date, end_date) do
    case {start_date, end_date} do
      {nil, nil} -> "长期有效"
      {start_date, nil} -> "#{start_date}起长期有效"
      {nil, end_date} -> "至#{end_date}"
      {start_date, end_date} -> "#{start_date}至#{end_date}"
    end
  end

  defp format_currency(amount_string) do
    case Integer.parse(amount_string) do
      {amount, ""} when amount >= 1000 ->
        amount
        |> Integer.to_string()
        |> String.reverse()
        |> String.replace(~r/(\d{3})(?=\d)/, "\\1,")
        |> String.reverse()
      _ ->
        amount_string
    end
  end

  def run_tests do
    IO.puts("🎰 充值转盘功能测试")
    IO.puts("=" <> String.duplicate("=", 30))
    IO.puts("")
    
    test_activity_period()
    test_jackpot_formatting()
    
    IO.puts("✅ 测试完成！")
  end
end

SimpleWheelTest.run_tests()
