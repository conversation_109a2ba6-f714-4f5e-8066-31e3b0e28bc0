{"attributes": [{"default": "fragment(\"gen_random_uuid()\")", "size": null, "type": "uuid", "source": "id", "references": null, "allow_nil?": false, "generated?": false, "primary_key?": true}, {"default": "nil", "size": null, "type": "text", "source": "subject", "references": null, "allow_nil?": false, "generated?": false, "primary_key?": false}, {"default": "nil", "size": null, "type": "text", "source": "body", "references": null, "allow_nil?": false, "generated?": false, "primary_key?": false}, {"default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "size": null, "type": "utc_datetime_usec", "source": "inserted_at", "references": null, "allow_nil?": false, "generated?": false, "primary_key?": false}, {"default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "size": null, "type": "utc_datetime_usec", "source": "updated_at", "references": null, "allow_nil?": false, "generated?": false, "primary_key?": false}], "table": "tickets", "hash": "9B5D9315CFB3750457FCF436EF56A515A5539855FE2669BA3DD7AD7A3CC29DC7", "repo": "Elixir.Demo.Repo", "identities": [], "schema": null, "multitenancy": {"global": null, "attribute": null, "strategy": null}, "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": false}