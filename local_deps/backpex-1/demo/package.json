{"private": true, "scripts": {"test": "env $(cat .env.test | xargs) mix test", "test:playwright": "env $(cat .env.test | xargs) mix test --include playwright:true", "erd": "env $(cat .env.test | xargs) mix ecto.gen.erd --output-path=ecto_erd.puml", "doctest": "env $(cat .env.test | xargs) mix test test/doc_test.exs", "lint": "run-p lint:*", "lint:mix": "mix format --check-formatted", "lint:credo": "mix credo --strict", "lint:sobelow": "mix sobelow --config", "lint:style": "stylelint assets/**/*.css", "lint:standard": "standard", "lint:deps-unused": "mix deps.unlock --check-unused", "lint:gettext": "mix gettext.extract --check-up-to-date", "format": "run-p format:*", "format:style": "stylelint assets/**/*.css --fix", "format:mix": "mix format", "format:standard": "standard --fix", "gettext": "mix gettext.extract --merge"}, "devDependencies": {"@sentry/browser": "9.29.0", "npm-run-all2": "7.0.2", "playwright": "1.52.0", "postcss": "8.5.6", "standard": "17.1.2", "stylelint": "16.20.0", "stylelint-config-standard": "38.0.0", "topbar": "3.0.0"}, "dependencies": {"daisyui": "^5.0.3"}}