name: CI

on:
  workflow_dispatch:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main
      - develop
  release:
    types: [published]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME_RUNTIME: ${{ github.repository }}/runtime

jobs:
  test:
    name: "Test backpex"
    runs-on: ubuntu-latest
    strategy:
      matrix:
        elixir: ["1.17", "1.16"]
        erlang: ["27.1", "26.2"]
        exclude:
          - elixir: "1.16"
            erlang: "27.1"

    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: erlef/setup-beam@75edbb82877ab10edeb12a99c3cf2f0909f3dc87 # v1.20.1
        with:
          otp-version: ${{ matrix.erlang }}
          elixir-version: ${{ matrix.elixir }}

      - name: Setup node
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 20
          cache: 'yarn'
          cache-dependency-path: yarn.lock

      - name: Restore the deps and _build cache
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        id: restore-cache
        env:
          MIX_LOCK_HASH: ${{ hashFiles(format('{0}{1}', github.workspace, '/mix.lock')) }}
        with:
          path: |
            deps
            _build
          key: ${{ runner.os }}-${{ matrix.elixir }}-${{ matrix.erlang }}-build-deps-mixlockhash-${{ env.MIX_LOCK_HASH }}
          restore-keys:  ${{ runner.os }}-${{ matrix.elixir }}-${{ matrix.erlang }}-build-deps-

      - name: Install dependencies
        if: steps.restore-cache.outputs.cache-hit != 'true'
        run: mix deps.get

      - name: Compile dependencies
        if: steps.restore-cache.outputs.cache-hit != 'true'
        run: mix deps.compile

      - name: Compile with warnings as errors
        run: mix compile --warnings-as-errors --force

      - name: Install node dependencies
        run: yarn install --pure-lockfile

      - name: Check formatting
        run: mix format --check-formatted

      - name: JavaScript Standard
        run: yarn run lint:standard

      - name: Build docs with warnings as errors
        run: mix docs --warnings-as-errors

      - name: Credo
        run: mix credo

      - name: Sobelow
        run: mix sobelow --config

      - name: Deps Unused
        run: mix deps.unlock --check-unused

      - name: Run tests
        run: mix test

      - name: Deps Audit
        continue-on-error: true
        run: mix deps.audit

      - name: Gettext Check
        run: mix gettext.extract --check-up-to-date

  publish:
    needs: test
    if: github.event_name == 'release'
    name: "Publish package on hex.pm"
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: erlef/setup-beam@75edbb82877ab10edeb12a99c3cf2f0909f3dc87 # v1.20.1
        id: beam
        with:
          version-file: .tool-versions
          version-type: strict

      - name: Install dependencies
        run: |
          mix local.hex --force --if-missing
          mix local.rebar --force --if-missing
          mix deps.get

      - name: Publish package on hex.pm
        env:
          HEX_API_KEY: ${{ secrets.HEX_API_KEY }}
        run: mix hex.publish --yes

  test-demo:
    name: "Test (Demo)"
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:16.9@sha256:2e7c735993bf456ee1977c40dd82e66875e25f7ee9dfe1e5118fb24887104d85
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - "5432:5432"

    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Setup beam
        uses: erlef/setup-beam@75edbb82877ab10edeb12a99c3cf2f0909f3dc87 # v1.20.1
        id: beam
        with:
          version-file: .tool-versions
          version-type: strict

      - name: Setup node
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 20
          cache: 'yarn'
          cache-dependency-path: demo/yarn.lock

      - name: Restore the deps and _build cache
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        id: restore-cache
        env:
          OTP_VERSION: ${{ steps.beam.outputs.otp-version }}
          ELIXIR_VERSION: ${{ steps.beam.outputs.elixir-version }}
          MIX_LOCK_HASH: ${{ hashFiles(format('{0}{1}', github.workspace, '/demo/mix.lock')) }}
        with:
          path: |
            demo/deps
            demo/_build
          key: ${{ runner.os }}-${{ env.ELIXIR_VERSION }}-${{ env.OTP_VERSION }}-build-deps-demo-mixlockhash-${{ env.MIX_LOCK_HASH }}
          restore-keys:  ${{ runner.os }}-${{ env.ELIXIR_VERSION }}-${{ env.OTP_VERSION }}-build-deps-demo-

      - name: Install dependencies
        if: steps.restore-cache.outputs.cache-hit != 'true'
        working-directory: demo
        run: mix deps.get

      - name: Compile dependencies
        if: steps.restore-cache.outputs.cache-hit != 'true'
        working-directory: demo
        run: mix deps.compile

      - name: Compile with warnings as errors
        working-directory: demo
        run: mix compile --warnings-as-errors --force

      - name: Install node dependencies
        working-directory: demo
        run: yarn install --pure-lockfile

      - name: lint:mix
        working-directory: demo
        run: yarn lint:mix

      - name: lint:credo
        working-directory: demo
        run: yarn lint:credo

      - name: lint:sobelow
        working-directory: demo
        run: yarn lint:sobelow

      - name: lint:style
        working-directory: demo
        run: yarn lint:style

      - name: lint:standard
        working-directory: demo
        run: yarn lint:standard

      - name: lint:deps-unused
        working-directory: demo
        run: yarn lint:deps-unused

      - name: lint:gettext
        working-directory: demo
        run: yarn lint:gettext

      - name: Run test
        working-directory: demo
        env:
          DB_HOSTNAME: localhost
          DB_USERNAME: postgres
          DB_PASSWORD: postgres
          DB_DATABASE: test
        run: yarn test

      - name: Install playwright
        working-directory: demo
        run: yarn playwright install chromium --with-deps --only-shell

      - name: Run playwright test
        working-directory: demo
        env:
          DB_HOSTNAME: localhost
          DB_USERNAME: postgres
          DB_PASSWORD: postgres
          DB_DATABASE: test
        run: yarn test:playwright
        continue-on-error: true

      - name: Deps audit
        working-directory: demo
        continue-on-error: true
        run: mix deps.audit

  build-runtime:
    name: "Build and push image (Demo)"
    runs-on: ubuntu-latest
    needs: [test-demo]
    env:
      PUSH_IMAGE: ${{ github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop') }}

    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3.11.1

      - name: Log in to the container registry
        if: env.PUSH_IMAGE == 'true'
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@902fa8ec7d6ecbf8d84d538b9b233a880e428804 # v5.7.0
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_RUNTIME }}

      - name: Build container
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          push: ${{ env.PUSH_IMAGE }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=registry,ref=${{ env.REGISTRY }}/${{ env.IMAGE_NAME_RUNTIME }}:buildcache
          cache-to: ${{ env.PUSH_IMAGE == 'true' && format('type=registry,ref={0}/{1}:buildcache,mode=max', env.REGISTRY, env.IMAGE_NAME_RUNTIME) || '' }}
          target: runtime
          build-args: |
            MIX_ENV=prod
