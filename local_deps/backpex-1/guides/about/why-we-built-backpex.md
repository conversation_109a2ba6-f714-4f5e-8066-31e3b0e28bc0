# Why we built Backpex?

After building several Phoenix applications, we realized that we were repeating ourselves when it came to building administration panels. We were writing the same CRUD views, search and filter functionality over and over again. We wanted a tool that would allow us to quickly scaffold these views and focus on building the core functionality of our applications.

The tool we wanted had to be able to serve as a simple backend administration panel in one project, while being the core of the application in another.

We looked at existing solutions, but found that none of them offered the flexibility and customization we were looking for. We decided to develop Backpex to solve this problem and provide a highly customizable administration panel for Phoenix LiveView applications.