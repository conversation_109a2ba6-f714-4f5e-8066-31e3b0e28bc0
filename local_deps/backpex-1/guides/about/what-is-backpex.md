# What is Backpex?

Backpex is a highly customizable administration panel for Phoenix LiveView applications. It allows you to quickly create CRUD views of your existing data using configurable *LiveResources*. Backpex integrates seamlessly with your existing Phoenix LiveView application and provides an easy way to manage your resources. It is highly customizable and can be extended with your own layouts, views, field types, filters and more.

Backpex is built on top of Phoenix LiveView and provides a rich set of features to manage your resources. With Backpex, you can set up an administration panel for your application in hours, not days.

Whether you want to quickly scaffold CRUD views for your existing data or build a full-fledged administration panel, Backpex has you covered.

TODO: add screenshots of key features here
