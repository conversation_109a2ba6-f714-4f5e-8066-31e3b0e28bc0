# Upgrading to v0.11

## Bump Your Deps

Update Backpex to the latest version:

```elixir
  defp deps do
    [
      {:backpex, "~> 0.11.0"}
    ]
  end
```

## Removed Alpine.js in favor of Backpex JS hooks

We removed the Alpine.js dependency and replaced it with custom hooks. In order to make them work, you now need to
import these Backpex hooks into your JS bundle. If you are not using Alpine.js, you can now remove it so that the
`app.js` looks something like this:

```javascript
import { Hooks as BackpexHooks } from 'backpex';

const Hooks = [] // your application hooks (optional)

const liveSocket = new LiveSocket('/live', Socket, {
  params: { _csrf_token: csrfToken },
  hooks: {...Hooks, ...BackpexHooks }
})
```

Some hooks come with Tailwind classes, so make sure you add the paths to your content section in your `tailwind.config.js`.

```javascript
..,
content: [
  ...,
  // add this line
  '../deps/backpex/assets/js/**/*.*js'
]
```

In case you are using the theme selector, you do not need to add the JS hook for that. It is in included in
`BackpexHooks` now. But make sure to add this line your `app.js` to reduce flickering:

```javascript
BackpexHooks.BackpexThemeSelector.setStoredTheme()
```

See demo setup for more information.

## Parameter changes in core modules

In case you are using `Backpex.Resource` or one of the `Backpex.Adapter` modules (`Backpex.Adapters.Ecto` or
`Backpex.Adapters.Ash`) directly check out the updated function definitions. This will also apply in case you built your
own adapter.

## Make sure to cover all cases with the `item_query/3` function

We have removed code that ensures that a fallback item query function is always added to your LiveResource. 

Make sure to always cover all possible cases or add a fallback `item_query/3` function that just returns the query.

For example:

```elixir
# in your resource configuration file (live resource)
use Backpex.LiveResource,
  # ...other options
  adapter_config: [
    # ...other adapter options
    item_query: &__MODULE__.item_query/3
  ]

  def item_query(query, :index, _assigns) do
    query
    |> where([post], post.published)
  end

  # make sure to add this fallback function
  def item_query(query, _live_action, _assigns) do
    query
  end
```

## We removed `Ecto.Query` import from LiveResource

Previously, we automatically imported `Ecto.Query` into LiveResources. We removed this behavior,
so you need to import it yourself if you need it, e.g. for the `item_query/3` callback.

## Component changes

- We have removed the [`Backpex.HTML.Resource.edit_panel/1`]() component and replaced it with a more usable `Backpex.HTML.Resource.edit_card/1` component.
- We have removed default top margin from `Backpex.HTML.Form.error/1` component, but added an attr to set classes from outside

