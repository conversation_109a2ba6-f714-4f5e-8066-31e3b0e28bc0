# Upgrading to v0.3

## Bump Your Deps

Update Backpex to the latest version:

```elixir
  defp deps do
    [
      {:backpex, "~> 0.3.0"}
    ]
  end
```

## Update calls to `Backpex.Resource`

We have updated certain functions in `Backpex.Resource` to make them more explicit and usable from outside. 

The following functions are affected:
- [`Backpex.Resource.get!/5`]() (`get/4` before)
- [`Backpex.Resource.delete/3`]() (`delete/2` before)
- [`Backpex.Resource.delete_all/4`]() (`delete_all/2` before)
- [`Backpex.Resource.update/5`]() (`update/2` before)
- [`Backpex.Resource.update_all/6`]() (`update_all/4` before)
- [`Backpex.Resource.insert/5`]() (`insert/2` before)

If you call one of these functions in your application, you will probably need to update the function call.

We added extensive documentation to the updated functions in `Backpex.Resource`.

See [Pull Request #269](https://github.com/naymspace/backpex/pull/269) for more information.

## Update Upload callback functions

We have updated `Backpex.Fields.Upload` and improved uploads in Backpex:

- Uploaded and existing files are now included in the change to allow them to be validated in the changeset
- Uploads can be required
- Uploads are not removed until form is saved
- Form field errors are displayed for upload field

For these requirements we have simplified the functions in `Backpex.FormComponent` and adapted the validation flow to classic Phoenix applications.

We also updated the callback functions / options used for `Backpex.Fields.Upload`. Among other things, we have split `consume/2` into several callbacks (`put_upload_change/6` and `consume_upload/4`). This allows developers to put uploaded files to the change before consuming the uploads. This makes it possible to do validation on uploads (e.g. require files to be uploaded).

We have also rewritten all the upload documentation in `Backpex.Fields.Upload`. It contains full examples for single and multiple upload fields.

We recommend that you read the new upload documentation and adapt the callback functions in your application accordingly.

See [Pull Request #269](https://github.com/naymspace/backpex/pull/269) for more information.
