# Upgrading to v0.5

## Bump Your Deps

Update Backpex to the latest version:

```elixir
  defp deps do
    [
      {:backpex, "~> 0.5.0"}
    ]
  end
```

## Uploads: Item in `consume_upload/4` now contains changes

Previously, we passed the item without its changes to the `consume_upload/4` callback function. With 0.5, we now pass the persisted item (with its changes) to the function.

## Resource Actions now receive the data instead of the params

Previously, Resource Actions received the params from the form. With 0.5, Resource Actions now receive the data from the form. The data is a map that contains the casted and validated data from the form (received from [`Ecto.Changeset.apply_action/2`](https://hexdocs.pm/ecto/Ecto.Changeset.html#apply_action/2)).

If you had a resource action like this:

```elixir
defmodule MyAppWeb.Admin.ResourceActions.Invite do
    # ...

    @impl Backpex.ResourceAction
    def handle(_socket, params) do
        # Send invite mail to users

        # We suppose there was no error sending the mail.
        if true do
            {:ok, "An invitation email to #{params["email"]} was sent successfully."}
        else
            {:error, "An error occurred while sending an invitation email to  #{params["email"]}!"}
        end
    end
end
```

You should update it to:

```elixir
defmodule MyAppWeb.Admin.ResourceActions.Invite do
    # ...

    @impl Backpex.ResourceAction
    def handle(_socket, data) do
        # Send invite mail to users

        # We suppose there was no error sending the mail.
        if true do
            {:ok, "An invitation email to #{data.email} was sent successfully."}
        else
            {:error, "An error occurred while sending an invitation email to  #{data.email}!"}
        end
    end
end
```

Note that the data is now casted. Therefore you now have atom keys instead of string keys.

## Item Actions now receive the data instead of the params

Previously, Item Actions received the params from the form. With 0.5, Item Actions now receive the data from the form. The data is a map that contains the casted and validated data from the form (received from [`Ecto.Changeset.apply_action/2`](https://hexdocs.pm/ecto/Ecto.Changeset.html#apply_action/2)).

If you had an item action like this:

```elixir
defmodule MyAppWeb.Admin.ItemActions.Delete do
    # ...

    @impl Backpex.ItemAction
    def handle(socket, items, params) do
        datetime = DateTime.truncate(DateTime.utc_now(), :second)

        socket =
            try do
                {:ok, _count} =
                    Backpex.Resource.update_all(
                        socket.assigns,
                        items,
                        [set: [deleted_at: datetime, reason: params["reason"]]],
                        "deleted"
                    )

                socket
                |> clear_flash()
                |> put_flash(:info, "Item(s) successfully deleted.")
            rescue
                socket
                |> clear_flash()
                |> put_flash(:error, error)
            end

        {:noreply, socket}
    end
end
```

You should update it to:

```elixir
defmodule MyAppWeb.Admin.ItemActions.Delete do
    # ...

    @impl Backpex.ItemAction
    def handle(socket, items, data) do
        datetime = DateTime.truncate(DateTime.utc_now(), :second)

        socket =
            try do
                {:ok, _count} =
                    Backpex.Resource.update_all(
                        socket.assigns,
                        items,
                        [set: [deleted_at: datetime, reason: data.reason]],
                        "deleted"
                )

                socket
                |> clear_flash()
                |> put_flash(:info, "Item(s) successfully deleted.")
            rescue
                socket
                |> clear_flash()
                |> put_flash(:error, error)
            end

        {:noreply, socket}
    end
end
```

Note that the data is now casted. Therefore you now have atom keys instead of string keys.

## Refactor the use of icons

We have refactored the way we use icons in Backpex. Previously, we installed [the Heroicons hex.pm package](https://hex.pm/packages/heroicons). We now require a Tailwind plugin that generates the styles for a new `Backpex.HTML.CoreComponents.icon/1` component. This is the default way of using heroicons in new Phoenix projects. We documented the new way in the [installation guide](get_started/installation.md#provide-tailwind-plugin-for-icons).
