# Upgrading to v0.12

## Bump Your Deps

Update Backpex to the latest version:

```elixir
  defp deps do
    [
      {:backpex, "~> 0.12.0"}
    ]
  end
```

## Upgrade to Tailwind 4 and daisyUI 5

The setup now requires you to use Tailwind 4 and daisyUI 5. All internal Backpex components have been upgraded. To
upgrade you application components, you should visit these upgrade guides:

- https://tailwindcss.com/docs/upgrade-guide
- https://daisyui.com/docs/upgrade/

See the upgraded [installation guide](get_started/installation.md) or our
[demo installation](https://github.com/naymspace/backpex/tree/develop/demo) on how the setup should look like.

The design might look a bit different than before. Thats because we went with some of the new defaults of daisyUI.
If you want to restore the previous look, you can overwrite most of the things with your own
[custom theme](https://daisyui.com/docs/themes/#how-to-add-a-new-custom-theme).

If you want to recreate the same look Backpex had before, use the custom theme provided in [this pull request comment](https://github.com/naymspace/backpex/pull/920#issuecomment-2722054893).

## PubSub config is now optional

You can safely remove the pubsub configuration from your LiveResources:

```elixir
    pubsub: [
        name: Demo.PubSub
        topic: "posts",
        event_prefix: "post_"
    ]
```

In order to make it work, you need to set your PubSub server in your config:

```elixir
config :backpex, :pubsub_server, MyApp.PubSub
```

In case you still want to overwrite the settings, the `name` option is now called `server`.

The option for `event_prefix` was removed. Broadcasted events are just called `created`, `updated` and `deleted` now.

See [Listen to PubSub Events](live_resource/listen-to-pubsub-events.md) for more info.

## `return_to/4` becomes `return_to/5`

We have added a `form_action` parameter to the `return_to` function.
This allows you to customize the URL based on whether the user cancels or saves an item.

If you previously had something like this in your LiveResource:

```elixir
@impl Backpex.LiveResource
def return_to(socket, assigns, _live_action, _item) do
  ~p"/admin/posts"
end
```

change it to:

```elixir
@impl Backpex.LiveResource
def return_to(socket, assigns, _live_action, _form_action, _item) do
  ~p"/admin/posts"
end
```

For more information on the new parameter and available options see the [Navigation Guide](/guides/live_resource/navigation.md)

## "Save & Continue editing" button is disabled by default

The "Save & Continue editing" button is now disabled by default. Use the `save_and_continue_editing?` option in your 
LiveResource to enable it.

```elixir
use Backpex.LiveResource,
  ...,
  save_and_continue_button?: true
```

## Translations have been updated

We added a new `translate/1` callback to LiveResources that allows you to translate and change any text in Backpex.

Previously, we had some callbacks to customize texts in Backpex:
- `search_placeholder/0`
- `create_button_label/0`
- `resource_created_message/0`

Such callbacks are limited and do not scale as we'd need to add another one as soon as someone wants to modify a different text.

Therefore, we removed all of the above callbacks.

You can now implement the `translate/1` callback in your LiveResource and match on the text. 
Then return any text you would like to replace the original text with.

For example, if you want to change the label of the "Cancel" and "Save" label in forms as well as the button label for creating a new resource:

```elixir
# in your LiveResource
@impl Backpex.LiveResource
def translate({"Cancel", _opts}), do: gettext("Go back")
def translate({"Save", _opts}), do: gettext("Continue")
def translate({"New %{resource}", opts}), do: gettext("Create %{resource}", opts)
```

The `opts` param contains any bindings you might need for constructing a text.

See [Translations Guide](/guides/translations/translations.md) for detailed information.

To support the new callback, we needed to update some function and components that you might use in your application.

`Backpex.translate/2` has been split into `Backpex.translate/2` and `Backpex.translate_error/1`. See the corresponding function docs for information on how to use them now.

The following components were affected:
- `Backpex.HTML.CoreComponents.filter_badge/1`
- `Backpex.HTML.Form.multi_select/1`
- `Backpex.HTML.Layout.flash_messages/1`
- `Backpex.HTML.Layout.theme_selector/1`
- `Backpex.HTML.Layout.modal/1`
- `Backpex.HTML.Resource.index_filter/1`
- `Backpex.HTML.Resource.pagination_info/1`
- `Backpex.HTML.Resource.pagination/1`

If you use any of them, make sure they work as expected. You might need to add certain attributes (see the corresponding component documentation).

## `Backpex.translate/2` has been split up
