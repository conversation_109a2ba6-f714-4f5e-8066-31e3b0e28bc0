# Upgrading to v0.13

## Bump Your Deps

Update Backpex to the latest version:

```elixir
  defp deps do
    [
      {:backpex, "~> 0.13.0"}
    ]
  end
```

## Removed assigns

We have removed a lot of socket assigns from `LiveResource`s. If you depend on them, you can get them via the
`@live_resource` assign which contains the module name of your `LiveResource`.

Before:

```elixir
@schema
@repo
@changeset_function
@singular_name
@plural_name
@full_text_search
```

After:

```elixir
@live_resource.config(:adapter_config)[:schema]
@live_resource.config(:adapter_config)[:repo]
@live_resource.config(:adapter_config)[:update_changeset] # or: :create_changeset
@live_resource.singular_name()
@live_resource.plural_name()
@live_resource.config(:full_text_search)
```

## Modified components

The following components have been modified:

- `Backpex.HTML.Layout.modal/1` (see [#951](https://github.com/naymspace/backpex/pull/951))
- `Backpex.HTML.Layout.flash_messages/1` (see [#1063](https://github.com/naymspace/backpex/pull/1063))

If you use any of these in your project codebase, make sure they work as expected. You may need to change some
attributes (see the component documentation).

## Translations

In case you want to translate Backpex, these are the added strings:

```
msgid "Attempting to reconnect..."
msgstr "

msgid "Hang in there while we get back on track..."
msgstr ""

msgid "Something went wrong!"
msgstr ""

msgid "We can't find the internet!"
msgstr ""
```

## Display `help_text` in your custom fields

We've added a new `help_text` field option to display a text below the input on form views. If you have custom fields in your application, you may want to support this option as well.

You can use the `Backpex.Field.help_text/2` function to get the help text.

If you use the `Backpex.HTML.Form.input/1` component, you can simply pass this value as the `help_text` attribute.

```elixir
<Backpex.HTML.Form.input
  type="text"
  field={@form[@name]}
  placeholder={@field_options[:placeholder]}
  translate_error_fun={Backpex.Field.translate_error_fun(@field_options, assigns)}
  <!-- add this line -->
  help_text={Backpex.Field.help_text(@field_options, assigns)} 
  phx-debounce={Backpex.Field.debounce(@field_options, assigns)}
  phx-throttle={Backpex.Field.throttle(@field_options, assigns)}
/>
```

## LiveResource is no longer a LiveView

We split the LiveResource module in Backpex to have dedicated LiveViews for `Index`, `Form` (Edit and New) and `Show` views.

The consequence is that your LiveResource module is no longer a LiveView. Therefore, you can no longer define `handle_event`, 
`handle_info` or `handle_params` callbacks or set `on_mount` hooks directly in your LiveResource module.

We are introducing a new `on_mount` LiveResource option that you can use to set `on_mount` hooks. This gives you the flexibility 
to still be able to attach `handle_event`,  `handle_info` or `handle_params` callbacks.

Before:

```elixir
use Backpex.LiveResource,
  ...

@impl Phoenix.LiveView
def handle_event(_params, _url, socket) do
  # Do stuff

  {:cont, socket}
end
```

After:

```elixir
use Backpex.LiveResource,
  ...,
  on_mount: {__MODULE__, :my_hook}

def on_mount(:my_hook, _params, _session, socket) do
  socket = Phoenix.LiveView.attach_hook(socket, :handle_event_callback, :handle_event, &handle_event/3)

  {:cont, socket}
end

def handle_event("my-event", _params_, socket) do
  # Do stuff

  # Make sure to halt as Backpex won't handle these events.
  {:halt, socket}
end

# Make sure to add a catch all event at the end. Otherwise Backpex won't receive internal events.
def handle_event(_event_, _params_, socket) do
  {:cont, socket}
end
```

See [on_mount Hook Guide](/guides/live_resource/on_mount-hook.md) for detailed information.
