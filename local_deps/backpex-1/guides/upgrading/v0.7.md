# Upgrading to v0.7

## Bump Your Deps

Update Backpex to the latest version:

```elixir
  defp deps do
    [
      {:backpex, "~> 0.7.0"}
    ]
  end
```

## Update calls to [`Backpex.Field.handle_index_editable/2`](Backpex.Field.html#handle_index_editable/3)

We have updated the arity and syntax of [`Backpex.Field.handle_index_editable/2`](Backpex.Field.html#handle_index_editable/3). It is now [`Backpex.Field.handle_index_editable/3`](Backpex.Field.html#handle_index_editable/3) and accepts the `socket`, the `value` and the `change`. We now need the value to update the form accordingly.

If you had code like this, e.g. for custom fields:

```elixir
@impl Phoenix.LiveComponent
def handle_event("update-field", %{"index_form" => %{"value" => value}}, socket) do
  Backpex.Field.handle_index_editable(socket, %{} |> Map.put(socket.assigns.name, value))
end
```

it should now look like this

```elixir
@impl Phoenix.LiveComponent
def handle_event("update-field", %{"index_form" => %{"value" => value}}, socket) do
  Backpex.Field.handle_index_editable(socket, value, Map.put(%{}, socket.assigns.name, value))
end
```

## Update calls to `Backpex.Resource`

We have updated certain functions in `Backpex.Resource`.

The following functions are affected:
- `Backpex.Resource.update/6` (`update/5` before)
- [`Backpex.Resource.insert/6`]() (`insert/5` before)
- [`Backpex.Resource.change/7`]()
- [`Backpex.Resource.put_assocs/2`]() (has been removed)

If you call one of these functions in your application, you will probably need to update the function call.

See `Backpex.Resource` for the updated documentation of the functions.

## Update your Item Actions

We've changed the arity of some item action callback functions.

- `icon/1` becomes `icon/2`
- `label/1` becomes `label/2` 

Both callback functions now receive the item as the second parameter. This allows you to construct the icon and label based on the corresponding item.

If you had an item action with code like this

```elixir
@impl Backpex.ItemAction
def icon(assigns) do
 ...
end

@impl Backpex.ItemAction
def label(_assigns) do
  ...
end
```

it should now look like this

```elixir
@impl Backpex.ItemAction
def icon(assigns, _item) do
 ...
end

@impl Backpex.ItemAction
def label(_assigns, _item) do
  ...
end
```

Read more about the new `item` parameter in [the item action guide](/guides/actions/item-actions.md#implementing-an-item-action).