# Upgrading to v0.9

## Bump your deps

Update Backpex to the latest version:

```elixir
  defp deps do
    [
      {:backpex, "~> 0.9.0"}
    ]
  end
```

## Refactor calls to [`Backpex.HTML.Form.field_input/1`]()

We've refactored the [`Backpex.HTML.Form.field_input/1`]() component and renamed it to `Backpex.HTML.Form.input/1`.

- It doesn't rely on a `field_options` map anymore (instead all options must be passed explicitly)
- It now supports lists for all class attributes (`class`, `input_class` and `input_wrapper_class`)
- It now accepts a `translate_error_fun` attribute

In addition, the `input_class` and `input_wrapper_class` attributes now completely override the defaults.

Make sure to update your calls to [`Backpex.HTML.Form.field_input/1`](). This may apply to your custom fields as well.

Before:

```elixir
@impl Backpex.Field
def render_form(assigns) do
  ~H"""
  <div>
    <Layout.field_container>
      <:label align={Backpex.Field.align_label(@field_options, assigns, :center)}>
        <Layout.input_label text={@field_options[:label]} />
      </:label>
      <BackpexForm.field_input type="text" field={@form[@name]} field_options={@field_options} />
    </Layout.field_container>
  </div>
  """
end
```

After:

```elixir
@impl Backpex.Field
def render_form(assigns) do
  ~H"""
  <div>
    <Layout.field_container>
      <:label align={Backpex.Field.align_label(@field_options, assigns, :center)}>
        <Layout.input_label text={@field_options[:label]} />
      </:label>
      <BackpexForm.input
        type="text"
        field={@form[@name]}
        translate_error_fun={Backpex.Field.translate_error_fun(@field_options, assigns)}
        phx-debounce={Backpex.Field.debounce(@field_options, assigns)}
        phx-throttle={Backpex.Field.throttle(@field_options, assigns)}
      />
    </Layout.field_container>
  </div>
  """
end
```

## `Backpex.LiveResource` function usage

Although the change is relatively small, if you are using public functions of the `Backpex.LiveResource` directly,
check the updated function definitions in the module documentation.

## Refactor custom fields

In case you built your own custom fields: We changed the way how to use the `Backpex.Field`.

Before:

```elixir
  use BackpexWeb, :field
```

After:

```elixir
  use Backpex.Field
```

In case your field has field-specific configuration options, you need to provide those when using `Backpex.Field`:

```elixir
  @config_schema [
    # see https://hexdocs.pm/nimble_options/NimbleOptions.html
    # or any other core backpex field for examples...
  ]

  use Backpex.Field, config_schema: @config_schema
```

## Removed string support on `throttle` field options

The fields that allow the `throttle` options previously supported a string value (e.g. `"500"`).
Please change it to an integer value (e.g. `500`).

## Resource Action and Item Action `init_change/1` is renamed

The term `init_change` was confusing because the result is being used as the base schema / item for the changeset function. Therefore we renamed the function to `base_schema/1` for both Item Actions and Resource Actions.

## Resource Action and Item Action `handle` functions behave differently

Both Item Action and Resource Action `handle` functions now have to return either `{:ok, socket}` or `{:error, changeset}`. A flash message is no longer added to the socket automatically.

Make sure to read the improved documentation for the `handle` functions to understand how you should use them now:

- `c:Backpex.ItemAction.handle/3`
- `c:Backpex.ResourceAction.handle/2`

## Translate new texts

The latest version of Backpex introduces the following texts

- "Save & Continue Editing"

Make sure you translate these texts in your translation files.
