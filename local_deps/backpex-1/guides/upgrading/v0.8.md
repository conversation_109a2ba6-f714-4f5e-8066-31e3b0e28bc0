# Upgrading to v0.8

## Bump your deps

Update Backpex to the latest version:

```elixir
  defp deps do
    [
      {:backpex, "~> 0.8.0"}
    ]
  end
```

## Introduction of the adapter pattern

### Changed `Backpex.LiveResource` configuration

With this version of Backpex we are starting to implement the adapter pattern for flexible data layers.
Going forward you need to move adapter-specific configuration to the `adapter_config` options. Before:

```elixir
  use Backpex.LiveResource,
    layout: {DemoWeb.Layouts, :admin},
    schema: Demo.User,
    repo: Demo.Repo,
    update_changeset: &Demo.User.changeset/3,
    create_changeset: &Demo.User.changeset/3,
    pubsub: Demo.PubSub,
    topic: "users",
    event_prefix: "user_"
```

After:

```elixir
  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ecto,
    adapter_config: [
      schema: Demo.User,
      repo: Demo.Repo,
      update_changeset: &Demo.User.changeset/3,
      create_changeset: &Demo.User.changeset/3,
    ],
    layout: {DemoWeb.Layouts, :admin},
    pubsub: [
      name: Demo.PubSub,
      topic: "users",
      event_prefix: "user_"
    ]
```

The `adapter` key is optional and defaults to `Backpex.Adapters.Ecto`. See `Backpex.Adapter` for more information.

As you may have noticed in the above example, we also changed the pubsub configuration syntax. 
You now have to provide a Keyword list with the `name`, `topic` and `event_prefix` key (see below).

In addition, all options of the LiveResource and the corresponding adapters are now validated at compile time.

### Refactor item query

With the release of the adapter pattern, the `item_query/3` function has to be configured in the adapter config.

If you had an `item_query/3` configuration like this:

```elixir
# in your resource configuration file (live resource)
use Backpex.LiveResource,
  # ...options

  @impl Backpex.LiveResource
  def item_query(query, :index, _assigns) do
    query
    |> where([post], post.published)
  end
```

change it to an adapter config:

```elixir
# in your resource configuration file (live resource)
use Backpex.LiveResource,
  # ...other options
  adapter_config: [
    # ...other adapter options
    item_query: &__MODULE__.item_query/3
  ]

  def item_query(query, :index, _assigns) do
    query
    |> where([post], post.published)
  end

  def item_query(query, _live_action_, _assigns) do
    query
  end
```

See [Item Query documentation](../live_resource/item-query.md) for more information.

> Note that the `item_query/3` function is only used in `Backpex.Adapters.Ecto`.

### Changed `Backpex.Resource` parameters

If you are not using this module directly in your code, you can safely ignore this section.

All functions in the `Backpex.Resource` module previously expected adapter specific parameters like `repo` and `schema`.
This is now simplified to just the `live_resource`. See `Backpex.Resource` for the updated functions.

### PubSub configuration syntax has changed

We have changed the syntax for configuring PubSub. You now have to provide a keyword list with the `name`, `topic` and 
`event_prefix` keys instead of separate options.

```elixir
  use Backpex.LiveResource,
    ...
    pubsub: Demo.PubSub,
    topic: "users",
    event_prefix: "user_"
```

After:

```elixir
  use Backpex.LiveResource,
    ...
    pubsub: [
      name: Demo.PubSub,
      topic: "users",
      event_prefix: "user_"
    ]
```

### Removed the ability to disable PubSub

We have stated in our documentation that you must configure PubSub in your LiveResource. However, you could set `pubsub` 
to `false`, which prevented PubSub events from being sent. We have removed this behavior and setting  `pubsub` to `false` 
will result in an error.

This configuration is **not** possible anymore:

```elixir
  use Backpex.LiveResource,
    ...
    pubsub: false
```

### No anonymous functions in LiveResource configuration anymore

it is no longer possible to use an anonymous function in LiveResource configuration options.

If you had something like this:

```elixir
use Backpex.LiveResource,
  init_order: fn _assigns -> %{by: :username, direction: :asc} end
```

you have to change it to this:

```elixir
use Backpex.LiveResource,
  init_order: &__MODULE__.init_order/1

def init_order(_assigns) do
  %{by: :username, direction: :asc}
end
```
