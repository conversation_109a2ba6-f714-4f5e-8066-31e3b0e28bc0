## This file is a PO Template file.
##
## "msgid"s here are often extracted from source code.
## Add new messages manually only if they're dynamic
## messages that can't be statically extracted.
##
## Run "mix gettext.extract" to bring this file up to
## date. Leave "msgstr"s empty as changing them here has no
## effect: edit them in PO (.po) files instead.
#
msgid ""
msgstr ""

#, elixir-autogen, elixir-format
msgid "%{count} %{resources} have been deleted successfully."
msgstr ""

#, elixir-autogen, elixir-format
msgid "%{resource} has been deleted successfully."
msgstr ""

#, elixir-autogen, elixir-format
msgid "%{resource} has been edited successfully."
msgstr ""

#, elixir-autogen, elixir-format
msgid "(%{count} total)"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Add entry"
msgstr ""

#, elixir-autogen, elixir-format
msgid "An error occurred while deleting %{count} %{resources}!"
msgstr ""

#, elixir-autogen, elixir-format
msgid "An error occurred while deleting the %{resource}!"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Apply"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Are you sure you want to delete %{count} items?"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Are you sure you want to delete the item?"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Attach %{resource}"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Attempting to reconnect..."
msgstr ""

#, elixir-autogen, elixir-format
msgid "Cancel"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Choose %{resource} ..."
msgstr ""

#, elixir-autogen, elixir-format
msgid "clear"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Clear %{name} filter"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Close modal"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Delete"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Deselect all"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Detach relation with index %{index}"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Edit"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Edit %{resource}"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Edit relation with index %{index}"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Error in relation with index %{index}"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Filters"
msgstr ""

#, elixir-autogen, elixir-format
msgid "From"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Hang in there while we get back on track..."
msgstr ""

#, elixir-autogen, elixir-format
msgid "Items %{from} to %{to}"
msgstr ""

#, elixir-autogen, elixir-format
msgid "New %{resource}"
msgstr ""

#, elixir-autogen, elixir-format
msgid "New %{resource} has been created successfully."
msgstr ""

#, elixir-autogen, elixir-format
msgid "Next Page"
msgstr ""

#, elixir-autogen, elixir-format
msgid "No %{resources} found"
msgstr ""

#, elixir-autogen, elixir-format
msgid "No options found"
msgstr ""

#, elixir-autogen, elixir-format
msgid "or drag and drop"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Previous Page"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Save"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Save & Continue editing"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Search"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Select all"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Select all items"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Select item with id: %{id}"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Select options..."
msgstr ""

#, elixir-autogen, elixir-format
msgid "selected"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Show"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Show more"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Something went wrong!"
msgstr ""

#, elixir-autogen, elixir-format
msgid "The item is used elsewhere."
msgstr ""

#, elixir-autogen, elixir-format
msgid "The items are used elsewhere."
msgstr ""

#, elixir-autogen, elixir-format
msgid "There are errors in the form."
msgstr ""

#, elixir-autogen, elixir-format
msgid "To"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Toggle columns"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Toggle metrics"
msgstr ""

#, elixir-autogen, elixir-format
msgid "too large"
msgstr ""

#, elixir-autogen, elixir-format
msgid "too many files"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Try a different filter setting or clear all filters."
msgstr ""

#, elixir-autogen, elixir-format
msgid "Try a different search term."
msgstr ""

#, elixir-autogen, elixir-format
msgid "unacceptable file type"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Unselect %{label}"
msgstr ""

#, elixir-autogen, elixir-format
msgid "Upload a file"
msgstr ""

#, elixir-autogen, elixir-format
msgid "We can't find the internet!"
msgstr ""
