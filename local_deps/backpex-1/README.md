[![CI](https://github.com/naymspace/backpex/actions/workflows/ci.yml/badge.svg)](https://github.com/naymspace/backpex/actions/workflows/ci.yml)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://github.com/naymspace/backpex/blob/develop/LICENSE.md)
[![Hex](https://img.shields.io/hexpm/v/backpex.svg)](https://hex.pm/packages/backpex)
[![Hex Docs](https://img.shields.io/badge/hex-docs-green)](https://hexdocs.pm/backpex)

<div align="center">
  <img src="https://github.com/naymspace/backpex/blob/develop/priv/static/images/logo.svg?raw=true" width="100" height="100">
  <br />
  <br />
  Phoenix LiveView Admin Panel · Backpex
  <br />
  <br />
  <span>📚 <a href="https://hexdocs.pm/backpex">Documentation</a></span>
  •
  <span>🛠️ <a href="https://hexdocs.pm/backpex/installation.html">Installation Guide</a></span>
  •
  <span>🩵 <a href="https://hexdocs.pm/backpex/contribute-to-backpex.html">Contribute</a></span>
</div>

# Backpex

Welcome! Backpex is a highly customizable administration panel for Phoenix LiveView applications. Quickly create beautiful CRUD views for your existing data using configurable *LiveResources*. Backpex integrates seamlessly with your existing Phoenix application and provides a simple way to manage your resources. It is highly customizable and can be extended with your own layouts, views, field types, filters and more.

![Backpex Screenshot](https://github.com/naymspace/backpex/blob/develop/priv/static/images/screenshot.png?raw=true)

<div align="center">
  <a href="https://backpex.live/"><strong>Visit our Live Demo →</strong></a>
</div>

## Key Features

- 📊 **LiveResources**: Quickly create LiveResource modules for your database tables with fully customizable CRUD views. Bring your own layout or use our components.
- 🔍 **Search and Filters**: Define searchable fields and add custom filters for instant, LiveView-powered results.
- ⚡ **Resource Actions**: Implement global custom actions like user invitations or exports, with support for additional form fields.
- 🔒 **Authorization**: Handle CRUD and custom action authorization via simple pattern matching, with optional integration for external authorization libraries.
- 🧩 **Field Types**: Out-of-the-box support for Text, Number, Date, Upload, and more. Easily extend with your own custom field type modules.
- 🔗 **Associations**: Effortlessly handle HasOne, BelongsTo, and HasMany(Through) associations with minimal configuration. Customize available options and rendered columns.
- 📈 **Metrics**: Add value metrics such as sums or averages for quick data insights, with more metric types on the horizon.

## Installation & Documentation

See our comprehensive [installation guide](https://hexdocs.pm/backpex/installation.html) for more information on how to install and configure Backpex in your Phoenix application.

We also provide a detailed [documentation](https://hexdocs.pm/backpex) with guides on how to use Backpex and how to customize it to your needs.

## Learn More

- [What is Backpex?](https://hexdocs.pm/backpex/what-is-backpex.html)
- [Why we built Backpex?](https://hexdocs.pm/backpex/what-is-backpex.html)
- [Contribute to Backpex](https://hexdocs.pm/backpex/contribute-to-backpex.html)

## License

Backpex is open-source software, licensed under the [MIT License](https://github.com/naymspace/backpex/blob/main/LICENSE.md).
