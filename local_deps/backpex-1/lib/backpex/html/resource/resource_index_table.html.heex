<table class="table">
  <thead class="bg-base-100 text-base-content uppercase">
    <tr id="row-headers" phx-hook="BackpexStickyActions" class="border-b-2">
      <th :if={Enum.any?(index_item_actions(@item_actions))}>
        <input
          phx-click="toggle-item-selection"
          type="checkbox"
          class="checkbox checkbox-sm"
          checked={@select_all}
          aria-label={Backpex.__("Select all items", @live_resource)}
        />
      </th>
      <th :for={{name, %{label: label} = field_options} <- @fields} :if={active?(@active_fields, name)} class="h-12">
        <div class={["text-xs font-medium", align(field_options), index_column_class(assigns, field_options)]}>
          <%= if Enum.member?(@orderable_fields, name) do %>
            <.order_link
              live_resource={@live_resource}
              socket={@socket}
              params={@params}
              label={label}
              name={name}
              query_options={@query_options}
            />
          <% else %>
            {label}
          <% end %>
        </div>
      </th>
      <th class={["bg-base-100", sticky_col_class()]}></th>
    </tr>
  </thead>
  <tbody>
    <tr
      :for={{item, index} <- Enum.with_index(@items)}
      id={"row-item-#{LiveResource.primary_value(item, @live_resource)}"}
      class={index_row_class(assigns, item, selected?(@selected_items, item), index)}
      phx-hook="BackpexStickyActions"
    >
      <td :if={Enum.any?(index_item_actions(@item_actions))} class="relative">
        <div :if={selected?(@selected_items, item)} class="bg-base-content absolute inset-y-0 left-0 w-0.5" />
        <input
          id={"select-input-#{LiveResource.primary_value(item, @live_resource)}"}
          phx-click="update-selected-items"
          phx-value-id={LiveResource.primary_value(item, @live_resource)}
          type="checkbox"
          class="checkbox checkbox-sm"
          checked={selected?(@selected_items, item)}
          aria-label={
            Backpex.__(
              {"Select item with id: %{id}", %{id: LiveResource.primary_value(item, @live_resource)}},
              @live_resource
            )
          }
        />
      </td>
      <td :for={{name, field_options} <- @fields} :if={active?(@active_fields, name)} class="text-base-content/75">
        <div class={[align(field_options), index_column_class(assigns, field_options)]}>
          <.resource_field name={name} item={item} {assigns} />
        </div>
      </td>
      <td class={[
        index_row_class(assigns, item, selected?(@selected_items, item), index),
        sticky_col_class(),
        "text-base-content/75 z-10 w-0 px-6"
      ]}>
        <div class={["flex items-center justify-end space-x-2"]}>
          <button
            :for={{key, action} <- row_item_actions(@item_actions)}
            :if={@live_resource.can?(assigns, key, item)}
            id={"item-action-#{key}-#{LiveResource.primary_value(item, @live_resource)}"}
            type="button"
            phx-click="item-action"
            phx-value-action-key={key}
            phx-value-item-id={LiveResource.primary_value(item, @live_resource)}
            aria-label={action.module.label(assigns, item)}
            phx-hook="BackpexTooltip"
            data-tooltip={action.module.label(assigns, item)}
          >
            {action.module.icon(assigns, item)}
          </button>
        </div>
      </td>
    </tr>
  </tbody>
</table>
