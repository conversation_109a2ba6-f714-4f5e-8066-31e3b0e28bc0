defmodule Backpex.LiveResource.Form do
  @moduledoc false
  use BackpexWeb, :html

  import Phoenix.Component

  alias Backpex.LiveResource
  alias Backpex.Resource

  require Backpex

  def mount(params, _session, socket, live_resource) do
    live_action = socket.assigns.live_action

    socket
    |> assign(:live_resource, live_resource)
    |> assign(:panels, live_resource.panels())
    |> assign(:fluid?, live_resource.config(:fluid?))
    |> assign(:params, params)
    |> assign(:page_title, page_title(live_resource, live_action))
    |> assign_fields(live_action)
    |> assign_item(live_action)
    |> can?(live_resource, live_action)
    |> assign_changeset(live_action)
    |> ok()
  end

  def handle_params(_params, _url, socket) do
    noreply(socket)
  end

  def render(assigns) do
    Backpex.HTML.Resource.resource_form(assigns)
  end

  def handle_info({:update_changeset, changeset}, socket) do
    socket
    |> assign(:changeset, changeset)
    |> noreply()
  end

  def handle_info({:put_assoc, {key, value} = _assoc}, socket) do
    changeset = Ecto.Changeset.put_assoc(socket.assigns.changeset, key, value)
    assocs = Map.get(socket.assigns, :assocs, []) |> Keyword.put(key, value)

    socket
    |> assign(:assocs, assocs)
    |> assign(:changeset, changeset)
    |> noreply()
  end

  def handle_event(_event, _params, socket) do
    noreply(socket)
  end

  defp page_title(live_resource, :new = _live_action) do
    Backpex.__({"New %{resource}", %{resource: live_resource.singular_name()}}, live_resource)
  end

  defp page_title(live_resource, :edit = _live_action) do
    Backpex.__({"Edit %{resource}", %{resource: live_resource.singular_name()}}, live_resource)
  end

  defp assign_item(socket, :new = _live_action) do
    live_resource = socket.assigns.live_resource
    adapter_config = live_resource.config(:adapter_config)

    empty_item =
      if is_ash_adapter?(live_resource) do
        # Ash适配器：使用resource创建空struct
        resource = adapter_config[:resource]
        if resource do
          struct(resource)
        else
          raise "Ash adapter requires :resource in adapter_config"
        end
      else
        # Ecto适配器：使用schema创建空struct
        schema = adapter_config[:schema]
        if schema do
          schema.__struct__()
        else
          raise "Ecto adapter requires :schema in adapter_config"
        end
      end

    assign(socket, :item, empty_item)
  end

  defp assign_item(socket, :edit = _live_action) do
    %{live_resource: live_resource, params: params} = socket.assigns

    backpex_id = Map.fetch!(params, "backpex_id")
    primary_value = URI.decode(backpex_id)

    item = Resource.get!(primary_value, socket.assigns, live_resource)

    assign(socket, :item, item)
  end

  defp can?(socket, live_resource, :new = live_action) do
    if not live_resource.can?(socket.assigns, live_action, nil), do: raise(Backpex.ForbiddenError)

    socket
  end

  defp can?(socket, live_resource, :edit = live_action) do
    if not live_resource.can?(socket.assigns, live_action, socket.assigns.item), do: raise(Backpex.ForbiddenError)

    socket
  end

  defp assign_fields(socket, live_action) do
    fields =
      socket.assigns.live_resource.validated_fields()
      |> LiveResource.filtered_fields_by_action(socket.assigns, live_action)

    assign(socket, :fields, fields)
  end

  defp assign_changeset(socket, live_action) do
    %{live_resource: live_resource, item: item, fields: fields} = socket.assigns

    if is_ash_adapter?(live_resource) do
      # Ash适配器：创建AshPhoenix.Form
      adapter_config = live_resource.config(:adapter_config)
      resource = adapter_config[:resource]

      form = case live_action do
        :new ->
          # 新建：使用for_create
          ash_action = get_ash_action(live_action, live_resource)
          AshPhoenix.Form.for_create(resource, ash_action,
            as: "change"
          )
        :edit ->
          # 编辑：使用for_update
          ash_action = get_ash_action(live_action, live_resource)
          AshPhoenix.Form.for_update(item, ash_action,
            as: "change"
          )
        _ ->
          # 其他情况：使用for_update
          ash_action = get_ash_action(live_action, live_resource)
          AshPhoenix.Form.for_update(item, ash_action,
            as: "change"
          )
      end

      # 为了兼容性，我们仍然创建changeset
      changeset = Resource.change(item, %{}, fields, socket.assigns, live_resource, action: :validate)

      socket
      |> assign(:changeset, changeset)
      |> assign(:form, form)
    else
      # Ecto适配器：使用原有逻辑
      changeset = changeset(live_resource, live_action)
      LiveResource.assign_changeset(socket, changeset, item, fields, live_action)
    end
  end

  defp changeset(live_resource, :new = _live_action), do: live_resource.config(:adapter_config)[:create_changeset]
  defp changeset(live_resource, :edit = _live_action), do: live_resource.config(:adapter_config)[:update_changeset]

  # 检测是否使用Ash适配器
  defp is_ash_adapter?(live_resource) do
    require Logger
    Logger.debug("Checking if live_resource is using Ash adapter, config: #{inspect(live_resource.config(:adapter))}")
    Logger.debug("Checking if live_resource is using Ash adapter, config #{inspect(live_resource.config(:adapter) == Backpex.Adapters.Ash)}")
    live_resource.config(:adapter) == Backpex.Adapters.Ash
  end



  # 获取Ash action名称
  defp get_ash_action(live_action, live_resource) do
    adapter_config = live_resource.config(:adapter_config)

    case live_action do
      :new -> adapter_config[:create_action] || :create
      :edit -> adapter_config[:update_action] || :update
      _ -> adapter_config[:update_action] || :update
    end
  end
end
