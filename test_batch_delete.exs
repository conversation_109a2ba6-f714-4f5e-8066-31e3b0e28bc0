# 测试批量删除功能的脚本

IO.puts("🧪 开始测试批量删除功能...")

# 1. 创建测试活动
IO.puts("\n1. 创建测试活动...")
activity = Teen.ActivitySystem.InviteCashActivity
|> Ash.Changeset.for_create(:create, %{
  title: "测试批量删除活动",
  description: "用于测试批量删除功能的活动",
  total_reward: 1000,
  initial_min: 10,
  initial_max: 100,
  start_date: ~D[2024-01-01],
  end_date: ~D[2024-12-31]
})
|> Ash.create!()

IO.puts("✅ 活动创建成功: #{activity.id}")

# 2. 为该活动创建多个奖励配置
IO.puts("\n2. 创建奖励配置...")

config1 = Teen.ActivitySystem.InviteRewardConfig
|> Ash.Changeset.for_create(:create, %{
  activity_id: activity.id,
  round_number: 1,
  task_type: :invite_register,
  reward_type: :cash,
  min_reward: Decimal.new("50"),
  max_reward: Decimal.new("100"),
  required_progress: 1
})
|> Ash.create!()

config2 = Teen.ActivitySystem.InviteRewardConfig
|> Ash.Changeset.for_create(:create, %{
  activity_id: activity.id,
  round_number: 2,
  task_type: :invite_recharge,
  reward_type: :cash,
  min_reward: Decimal.new("100"),
  max_reward: Decimal.new("200"),
  required_progress: 3
})
|> Ash.create!()

config3 = Teen.ActivitySystem.InviteRewardConfig
|> Ash.Changeset.for_create(:create, %{
  activity_id: activity.id,
  round_number: 3,
  task_type: :invite_play,
  reward_type: :coins,
  min_reward: Decimal.new("200"),
  max_reward: Decimal.new("300"),
  required_progress: 5
})
|> Ash.create!()

IO.puts("✅ 奖励配置创建成功:")
IO.puts("  - 配置1: #{config1.id}")
IO.puts("  - 配置2: #{config2.id}")
IO.puts("  - 配置3: #{config3.id}")

# 3. 验证配置存在
IO.puts("\n3. 验证奖励配置存在...")
existing_configs = Teen.ActivitySystem.InviteRewardConfig
|> Ash.Query.for_read(:list_by_activity, %{activity_id: activity.id})
|> Ash.read!()

IO.puts("✅ 找到 #{length(existing_configs)} 个奖励配置")

# 4. 测试批量删除奖励配置功能
IO.puts("\n4. 测试批量删除奖励配置功能...")
case Teen.ActivitySystem.InviteRewardConfig.destroy_by_activity(activity.id) do
  :ok ->
    IO.puts("✅ 批量删除奖励配置成功")
  {:error, reason} ->
    IO.puts("❌ 批量删除奖励配置失败: #{inspect(reason)}")
    raise "批量删除失败"
end

# 5. 验证奖励配置是否被删除
IO.puts("\n5. 验证奖励配置是否被删除...")
remaining_configs = Teen.ActivitySystem.InviteRewardConfig
|> Ash.Query.for_read(:list_by_activity, %{activity_id: activity.id})
|> Ash.read!()

if Enum.empty?(remaining_configs) do
  IO.puts("✅ 所有奖励配置已被正确删除")
else
  IO.puts("❌ 还有 #{length(remaining_configs)} 个奖励配置未被删除")
  raise "删除不完整"
end

# 6. 删除测试活动
IO.puts("\n6. 删除测试活动...")
try do
  deleted_activity = activity |> Ash.Changeset.for_destroy(:destroy) |> Ash.destroy!()
  IO.puts("✅ 活动删除成功: #{deleted_activity.id}")
rescue
  error ->
    IO.puts("❌ 活动删除失败: #{inspect(error)}")
    raise error
end

IO.puts("\n🎉 批量删除功能测试通过！")
IO.puts("🏁 测试完成")
