# 简单测试 claim_limit 逻辑

defmodule SimpleTest do
  def test_create_logic do
    IO.puts("=== 测试创建逻辑 ===")
    
    # 测试用户设置了 claim_limit 的情况
    user_claim_limit = 5
    task_type = :vip_daily_login
    
    {_default_frequency, default_claim_limit} = case task_type do
      :vip_daily_login -> {1, nil}
      :vip_level_upgrade -> {nil, 1}
      _ -> {nil, nil}
    end
    
    # 修复后的逻辑：只有在用户没有设置时才使用默认值
    final_claim_limit = if is_nil(user_claim_limit) do
      default_claim_limit
    else
      user_claim_limit
    end
    
    IO.puts("用户设置的 claim_limit: #{inspect(user_claim_limit)}")
    IO.puts("任务类型默认 claim_limit: #{inspect(default_claim_limit)}")
    IO.puts("最终 claim_limit: #{inspect(final_claim_limit)}")
    IO.puts("✅ 用户设置被保留: #{final_claim_limit == user_claim_limit}")
    IO.puts("")
    
    # 测试用户没有设置 claim_limit 的情况
    user_claim_limit2 = nil
    task_type2 = :vip_level_upgrade
    
    {_default_frequency2, default_claim_limit2} = case task_type2 do
      :vip_daily_login -> {1, nil}
      :vip_level_upgrade -> {nil, 1}
      _ -> {nil, nil}
    end
    
    final_claim_limit2 = if is_nil(user_claim_limit2) do
      default_claim_limit2
    else
      user_claim_limit2
    end
    
    IO.puts("用户设置的 claim_limit: #{inspect(user_claim_limit2)}")
    IO.puts("任务类型默认 claim_limit: #{inspect(default_claim_limit2)}")
    IO.puts("最终 claim_limit: #{inspect(final_claim_limit2)}")
    IO.puts("✅ 使用默认值: #{final_claim_limit2 == default_claim_limit2}")
    IO.puts("")
  end
  
  def run do
    IO.puts("🧪 测试 claim_limit 修复逻辑")
    IO.puts("=" <> String.duplicate("=", 40))
    IO.puts("")
    
    test_create_logic()
    
    IO.puts("🎯 结论:")
    IO.puts("修复后的逻辑确保:")
    IO.puts("1. 用户明确设置的 claim_limit 值会被保留")
    IO.puts("2. 用户未设置时才使用任务类型的默认值")
    IO.puts("3. 解决了之前自动覆盖用户输入的问题")
  end
end

SimpleTest.run()
