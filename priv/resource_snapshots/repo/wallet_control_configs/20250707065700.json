{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "game_id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "wallet_control_configs_game_id_fkey", "on_delete": null, "on_update": null, "primary_key?": false, "schema": "public", "table": "game_configs"}, "scale": null, "size": null, "source": "game_id", "type": "bigint"}, {"allow_nil?": false, "default": "20", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "collect_ratio", "type": "bigint"}, {"allow_nil?": false, "default": "25", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "release_ratio", "type": "bigint"}, {"allow_nil?": false, "default": "10", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "sensitivity", "type": "bigint"}, {"allow_nil?": false, "default": "true", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "is_enabled", "type": "boolean"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "description", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": "channel_id", "global": true, "strategy": "attribute"}, "name": "wallet_control_configs_created_by_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "users"}, "scale": null, "size": null, "source": "created_by", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": "channel_id", "global": true, "strategy": "attribute"}, "name": "wallet_control_configs_updated_by_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "users"}, "scale": null, "size": null, "source": "updated_by", "type": "uuid"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "2350ECAA6E910D32360B66F9E2EE5F28DA5BFA327CE0F3B3F9200DCA21C50D2F", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "wallet_control_configs_unique_game_id_index", "keys": [{"type": "atom", "value": "game_id"}], "name": "unique_game_id", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "wallet_control_configs"}