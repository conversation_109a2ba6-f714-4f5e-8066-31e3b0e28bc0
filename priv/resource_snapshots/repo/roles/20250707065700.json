{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "name", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "code", "type": "text"}, {"allow_nil?": false, "default": "0", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "level", "type": "bigint"}, {"allow_nil?": false, "default": "1", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "status", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "description", "type": "text"}, {"allow_nil?": true, "default": "[]", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "permission_codes", "type": ["array", "text"]}, {"allow_nil?": true, "default": "[]", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "menu_permissions", "type": ["array", "text"]}, {"allow_nil?": true, "default": "%{}", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "data_permissions", "type": "map"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "C6832B0E89E9DA8FD2446D6D08D13EE11F34357098A0DB194D5F179CA4FAE25F", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "roles_unique_code_index", "keys": [{"type": "atom", "value": "code"}], "name": "unique_code", "nils_distinct?": true, "where": null}, {"all_tenants?": false, "base_filter": null, "index_name": "roles_unique_name_index", "keys": [{"type": "atom", "value": "name"}], "name": "unique_name", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "roles"}