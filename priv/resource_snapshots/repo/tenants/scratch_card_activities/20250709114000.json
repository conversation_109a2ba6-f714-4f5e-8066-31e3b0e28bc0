{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "scratch_card_activities_activity_id_fkey", "on_delete": "delete", "on_update": null, "primary_key?": true, "schema": "public", "table": "activities"}, "scale": null, "size": null, "source": "activity_id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "activity_title", "type": "text"}, {"allow_nil?": false, "default": "30", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "claimable_count", "type": "bigint"}, {"allow_nil?": false, "default": "\"30\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "reward1_probability", "type": "decimal"}, {"allow_nil?": false, "default": "\"30\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "reward2_probability", "type": "decimal"}, {"allow_nil?": false, "default": "\"40\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "reward3_probability", "type": "decimal"}, {"allow_nil?": false, "default": "\"enabled\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "status", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "description", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "start_date", "type": "date"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "end_date", "type": "date"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "max_participants", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "daily_limit", "type": "bigint"}, {"allow_nil?": false, "default": "0", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "sort_order", "type": "bigint"}, {"allow_nil?": true, "default": "%{}", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "config_data", "type": "map"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "3080982B8ABD19687EAFBC9EDBAFD35FAAD01B7543EA3967703F9451F67A7745", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "scratch_card_activities_unique_activity_title_index", "keys": [{"type": "atom", "value": "activity_title"}], "name": "unique_activity_title", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": false, "strategy": "context"}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "scratch_card_activities"}