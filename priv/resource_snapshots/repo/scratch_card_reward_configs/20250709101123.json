{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "activity_title", "type": "text"}, {"allow_nil?": false, "default": "30", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "claimable_count", "type": "bigint"}, {"allow_nil?": false, "default": "\"30\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "reward1_probability", "type": "decimal"}, {"allow_nil?": false, "default": "\"30\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "reward2_probability", "type": "decimal"}, {"allow_nil?": false, "default": "\"40\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "reward3_probability", "type": "decimal"}, {"allow_nil?": false, "default": "\"enabled\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "status", "type": "text"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": false, "strategy": "context"}, "name": "scratch_card_reward_configs_activity_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "scratch_card_activities"}, "scale": null, "size": null, "source": "activity_id", "type": "uuid"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "8D344B9C3115AAAB9DF65832B42AF35D0A0210024B4BA888EB711676F2C75A29", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "scratch_card_reward_configs_unique_activity_config_index", "keys": [{"type": "atom", "value": "activity_id"}], "name": "unique_activity_config", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "scratch_card_reward_configs"}