{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "numeric_id", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "username", "type": "citext"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "email", "type": "citext"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "phone", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "phone_verified_at", "type": "utc_datetime"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "last_offline_at", "type": "utc_datetime"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "hashed_password", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "confirmed_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "-1", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "agent_level", "type": "bigint"}, {"allow_nil?": false, "default": "0", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "permission_level", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "channel_id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "users_channel_id_fkey", "on_delete": null, "on_update": null, "primary_key?": false, "schema": "public", "table": "channels"}, "scale": null, "size": null, "source": "channel_id", "type": "text"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "ED57165E6FE1929C4D17EFB46907AA4295149706AF0935BB9CA59BAB67E41B2D", "identities": [{"all_tenants?": true, "base_filter": null, "index_name": "users_global_username_index", "keys": [{"type": "atom", "value": "username"}], "name": "global_username", "nils_distinct?": true, "where": null}, {"all_tenants?": false, "base_filter": null, "index_name": "users_unique_email_index", "keys": [{"type": "atom", "value": "email"}], "name": "unique_email", "nils_distinct?": true, "where": null}, {"all_tenants?": false, "base_filter": null, "index_name": "users_unique_numeric_id_index", "keys": [{"type": "atom", "value": "numeric_id"}], "name": "unique_numeric_id", "nils_distinct?": true, "where": null}, {"all_tenants?": false, "base_filter": null, "index_name": "users_unique_phone_index", "keys": [{"type": "atom", "value": "phone"}], "name": "unique_phone", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": "channel_id", "global": true, "strategy": "attribute"}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "users"}