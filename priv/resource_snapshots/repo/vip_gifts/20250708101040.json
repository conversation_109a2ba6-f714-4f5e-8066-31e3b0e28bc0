{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "title", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "description", "type": "text"}, {"allow_nil?": false, "default": "\"vip_daily\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "task_type", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "vip_level", "type": "bigint"}, {"allow_nil?": false, "default": "\"0\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "daily_reward", "type": "decimal"}, {"allow_nil?": false, "default": "\"0\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "weekly_reward", "type": "decimal"}, {"allow_nil?": false, "default": "\"0\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "monthly_reward", "type": "decimal"}, {"allow_nil?": false, "default": "\"coins\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "reward_type", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "start_date", "type": "date"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "end_date", "type": "date"}, {"allow_nil?": false, "default": "\"enabled\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "status", "type": "text"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "2DF71B2C15662D64026DA91D8290CB9EB31E1B4467F9903042C7783436D6F034", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "vip_gifts_unique_vip_level_index", "keys": [{"type": "atom", "value": "vip_level"}], "name": "unique_vip_level", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "vip_gifts"}