defmodule Cypridina.Repo.Migrations.RemoveVipLevelUniqueConstraint do
  @moduledoc """
  Removes the unique constraint on vip_level in vip_gifts table.

  This allows multiple VIP gifts to be created for the same VIP level,
  which is needed for different task types and reward configurations.
  """

  use Ecto.Migration

  def up do
    # Remove the unique constraint on vip_level
    drop_if_exists unique_index(:vip_gifts, [:vip_level], name: "vip_gifts_unique_vip_level_index")
  end

  def down do
    # Recreate the unique constraint on vip_level
    create unique_index(:vip_gifts, [:vip_level], name: "vip_gifts_unique_vip_level_index")
  end
end
