defmodule Cypridina.Repo.Migrations.AddTitleDescriptionToGameTaskManual do
  @moduledoc """
  Manually adds title and description columns to game_tasks table.
  """

  use Ecto.Migration

  def up do
    alter table(:game_tasks) do
      add :title, :text, null: true
      add :description, :text, null: true
    end
  end

  def down do
    alter table(:game_tasks) do
      remove :title
      remove :description
    end
  end
end
