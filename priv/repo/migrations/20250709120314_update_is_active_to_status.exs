defmodule Cypridina.Repo.Migrations.UpdateIsActiveToStatus do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:scratch_card_task_levels, "scratch_card_task_levels_activity_id_fkey")

    alter table(:scratch_card_task_levels) do
      modify :activity_id,
             references(:scratch_card_activities,
               column: :id,
               name: "scratch_card_task_levels_activity_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    drop constraint(:scratch_card_task_rounds, "scratch_card_task_rounds_activity_id_fkey")

    alter table(:scratch_card_task_rounds) do
      modify :activity_id,
             references(:scratch_card_activities,
               column: :id,
               name: "scratch_card_task_rounds_activity_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    rename table(:scratch_card_level_rewards), :is_active, to: :status

    alter table(:scratch_card_level_rewards) do
      modify :status, :text, default: "active"
    end
  end

  def down do
    alter table(:scratch_card_level_rewards) do
      modify :status, :boolean, default: true
    end

    rename table(:scratch_card_level_rewards), :status, to: :is_active

    drop constraint(:scratch_card_task_rounds, "scratch_card_task_rounds_activity_id_fkey")

    alter table(:scratch_card_task_rounds) do
      modify :activity_id,
             references(:scratch_card_activities,
               column: :id,
               name: "scratch_card_task_rounds_activity_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    drop constraint(:scratch_card_task_levels, "scratch_card_task_levels_activity_id_fkey")

    alter table(:scratch_card_task_levels) do
      modify :activity_id,
             references(:scratch_card_activities,
               column: :id,
               name: "scratch_card_task_levels_activity_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end
  end
end
