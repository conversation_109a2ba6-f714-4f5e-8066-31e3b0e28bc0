defmodule Cypridina.Repo.Migrations.UpdateVipGiftRewardTypes do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:vip_gifts) do
      modify :monthly_reward, :bigint, default: 0
      modify :weekly_reward, :bigint, default: 0
      modify :daily_reward, :bigint, default: 0
      add :reward_amount, :bigint, null: false, default: 0
    end
  end

  def down do
    alter table(:vip_gifts) do
      remove :reward_amount
      modify :daily_reward, :decimal, default: "0"
      modify :weekly_reward, :decimal, default: "0"
      modify :monthly_reward, :decimal, default: "0"
    end
  end
end
