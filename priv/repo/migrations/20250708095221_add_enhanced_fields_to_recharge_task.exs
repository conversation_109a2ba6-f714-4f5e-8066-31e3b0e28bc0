defmodule Cypridina.Repo.Migrations.AddEnhancedFieldsToRechargeTask do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:recharge_tasks) do
      add :title, :text
      add :description, :text
      add :priority, :bigint, null: false, default: 0
      add :max_claims, :bigint
      add :start_date, :date
      add :end_date, :date
    end

    drop_if_exists unique_index(:recharge_tasks, [:recharge_amount],
                     name: "recharge_tasks_unique_recharge_amount_index"
                   )

    create unique_index(:recharge_tasks, [:recharge_amount, :reward_type],
             name: "recharge_tasks_unique_recharge_amount_and_type_index"
           )
  end

  def down do
    drop_if_exists unique_index(:recharge_tasks, [:recharge_amount, :reward_type],
                     name: "recharge_tasks_unique_recharge_amount_and_type_index"
                   )

    create unique_index(:recharge_tasks, [:recharge_amount],
             name: "recharge_tasks_unique_recharge_amount_index"
           )

    alter table(:recharge_tasks) do
      remove :end_date
      remove :start_date
      remove :max_claims
      remove :priority
      remove :description
      remove :title
    end
  end
end
