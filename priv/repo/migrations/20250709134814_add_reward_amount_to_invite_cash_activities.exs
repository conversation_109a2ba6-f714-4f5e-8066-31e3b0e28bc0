defmodule Cypridina.Repo.Migrations.AddRewardAmountToInviteCashActivities do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:invite_cash_activities) do
      add :reward_amount, :decimal
    end
  end

  def down do
    alter table(:invite_cash_activities) do
      remove :reward_amount
    end
  end
end
