defmodule Cypridina.Repo.Migrations.AddEnhancedFieldsToRechargeWheel do
  @moduledoc """
  添加增强字段到 recharge_wheels 表
  
  添加以下字段：
  - title: 转盘活动标题
  - description: 转盘活动描述
  - jackpot_pool: 奖金池金额
  - start_date: 活动开始日期
  - end_date: 活动结束日期
  """

  use Ecto.Migration

  def up do
    alter table(:recharge_wheels) do
      add :title, :text, null: false, default: "充值转盘活动"
      add :description, :text
      add :jackpot_pool, :decimal, null: false, default: 0
      add :start_date, :date
      add :end_date, :date
    end

    # 为现有记录设置有意义的标题
    execute """
    UPDATE recharge_wheels 
    SET title = '充值转盘活动 - ' || cumulative_recharge || '分'
    WHERE title = '充值转盘活动'
    """

    # 创建唯一索引确保标题唯一性（暂时注释掉，等迁移完成后再启用）
    # create unique_index(:recharge_wheels, [:title], name: "recharge_wheels_unique_title_index")
  end

  def down do
    # drop_if_exists unique_index(:recharge_wheels, [:title], name: "recharge_wheels_unique_title_index")
    
    alter table(:recharge_wheels) do
      remove :end_date
      remove :start_date
      remove :jackpot_pool
      remove :description
      remove :title
    end
  end
end
