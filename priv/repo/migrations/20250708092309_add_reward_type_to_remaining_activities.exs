defmodule Cypridina.Repo.Migrations.AddRewardTypeToRemainingActivities do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:vip_gifts) do
      add :reward_type, :text, null: false, default: "coins"
    end

    alter table(:recharge_tasks) do
      add :reward_type, :text, null: false, default: "coins"
    end

    alter table(:invite_cash_activities) do
      add :reward_type, :text, null: false, default: "coins"
    end
  end

  def down do
    alter table(:invite_cash_activities) do
      remove :reward_type
    end

    alter table(:recharge_tasks) do
      remove :reward_type
    end

    alter table(:vip_gifts) do
      remove :reward_type
    end
  end
end
