defmodule Cypridina.Repo.Migrations.CreateActivitiesTable do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:recharge_wheels) do
      add :activity_id, :uuid
    end

    alter table(:scratch_card_level_rewards) do
      remove :task_level_id
      remove :actual_max_reward
      remove :max_reward
      remove :min_reward
      modify :probability, :decimal, default: "0"
      modify :reward_type, :text, default: "coins"
      add :reward_config_id, :uuid, null: false
      add :level, :bigint, null: false
      add :reward_amount, :decimal, null: false, default: "0"
      add :is_active, :boolean, null: false, default: true
    end

    drop_if_exists unique_index(:scratch_card_level_rewards, [:task_level_id, :sort_order],
                     name: "scratch_card_level_rewards_unique_level_reward_sort_index"
                   )

    create unique_index(:scratch_card_level_rewards, [:reward_config_id, :level, :sort_order],
             name: "scratch_card_level_rewards_unique_level_reward_sort_index"
           )

    create table(:activities, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
    end

    alter table(:recharge_wheels) do
      modify :activity_id,
             references(:activities,
               column: :id,
               name: "recharge_wheels_activity_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end

    alter table(:activities) do
      add :name, :text, null: false
      add :description, :text
      add :type, :text, null: false
      add :status, :text, null: false, default: "draft"
      add :start_date, :date
      add :end_date, :date
      add :config_data, :map, null: false, default: %{}

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:activities, [:name], name: "activities_unique_name_index")
  end

  def down do
    drop_if_exists unique_index(:activities, [:name], name: "activities_unique_name_index")

    alter table(:activities) do
      remove :updated_at
      remove :inserted_at
      remove :config_data
      remove :end_date
      remove :start_date
      remove :status
      remove :type
      remove :description
      remove :name
    end

    drop constraint(:recharge_wheels, "recharge_wheels_activity_id_fkey")

    alter table(:recharge_wheels) do
      modify :activity_id, :uuid
    end

    drop table(:activities)

    drop_if_exists unique_index(
                     :scratch_card_level_rewards,
                     [:reward_config_id, :level, :sort_order],
                     name: "scratch_card_level_rewards_unique_level_reward_sort_index"
                   )

    create unique_index(:scratch_card_level_rewards, [:task_level_id, :sort_order],
             name: "scratch_card_level_rewards_unique_level_reward_sort_index"
           )

    drop constraint(
           :scratch_card_level_rewards,
           "scratch_card_level_rewards_reward_config_id_fkey"
         )

    alter table(:scratch_card_level_rewards) do
      remove :is_active
      remove :reward_amount
      remove :level
      remove :reward_config_id
      modify :reward_type, :text, default: nil
      modify :probability, :decimal, default: nil
      add :min_reward, :decimal, null: false
      add :max_reward, :decimal, null: false
      add :actual_max_reward, :decimal, null: false

      add :task_level_id,
          references(:scratch_card_task_levels,
            column: :id,
            name: "scratch_card_level_rewards_task_level_id_fkey",
            type: :uuid,
            prefix: "public"
          )
    end

    alter table(:recharge_wheels) do
      remove :activity_id
    end
  end
end
