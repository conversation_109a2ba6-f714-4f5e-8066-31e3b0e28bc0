defmodule Cypridina.Repo.Migrations.AddVipGiftFrequencyFields do
  @moduledoc """
  Adds reward_frequency and max_claims fields to vip_gifts table
  and removes redundant daily_reward, weekly_reward, monthly_reward fields.
  
  This migration implements the unified reward system design.
  """

  use Ecto.Migration

  def up do
    alter table(:vip_gifts) do
      # Add new frequency control fields
      add :reward_frequency, :integer, null: true, comment: "Reward frequency in days (1=daily, 7=weekly, 30=monthly, null=one-time)"
      add :max_claims, :integer, null: true, comment: "Maximum number of claims allowed (null=unlimited)"
      
      # Remove redundant reward fields
      remove :daily_reward
      remove :weekly_reward  
      remove :monthly_reward
    end

    # Create index for better query performance
    create index(:vip_gifts, [:reward_frequency])
    create index(:vip_gifts, [:max_claims])
  end

  def down do
    alter table(:vip_gifts) do
      # Remove new fields
      remove :reward_frequency
      remove :max_claims
      
      # Restore old fields
      add :daily_reward, :bigint, default: 0
      add :weekly_reward, :bigint, default: 0
      add :monthly_reward, :bigint, default: 0
    end

    # Remove indexes
    drop_if_exists index(:vip_gifts, [:reward_frequency])
    drop_if_exists index(:vip_gifts, [:max_claims])
  end
end
