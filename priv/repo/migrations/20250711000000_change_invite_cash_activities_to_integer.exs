defmodule Cypridina.Repo.Migrations.ChangeInviteCashActivitiesToInteger do
  @moduledoc """
  将 invite_cash_activities 表中的 decimal 字段改为 integer 类型
  统一使用 integer 类型存储金额（以分为单位）
  """

  use Ecto.Migration

  def up do
    alter table(:invite_cash_activities) do
      # 将 decimal 字段改为 bigint 类型
      # 使用 USING 子句来转换现有数据
      modify :total_reward, :bigint, using: "ROUND(total_reward)::bigint"
      modify :initial_min, :bigint, using: "ROUND(initial_min)::bigint"  
      modify :initial_max, :bigint, using: "ROUND(initial_max)::bigint"
    end
  end

  def down do
    alter table(:invite_cash_activities) do
      # 回滚时将 bigint 改回 decimal
      modify :total_reward, :decimal, using: "total_reward::decimal"
      modify :initial_min, :decimal, using: "initial_min::decimal"
      modify :initial_max, :decimal, using: "initial_max::decimal"
    end
  end
end
