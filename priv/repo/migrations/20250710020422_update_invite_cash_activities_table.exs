defmodule Cypridina.Repo.Migrations.UpdateInviteCashActivitiesTable do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:invite_reward_configs) do
      modify :reward_type, :text, default: "coins"
      modify :activity_id, :uuid, null: false
      add :task_type, :text, null: false, default: "invite_register"
      add :reward_amount, :decimal
      add :required_progress, :bigint, null: false, default: 1
      add :probability, :decimal, null: false, default: "1.0"
      add :description, :text
      add :sort_order, :bigint, null: false, default: 0
    end

    alter table(:invite_cash_activities) do
      remove :reward_type
      remove :reward_amount
      remove :task_type
    end
  end

  def down do
    alter table(:invite_cash_activities) do
      add :task_type, :text, null: false, default: "invite_register"
      add :reward_amount, :decimal
      add :reward_type, :text, null: false, default: "coins"
    end

    alter table(:invite_reward_configs) do
      remove :sort_order
      remove :description
      remove :probability
      remove :required_progress
      remove :reward_amount
      remove :task_type
      modify :activity_id, :uuid, null: true
      modify :reward_type, :text, default: nil
    end
  end
end
