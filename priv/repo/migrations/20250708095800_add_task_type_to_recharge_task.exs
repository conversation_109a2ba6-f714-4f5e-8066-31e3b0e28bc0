defmodule Cypridina.Repo.Migrations.AddTaskTypeToRechargeTask do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:recharge_tasks) do
      add :task_type, :text, null: false, default: "single_recharge"
    end
  end

  def down do
    alter table(:recharge_tasks) do
      remove :task_type
    end
  end
end
