defmodule Cypridina.Repo.Migrations.UpdateSevenDayTaskRewardAmountToInteger do
  @moduledoc """
  Updates seven_day_tasks.reward_amount from numeric to bigint to match standardization.
  """

  use Ecto.Migration

  def up do
    alter table(:seven_day_tasks) do
      modify :reward_amount, :bigint, default: 0, null: false
    end
  end

  def down do
    alter table(:seven_day_tasks) do
      modify :reward_amount, :numeric, default: 0, null: false
    end
  end
end
