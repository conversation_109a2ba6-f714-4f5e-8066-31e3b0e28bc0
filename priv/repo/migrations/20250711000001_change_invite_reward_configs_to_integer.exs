defmodule Cypridina.Repo.Migrations.ChangeInviteRewardConfigsToInteger do
  @moduledoc """
  将 invite_reward_configs 表中的 decimal 字段改为 integer 类型
  统一使用 integer 类型存储金额（以分为单位）
  
  修改字段：
  - reward_amount: decimal -> bigint
  - min_reward: decimal -> bigint  
  - max_reward: decimal -> bigint
  
  保持字段：
  - probability: 保持 decimal 类型（概率值 0-1）
  """

  use Ecto.Migration

  def up do
    alter table(:invite_reward_configs) do
      # 将金额相关的 decimal 字段改为 bigint 类型
      # 使用 USING 子句来转换现有数据
      modify :reward_amount, :bigint, using: "CASE WHEN reward_amount IS NULL THEN NULL ELSE ROUND(reward_amount)::bigint END"
      modify :min_reward, :bigint, using: "ROUND(min_reward)::bigint"
      modify :max_reward, :bigint, using: "ROUND(max_reward)::bigint"
    end
  end

  def down do
    alter table(:invite_reward_configs) do
      # 回滚时将 bigint 改回 decimal
      modify :reward_amount, :decimal, using: "CASE WHEN reward_amount IS NULL THEN NULL ELSE reward_amount::decimal END"
      modify :min_reward, :decimal, using: "min_reward::decimal"
      modify :max_reward, :decimal, using: "max_reward::decimal"
    end
  end
end
