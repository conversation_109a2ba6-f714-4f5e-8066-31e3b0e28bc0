defmodule Cypridina.Repo.Migrations.AddUnifiedFieldsToAllActivities do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:vip_gifts) do
      add :title, :text
      add :description, :text
      add :task_type, :text, null: false, default: "vip_daily"
      add :start_date, :date
      add :end_date, :date
    end

    alter table(:first_recharge_gifts) do
      add :description, :text
      add :task_type, :text, null: false, default: "first_recharge"
      add :start_date, :date
      add :end_date, :date
    end

    alter table(:weekly_cards) do
      add :description, :text
      add :task_type, :text, null: false, default: "weekly_card"
      add :start_date, :date
      add :end_date, :date
    end

    alter table(:binding_rewards) do
      add :task_type, :text, null: false, default: "binding_verification"
      add :start_date, :date
      add :end_date, :date
    end

    alter table(:seven_day_tasks) do
      add :title, :text
      add :task_type, :text, null: false, default: "daily_login"
      add :start_date, :date
      add :end_date, :date
    end

    alter table(:free_bonus_tasks) do
      add :description, :text
      add :task_type, :text, null: false, default: "share_task"
      add :reward_amount, :decimal, null: false, default: "0"
      add :reward_type, :text, null: false, default: "coins"
      add :start_date, :date
      add :end_date, :date
    end

    alter table(:invite_cash_activities) do
      add :description, :text
      add :task_type, :text, null: false, default: "invite_register"
      add :start_date, :date
      add :end_date, :date
    end
  end

  def down do
    alter table(:invite_cash_activities) do
      remove :end_date
      remove :start_date
      remove :task_type
      remove :description
    end

    alter table(:free_bonus_tasks) do
      remove :end_date
      remove :start_date
      remove :reward_type
      remove :reward_amount
      remove :task_type
      remove :description
    end

    alter table(:seven_day_tasks) do
      remove :end_date
      remove :start_date
      remove :task_type
      remove :title
    end

    alter table(:binding_rewards) do
      remove :end_date
      remove :start_date
      remove :task_type
    end

    alter table(:weekly_cards) do
      remove :end_date
      remove :start_date
      remove :task_type
      remove :description
    end

    alter table(:first_recharge_gifts) do
      remove :end_date
      remove :start_date
      remove :task_type
      remove :description
    end

    alter table(:vip_gifts) do
      remove :end_date
      remove :start_date
      remove :task_type
      remove :description
      remove :title
    end
  end
end
