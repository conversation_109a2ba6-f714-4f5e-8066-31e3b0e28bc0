defmodule Cypridina.Repo.Migrations.UpdateSevenDayTaskRewardAmountType do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:game_tasks) do
      modify :reward_type, :text, null: false
    end

    alter table(:seven_day_tasks) do
      modify :reward_amount, :bigint, default: 0
    end
  end

  def down do
    alter table(:seven_day_tasks) do
      modify :reward_amount, :decimal, default: "0"
    end

    alter table(:game_tasks) do
      modify :reward_type, :text, null: true
    end
  end
end
