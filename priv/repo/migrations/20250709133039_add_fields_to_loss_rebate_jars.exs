defmodule Cypridina.Repo.Migrations.AddFieldsToLossRebateJars do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:loss_rebate_jars) do
      remove :max_rebate
      remove :loss_threshold
      add :task_type, :text, null: false, default: "loss_rebate"
      add :description, :text
      add :min_loss_amount, :decimal, null: false
      add :max_rebate_amount, :decimal, null: false
      add :reward_amount, :decimal
      add :calculation_period, :text, default: "daily"
      add :reward_type, :text, null: false, default: "coins"
      add :auto_distribute, :boolean, null: false, default: false
      add :start_date, :date
      add :end_date, :date
    end
  end

  def down do
    alter table(:loss_rebate_jars) do
      remove :end_date
      remove :start_date
      remove :auto_distribute
      remove :reward_type
      remove :calculation_period
      remove :reward_amount
      remove :max_rebate_amount
      remove :min_loss_amount
      remove :description
      remove :task_type
      add :loss_threshold, :decimal, null: false
      add :max_rebate, :decimal, null: false
    end
  end
end
