defmodule Cypridina.Repo.Migrations.RemoveIsActiveFromSevenDayTasks do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:seven_day_tasks) do
      remove :is_active
    end
  end

  def down do
    alter table(:seven_day_tasks) do
      add :is_active, :boolean, null: false, default: true
    end
  end
end
