defmodule Cypridina.Repo.Migrations.AddGameTaskFields do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:game_tasks) do
      add :reward_type, :text, default: "coins"
      add :start_date, :date
      add :end_date, :date
    end
  end

  def down do
    alter table(:game_tasks) do
      remove :end_date
      remove :start_date
      remove :reward_type
    end
  end
end
