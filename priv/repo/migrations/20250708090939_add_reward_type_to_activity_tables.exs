defmodule Cypridina.Repo.Migrations.AddRewardTypeToActivityTables do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:game_tasks) do
      modify :game_name, :text, default: "all"
    end

    alter table(:first_recharge_gifts) do
      add :reward_type, :text, null: false, default: "coins"
    end

    alter table(:weekly_cards) do
      add :reward_type, :text, null: false, default: "coins"
    end

    alter table(:binding_rewards) do
      add :reward_type, :text, null: false, default: "coins"
      add :one_time_only, :boolean, null: false, default: true
      add :verification_required, :boolean, null: false, default: true
      add :description, :text
    end

    alter table(:seven_day_tasks) do
      add :reward_type, :text, null: false, default: "coins"
      add :is_special, :boolean, null: false, default: false
      add :description, :text
      add :is_active, :boolean, null: false, default: true
    end
  end

  def down do
    alter table(:seven_day_tasks) do
      remove :is_active
      remove :description
      remove :is_special
      remove :reward_type
    end

    alter table(:binding_rewards) do
      remove :description
      remove :verification_required
      remove :one_time_only
      remove :reward_type
    end

    alter table(:weekly_cards) do
      remove :reward_type
    end

    alter table(:first_recharge_gifts) do
      remove :reward_type
    end

    alter table(:game_tasks) do
      modify :game_name, :text, default: nil
    end
  end
end
