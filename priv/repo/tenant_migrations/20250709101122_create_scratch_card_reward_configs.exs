defmodule Cypridina.Repo.TenantMigrations.CreateScratchCardRewardConfigs do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:scratch_card_activities, primary_key: false, prefix: prefix()) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :activity_title, :text, null: false
      add :claimable_count, :bigint, null: false, default: 30
      add :reward_probability, :decimal, null: false, default: "10"
      add :status, :text, null: false, default: "enabled"
      add :description, :text
      add :start_date, :date
      add :end_date, :date
      add :max_participants, :bigint
      add :daily_limit, :bigint
      add :sort_order, :bigint, null: false, default: 0
      add :config_data, :map, default: %{}

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:scratch_card_activities, [:activity_title],
             name: "scratch_card_activities_unique_activity_title_index"
           )
  end

  def down do
    drop_if_exists unique_index(:scratch_card_activities, [:activity_title],
                     name: "scratch_card_activities_unique_activity_title_index"
                   )

    drop table(:scratch_card_activities, prefix: prefix())
  end
end
