defmodule Cypridina.Repo.TenantMigrations.CreateActivitiesTable do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:scratch_card_activities, prefix: prefix()) do
      remove :reward_probability

      add :activity_id,
          references(:activities,
            column: :id,
            name: "scratch_card_activities_activity_id_fkey",
            type: :uuid,
            prefix: "public",
            on_delete: :delete_all
          ),
          null: false

      add :reward1_probability, :decimal, null: false, default: "30"
      add :reward2_probability, :decimal, null: false, default: "30"
      add :reward3_probability, :decimal, null: false, default: "40"
    end
  end

  def down do
    drop constraint(:scratch_card_activities, "scratch_card_activities_activity_id_fkey")

    alter table(:scratch_card_activities, prefix: prefix()) do
      remove :reward3_probability
      remove :reward2_probability
      remove :reward1_probability
      remove :activity_id
      add :reward_probability, :decimal, null: false, default: "10"
    end
  end
end
