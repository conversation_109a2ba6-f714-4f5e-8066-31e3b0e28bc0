# VIP等级重置脚本
# 使用方法: mix run priv/repo/reset_vip_levels.exs

alias Teen.GameManagement.VipLevel
alias Cypridina.Repo

IO.puts("🔄 开始重置VIP等级数据...")

# 删除所有现有的VIP等级
case Ash.bulk_destroy(VipLevel, :destroy, %{}, return_errors?: true) do
  %{records: deleted_records} ->
    IO.puts("✅ 已删除 #{length(deleted_records)} 个现有VIP等级")
  {:error, error} ->
    IO.puts("⚠️  删除现有VIP等级时出现错误: #{inspect(error)}")
end

IO.puts("📝 开始创建新的VIP等级...")

# 调用初始化函数
Teen.GameManagement.seed_default_vip_levels()

# 验证创建结果
case VipLevel.list_active_levels() do
  {:ok, vip_levels} ->
    IO.puts("🎉 VIP等级重置完成！")
    IO.puts("📊 当前VIP等级列表:")
    
    vip_levels
    |> Enum.sort_by(& &1.level)
    |> Enum.each(fn vip_level ->
      IO.puts("  VIP#{vip_level.level} - #{vip_level.level_name} (充值要求: #{vip_level.recharge_requirement}分)")
    end)
    
    IO.puts("\n✨ 总计: #{length(vip_levels)} 个VIP等级")
    
  {:error, error} ->
    IO.puts("❌ 验证VIP等级时出现错误: #{inspect(error)}")
end

IO.puts("\n🔧 重置完成！现在您可以在管理界面看到完整的VIP等级选项。")
