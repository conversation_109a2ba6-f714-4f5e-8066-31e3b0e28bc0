#!/usr/bin/env elixir

# 测试删除功能的脚本
Mix.install([
  {:ash, "~> 3.0"}
])

# 模拟测试删除功能
defmodule TestDeleteFunction do
  def test_destroy_by_activity do
    IO.puts("测试 destroy_by_activity 函数...")
    
    # 测试用例1: 空的 activity_id
    test_case_1 = ""
    IO.puts("测试用例1: 空的 activity_id")
    
    # 测试用例2: 正常的 activity_id
    test_case_2 = "test_activity_123"
    IO.puts("测试用例2: 正常的 activity_id: #{test_case_2}")
    
    # 测试用例3: 不存在的 activity_id
    test_case_3 = "non_existent_activity"
    IO.puts("测试用例3: 不存在的 activity_id: #{test_case_3}")
    
    IO.puts("所有测试用例已准备完成")
  end
  
  def test_bulk_destroy_logic do
    IO.puts("测试批量删除逻辑...")
    
    # 模拟配置列表
    configs = [
      %{id: 1, activity_id: "test_activity_123"},
      %{id: 2, activity_id: "test_activity_123"},
      %{id: 3, activity_id: "test_activity_123"}
    ]
    
    IO.puts("模拟找到 #{length(configs)} 个配置需要删除")
    
    # 模拟批量删除结果
    result = %{status: :success, count: length(configs)}
    IO.puts("模拟批量删除结果: #{inspect(result)}")
    
    case result do
      %{status: :success} = result ->
        IO.puts("✅ 成功删除 #{result.count || length(configs)} 个奖励配置")
        :ok
        
      %{status: :error, errors: errors} ->
        IO.puts("❌ 批量删除失败: #{inspect(errors)}")
        {:error, "批量删除失败: #{inspect(errors)}"}
        
      {:error, reason} ->
        IO.puts("❌ 删除操作失败: #{inspect(reason)}")
        {:error, "删除操作失败: #{inspect(reason)}"}
    end
  end
end

# 运行测试
IO.puts("=== 开始测试删除功能 ===")
TestDeleteFunction.test_destroy_by_activity()
IO.puts("")
TestDeleteFunction.test_bulk_destroy_logic()
IO.puts("=== 测试完成 ===")
