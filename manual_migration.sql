-- 手动执行的 SQL 迁移脚本
-- 为 recharge_wheels 表添加增强字段

-- 添加新字段
ALTER TABLE recharge_wheels 
ADD COLUMN title TEXT NOT NULL DEFAULT '充值转盘活动',
ADD COLUMN description TEXT,
ADD COLUMN jackpot_pool DECIMAL NOT NULL DEFAULT 0,
ADD COLUMN start_date DATE,
ADD COLUMN end_date DATE;

-- 为现有记录设置有意义的标题
UPDATE recharge_wheels 
SET title = '充值转盘活动 - ' || cumulative_recharge || '分'
WHERE title = '充值转盘活动';

-- 创建唯一索引确保标题唯一性
CREATE UNIQUE INDEX recharge_wheels_unique_title_index 
ON recharge_wheels (title);

-- 验证表结构
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'recharge_wheels' 
ORDER BY ordinal_position;
