defmodule CypridinaWeb.AuthControllerTest do
  use CypridinaWeb.ConnCase

  describe "用户认证" do
    test "register/2 注册新用户", %{conn: conn} do
      conn =
        post(conn, ~p"/api/register", %{
          "user" => %{
            "nickname" => "测试用户",
            "email" => "<EMAIL>",
            "password" => "password123"
          }
        })

      assert json_response(conn, 201)["id"]
    end

    test "login/2 成功登录", %{conn: conn} do
      # 先创建一个用户
      {:ok, user} =
        Cypridina.Accounts.Auth.register_user(%{
          nickname: "测试用户",
          email: "<EMAIL>",
          password: "password123"
        })

      # 尝试登录
      conn =
        post(conn, ~p"/api/login", %{
          "email" => "<EMAIL>",
          "password" => "password123"
        })

      assert %{"token" => _token, "user" => _user} = json_response(conn, 200)
    end

    test "request_phone_code/2 请求验证码", %{conn: conn} do
      conn =
        post(conn, ~p"/api/login/phone/code", %{
          "phone" => "***********"
        })

      assert json_response(conn, 200)["message"] == "Verification code sent"
    end
  end
end
