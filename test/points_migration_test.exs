defmodule PointsMigrationTest do
  use ExUnit.Case, async: true

  test "Accounts module functions exist and work" do
    # Test that the main functions exist
    assert function_exported?(Cypridina.Accounts, :add_points, 2)
    assert function_exported?(Cypridina.Accounts, :subtract_points, 2)
    assert function_exported?(Cypridina.Accounts, :transfer_points, 4)
    assert function_exported?(Cypridina.Accounts, :get_user_points, 1)
  end

  test "Ledger module functions exist" do
    # Test that Ledger functions exist
    assert function_exported?(Cypridina.Ledger, :add_user_points, 3)
    assert function_exported?(Cypridina.Ledger, :subtract_user_points, 3)
    assert function_exported?(Cypridina.Ledger, :transfer_user_points, 4)
    assert function_exported?(Cypridina.Ledger, :get_user_balance, 2)
  end

  test "Old modules are removed" do
    # Test that old modules don't exist
    refute Code.ensure_loaded?(Cypridina.PointsLedgerBridge)
    refute Code.ensure_loaded?(RacingGame.PointsTransaction)
    refute Code.ensure_loaded?(Cypridina.Accounts.UserAsset)
  end

  test "User model doesn't have asset relationship" do
    # Test that User model doesn't have asset relationship
    user_module = Cypridina.Accounts.User
    assert Code.ensure_loaded?(user_module)

    # Check that asset relationship is not defined by checking if the function exists
    refute function_exported?(user_module, :asset, 0)
  end
end
