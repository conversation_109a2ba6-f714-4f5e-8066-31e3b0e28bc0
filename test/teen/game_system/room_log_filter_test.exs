defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.Cypridina.RoomSystem.RoomLogFilterTest do
  use ExUnit.Case, async: false

  alias <PERSON><PERSON><PERSON><PERSON>.Teen.Cypridina.RoomSystem.RoomLogFilter

  require Lo<PERSON>

  describe "房间日志过滤器" do
    test "创建和删除房间日志过滤器" do
      room_id = "test_room_#{System.unique_integer([:positive])}"

      # 创建房间日志过滤器
      assert {:ok, _handler_id} = RoomLogFilter.create_room_logger(room_id)

      # 验证日志文件目录被创建
      log_file = "logs/rooms/room_#{room_id}.log"
      assert File.exists?(Path.dirname(log_file))

      # 删除房间日志过滤器
      assert :ok = RoomLogFilter.remove_room_logger(room_id)
    end

    test "房间日志过滤函数" do
      room_id = "test_room_123"

      # 匹配的room_id应该记录
      matching_event = %{meta: %{room_id: room_id}}
      assert :log = RoomLogFilter.room_log_filter(matching_event, room_id)

      # 不匹配的room_id应该忽略
      non_matching_event = %{meta: %{room_id: "other_room"}}
      assert :ignore = RoomLogFilter.room_log_filter(non_matching_event, room_id)

      # 没有room_id的事件应该忽略
      no_room_id_event = %{meta: %{}}
      assert :ignore = RoomLogFilter.room_log_filter(no_room_id_event, room_id)
    end

    test "全局日志过滤函数" do
      # 有room_id的日志应该被忽略（由房间handler处理）
      room_event = %{meta: %{room_id: "some_room"}}
      assert :ignore = RoomLogFilter.global_log_filter(room_event, nil)

      # 没有room_id的日志应该正常记录
      normal_event = %{meta: %{}}
      assert :log = RoomLogFilter.global_log_filter(normal_event, nil)

      # 其他metadata的日志应该正常记录
      other_event = %{meta: %{user_id: 123}}
      assert :log = RoomLogFilter.global_log_filter(other_event, nil)
    end
  end
end
