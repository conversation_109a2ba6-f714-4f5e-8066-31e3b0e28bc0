defmodule <PERSON>pridina.Teen.GameSystem.Games.LongHu.LongHuHistoryTest do
  use ExUnit.Case, async: true

  alias <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.LongHu.LongHuMessageBuilder

  describe "历史记录协议" do
    test "build_history_trend/1 应该构建正确的协议消息" do
      formatted_data = %{
        "longwinnum" => 2,
        "huwinnum" => 1,
        "hewinnum" => 1,
        "1" => %{"win" => 1, "round" => 4},
        "2" => %{"win" => 2, "round" => 3},
        "3" => %{"win" => 3, "round" => 2},
        "4" => %{"win" => 1, "round" => 1}
      }

      message = LongHuMessageBuilder.build_history_trend(formatted_data)

      # 验证协议格式
      assert message["mainId"] == 5
      assert message["subId"] == 3916
      assert message["data"] == formatted_data
    end

    test "build_history/4 应该构建正确的历史记录协议消息" do
      formatted_history = [
        %{
          "round" => 1,
          "result" => 1,
          "cards" => %{
            "long" => %{"suit" => 1, "rank" => 10},
            "hu" => %{"suit" => 2, "rank" => 5}
          },
          "total_bets" => %{"long" => 1000, "hu" => 500, "he" => 200},
          "timestamp" => 1_640_995_200_000
        }
      ]

      message = LongHuMessageBuilder.build_history(0, 1, 1, formatted_history)

      # 验证协议格式
      assert message["mainId"] == 5
      assert message["subId"] == 3916
      assert message["data"]["data"]["page"] == 0
      assert message["data"]["data"]["count"] == 1
      assert message["data"]["data"]["total"] == 1
      assert message["data"]["data"]["history"] == formatted_history
    end
  end

  describe "客户端兼容性" do
    test "历史数据格式应该与客户端期望完全匹配" do
      # 测试客户端期望的走势图数据格式
      # 确保统计数据与历史记录数量匹配：2个龙 + 2个虎 + 1个和 = 5条记录
      trend_data = %{
        "longwinnum" => 2,
        "huwinnum" => 2,
        "hewinnum" => 1,
        "1" => %{"win" => 1, "round" => 10, "timestamp" => 1_640_995_200_000},
        "2" => %{"win" => 2, "round" => 9, "timestamp" => 1_640_995_100_000},
        "3" => %{"win" => 3, "round" => 8, "timestamp" => 1_640_995_000_000},
        "4" => %{"win" => 1, "round" => 7, "timestamp" => 1_640_994_900_000},
        "5" => %{"win" => 2, "round" => 6, "timestamp" => 1_640_994_800_000},
        "data" => [
          %{"win" => 1, "round" => 10, "timestamp" => 1_640_995_200_000},
          %{"win" => 2, "round" => 9, "timestamp" => 1_640_995_100_000},
          %{"win" => 3, "round" => 8, "timestamp" => 1_640_995_000_000},
          %{"win" => 1, "round" => 7, "timestamp" => 1_640_994_900_000},
          %{"win" => 2, "round" => 6, "timestamp" => 1_640_994_800_000}
        ]
      }

      # 客户端期望的关键字段
      required_fields = ["longwinnum", "huwinnum", "hewinnum"]

      Enum.each(required_fields, fn field ->
        assert Map.has_key?(trend_data, field), "缺少必需字段: #{field}"
        assert is_integer(trend_data[field]), "#{field} 应该是整数"
      end)

      # 验证历史记录项的格式
      Enum.each(1..5, fn i ->
        key = to_string(i)
        assert Map.has_key?(trend_data, key), "缺少历史记录项: #{key}"

        history_item = trend_data[key]
        assert Map.has_key?(history_item, "win"), "历史记录项缺少 win 字段"
        assert Map.has_key?(history_item, "round"), "历史记录项缺少 round 字段"
        assert is_integer(history_item["win"]), "win 字段应该是整数"
        assert history_item["win"] in [1, 2, 3], "win 字段应该是 1、2 或 3"
        assert is_integer(history_item["round"]), "round 字段应该是整数"
      end)

      # 验证统计数据的一致性
      total_games = trend_data["longwinnum"] + trend_data["huwinnum"] + trend_data["hewinnum"]

      history_count =
        Enum.count(trend_data, fn {key, _value} ->
          String.match?(key, ~r/^\d+$/)
        end)

      # 统计数据应该等于历史记录数量（因为每个历史记录都有一个结果）
      assert total_games == history_count,
             "统计数据应该与历史记录数量一致，期望: #{history_count}, 实际: #{total_games}"

      # 验证data数组格式
      assert Map.has_key?(trend_data, "data"), "应该包含data数组字段"
      assert is_list(trend_data["data"]), "data字段应该是数组"
      assert length(trend_data["data"]) == history_count, "data数组长度应该与历史记录数量一致"

      # 验证data数组中的每个元素格式
      Enum.each(trend_data["data"], fn item ->
        assert Map.has_key?(item, "win"), "data数组项应该包含win字段"
        assert Map.has_key?(item, "round"), "data数组项应该包含round字段"
        assert Map.has_key?(item, "timestamp"), "data数组项应该包含timestamp字段"
        assert item["win"] in [1, 2, 3], "win字段应该是1、2或3"
      end)
    end

    test "协议消息格式应该符合客户端期望" do
      # 测试走势图协议消息
      trend_data = %{
        "longwinnum" => 1,
        "huwinnum" => 1,
        "hewinnum" => 1,
        "1" => %{"win" => 1, "round" => 3},
        "2" => %{"win" => 2, "round" => 2},
        "3" => %{"win" => 3, "round" => 1}
      }

      message = LongHuMessageBuilder.build_history_trend(trend_data)

      # 验证协议结构
      assert is_map(message)
      assert Map.has_key?(message, "mainId")
      assert Map.has_key?(message, "subId")
      assert Map.has_key?(message, "data")

      # 验证协议ID
      # MainProto.XC
      assert message["mainId"] == 5
      # SC_LHD_HISTORY_P
      assert message["subId"] == 3916

      # 验证数据格式
      assert message["data"] == trend_data
    end
  end
end
