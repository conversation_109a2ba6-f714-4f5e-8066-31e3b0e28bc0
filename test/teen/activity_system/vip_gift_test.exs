defmodule Teen.ActivitySystem.VipGiftTest do
  use Cypridina.DataCase

  alias Teen.ActivitySystem.VipGift

  describe "vip gifts" do
    test "creates vip gift with valid attributes" do
      attrs = %{
        title: "VIP1每日登录礼包",
        description: "VIP1用户每日登录可获得金币奖励",
        task_type: :vip_daily_login,
        vip_level: 1,
        reward_amount: 1000,
        reward_type: :coins,
        status: :enabled
      }

      assert {:ok, gift} = VipGift.create(attrs)
      assert gift.title == "VIP1每日登录礼包"
      assert gift.vip_level == 1
      assert gift.reward_amount == 1000
      assert gift.task_type == :vip_daily_login
      assert gift.reward_type == :coins
      assert gift.status == :enabled
    end

    test "sets default values correctly" do
      attrs = %{
        title: "VIP升级礼包",
        description: "VIP升级奖励",
        task_type: :vip_level_upgrade,
        vip_level: 2,
        reward_amount: 5000,
        reward_type: :coins,
        status: :enabled
      }

      assert {:ok, gift} = VipGift.create(attrs)
      assert gift.max_claims == 1  # 默认值，因为是升级类型
      assert gift.reward_frequency == nil
    end

    test "validates required fields" do
      attrs = %{}
      assert {:error, changeset} = VipGift.create(attrs)
      assert changeset.errors[:title]
      assert changeset.errors[:vip_level]
      assert changeset.errors[:reward_amount]
    end

    test "validates vip_level range" do
      attrs = %{
        title: "测试礼包",
        description: "测试",
        task_type: :vip_daily_login,
        vip_level: 15,  # 超出范围
        reward_amount: 1000,
        reward_type: :coins,
        status: :enabled
      }

      assert {:error, changeset} = VipGift.create(attrs)
      assert changeset.errors[:vip_level]
    end

    test "validates positive reward_amount" do
      attrs = %{
        title: "测试礼包",
        description: "测试",
        task_type: :vip_daily_login,
        vip_level: 1,
        reward_amount: -100,  # 负数
        reward_type: :coins,
        status: :enabled
      }

      assert {:error, changeset} = VipGift.create(attrs)
      assert changeset.errors[:reward_amount]
    end

    test "updates vip gift" do
      attrs = %{
        title: "原始礼包",
        description: "原始描述",
        task_type: :vip_daily_login,
        vip_level: 1,
        reward_amount: 1000,
        reward_type: :coins,
        status: :enabled
      }

      assert {:ok, gift} = VipGift.create(attrs)

      update_attrs = %{
        title: "更新后的礼包",
        reward_amount: 1500
      }

      assert {:ok, updated_gift} = VipGift.update(gift, update_attrs)
      assert updated_gift.title == "更新后的礼包"
      assert updated_gift.reward_amount == 1500
    end

    test "deletes vip gift" do
      attrs = %{
        title: "待删除礼包",
        description: "测试删除",
        task_type: :vip_daily_login,
        vip_level: 1,
        reward_amount: 1000,
        reward_type: :coins,
        status: :enabled
      }

      assert {:ok, gift} = VipGift.create(attrs)
      assert :ok = VipGift.destroy(gift)

      # 验证已删除
      gifts = VipGift.read!()
      refute Enum.any?(gifts, fn g -> g.id == gift.id end)
    end

    test "queries by vip level" do
      # 创建不同 VIP 等级的礼包
      {:ok, _gift1} = VipGift.create(%{
        title: "VIP1礼包",
        description: "VIP1奖励",
        task_type: :vip_daily_login,
        vip_level: 1,
        reward_amount: 1000,
        reward_type: :coins,
        status: :enabled
      })

      {:ok, _gift2} = VipGift.create(%{
        title: "VIP2礼包",
        description: "VIP2奖励",
        task_type: :vip_daily_login,
        vip_level: 2,
        reward_amount: 2000,
        reward_type: :coins,
        status: :enabled
      })

      # 查询 VIP1 礼包
      import Ash.Query
      query = VipGift |> filter(vip_level == 1)
      vip1_gifts = VipGift.read!(query: query)

      assert length(vip1_gifts) == 1
      assert hd(vip1_gifts).vip_level == 1
    end

    test "queries by status" do
      {:ok, _enabled_gift} = VipGift.create(%{
        title: "启用礼包",
        description: "启用的礼包",
        task_type: :vip_daily_login,
        vip_level: 1,
        reward_amount: 1000,
        reward_type: :coins,
        status: :enabled
      })

      {:ok, _disabled_gift} = VipGift.create(%{
        title: "禁用礼包",
        description: "禁用的礼包",
        task_type: :vip_daily_login,
        vip_level: 1,
        reward_amount: 1000,
        reward_type: :coins,
        status: :disabled
      })

      # 查询启用的礼包
      import Ash.Query
      query = VipGift |> filter(status == :enabled)
      enabled_gifts = VipGift.read!(query: query)

      assert length(enabled_gifts) == 1
      assert hd(enabled_gifts).status == :enabled
    end

    test "queries by task type" do
      {:ok, _daily_gift} = VipGift.create(%{
        title: "每日登录礼包",
        description: "每日登录奖励",
        task_type: :vip_daily_login,
        vip_level: 1,
        reward_amount: 1000,
        reward_type: :coins,
        status: :enabled
      })

      {:ok, _upgrade_gift} = VipGift.create(%{
        title: "升级礼包",
        description: "升级奖励",
        task_type: :vip_level_upgrade,
        vip_level: 1,
        reward_amount: 5000,
        reward_type: :coins,
        status: :enabled
      })

      # 查询每日登录礼包
      import Ash.Query
      query = VipGift |> filter(task_type == :vip_daily_login)
      daily_gifts = VipGift.read!(query: query)

      assert length(daily_gifts) == 1
      assert hd(daily_gifts).task_type == :vip_daily_login
    end
  end
end
