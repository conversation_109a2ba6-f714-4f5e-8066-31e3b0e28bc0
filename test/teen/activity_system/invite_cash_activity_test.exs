defmodule Teen.ActivitySystem.InviteCashActivityTest do
  use ExUnit.Case, async: false

  alias Teen.ActivitySystem.InviteCashActivity
  alias Teen.ActivitySystem.InviteRewardConfig

  setup do
    # 确保数据库连接
    :ok = Ecto.Adapters.SQL.Sandbox.checkout(Cypridina.Repo)
    Ecto.Adapters.SQL.Sandbox.mode(Cypridina.Repo, {:shared, self()})
    :ok
  end

  describe "create_with_configs/1" do
    test "creates activity with multiple reward configurations" do
      # 准备测试数据
      activity_params = %{
        title: "测试邀请活动",
        description: "这是一个测试邀请活动",
        total_reward: Decimal.new("10000"),
        initial_min: Decimal.new("100"),
        initial_max: Decimal.new("500"),
        start_date: ~D[2025-01-01],
        end_date: ~D[2025-12-31]
      }

      reward_configs = [
        %{
          "round_number" => 1,
          "task_type" => "invite_register",
          "reward_type" => "coins",
          "min_reward" => Decimal.new("50"),
          "max_reward" => Decimal.new("100"),
          "reward_amount" => Decimal.new("75"),
          "required_progress" => 1,
          "probability" => Decimal.new("1.0"),
          "description" => "邀请注册奖励",
          "sort_order" => 1
        },
        %{
          "round_number" => 2,
          "task_type" => "invite_recharge",
          "reward_type" => "cash",
          "min_reward" => Decimal.new("100"),
          "max_reward" => Decimal.new("200"),
          "reward_amount" => Decimal.new("150"),
          "required_progress" => 1,
          "probability" => Decimal.new("0.8"),
          "description" => "邀请充值奖励",
          "sort_order" => 2
        }
      ]

      # 执行创建操作
      result = Ash.create(InviteCashActivity, Map.merge(activity_params, %{reward_configs: reward_configs}), action: :create_with_configs)

      # 验证结果
      assert {:ok, activity} = result
      assert activity.title == "测试邀请活动"
      assert activity.status == :enabled

      # 验证奖励配置是否正确创建
      query = Ash.Query.filter(InviteRewardConfig, activity_id == ^activity.id)
      {:ok, config_list} = Ash.read(InviteRewardConfig, query: query)
      assert length(config_list) == 2

      # 验证第一个配置
      config1 = Enum.find(config_list, &(&1.round_number == 1))
      assert config1.task_type == :invite_register
      assert config1.reward_type == :coins
      assert Decimal.equal?(config1.reward_amount, Decimal.new("75"))

      # 验证第二个配置
      config2 = Enum.find(config_list, &(&1.round_number == 2))
      assert config2.task_type == :invite_recharge
      assert config2.reward_type == :cash
      assert Decimal.equal?(config2.reward_amount, Decimal.new("150"))
    end

    test "creates activity without reward configurations" do
      activity_params = %{
        title: "简单邀请活动",
        description: "没有奖励配置的活动",
        total_reward: Decimal.new("5000"),
        initial_min: Decimal.new("50"),
        initial_max: Decimal.new("200")
      }

      result = Ash.create(InviteCashActivity, Map.merge(activity_params, %{reward_configs: []}), action: :create_with_configs)

      assert {:ok, activity} = result
      assert activity.title == "简单邀请活动"
      assert activity.status == :enabled

      # 验证没有奖励配置
      query = Ash.Query.filter(InviteRewardConfig, activity_id == ^activity.id)
      {:ok, config_list} = Ash.read(InviteRewardConfig, query: query)
      assert length(config_list) == 0
    end

    test "fails when reward config creation fails" do
      activity_params = %{
        title: "失败测试活动",
        total_reward: Decimal.new("1000"),
        initial_min: Decimal.new("10"),
        initial_max: Decimal.new("50")
      }

      # 使用无效的奖励配置（缺少必需字段）
      invalid_reward_configs = [
        %{
          "round_number" => 1,
          # 缺少 task_type, reward_type, min_reward, max_reward
        }
      ]

      result = Ash.create(InviteCashActivity, Map.merge(activity_params, %{reward_configs: invalid_reward_configs}), action: :create_with_configs)

      # 应该失败
      assert {:error, _error} = result
    end
  end

  describe "regular create/1" do
    test "creates activity without reward configurations" do
      activity_params = %{
        title: "普通邀请活动",
        description: "使用普通创建方法",
        total_reward: Decimal.new("3000"),
        initial_min: Decimal.new("30"),
        initial_max: Decimal.new("100")
      }

      result = Ash.create(InviteCashActivity, activity_params)

      assert {:ok, activity} = result
      assert activity.title == "普通邀请活动"
      assert activity.status == :enabled
    end
  end
end
