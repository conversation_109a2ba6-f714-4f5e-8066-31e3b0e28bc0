defmodule Teen.ActivitySystem.ScratchCardActivityTest do
  use ExUnit.Case, async: true

  alias Teen.ActivitySystem.{Activity, ScratchCardActivity, ScratchCardLevelReward}

  describe "ScratchCardActivity" do
    test "resource is defined correctly" do
      # 测试资源是否正确定义
      assert function_exported?(ScratchCardActivity, :spark_dsl_config, 0)
      assert function_exported?(ScratchCardActivity, :create, 1)
      assert function_exported?(ScratchCardActivity, :read, 0)
    end

    test "has correct attributes" do
      # 测试属性是否正确定义
      attributes = ScratchCardActivity |> Ash.Resource.Info.attributes() |> Enum.map(& &1.name)

      assert :id in attributes
      assert :activity_id in attributes
      assert :activity_title in attributes
      assert :claimable_count in attributes
      assert :reward1_probability in attributes
      assert :reward2_probability in attributes
      assert :reward3_probability in attributes
      assert :status in attributes
    end

    test "has correct actions" do
      # 测试动作是否正确定义
      actions = ScratchCardActivity |> Ash.Resource.Info.actions() |> Enum.map(& &1.name)

      assert :create in actions
      assert :read in actions
      assert :update in actions
      assert :destroy in actions
      assert :list_active_activities in actions
      assert :enable_activity in actions
      assert :disable_activity in actions
    end

    test "has correct relationships" do
      # 测试关系是否正确定义
      relationships = ScratchCardActivity |> Ash.Resource.Info.relationships() |> Enum.map(& &1.name)

      assert :activity in relationships
      assert :task_rounds in relationships
      assert :task_levels in relationships
      assert :level_rewards in relationships
    end
  end

  describe "ScratchCardLevelReward" do
    test "resource is defined correctly" do
      # 测试资源是否正确定义
      assert function_exported?(ScratchCardLevelReward, :spark_dsl_config, 0)
      assert function_exported?(ScratchCardLevelReward, :create, 1)
      assert function_exported?(ScratchCardLevelReward, :read, 0)
    end

    test "has correct attributes" do
      # 测试属性是否正确定义
      attributes = ScratchCardLevelReward |> Ash.Resource.Info.attributes() |> Enum.map(& &1.name)

      assert :id in attributes
      assert :reward_config_id in attributes
      assert :level in attributes
      assert :reward_type in attributes
      assert :reward_amount in attributes
      assert :probability in attributes
      assert :sort_order in attributes
      assert :is_active in attributes
    end

    test "has correct actions" do
      # 测试动作是否正确定义
      actions = ScratchCardLevelReward |> Ash.Resource.Info.actions() |> Enum.map(& &1.name)

      assert :create in actions
      assert :read in actions
      assert :update in actions
      assert :destroy in actions
      assert :list_by_config in actions
      assert :enable_reward in actions
      assert :disable_reward in actions
    end

    test "has correct relationships" do
      # 测试关系是否正确定义
      relationships = ScratchCardLevelReward |> Ash.Resource.Info.relationships() |> Enum.map(& &1.name)

      assert :reward_config in relationships
    end
  end

  describe "Integration Tests" do
    test "can create scratch card activity with valid data" do
      # 先创建通用活动
      {:ok, base_activity} = Activity.create(%{
        name: "测试通用活动",
        type: :scratch_card,
        status: :active,
        description: "测试用的通用活动"
      })

      # 测试创建刮刮卡活动
      attrs = %{
        activity_id: base_activity.id,
        activity_title: "测试刮刮卡活动",
        claimable_count: 30,
        reward1_probability: Decimal.new("30"),
        reward2_probability: Decimal.new("30"),
        reward3_probability: Decimal.new("40"),
        status: :enabled
      }

      assert {:ok, activity} = ScratchCardActivity.create(attrs)
      assert activity.activity_id == base_activity.id
      assert activity.activity_title == "测试刮刮卡活动"
      assert activity.claimable_count == 30
      assert Decimal.equal?(activity.reward1_probability, Decimal.new("30"))
      assert Decimal.equal?(activity.reward2_probability, Decimal.new("30"))
      assert Decimal.equal?(activity.reward3_probability, Decimal.new("40"))
      assert activity.status == :enabled
    end

    test "validates probability sum does not exceed 100%" do
      # 先创建通用活动
      {:ok, base_activity} = Activity.create(%{
        name: "测试概率验证活动",
        type: :scratch_card,
        status: :active,
        description: "测试概率验证用的通用活动"
      })

      # 测试概率总和验证
      attrs = %{
        activity_id: base_activity.id,
        activity_title: "测试概率验证",
        claimable_count: 30,
        reward1_probability: Decimal.new("50"),
        reward2_probability: Decimal.new("40"),
        reward3_probability: Decimal.new("20"),  # 总和 110% > 100%
        status: :enabled
      }

      assert {:error, changeset} = ScratchCardActivity.create(attrs)
      assert changeset.errors[:reward1_probability]  # 错误应该在第一个概率字段上
    end

    test "can create level reward for scratch card activity" do
      # 先创建通用活动
      {:ok, base_activity} = Activity.create(%{
        name: "测试等级奖励活动",
        type: :scratch_card,
        status: :active,
        description: "测试等级奖励用的通用活动"
      })

      # 先创建刮刮卡活动
      activity_attrs = %{
        activity_id: base_activity.id,
        activity_title: "测试活动",
        claimable_count: 30,
        reward1_probability: Decimal.new("30"),
        reward2_probability: Decimal.new("30"),
        reward3_probability: Decimal.new("40"),
        status: :enabled
      }

      assert {:ok, activity} = ScratchCardActivity.create(activity_attrs)

      # 创建等级奖励
      reward_attrs = %{
        reward_config_id: activity.id,
        level: 1,
        reward_type: :coins,
        reward_amount: Decimal.new("100"),
        probability: Decimal.new("50"),
        sort_order: 1,
        is_active: true
      }

      assert {:ok, reward} = ScratchCardLevelReward.create(reward_attrs)
      assert reward.reward_config_id == activity.id
      assert reward.level == 1
      assert reward.reward_type == :coins
      assert Decimal.equal?(reward.reward_amount, Decimal.new("100"))
      assert Decimal.equal?(reward.probability, Decimal.new("50"))
      assert reward.sort_order == 1
      assert reward.is_active == true
    end
  end
end
