defmodule Teen.ActivitySystem.ActivityServiceTest do
  @moduledoc """
  活动服务测试

  测试活动系统的核心业务逻辑
  """

  use Cypridina.DataCase, async: true

  alias Teen.ActivitySystem.{
    ActivityService,
    GameTask,
    WeeklyCard,
    SevenDayTask,
    VipGift,
    RechargeTask,
    FirstRechargeGift,
    BindingReward,
    UserActivityParticipation,
    RewardClaimRecord
  }

  alias Cypridina.Accounts.User

  describe "can_participate?/3" do
    test "returns true when user has not participated in activity" do
      user = insert(:user)
      
      assert ActivityService.can_participate?(user.id, :game_task, nil) == true
    end

    test "returns false when user is already participating" do
      user = insert(:user)
      
      # 创建参与记录
      {:ok, _participation} = ActivityService.participate_activity(user.id, :game_task, nil)
      
      assert ActivityService.can_participate?(user.id, :game_task, nil) == false
    end
  end

  describe "participate_activity/4" do
    test "creates participation record for new activity" do
      user = insert(:user)
      
      assert {:ok, participation} = ActivityService.participate_activity(user.id, :game_task, nil)
      assert participation.user_id == user.id
      assert participation.activity_type == :game_task
      assert participation.status == :active
      assert participation.progress == 0
    end

    test "returns error when user is already participating" do
      user = insert(:user)
      
      {:ok, _participation} = ActivityService.participate_activity(user.id, :game_task, nil)
      
      assert {:error, :already_participating} = ActivityService.participate_activity(user.id, :game_task, nil)
    end
  end

  describe "update_progress/4" do
    test "updates user activity progress" do
      user = insert(:user)
      {:ok, participation} = ActivityService.participate_activity(user.id, :game_task, nil)
      
      assert {:ok, updated_participation} = ActivityService.update_progress(user.id, :game_task, nil, 5)
      assert updated_participation.progress == 5
    end

    test "returns error when user is not participating" do
      user = insert(:user)
      
      assert {:error, :not_participating} = ActivityService.update_progress(user.id, :game_task, nil, 1)
    end
  end

  describe "can_claim_reward?/3" do
    test "returns true when user meets reward criteria for game task" do
      user = insert(:user)
      game_task = insert(:game_task, required_count: 10, reward_amount: Decimal.new("100"))
      
      {:ok, participation} = ActivityService.participate_activity(user.id, :game_task, game_task.id)
      {:ok, _} = ActivityService.update_progress(user.id, :game_task, game_task.id, 10)
      
      assert ActivityService.can_claim_reward?(user.id, :game_task, game_task.id) == true
    end

    test "returns false when user does not meet reward criteria" do
      user = insert(:user)
      game_task = insert(:game_task, required_count: 10, reward_amount: Decimal.new("100"))
      
      {:ok, participation} = ActivityService.participate_activity(user.id, :game_task, game_task.id)
      {:ok, _} = ActivityService.update_progress(user.id, :game_task, game_task.id, 5)
      
      assert ActivityService.can_claim_reward?(user.id, :game_task, game_task.id) == false
    end
  end

  describe "claim_reward/3" do
    test "successfully claims reward when eligible" do
      user = insert(:user)
      game_task = insert(:game_task, required_count: 10, reward_amount: Decimal.new("100"))
      
      {:ok, _} = ActivityService.participate_activity(user.id, :game_task, game_task.id)
      {:ok, _} = ActivityService.update_progress(user.id, :game_task, game_task.id, 10)
      
      assert {:ok, reward_amount} = ActivityService.claim_reward(user.id, :game_task, game_task.id)
      assert Decimal.equal?(reward_amount, Decimal.new("100"))
      
      # 验证奖励记录已创建
      {:ok, records} = RewardClaimRecord.list_by_user(%{user_id: user.id})
      assert length(records) == 1
      
      record = List.first(records)
      assert record.activity_type == :game_task
      assert record.reward_type == :coins
      assert Decimal.equal?(record.reward_amount, Decimal.new("100"))
    end

    test "returns error when user is not eligible" do
      user = insert(:user)
      game_task = insert(:game_task, required_count: 10, reward_amount: Decimal.new("100"))
      
      {:ok, _} = ActivityService.participate_activity(user.id, :game_task, game_task.id)
      {:ok, _} = ActivityService.update_progress(user.id, :game_task, game_task.id, 5)
      
      assert {:error, :not_eligible} = ActivityService.claim_reward(user.id, :game_task, game_task.id)
    end
  end

  describe "get_user_activity_stats/3" do
    test "returns user activity statistics" do
      user = insert(:user)
      
      # 创建一些奖励记录
      insert(:reward_claim_record, user_id: user.id, reward_amount: Decimal.new("100"))
      insert(:reward_claim_record, user_id: user.id, reward_amount: Decimal.new("200"))
      
      {:ok, stats} = ActivityService.get_user_activity_stats(user.id)
      
      # 这里应该验证统计数据的正确性
      assert is_list(stats) or is_map(stats)
    end
  end

  # 辅助函数用于创建测试数据
  defp insert(factory_name, attrs \\ %{}) do
    case factory_name do
      :user ->
        %User{
          id: Ecto.UUID.generate(),
          username: "test_user_#{System.unique_integer()}",
          email: "test#{System.unique_integer()}@example.com",
          permission_level: 1
        }
        |> Map.merge(attrs)
        |> Cypridina.Repo.insert!()

      :game_task ->
        %GameTask{
          id: Ecto.UUID.generate(),
          task_name: "测试游戏任务",
          game_id: "test_game",
          game_name: "测试游戏",
          task_type: :game_rounds,
          required_count: 10,
          max_claims: 1,
          reward_amount: Decimal.new("100"),
          status: :enabled
        }
        |> Map.merge(attrs)
        |> Cypridina.Repo.insert!()

      :reward_claim_record ->
        %RewardClaimRecord{
          id: Ecto.UUID.generate(),
          user_id: attrs[:user_id] || Ecto.UUID.generate(),
          activity_type: :game_task,
          reward_type: :coins,
          reward_amount: attrs[:reward_amount] || Decimal.new("100"),
          claimed_at: DateTime.utc_now()
        }
        |> Map.merge(attrs)
        |> Cypridina.Repo.insert!()
    end
  end
end
