defmodule Cypridina.Protocol.PhoneVerificationIntegrationTest do
  use ExUnit.Case, async: false
  alias Cy<PERSON><PERSON>ina.Protocol.WebSocketHandler

  describe "phone verification protocol integration" do
    test "handles complete phone verification flow with valid phone" do
      # 使用一个有效的手机号格式
      message = %{
        main_id: 0,
        sub_id: 29,
        data: %{
          # 标准的中国手机号格式
          "phone" => "13800138000",
          "type" => 2,
          "siteid" => 501
        }
      }

      state = %{ip_address: "127.0.0.1"}

      # 调用协议处理函数
      result = WebSocketHandler.handle_message(message, state)

      # 验证返回格式
      assert {:reply, response, ^state} = result
      assert response["mainId"] == 0
      assert response["subId"] == 30
      assert is_map(response["data"])

      # 验证响应数据包含必要字段
      data = response["data"]
      assert Map.has_key?(data, "status")
      assert Map.has_key?(data, "message")
      assert Map.has_key?(data, "phone")
      assert data["phone"] == "13800138000"

      # 由于我们没有配置真实的SMS服务，可能会失败，但格式应该正确
      # 状态码应该是 0（成功）或 2（发送失败）
      assert data["status"] in [0, 2]
    end

    test "handles phone verification with minimal data" do
      message = %{
        main_id: 0,
        sub_id: 29,
        data: %{
          "phone" => "***********"
          # 只提供手机号，其他使用默认值
        }
      }

      state = %{ip_address: "*************"}

      result = WebSocketHandler.handle_message(message, state)

      assert {:reply, response, ^state} = result
      assert response["mainId"] == 0
      assert response["subId"] == 30

      data = response["data"]
      assert data["phone"] == "***********"

      # 验证默认值
      if data["status"] == 0 do
        # 默认登录类型
        assert data["type"] == 2
        # 默认站点ID
        assert data["siteid"] == 1
      end
    end

    test "handles protocol routing correctly" do
      # 测试协议路由是否正确识别
      message = %{
        main_id: 0,
        sub_id: 29,
        data: %{"phone" => "***********"}
      }

      state = %{ip_address: "********"}

      # 确保消息被正确路由到我们的处理函数
      result = WebSocketHandler.handle_message(message, state)

      # 应该返回我们期望的响应格式，而不是未处理的消息
      assert {:reply, _response, ^state} = result
    end

    test "preserves state during message handling" do
      message = %{
        main_id: 0,
        sub_id: 29,
        data: %{"phone" => "***********"}
      }

      original_state = %{
        ip_address: "**********",
        user_id: "test-user-123",
        session_data: %{some: "data"}
      }

      result = WebSocketHandler.handle_message(message, original_state)

      # 状态应该保持不变
      assert {:reply, _response, ^original_state} = result
    end
  end
end
