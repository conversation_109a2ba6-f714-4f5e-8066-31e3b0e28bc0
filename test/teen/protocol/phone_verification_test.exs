defmodule Cypridina.Protocol.PhoneVerificationTest do
  use ExUnit.Case, async: true
  alias Cypridina.Protocol.WebSocketHandler

  describe "handle_request_phone_verification_code/2" do
    test "handles invalid phone number" do
      message = %{
        main_id: 0,
        sub_id: 29,
        data: %{
          # 太短的手机号
          "phone" => "123",
          "type" => 2,
          "siteid" => 501
        }
      }

      state = %{ip_address: "127.0.0.1"}

      {:reply, response, ^state} = WebSocketHandler.handle_message(message, state)

      assert response["mainId"] == 0
      assert response["subId"] == 30
      assert response["data"]["status"] == 1
      assert response["data"]["message"] == "手机号格式不正确"
      assert response["data"]["phone"] == "123"
    end

    test "handles missing data fields with defaults" do
      message = %{
        main_id: 0,
        sub_id: 29,
        data: %{
          # 短手机号，会触发格式错误
          "phone" => "123"
          # 缺少 type 和 siteid
        }
      }

      state = %{ip_address: "127.0.0.1"}

      {:reply, response, ^state} = WebSocketHandler.handle_message(message, state)

      # 由于手机号格式错误，应该返回错误状态
      assert response["mainId"] == 0
      assert response["subId"] == 30
      assert response["data"]["status"] == 1
      assert response["data"]["message"] == "手机号格式不正确"
    end

    test "handles empty data" do
      message = %{
        main_id: 0,
        sub_id: 29,
        data: %{}
      }

      state = %{ip_address: "127.0.0.1"}

      {:reply, response, ^state} = WebSocketHandler.handle_message(message, state)

      # 空手机号应该触发格式错误
      assert response["mainId"] == 0
      assert response["subId"] == 30
      assert response["data"]["status"] == 1
      assert response["data"]["message"] == "手机号格式不正确"
      assert response["data"]["phone"] == ""
    end

    test "handles nil data" do
      message = %{
        main_id: 0,
        sub_id: 29,
        data: nil
      }

      state = %{ip_address: "127.0.0.1"}

      {:reply, response, ^state} = WebSocketHandler.handle_message(message, state)

      # nil data 应该触发格式错误
      assert response["mainId"] == 0
      assert response["subId"] == 30
      assert response["data"]["status"] == 1
      assert response["data"]["message"] == "手机号格式不正确"
      assert response["data"]["phone"] == ""
    end
  end
end
