defmodule Teen.CustomerServiceDuplicateSessionTest do
  @moduledoc """
  测试客服聊天系统的重复会话检查功能
  """
  
  use CypridinaWeb.ConnCase, async: false
  
  alias Teen.CustomerService
  alias Cypridina.Accounts.User
  
  setup do
    # 创建测试用户
    {:ok, user} = User
    |> Ash.Changeset.for_create(:create, %{
      username: "test_user_#{:rand.uniform(10000)}",
      email: "test#{:rand.uniform(10000)}@example.com"
    })
    |> Ash.create()
    
    %{user: user}
  end
  
  describe "duplicate session prevention" do
    test "has_active_chat_session?/1 returns false for user without active sessions", %{user: user} do
      refute CustomerService.has_active_chat_session?(user.id)
    end
    
    test "has_active_chat_session?/1 returns true for user with active session", %{user: user} do
      # 创建一个活跃会话（status = 0）
      {:ok, _chat} = CustomerService.create_customer_chat(%{
        user_id: user.id,
        platform: "admin",
        question: "测试问题",
        reply_content: "",
        status: 0,  # 未处理状态
        customer_service_id: nil,
        processed_at: nil
      })
      
      assert CustomerService.has_active_chat_session?(user.id)
    end
    
    test "has_active_chat_session?/1 returns false for user with only processed sessions", %{user: user} do
      # 创建一个已处理会话（status = 1）
      {:ok, _chat} = CustomerService.create_customer_chat(%{
        user_id: user.id,
        platform: "admin",
        question: "测试问题",
        reply_content: "测试回复",
        status: 1,  # 已处理状态
        customer_service_id: nil,
        processed_at: DateTime.utc_now()
      })
      
      refute CustomerService.has_active_chat_session?(user.id)
    end
    
    test "get_active_chat_session/1 returns nil for user without active sessions", %{user: user} do
      assert {:ok, nil} = CustomerService.get_active_chat_session(user.id)
    end
    
    test "get_active_chat_session/1 returns active session for user with active session", %{user: user} do
      # 创建一个活跃会话
      {:ok, created_chat} = CustomerService.create_customer_chat(%{
        user_id: user.id,
        platform: "admin",
        question: "测试问题",
        reply_content: "",
        status: 0,  # 未处理状态
        customer_service_id: nil,
        processed_at: nil
      })
      
      assert {:ok, active_chat} = CustomerService.get_active_chat_session(user.id)
      assert active_chat.id == created_chat.id
      assert active_chat.status == 0
    end
    
    test "create_customer_chat_with_check/1 creates new session when no active session exists", %{user: user} do
      attrs = %{
        user_id: user.id,
        platform: "admin",
        question: "新聊天",
        reply_content: "",
        status: 0,
        customer_service_id: nil,
        processed_at: nil
      }
      
      assert {:ok, chat} = CustomerService.create_customer_chat_with_check(attrs)
      assert chat.user_id == user.id
      assert chat.question == "新聊天"
    end
    
    test "create_customer_chat_with_check/1 prevents duplicate session when active session exists", %{user: user} do
      # 先创建一个活跃会话
      {:ok, existing_chat} = CustomerService.create_customer_chat(%{
        user_id: user.id,
        platform: "admin",
        question: "第一个聊天",
        reply_content: "",
        status: 0,  # 未处理状态
        customer_service_id: nil,
        processed_at: nil
      })
      
      # 尝试创建第二个会话
      attrs = %{
        user_id: user.id,
        platform: "admin",
        question: "第二个聊天",
        reply_content: "",
        status: 0,
        customer_service_id: nil,
        processed_at: nil
      }
      
      assert {:error, {:already_exists, returned_chat}} = CustomerService.create_customer_chat_with_check(attrs)
      assert returned_chat.id == existing_chat.id
    end
    
    test "create_customer_chat_with_check/1 allows new session after previous session is processed", %{user: user} do
      # 创建并处理一个会话
      {:ok, first_chat} = CustomerService.create_customer_chat(%{
        user_id: user.id,
        platform: "admin",
        question: "第一个聊天",
        reply_content: "已回复",
        status: 1,  # 已处理状态
        customer_service_id: nil,
        processed_at: DateTime.utc_now()
      })
      
      # 创建新会话应该成功
      attrs = %{
        user_id: user.id,
        platform: "admin",
        question: "第二个聊天",
        reply_content: "",
        status: 0,
        customer_service_id: nil,
        processed_at: nil
      }
      
      assert {:ok, second_chat} = CustomerService.create_customer_chat_with_check(attrs)
      assert second_chat.id != first_chat.id
      assert second_chat.question == "第二个聊天"
    end
  end
  
  describe "user filtering" do
    test "filters out users with active chat sessions" do
      # 这个测试需要在实际的LiveView环境中运行
      # 或者可以直接测试过滤逻辑
      
      # 创建两个用户
      {:ok, user1} = User
      |> Ash.Changeset.for_create(:create, %{
        username: "user1_#{:rand.uniform(10000)}",
        email: "user1_#{:rand.uniform(10000)}@example.com"
      })
      |> Ash.create()
      
      {:ok, user2} = User
      |> Ash.Changeset.for_create(:create, %{
        username: "user2_#{:rand.uniform(10000)}",
        email: "user2_#{:rand.uniform(10000)}@example.com"
      })
      |> Ash.create()
      
      # 给user1创建活跃会话
      {:ok, _chat} = CustomerService.create_customer_chat(%{
        user_id: user1.id,
        platform: "admin",
        question: "用户1的聊天",
        reply_content: "",
        status: 0,  # 未处理状态
        customer_service_id: nil,
        processed_at: nil
      })
      
      # 测试过滤逻辑
      all_users = [user1, user2]
      available_users = Enum.reject(all_users, fn user ->
        CustomerService.has_active_chat_session?(user.id)
      end)
      
      # user1应该被过滤掉，user2应该保留
      assert length(available_users) == 1
      assert hd(available_users).id == user2.id
    end
  end
end
