defmodule CypridinaWeb.LobbyChannelOfflineTimeTest do
  @moduledoc """
  测试LobbyChannel的离线时间更新功能
  """
  
  use CypridinaWeb.ChannelCase, async: false
  
  alias <PERSON><PERSON><PERSON>inaW<PERSON>.LobbyChannel
  alias <PERSON>pridina.Accounts.User
  alias Teen.UserPresence
  
  setup do
    # 创建测试用户
    {:ok, user} = User
    |> Ash.Changeset.for_create(:create, %{
      username: "test_user_#{:rand.uniform(10000)}",
      email: "test#{:rand.uniform(10000)}@example.com"
    })
    |> Ash.create()
    
    # 创建socket
    socket = socket(CypridinaWeb.PhoenixSocket, "user_id", %{
      authenticate: true,
      current_user: user,
      user_id: user.id,
      session_id: "test_session_#{:rand.uniform(10000)}"
    })
    
    %{user: user, socket: socket}
  end
  
  describe "LobbyChannel offline time management" do
    test "sets user online when joining channel", %{user: user, socket: socket} do
      # 首先设置用户为离线状态
      {:ok, offline_user} = user
      |> Ash.Changeset.for_update(:set_offline)
      |> Ash.update()
      
      assert offline_user.last_offline_at != nil
      
      # 加入频道
      {:ok, _, _socket} = subscribe_and_join(socket, LobbyChannel, "game:lobby")
      
      # 等待一下让异步操作完成
      Process.sleep(100)
      
      # 验证用户状态已更新为在线
      {:ok, updated_user} = User |> Ash.get(user.id)
      assert updated_user.last_offline_at == nil
    end
    
    test "sets user offline when leaving channel", %{user: user, socket: socket} do
      # 首先设置用户为在线状态
      {:ok, online_user} = user
      |> Ash.Changeset.for_update(:set_online)
      |> Ash.update()
      
      assert online_user.last_offline_at == nil
      
      # 加入频道
      {:ok, _, channel_socket} = subscribe_and_join(socket, LobbyChannel, "game:lobby")
      
      # 离开频道
      leave(channel_socket)
      
      # 等待一下让异步操作完成
      Process.sleep(100)
      
      # 验证用户状态已更新为离线
      {:ok, updated_user} = User |> Ash.get(user.id)
      assert updated_user.last_offline_at != nil
      assert DateTime.diff(DateTime.utc_now(), updated_user.last_offline_at, :second) < 5
    end
    
    test "handles user status update errors gracefully", %{socket: socket} do
      # 使用不存在的用户ID测试错误处理
      invalid_socket = socket
      |> assign(:user_id, 999999)
      |> assign(:current_user, %{id: 999999})
      
      # 这应该不会导致Channel崩溃
      assert {:ok, _, _socket} = subscribe_and_join(invalid_socket, LobbyChannel, "game:lobby")
    end
  end
  
  describe "update_user_online_status/2 function" do
    test "successfully updates user to online status", %{user: user} do
      # 首先设置为离线
      {:ok, _} = user |> Ash.Changeset.for_update(:set_offline) |> Ash.update()
      
      # 通过反射调用私有函数进行测试
      # 注意：这里需要实际的实现来测试私有函数
      # 或者可以通过集成测试来验证功能
      
      {:ok, updated_user} = User |> Ash.get(user.id)
      # 验证初始状态
      assert updated_user.last_offline_at != nil
    end
    
    test "successfully updates user to offline status", %{user: user} do
      # 首先设置为在线
      {:ok, _} = user |> Ash.Changeset.for_update(:set_online) |> Ash.update()
      
      {:ok, updated_user} = User |> Ash.get(user.id)
      # 验证初始状态
      assert updated_user.last_offline_at == nil
    end
  end
  
  describe "integration with Phoenix Presence" do
    test "presence tracking works with database updates", %{user: user, socket: socket} do
      # 验证用户初始不在线
      refute UserPresence.user_online?(user.id)
      
      # 加入频道
      {:ok, _, _channel_socket} = subscribe_and_join(socket, LobbyChannel, "game:lobby")
      
      # 等待Presence更新
      Process.sleep(100)
      
      # 验证Presence状态
      assert UserPresence.user_online?(user.id)
      
      # 验证数据库状态
      {:ok, updated_user} = User |> Ash.get(user.id)
      assert updated_user.last_offline_at == nil
    end
  end
end
