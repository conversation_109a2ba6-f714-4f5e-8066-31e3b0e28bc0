defmodule <PERSON><PERSON><PERSON><PERSON>.Accounts.UserVersionTest do
  use <PERSON><PERSON><PERSON><PERSON>.DataCase, async: true

  alias <PERSON><PERSON><PERSON><PERSON>.Accounts.{User, UserVers<PERSON>}

  describe "audit trail functionality" do
    test "user creation is tracked" do
      # Create user
      {:ok, user} = User.create_guest_user()

      # Check audit trail
      versions = UserVersion.by_user!(user.id)
      assert length(versions) >= 1

      # Find creation version
      creation_version = Enum.find(versions, &(&1.action_name == "create_guest_user"))
      assert creation_version != nil
      assert creation_version.resource_id == user.id
      assert creation_version.version_number == 1
    end

    test "user updates are tracked" do
      # Create user
      {:ok, user} = User.create_guest_user()

      # Update permission level
      {:ok, _updated_user} = User.update_permission_level(user, %{permission_level: 1})

      # Check audit trail
      versions = UserVersion.by_user!(user.id)
      assert length(versions) >= 2

      # Find update version
      update_version = Enum.find(versions, &(&1.action_name == "update_permission_level"))
      assert update_version != nil
      assert update_version.resource_id == user.id
      assert update_version.version_number > 1

      # Check changes
      assert update_version.changes != nil
      changes = update_version.changes
      assert Map.has_key?(changes, "permission_level")
    end

    test "sensitive fields are ignored" do
      username = "testuser_#{:rand.uniform(10000)}"

      # Create registered user (has password)
      {:ok, user} =
        User.register_with_username(%{
          username: username,
          password: "password123",
          password_confirmation: "password123"
        })

      # Check audit trail
      versions = UserVersion.by_user!(user.id)
      creation_version = Enum.find(versions, &(&1.action_name == "register_with_username"))

      # Verify sensitive fields are not stored
      changes = creation_version.changes
      refute Map.has_key?(changes, "hashed_password")
      refute Map.has_key?(changes, "password")
    end

    test "recent_changes/1 returns paginated results" do
      # Create user
      {:ok, user} = User.create_guest_user()

      # Make several updates
      {:ok, _} = User.update_permission_level(user, %{permission_level: 1})
      {:ok, _} = User.update_agent_level(user, %{agent_level: 0})

      # Get recent changes
      recent_versions = UserVersion.recent_changes!(%{user_id: user.id, limit: 2})
      assert length(recent_versions) <= 2

      # Should be sorted by inserted_at desc
      if length(recent_versions) > 1 do
        [first, second | _] = recent_versions
        assert DateTime.compare(first.inserted_at, second.inserted_at) in [:gt, :eq]
      end
    end
  end

  describe "version queries" do
    test "by_resource_id/1 finds all versions for a user" do
      {:ok, user} = User.create_guest_user()
      {:ok, _} = User.update_permission_level(user, %{permission_level: 1})

      versions = UserVersion.by_resource_id!(user.id)
      assert length(versions) >= 2

      # All versions should be for the same user
      Enum.each(versions, fn version ->
        assert version.resource_id == user.id
      end)
    end

    test "by_user/1 is alias for by_resource_id/1" do
      {:ok, user} = User.create_guest_user()

      versions_by_resource = UserVersion.by_resource_id!(user.id)
      versions_by_user = UserVersion.by_user!(user.id)

      assert length(versions_by_resource) == length(versions_by_user)
      assert Enum.map(versions_by_resource, & &1.id) == Enum.map(versions_by_user, & &1.id)
    end
  end

  describe "version relationships" do
    test "version belongs_to user" do
      {:ok, user} = User.create_guest_user()

      versions = UserVersion.by_user!(user.id)
      version = List.first(versions)

      # Load user relationship
      version_with_user = UserVersion.read!(version.id, load: [:user])
      assert version_with_user.user.id == user.id
      assert version_with_user.user.username == user.username
    end

    test "version belongs_to actor when available" do
      # This test would require setting up an actor context
      # For now, just verify the relationship exists
      {:ok, user} = User.create_guest_user()

      versions = UserVersion.by_user!(user.id)
      version = List.first(versions)

      # Actor might be nil for system operations
      version_with_actor = UserVersion.read!(version.id, load: [:actor])
      # actor can be nil for system operations, so we just check it loads
      assert version_with_actor != nil
    end
  end

  describe "version calculations" do
    test "change_summary calculation" do
      {:ok, user} = User.create_guest_user()
      {:ok, _} = User.update_permission_level(user, %{permission_level: 1})

      versions = UserVersion.by_user!(user.id)

      # Load with calculations
      versions_with_summary =
        Enum.map(versions, fn version ->
          UserVersion.read!(version.id, load: [:change_summary])
        end)

      # Check summaries
      Enum.each(versions_with_summary, fn version ->
        assert version.change_summary != nil
        assert is_binary(version.change_summary)
      end)
    end
  end
end
