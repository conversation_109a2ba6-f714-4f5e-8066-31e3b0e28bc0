defmodule <PERSON>pridina.Accounts.AccountIdentifierTest do
  use ExUnit.Case, async: true

  alias <PERSON><PERSON><PERSON><PERSON>.Accounts.AccountIdentifier

  describe "system/1" do
    test "generates system account identifier with default currency" do
      assert AccountIdentifier.system(:main) == "system:XAA:main"
      assert AccountIdentifier.system(:rewards) == "system:XAA:rewards"
      assert AccountIdentifier.system(:fees) == "system:XAA:fees"
    end

    test "generates system account identifier with custom currency" do
      assert AccountIdentifier.system(:main, :USD) == "system:USD:main"
      assert AccountIdentifier.system(:rewards, :EUR) == "system:EUR:rewards"
    end

    test "backward compatibility with build_system_identifier" do
      assert AccountIdentifier.build_system_identifier(:main) == AccountIdentifier.system(:main)
      assert AccountIdentifier.build_system_identifier(:rewards, :USD) == AccountIdentifier.system(:rewards, :USD)
    end
  end

  describe "parse/1" do
    test "parses new system account format" do
      assert {:ok, %{type: :system, currency: :XAA, account_type: :main}} =
        AccountIdentifier.parse("system:XAA:main")

      assert {:ok, %{type: :system, currency: :USD, account_type: :rewards}} =
        AccountIdentifier.parse("system:USD:rewards")
    end

    test "parses old system account format for backward compatibility" do
      assert {:ok, %{type: :system, currency: :XAA, account_type: :main}} =
        AccountIdentifier.parse("system:main")

      assert {:ok, %{type: :system, currency: :XAA, account_type: :rewards}} =
        AccountIdentifier.parse("system:rewards")
    end

    test "parses user account format" do
      assert {:ok, %{type: :user, currency: :XAA, id: "user_123"}} =
        AccountIdentifier.parse("user:XAA:user_123")
    end

    test "parses game account format" do
      assert {:ok, %{type: :game, currency: :XAA, id: "game_456"}} =
        AccountIdentifier.parse("game:XAA:game_456")
    end

    test "backward compatibility with parse_identifier" do
      identifier = "user:XAA:user_123"
      assert AccountIdentifier.parse_identifier(identifier) == AccountIdentifier.parse(identifier)
    end
  end

  describe "valid?/1" do
    test "validates new system account format" do
      assert AccountIdentifier.valid?("system:XAA:main") == true
      assert AccountIdentifier.valid?("system:USD:rewards") == true
    end

    test "validates old system account format for backward compatibility" do
      assert AccountIdentifier.valid?("system:main") == true
      assert AccountIdentifier.valid?("system:rewards") == true
    end

    test "validates user account format" do
      assert AccountIdentifier.valid?("user:XAA:user_123") == true
    end

    test "validates game account format" do
      assert AccountIdentifier.valid?("game:XAA:game_456") == true
    end

    test "rejects invalid formats" do
      assert AccountIdentifier.valid?("invalid:format") == false
      assert AccountIdentifier.valid?("system") == false
      assert AccountIdentifier.valid?("user:XAA") == false
    end

    test "backward compatibility with valid_identifier?" do
      identifier = "user:XAA:user_123"
      assert AccountIdentifier.valid_identifier?(identifier) == AccountIdentifier.valid?(identifier)
    end
  end

  describe "extract_system_account_type/1" do
    test "extracts account type from new format" do
      assert {:ok, :main} = AccountIdentifier.extract_system_account_type("system:XAA:main")
      assert {:ok, :rewards} = AccountIdentifier.extract_system_account_type("system:USD:rewards")
    end

    test "extracts account type from old format" do
      assert {:ok, :main} = AccountIdentifier.extract_system_account_type("system:main")
      assert {:ok, :rewards} = AccountIdentifier.extract_system_account_type("system:rewards")
    end

    test "returns error for non-system accounts" do
      assert {:error, {:wrong_type, :user}} =
        AccountIdentifier.extract_system_account_type("user:XAA:user_123")
    end
  end

  describe "round-trip consistency" do
    test "system account identifier round-trip with new format" do
      original_type = :main
      identifier = AccountIdentifier.system(original_type)
      assert {:ok, %{type: :system, account_type: ^original_type}} =
        AccountIdentifier.parse(identifier)
    end

    test "system account identifier round-trip with custom currency" do
      original_type = :rewards
      currency = :USD
      identifier = AccountIdentifier.system(original_type, currency)
      assert {:ok, %{type: :system, currency: ^currency, account_type: ^original_type}} =
        AccountIdentifier.parse(identifier)
    end
  end

  describe "cache key compatibility" do
    test "cache keys remain unchanged for system accounts" do
      # Cache keys should still use the simple format for efficiency
      assert AccountIdentifier.cache_key(:system, :main) == "system:main"
      assert AccountIdentifier.cache_key(:system, :rewards) == "system:rewards"
    end

    test "backward compatibility with build_cache_key" do
      assert AccountIdentifier.build_cache_key(:system, :main) == AccountIdentifier.cache_key(:system, :main)
    end

    test "parse cache key works correctly" do
      assert {:ok, %{type: :system, account_type: :main}} =
        AccountIdentifier.parse_cache_key("system:main")
    end
  end
end
