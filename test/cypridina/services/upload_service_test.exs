defmodule Cypridina.Services.UploadServiceTest do
  @moduledoc """
  测试Waffle文件上传服务
  """

  use ExUnit.Case
  alias Cypridina.Services.UploadService

  describe "upload_avatar_from_base64/3" do
    test "handles base64 data processing" do
      user_id = Ecto.UUID.generate()

      # 创建一个简单的1x1像素PNG图片的base64数据
      # 这是一个透明的1x1像素PNG图片
      base64_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg=="

      # 测试上传
      case UploadService.upload_avatar_from_base64(user_id, base64_data, "png") do
        {:ok, urls} ->
          assert is_map(urls)
          assert Map.has_key?(urls, :original)
          assert Map.has_key?(urls, :thumb)
          assert is_binary(urls.original)
          assert is_binary(urls.thumb)

        {:error, reason} ->
          # 如果MinIO不可用，测试应该跳过而不是失败
          IO.puts("MinIO可能不可用，跳过上传测试: #{inspect(reason)}")
          :ok
      end
    end

    test "handles invalid base64 data" do
      user_id = Ecto.UUID.generate()

      # 测试无效的base64数据
      invalid_base64 = "invalid_base64_data"

      case UploadService.upload_avatar_from_base64(user_id, invalid_base64, "png") do
        {:error, _reason} ->
          # 应该返回错误
          assert true

        {:ok, _urls} ->
          # 如果成功了，说明有问题
          flunk("应该返回错误，但却成功了")
      end
    end
  end

  describe "generate_presigned_upload_url/3" do
    test "generates presigned URL successfully" do
      user_id = Ecto.UUID.generate()

      case UploadService.generate_presigned_upload_url(user_id, "jpg", "image/jpeg") do
        {:ok, upload_url, file_path} ->
          assert is_binary(upload_url)
          assert is_binary(file_path)
          assert String.contains?(file_path, "avatars/#{user_id}")
          assert String.ends_with?(file_path, ".jpg")

        {:error, reason} ->
          # 如果MinIO不可用，测试应该跳过而不是失败
          IO.puts("MinIO可能不可用，跳过预签名URL测试: #{inspect(reason)}")
          :ok
      end
    end
  end

  describe "upload_file/3" do
    test "uploads file successfully with Plug.Upload struct" do
      user_id = Ecto.UUID.generate()

      # 创建临时文件
      temp_path = System.tmp_dir!()
      file_path = Path.join(temp_path, "test_file.txt")
      File.write!(file_path, "Hello, World!")

      # 创建Plug.Upload结构
      upload = %Plug.Upload{
        path: file_path,
        filename: "test_file.txt",
        content_type: "text/plain"
      }

      case UploadService.upload_file(user_id, upload) do
        {:ok, url} ->
          assert is_binary(url)

        {:error, reason} ->
          # 如果MinIO不可用，测试应该跳过而不是失败
          IO.puts("MinIO可能不可用，跳过文件上传测试: #{inspect(reason)}")
          :ok
      end

      # 清理临时文件
      File.rm(file_path)
    end
  end
end
