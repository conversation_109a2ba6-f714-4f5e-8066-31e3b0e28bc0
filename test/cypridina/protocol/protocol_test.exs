defmodule Cypridina.Protocol.ProtocolTest do
  use ExUnit.Case, async: true

  alias Cy<PERSON><PERSON>ina.Protocol.WebSocketHandler
  alias Cy<PERSON><PERSON>ina.Protocol.ProtocolHandlers
  alias Cy<PERSON><PERSON>ina.Protocol.ExtendedHandlers
  alias Cypridina.Protocol.SystemHandlers

  @test_state %{
    session_id: "test_session_123",
    user_id: "test_user_456",
    connected_at: System.system_time(:millisecond),
    authenticate: true,
    room_id: nil,
    socket_info: %{}
  }

  describe "RegLogin协议测试" do
    test "处理注册请求" do
      message = %{
        main_id: 0,
        sub_id: 0,
        data: %{
          "username" => "testuser",
          "password" => "123456",
          "nickname" => "测试用户"
        }
      }

      {:reply, response, _state} = WebSocketHandler.handle_message(message, @test_state)

      assert response["mainId"] == 0
      assert response["subId"] == 1
      assert response["data"]["status"] == 0
    end

    test "处理登录请求" do
      message = %{
        main_id: 0,
        sub_id: 2,
        data: %{
          "account" => "testuser",
          "password" => "123456",
          "accounttype" => 1,
          "siteid" => 1
        }
      }

      {:reply, response, _state} = WebSocketHandler.handle_message(message, @test_state)

      assert response["mainId"] == 0
      assert response["subId"] == 3
      assert response["data"]["code"] == 0
      assert response["data"]["playerid"] != nil
      assert response["data"]["Function"]["6"]["nickname"] == "testuser"
      assert response["data"]["loginparam"]["account"] == "testuser"
    end

    test "处理心跳检查" do
      message = %{
        main_id: 0,
        sub_id: 19,
        data: %{}
      }

      {:reply, response, _state} = WebSocketHandler.handle_message(message, @test_state)

      assert response["mainId"] == 0
      assert response["subId"] == 20
      assert response["data"]["server_time"] != nil
    end

    test "处理请求验证码" do
      message = %{
        main_id: 0,
        sub_id: 41,
        data: %{"siteid" => 1}
      }

      {:reply, response, _state} = SystemHandlers.handle_request_vercode(message, @test_state)

      assert response["mainId"] == 0
      assert response["subId"] == 42
      assert response["data"]["status"] == 0
      assert response["data"]["verification_code"] != nil
    end
  end

  describe "FindPsw协议测试" do
    test "处理找回密码请求" do
      message = %{
        main_id: 1,
        sub_id: 0,
        data: %{"username" => "testuser"}
      }

      {:reply, response, _state} = ProtocolHandlers.handle_find_password(message, @test_state)

      assert response["mainId"] == 1
      assert response["subId"] == 1
      assert response["data"]["status"] == 0
    end

    test "处理请求手机验证码" do
      message = %{
        main_id: 1,
        sub_id: 2,
        data: %{"phone" => "1234567890"}
      }

      {:reply, response, _state} =
        ProtocolHandlers.handle_find_password_request_code(message, @test_state)

      assert response["mainId"] == 1
      assert response["subId"] == 3
      assert response["data"]["status"] == 0
    end

    test "处理设置新密码" do
      message = %{
        main_id: 1,
        sub_id: 7,
        data: %{"new_password" => "newpassword123"}
      }

      {:reply, response, _state} =
        ProtocolHandlers.handle_find_password_set_new_password(message, @test_state)

      assert response["mainId"] == 1
      assert response["subId"] == 8
      assert response["data"]["status"] == 0
    end
  end

  describe "NoticeManager协议测试" do
    test "处理发送公告请求" do
      message = %{
        main_id: 15,
        sub_id: 1,
        data: %{
          "content" => "这是一条测试公告",
          "type" => 1
        }
      }

      {:reply, response, _state} = ProtocolHandlers.handle_send_notice(message, @test_state)

      assert response["mainId"] == 15
      assert response["subId"] == 2
      assert response["data"]["status"] == 0
    end

    test "处理请求系统公告" do
      message = %{
        main_id: 15,
        sub_id: 5,
        data: %{}
      }

      {:reply, response, _state} =
        ProtocolHandlers.handle_request_system_notice(message, @test_state)

      assert response["mainId"] == 15
      assert response["subId"] == 6
      assert response["data"]["status"] == 0
      assert is_list(response["data"]["notices"])
    end
  end

  describe "MailManager协议测试" do
    test "处理请求邮件内容" do
      message = %{
        main_id: 14,
        sub_id: 0,
        data: %{"mail_id" => 1}
      }

      {:reply, response, _state} = ProtocolHandlers.handle_request_mail_info(message, @test_state)

      assert response["mainId"] == 14
      assert response["subId"] == 1
      assert response["data"]["status"] == 0
      assert response["data"]["mail_info"] != nil
    end

    test "处理请求邮件列表" do
      message = %{
        main_id: 14,
        sub_id: 5,
        data: %{"page" => 1, "page_size" => 10}
      }

      {:reply, response, _state} = ProtocolHandlers.handle_request_mail_list(message, @test_state)

      assert response["mainId"] == 14
      assert response["subId"] == 6
      assert response["data"]["status"] == 0
      assert is_list(response["data"]["mail_list"])
    end
  end

  describe "Rank协议测试" do
    test "处理获取排行榜信息" do
      message = %{
        main_id: 40,
        sub_id: 0,
        data: %{"type" => 1}
      }

      {:reply, response, _state} = ProtocolHandlers.handle_rank_data(message, @test_state)

      assert response["mainId"] == 40
      assert response["subId"] == 1
      assert response["data"]["status"] == 0
      assert is_list(response["data"]["rank_list"])
    end

    test "处理获取自己的排行榜数据" do
      message = %{
        main_id: 40,
        sub_id: 4,
        data: %{"type" => 1}
      }

      {:reply, response, _state} = ProtocolHandlers.handle_self_rank_data(message, @test_state)

      assert response["mainId"] == 40
      assert response["subId"] == 5
      assert response["data"]["status"] == 0
      assert is_integer(response["data"]["my_rank"])
    end
  end

  describe "QMAgent协议测试" do
    test "处理获得推广佣金信息" do
      message = %{
        main_id: 41,
        sub_id: 0,
        data: %{}
      }

      {:reply, response, _state} =
        ExtendedHandlers.handle_agent_promotion_data(message, @test_state)

      assert response["mainId"] == 41
      assert response["subId"] == 1
      assert response["data"]["status"] == 0
      assert response["data"]["promotion_data"] != nil
    end

    test "处理领取佣金" do
      message = %{
        main_id: 41,
        sub_id: 2,
        data: %{"amount" => 1000}
      }

      {:reply, response, _state} = ExtendedHandlers.handle_agent_get_money(message, @test_state)

      assert response["mainId"] == 41
      assert response["subId"] == 3
      assert response["data"]["status"] == 0
    end
  end

  describe "Task协议测试" do
    test "处理更新任务列表信息" do
      message = %{
        main_id: 42,
        sub_id: 0,
        data: %{}
      }

      {:reply, response, _state} = ExtendedHandlers.handle_update_task_list(message, @test_state)

      assert response["mainId"] == 42
      assert response["subId"] == 1
      assert response["data"]["status"] == 0
      assert is_list(response["data"]["task_list"])
    end

    test "处理领取任务奖励" do
      message = %{
        main_id: 42,
        sub_id: 2,
        data: %{"task_id" => 1}
      }

      {:reply, response, _state} = ExtendedHandlers.handle_get_task_reward(message, @test_state)

      assert response["mainId"] == 42
      assert response["subId"] == 3
      assert response["data"]["status"] == 0
    end
  end

  describe "HallActivity协议测试" do
    test "处理请求登录活动信息" do
      message = %{
        main_id: 101,
        sub_id: 0,
        data: %{}
      }

      {:reply, response, _state} = ExtendedHandlers.handle_login_cash_info(message, @test_state)

      assert response["mainId"] == 101
      assert response["subId"] == 1
      assert response["data"]["status"] == 0
      assert response["data"]["activity_info"] != nil
    end

    test "处理领取登录活动奖励" do
      message = %{
        main_id: 101,
        sub_id: 2,
        data: %{"fetchtype" => 0, "ip" => "127.0.0.1"}
      }

      {:reply, response, _state} =
        ExtendedHandlers.handle_fetch_login_cash_award(message, @test_state)

      assert response["mainId"] == 101
      assert response["subId"] == 3
      assert response["data"]["status"] == 0
    end
  end
end
