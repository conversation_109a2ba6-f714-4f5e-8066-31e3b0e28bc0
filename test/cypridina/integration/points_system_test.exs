defmodule Cypridina.Integration.PointsSystemTest do
  use Cy<PERSON><PERSON>ina.DataCase, async: true

  alias <PERSON><PERSON><PERSON><PERSON>.Accounts.User
  alias <PERSON><PERSON><PERSON>ina.Ledger.Account

  describe "end-to-end points system integration" do
    test "complete user lifecycle with points" do
      # 1. Create user - should auto-create account and asset
      {:ok, user} = User.create_guest_user()

      # Verify all components are created
      user_full = User.read!(user.id, load: [:point_account])
      assert user_full.point_account != nil
      assert Cypridina.Accounts.get_user_points(user.id) == 0

      # 2. Add points using account system (simulating game winnings)
      %{points: _new_balance} =
        Cypridina.Accounts.add_points(user.id, 1000,
          transaction_type: :game_win,
          description: "Game winnings"
        )

      # 3. Verify points from account system
      current_points = Cypridina.Accounts.get_user_points(user.id)
      assert current_points == 1000

      # 4. Verify account exists for potential transfers
      account = Account.by_user_id!(user.id)
      assert account.account_type == :user
      assert account.currency == :XAA
      assert account.user_id == user.id
    end

    test "multiple users with separate accounts" do
      # Create multiple users
      {:ok, user1} = User.create_guest_user()

      {:ok, user2} =
        User.register_with_username(%{
          username: "testuser_#{:rand.uniform(10000)}",
          password: "password123",
          password_confirmation: "password123"
        })

      # Each should have their own account
      account1 = Account.by_user_id!(user1.id)
      account2 = Account.by_user_id!(user2.id)

      assert account1.id != account2.id
      assert account1.user_id == user1.id
      assert account2.user_id == user2.id

      # Verify separate point balances
      assert Cypridina.Accounts.get_user_points(user1.id) == 0
      assert Cypridina.Accounts.get_user_points(user2.id) == 0
    end

    test "user deletion cleanup" do
      # Create user with all components
      {:ok, user} = User.create_guest_user()
      user_id = user.id

      # Get related resource IDs
      account = Account.by_user_id!(user_id)
      account_id = account.id

      # Delete user (this should handle cleanup based on relationships)
      # Note: Actual deletion behavior depends on your cascade settings
      # This test documents expected behavior

      # For now, just verify the resources exist
      assert Account.read!(account_id) != nil

      # If you implement cascade deletion, you would test:
      # User.destroy!(user)
      # assert_raise Ash.Error.Query.NotFound, fn -> Account.read!(account_id) end
    end
  end

  describe "points system consistency" do
    test "account system points are consistent" do
      {:ok, user} = User.create_guest_user()

      # Test with various point values
      test_values = [100, 1000, 50000, 999_999]

      Enum.each(test_values, fn points ->
        # Add points using account system
        %{points: _new_balance} =
          Cypridina.Accounts.add_points(user.id, points,
            transaction_type: :system_adjust,
            description: "Test points"
          )

        # Verify points are correct
        current_points = Cypridina.Accounts.get_user_points(user.id)
        assert current_points >= points
      end)
    end

    test "account is properly linked to user" do
      {:ok, user} = User.create_guest_user()

      # Load all relationships
      user_full = User.read!(user.id, load: [:point_account])

      # Verify forward relationships
      assert user_full.point_account.user_id == user.id

      # Verify reverse relationships
      account_with_user = Account.read!(user_full.point_account.id, load: [:user])
      assert account_with_user.user.id == user.id
    end
  end

  describe "error handling" do
    test "invalid user creation fails gracefully" do
      # Test with invalid username
      assert {:error, _} =
               User.register_with_username(%{
                 # too short
                 username: "ab",
                 password: "password123",
                 password_confirmation: "password123"
               })

      # Test with mismatched passwords
      assert {:error, _} =
               User.register_with_username(%{
                 username: "validuser",
                 password: "password123",
                 password_confirmation: "different"
               })
    end

    test "duplicate username prevention" do
      username = "uniqueuser_#{:rand.uniform(10000)}"

      # Create first user
      assert {:ok, _user1} =
               User.register_with_username(%{
                 username: username,
                 password: "password123",
                 password_confirmation: "password123"
               })

      # Attempt to create second user with same username should fail
      assert {:error, _} =
               User.register_with_username(%{
                 username: username,
                 password: "password456",
                 password_confirmation: "password456"
               })
    end
  end
end
