defmodule Cypridina.LedgerTest do
  use Cypridina.DataCase

  alias <PERSON>pridina.Ledger

  describe "账户管理" do
    test "创建用户账户" do
      user_id = "test_user_123"

      assert {:ok, account} = Ledger.create_user_account(user_id)
      assert account.account_type == :user
      assert account.user_id == user_id
      assert account.identifier == "user:#{user_id}"
      assert account.currency == "POINTS"
      assert account.is_active == true
    end

    test "获取用户账户（自动创建）" do
      user_id = "test_user_456"

      assert {:ok, account} = Ledger.get_user_account(user_id)
      assert account.account_type == :user
      assert account.user_id == user_id
    end

    test "创建系统账户" do
      assert {:ok, account} = Ledger.create_system_account(:rewards)
      assert account.account_type == :system
      assert account.system_account_type == :rewards
      assert account.identifier == "system:rewards"
    end
  end

  describe "积分操作" do
    setup do
      user_id = "test_user_789"
      {:ok, user_account} = Ledger.create_user_account(user_id)
      {:ok, system_account} = Ledger.create_system_account(:rewards)

      %{user_id: user_id, user_account: user_account, system_account: system_account}
    end

    test "用户充值积分", %{user_id: user_id} do
      amount = 1000

      assert {:ok, transfer} =
               Ledger.add_user_points(user_id, amount,
                 description: "测试充值",
                 transaction_type: :manual_add
               )

      assert transfer.transaction_type == :manual_add
      assert transfer.amount == Decimal.new(amount)
      assert transfer.description == "测试充值"

      # 检查余额
      assert {:ok, balance} = Ledger.get_user_balance(user_id)
      assert balance == Decimal.new(amount)
    end

    test "用户消费积分", %{user_id: user_id} do
      # 先充值
      assert {:ok, _} = Ledger.add_user_points(user_id, 1000)

      # 再消费
      amount = 300

      assert {:ok, transfer} =
               Ledger.subtract_user_points(user_id, amount,
                 description: "测试消费",
                 transaction_type: :manual_subtract
               )

      assert transfer.transaction_type == :manual_subtract
      assert transfer.amount == Decimal.new(amount)

      # 检查余额
      assert {:ok, balance} = Ledger.get_user_balance(user_id)
      assert balance == Decimal.new(700)
    end

    test "用户间转账" do
      from_user_id = "from_user_123"
      to_user_id = "to_user_456"

      # 创建账户并给from_user充值
      {:ok, _} = Ledger.create_user_account(from_user_id)
      {:ok, _} = Ledger.create_user_account(to_user_id)
      {:ok, _} = Ledger.add_user_points(from_user_id, 1000)

      # 转账
      amount = 200

      assert {:ok, transfer} =
               Ledger.transfer_user_points(from_user_id, to_user_id, amount, description: "测试转账")

      assert transfer.transaction_type == :transfer
      assert transfer.amount == Decimal.new(amount)

      # 检查余额
      assert {:ok, from_balance} = Ledger.get_user_balance(from_user_id)
      assert {:ok, to_balance} = Ledger.get_user_balance(to_user_id)

      assert from_balance == Decimal.new(800)
      assert to_balance == Decimal.new(200)
    end

    test "游戏相关操作", %{user_id: user_id} do
      # 先充值
      {:ok, _} = Ledger.add_user_points(user_id, 1000)

      # 游戏投注
      bet_amount = 100

      assert {:ok, bet_transfer} =
               Ledger.game_bet(user_id, bet_amount,
                 game_type: :longhu,
                 room_id: "room_123",
                 description: "龙虎斗投注"
               )

      assert bet_transfer.transaction_type == :game_bet
      assert bet_transfer.metadata["game_type"] == :longhu
      assert bet_transfer.metadata["room_id"] == "room_123"

      # 游戏获奖
      win_amount = 200

      assert {:ok, win_transfer} =
               Ledger.game_win(user_id, win_amount,
                 game_type: :longhu,
                 room_id: "room_123",
                 description: "龙虎斗获奖"
               )

      assert win_transfer.transaction_type == :game_win

      # 检查最终余额：1000 - 100 + 200 = 1100
      assert {:ok, balance} = Ledger.get_user_balance(user_id)
      assert balance == Decimal.new(1100)
    end
  end

  describe "历史查询" do
    setup do
      user_id = "history_user_123"
      {:ok, _} = Ledger.create_user_account(user_id)
      {:ok, _} = Ledger.add_user_points(user_id, 1000)
      {:ok, _} = Ledger.subtract_user_points(user_id, 200)
      {:ok, _} = Ledger.game_bet(user_id, 100)

      %{user_id: user_id}
    end

    test "获取用户转账历史", %{user_id: user_id} do
      assert {:ok, transfers} = Ledger.get_user_transfer_history(user_id)
      assert length(transfers) == 3

      # 按时间倒序排列
      [latest | _] = transfers
      assert latest.transaction_type == :game_bet
    end

    test "获取用户余额历史", %{user_id: user_id} do
      assert {:ok, balances} = Ledger.get_user_balance_history(user_id)
      assert length(balances) >= 3
    end
  end
end
