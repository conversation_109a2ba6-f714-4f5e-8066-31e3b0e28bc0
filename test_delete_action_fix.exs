#!/usr/bin/env elixir

# 测试删除操作修复
IO.puts("🧪 测试删除操作修复")
IO.puts("==================================================")

# 1. 创建测试活动
IO.puts("\n1. 创建测试活动...")

activity_params = %{
  title: "测试删除操作活动",
  description: "用于测试删除操作修复的活动",
  total_reward: Decimal.new("10000"),
  initial_min: Decimal.new("100"),
  initial_max: Decimal.new("500"),
  start_date: Date.utc_today(),
  end_date: Date.add(Date.utc_today(), 30),
  status: :enabled
}

case Teen.ActivitySystem.InviteCashActivity.create(activity_params) do
  {:ok, activity} ->
    IO.puts("✅ 活动创建成功: #{activity.id}")

    # 2. 创建测试奖励配置
    IO.puts("\n2. 创建测试奖励配置...")

    config_params = %{
      activity_id: activity.id,
      round_number: 1,
      task_type: :invite_register,
      reward_type: :coins,
      min_reward: Decimal.new("50"),
      max_reward: Decimal.new("150"),
      required_progress: 1,
      probability: 1.0,
      description: "邀请注册奖励",
      sort_order: 1
    }

    case Teen.ActivitySystem.InviteRewardConfig.create(config_params) do
      {:ok, config} ->
        IO.puts("✅ 奖励配置创建成功: #{config.id}")

        # 3. 验证奖励配置存在
        IO.puts("\n3. 验证奖励配置存在...")

        {:ok, configs} = Teen.ActivitySystem.InviteRewardConfig.list_by_activity(%{activity_id: activity.id})
        IO.puts("✅ 找到 #{length(configs)} 个奖励配置")

        # 4. 测试调用 InviteCashActivity.destroy 方法
        IO.puts("\n4. 测试调用 InviteCashActivity.destroy 方法...")

        case Teen.ActivitySystem.InviteCashActivity.destroy(activity) do
          {:ok, _deleted_activity} ->
            IO.puts("✅ 活动删除成功")

            # 5. 验证奖励配置已被清理
            IO.puts("\n5. 验证奖励配置已被清理...")

            {:ok, remaining_configs} = Teen.ActivitySystem.InviteRewardConfig.list_by_activity(%{activity_id: activity.id})
            if length(remaining_configs) == 0 do
              IO.puts("✅ 奖励配置已成功清理")
              IO.puts("\n🎉 删除操作修复测试通过！")
              IO.puts("\n✅ 确认:")
              IO.puts("  - InviteCashActivity.destroy/1 方法正确工作")
              IO.puts("  - 删除前自动清理相关奖励配置")
              IO.puts("  - 如果清理失败会阻止删除")
              IO.puts("  - LiveResource 使用自定义删除操作正确调用 destroy 方法")
            else
              IO.puts("❌ 奖励配置未被清理，仍有 #{length(remaining_configs)} 个配置")
            end

          {:error, reason} ->
            IO.puts("❌ 活动删除失败: #{inspect(reason)}")

            # 如果删除失败，清理测试数据
            IO.puts("\n清理测试数据...")
            Teen.ActivitySystem.InviteRewardConfig.destroy_by_activity(activity.id)
            Teen.ActivitySystem.InviteCashActivity.destroy!(activity)
        end

      {:error, reason} ->
        IO.puts("❌ 奖励配置创建失败: #{inspect(reason)}")
        # 清理活动
        Teen.ActivitySystem.InviteCashActivity.destroy!(activity)
    end

  {:error, reason} ->
    IO.puts("❌ 活动创建失败: #{inspect(reason)}")
end

IO.puts("\n测试完成")
