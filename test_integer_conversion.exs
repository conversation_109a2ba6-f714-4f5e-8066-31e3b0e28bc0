# 测试 integer 类型转换
Application.ensure_all_started(:cypridina)

IO.puts("🧪 测试邀请奖励系统 integer 类型转换...")

alias Teen.ActivitySystem.InviteCashActivity
alias Teen.ActivitySystem.InviteRewardConfig

# 测试创建活动
IO.puts("\n1️⃣ 测试创建邀请现金活动...")

activity_params = %{
  title: "Integer测试活动",
  description: "测试integer类型的活动",
  total_reward: 10000,  # integer 类型
  initial_min: 100,     # integer 类型
  initial_max: 500,     # integer 类型
  start_date: Date.utc_today(),
  end_date: Date.add(Date.utc_today(), 30),
  status: :enabled
}

case InviteCashActivity |> Ash.Changeset.for_create(:create, activity_params) |> Ash.create() do
  {:ok, activity} ->
    IO.puts("✅ 活动创建成功: #{activity.title}")
    IO.puts("   total_reward: #{activity.total_reward} (类型: #{inspect(activity.total_reward.__struct__)})")
    IO.puts("   initial_min: #{activity.initial_min} (类型: #{inspect(activity.initial_min.__struct__)})")
    IO.puts("   initial_max: #{activity.initial_max} (类型: #{inspect(activity.initial_max.__struct__)})")

    # 测试创建奖励配置
    IO.puts("\n2️⃣ 测试创建邀请奖励配置...")

    config_params = %{
      activity_id: activity.id,
      round_number: 1,
      task_type: :invite_register,
      reward_type: :coins,
      reward_amount: 200,    # integer 类型
      min_reward: 100,       # integer 类型
      max_reward: 300,       # integer 类型
      required_progress: 1,
      probability: Decimal.new("1.0"),  # decimal 类型（概率）
      description: "第一轮奖励",
      sort_order: 1
    }

    case InviteRewardConfig |> Ash.Changeset.for_create(:create, config_params) |> Ash.create() do
      {:ok, config} ->
        IO.puts("✅ 奖励配置创建成功")
        IO.puts("   reward_amount: #{config.reward_amount} (类型: #{inspect(config.reward_amount.__struct__)})")
        IO.puts("   min_reward: #{config.min_reward} (类型: #{inspect(config.min_reward.__struct__)})")
        IO.puts("   max_reward: #{config.max_reward} (类型: #{inspect(config.max_reward.__struct__)})")
        IO.puts("   probability: #{config.probability} (类型: #{inspect(config.probability.__struct__)})")

        # 清理测试数据
        IO.puts("\n3️⃣ 清理测试数据...")
        config |> Ash.Changeset.for_destroy(:destroy) |> Ash.destroy!()
        activity |> Ash.Changeset.for_destroy(:destroy) |> Ash.destroy!()
        IO.puts("✅ 测试数据清理完成")

      {:error, error} ->
        IO.puts("❌ 奖励配置创建失败: #{inspect(error)}")
        activity |> Ash.Changeset.for_destroy(:destroy) |> Ash.destroy!()
    end

  {:error, error} ->
    IO.puts("❌ 活动创建失败: #{inspect(error)}")
end

IO.puts("\n🎉 测试完成！")
