# 充值转盘数据库迁移指南

## 📋 当前状态

已完成的工作：
- ✅ 资源文件 (`recharge_wheel.ex`) 已定义所有字段
- ✅ LiveView 文件 (`recharge_wheel_live.ex`) 已配置所有字段
- ✅ 迁移文件已创建 (`20250709150000_add_enhanced_fields_to_recharge_wheel.exs`)
- ✅ 计算字段和业务逻辑已实现

## 🎯 需要执行的数据库迁移

### 方法一：使用 Mix 命令（推荐）

```bash
# 设置数据库环境变量
export DATABASE_URL="ecto://postgres:postgres@localhost/cypridina_dev"

# 运行迁移
mix ecto.migrate
```

### 方法二：手动执行 SQL（如果 Mix 命令失败）

连接到 PostgreSQL 数据库并执行以下 SQL：

```sql
-- 添加新字段到 recharge_wheels 表
ALTER TABLE recharge_wheels 
ADD COLUMN title TEXT NOT NULL DEFAULT '充值转盘活动',
ADD COLUMN description TEXT,
ADD COLUMN jackpot_pool DECIMAL NOT NULL DEFAULT 0,
ADD COLUMN start_date DATE,
ADD COLUMN end_date DATE;

-- 为现有记录设置有意义的标题
UPDATE recharge_wheels 
SET title = '充值转盘活动 - ' || cumulative_recharge || '分'
WHERE title = '充值转盘活动';

-- 创建唯一索引确保标题唯一性
CREATE UNIQUE INDEX recharge_wheels_unique_title_index 
ON recharge_wheels (title);
```

### 方法三：使用 Docker PostgreSQL（如果本地 PostgreSQL 有问题）

```bash
# 启动 PostgreSQL 容器
docker run --name postgres-cypridina \
  -e POSTGRES_PASSWORD=postgres \
  -e POSTGRES_DB=cypridina_dev \
  -p 5432:5432 -d postgres:13

# 等待容器启动
sleep 10

# 设置环境变量并运行迁移
export DATABASE_URL="ecto://postgres:postgres@localhost/cypridina_dev"
mix ecto.migrate
```

## 🔍 验证迁移结果

迁移完成后，验证表结构：

```sql
-- 查看表结构
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'recharge_wheels' 
ORDER BY ordinal_position;
```

期望的字段列表：
- `id` (uuid, not null)
- `cumulative_recharge` (numeric, not null)
- `wheel_spins` (bigint, not null, default: 1)
- `status` (text, not null, default: 'enabled')
- `inserted_at` (timestamp, not null)
- `updated_at` (timestamp, not null)
- `title` (text, not null) ← 新增
- `description` (text) ← 新增
- `jackpot_pool` (numeric, not null, default: 0) ← 新增
- `start_date` (date) ← 新增
- `end_date` (date) ← 新增

## 🚀 迁移后的操作

1. **重启应用服务器**
   ```bash
   # 停止当前服务器 (Ctrl+C)
   # 重新启动
   mix phx.server
   ```

2. **访问管理界面**
   - 打开浏览器访问：`http://localhost:4002/admin/recharge-wheels`
   - 验证所有字段都正确显示

3. **创建测试数据**
   ```elixir
   # 在 IEx 中测试创建转盘
   iex -S mix
   
   Teen.ActivitySystem.RechargeWheel.create(%{
     title: "测试转盘",
     description: "这是一个测试转盘活动",
     cumulative_recharge: Decimal.new("100000"),
     wheel_spins: 3,
     jackpot_pool: Decimal.new("500000"),
     start_date: ~D[2024-01-01],
     end_date: ~D[2024-12-31]
   })
   ```

## 🎯 功能特性

迁移完成后，充值转盘系统将具备：

### 基础功能
- ✅ 转盘活动标题和描述管理
- ✅ 累计充值金额要求设置
- ✅ 转盘次数配置
- ✅ 奖金池金额管理
- ✅ 活动时间范围控制
- ✅ 活动状态管理

### 计算字段
- ✅ `is_active`: 自动计算活动是否当前有效
- ✅ `activity_period`: 友好显示活动期间
- ✅ `jackpot_display`: 格式化显示奖金池

### 业务逻辑
- ✅ 用户参与资格检查
- ✅ 奖金池增减管理
- ✅ 转盘次数计算
- ✅ 活动时间验证

### 管理界面
- ✅ 完整的 CRUD 操作
- ✅ 自定义字段渲染
- ✅ 数据验证和约束
- ✅ 友好的用户界面

## ⚠️ 注意事项

1. **数据备份**：在执行迁移前建议备份数据库
2. **唯一性约束**：标题字段有唯一性约束，确保不重复
3. **默认值**：新字段会为现有记录设置合理的默认值
4. **索引性能**：新增的唯一索引会提高查询性能

## 🆘 故障排除

如果遇到问题：

1. **迁移失败**：检查数据库连接和权限
2. **字段冲突**：确保表中没有同名字段
3. **约束错误**：检查现有数据是否符合新约束
4. **服务启动失败**：检查资源文件语法和字段定义

完成迁移后，充值转盘系统将具备完整的企业级功能！
