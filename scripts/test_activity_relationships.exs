try do
  # 测试活动与奖励配置关联关系的脚本
  alias Teen.ActivitySystem.{InviteCashActivity, InviteRewardConfig}

  IO.puts("开始测试活动与奖励配置的关联关系...")

  # 1. 创建带多个奖励配置的活动
  activity_params = %{
    title: "关联测试活动",
    description: "测试一对多关联关系",
    total_reward: Decimal.new("20000"),
    initial_min: Decimal.new("200"),
    initial_max: Decimal.new("800"),
    start_date: ~D[2025-01-01],
    end_date: ~D[2025-12-31]
  }

  reward_configs = [
    %{
      "round_number" => 1,
      "task_type" => "invite_register",
      "reward_type" => "coins",
      "min_reward" => Decimal.new("50"),
      "max_reward" => Decimal.new("100"),
      "reward_amount" => Decimal.new("75"),
      "required_progress" => 1,
      "probability" => Decimal.new("1.0"),
      "description" => "邀请注册奖励",
      "sort_order" => 1
    },
    %{
      "round_number" => 2,
      "task_type" => "invite_recharge",
      "reward_type" => "cash",
      "min_reward" => Decimal.new("100"),
      "max_reward" => Decimal.new("200"),
      "reward_amount" => Decimal.new("150"),
      "required_progress" => 1,
      "probability" => Decimal.new("0.8"),
      "description" => "邀请充值奖励",
      "sort_order" => 2
    },
    %{
      "round_number" => 3,
      "task_type" => "invite_play",
      "reward_type" => "coins",
      "min_reward" => Decimal.new("200"),
      "max_reward" => Decimal.new("300"),
      "reward_amount" => Decimal.new("250"),
      "required_progress" => 5,
      "probability" => Decimal.new("0.6"),
      "description" => "邀请游戏奖励",
      "sort_order" => 3
    }
  ]

  IO.puts("创建活动和奖励配置...")

  # 创建活动
  case Ash.create(InviteCashActivity, Map.merge(activity_params, %{reward_configs: reward_configs}), action: :create_with_configs) do
    {:ok, activity} ->
      IO.puts("✅ 活动创建成功!")
      IO.puts("活动ID: #{activity.id}")
      IO.puts("活动标题: #{activity.title}")

      # 2. 测试通过活动获取关联的奖励配置
      IO.puts("\n--- 测试关联查询 ---")

      case Ash.get(InviteCashActivity, activity.id, action: :get_with_configs) do
        {:ok, activity_with_configs} ->
          IO.puts("✅ 成功获取活动及其关联的奖励配置")
          IO.puts("关联的奖励配置数量: #{length(activity_with_configs.invite_reward_configs)}")

          Enum.each(activity_with_configs.invite_reward_configs, fn config ->
            IO.puts("  - 轮次 #{config.round_number}: #{config.task_type} -> #{config.reward_type} (#{config.reward_amount})")
          end)

        {:error, error} ->
          IO.puts("❌ 获取关联配置失败: #{inspect(error)}")
      end

      # 3. 测试手动计算统计信息
      IO.puts("\n--- 测试统计信息 ---")

      case Ash.get(InviteCashActivity, activity.id, action: :get_with_configs) do
        {:ok, activity_with_configs} ->
          configs = activity_with_configs.invite_reward_configs
          total_count = length(configs)
          max_round = if total_count > 0, do: Enum.max_by(configs, & &1.round_number).round_number, else: 0
          total_amount = configs
                        |> Enum.map(& &1.reward_amount || Decimal.new("0"))
                        |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)

          IO.puts("✅ 统计信息计算成功")
          IO.puts("配置总数: #{total_count}")
          IO.puts("最大轮次: #{max_round}")
          IO.puts("总奖励金额: #{total_amount}")

        {:error, error} ->
          IO.puts("❌ 获取活动配置失败: #{inspect(error)}")
      end

      # 4. 测试通过奖励配置获取关联的活动
      IO.puts("\n--- 测试反向关联查询 ---")

      case Ash.read(InviteRewardConfig) do
        {:ok, configs} ->
          activity_configs = Enum.filter(configs, &(&1.activity_id == activity.id))

          if length(activity_configs) > 0 do
            first_config = List.first(activity_configs)

            case Ash.load(first_config, :activity) do
              {:ok, config_with_activity} ->
                IO.puts("✅ 反向关联查询成功")
                IO.puts("通过奖励配置获取到的活动标题: #{config_with_activity.activity.title}")

              {:error, error} ->
                IO.puts("❌ 反向关联查询失败: #{inspect(error)}")
            end
          end

        {:error, error} ->
          IO.puts("❌ 读取奖励配置失败: #{inspect(error)}")
      end

      # 5. 测试列表查询（带关联）
      IO.puts("\n--- 测试列表查询（带关联） ---")

      case Ash.read(InviteCashActivity, action: :list_with_configs) do
        {:ok, activities} ->
          IO.puts("✅ 列表查询成功")
          IO.puts("活动总数: #{length(activities)}")

          Enum.each(activities, fn act ->
            config_count = length(act.invite_reward_configs || [])
            IO.puts("  - #{act.title}: #{config_count} 个奖励配置")
          end)

        {:error, error} ->
          IO.puts("❌ 列表查询失败: #{inspect(error)}")
      end

    {:error, error} ->
      IO.puts("❌ 活动创建失败: #{inspect(error)}")
  end

  IO.puts("\n测试完成!")
rescue
  e ->
    IO.puts("❌ 测试过程中发生错误: #{inspect(e)}")
end
