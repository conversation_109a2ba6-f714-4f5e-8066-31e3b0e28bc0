try do
  # 测试多表创建功能的脚本
  alias Teen.ActivitySystem.{InviteCashActivity, InviteRewardConfig}

  IO.puts("开始测试多表创建功能...")

  # 准备测试数据
  activity_params = %{
    title: "测试邀请活动",
    description: "这是一个测试邀请活动",
    total_reward: Decimal.new("10000"),
    initial_min: Decimal.new("100"),
    initial_max: Decimal.new("500"),
    start_date: ~D[2025-01-01],
    end_date: ~D[2025-12-31]
  }

  reward_configs = [
    %{
      "round_number" => 1,
      "task_type" => "invite_register",
      "reward_type" => "coins",
      "min_reward" => Decimal.new("50"),
      "max_reward" => Decimal.new("100"),
      "reward_amount" => Decimal.new("75"),
      "required_progress" => 1,
      "probability" => Decimal.new("1.0"),
      "description" => "邀请注册奖励",
      "sort_order" => 1
    }
  ]

  IO.puts("准备创建活动和奖励配置...")

  # 执行创建操作
  case Ash.create(InviteCashActivity, Map.merge(activity_params, %{reward_configs: reward_configs}), action: :create_with_configs) do
    {:ok, activity} ->
      IO.puts("✅ 活动创建成功!")
      IO.puts("活动ID: #{activity.id}")
      IO.puts("活动标题: #{activity.title}")
      IO.puts("活动状态: #{activity.status}")

      # 验证奖励配置是否正确创建
      case Ash.read(InviteRewardConfig) do
        {:ok, config_list} ->
          activity_configs = Enum.filter(config_list, &(&1.activity_id == activity.id))
          IO.puts("✅ 奖励配置创建成功!")
          IO.puts("配置数量: #{length(activity_configs)}")

          Enum.each(activity_configs, fn config ->
            IO.puts("  - 轮次 #{config.round_number}: #{config.task_type} -> #{config.reward_type} (#{config.reward_amount})")
          end)

        {:error, error} ->
          IO.puts("❌ 读取奖励配置失败: #{inspect(error)}")
      end

    {:error, error} ->
      IO.puts("❌ 活动创建失败: #{inspect(error)}")
  end

  IO.puts("测试完成!")
rescue
  e ->
    IO.puts("❌ 测试过程中发生错误: #{inspect(e)}")
end
