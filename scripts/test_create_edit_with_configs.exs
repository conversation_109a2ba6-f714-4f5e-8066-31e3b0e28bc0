try do
  # 测试创建和编辑功能的脚本
  alias Teen.ActivitySystem.{InviteCashActivity, InviteRewardConfig}

  IO.puts("开始测试创建和编辑功能...")

  # 1. 测试普通创建（不带奖励配置）
  IO.puts("\n=== 测试1: 普通创建（不带奖励配置） ===")

  simple_activity_params = %{
    title: "简单活动",
    description: "不带奖励配置的活动",
    total_reward: Decimal.new("5000"),
    initial_min: Decimal.new("100"),
    initial_max: Decimal.new("300"),
    start_date: ~D[2025-01-01],
    end_date: ~D[2025-06-30]
  }

  case Ash.create(InviteCashActivity, simple_activity_params) do
    {:ok, simple_activity} ->
      IO.puts("✅ 简单活动创建成功!")
      IO.puts("活动ID: #{simple_activity.id}")
      IO.puts("活动标题: #{simple_activity.title}")

    {:error, error} ->
      IO.puts("❌ 简单活动创建失败: #{inspect(error)}")
  end

  # 2. 测试带奖励配置的创建
  IO.puts("\n=== 测试2: 带奖励配置的创建 ===")

  activity_with_configs_params = %{
    title: "完整配置活动",
    description: "带有多个奖励配置的活动",
    total_reward: Decimal.new("15000"),
    initial_min: Decimal.new("200"),
    initial_max: Decimal.new("600"),
    start_date: ~D[2025-02-01],
    end_date: ~D[2025-08-31],
    reward_configs: [
      %{
        "round_number" => 1,
        "task_type" => "invite_register",
        "reward_type" => "coins",
        "min_reward" => Decimal.new("50"),
        "max_reward" => Decimal.new("100"),
        "reward_amount" => Decimal.new("75"),
        "required_progress" => 1,
        "probability" => Decimal.new("1.0"),
        "description" => "邀请注册奖励",
        "sort_order" => 1
      },
      %{
        "round_number" => 2,
        "task_type" => "invite_recharge",
        "reward_type" => "cash",
        "min_reward" => Decimal.new("100"),
        "max_reward" => Decimal.new("200"),
        "reward_amount" => Decimal.new("150"),
        "required_progress" => 1,
        "probability" => Decimal.new("0.8"),
        "description" => "邀请充值奖励",
        "sort_order" => 2
      }
    ]
  }

  case Ash.create(InviteCashActivity, activity_with_configs_params, action: :create_with_configs) do
    {:ok, activity_with_configs} ->
      IO.puts("✅ 带配置活动创建成功!")
      IO.puts("活动ID: #{activity_with_configs.id}")
      IO.puts("活动标题: #{activity_with_configs.title}")

      # 验证奖励配置是否创建成功
      case Ash.get(InviteCashActivity, activity_with_configs.id, action: :get_with_configs) do
        {:ok, loaded_activity} ->
          IO.puts("✅ 奖励配置验证成功")
          IO.puts("配置数量: #{length(loaded_activity.invite_reward_configs)}")

          Enum.each(loaded_activity.invite_reward_configs, fn config ->
            IO.puts("  - 轮次 #{config.round_number}: #{config.task_type} -> #{config.reward_type} (#{config.reward_amount})")
          end)

        {:error, error} ->
          IO.puts("❌ 奖励配置验证失败: #{inspect(error)}")
      end

      # 3. 测试合并更新（添加新配置）
      IO.puts("\n=== 测试3: 合并更新（添加新配置） ===")

      additional_configs = [
        %{
          "round_number" => 3,
          "task_type" => "invite_play",
          "reward_type" => "coins",
          "min_reward" => Decimal.new("200"),
          "max_reward" => Decimal.new("400"),
          "reward_amount" => Decimal.new("300"),
          "required_progress" => 3,
          "probability" => Decimal.new("0.6"),
          "description" => "邀请游戏奖励",
          "sort_order" => 3
        }
      ]

      update_params = %{
        title: "完整配置活动（已更新）",
        reward_configs: additional_configs,
        replace_configs: false  # 合并模式
      }

      case Ash.update(activity_with_configs, update_params, action: :update_with_configs) do
        {:ok, updated_activity} ->
          IO.puts("✅ 合并更新成功!")
          IO.puts("更新后标题: #{updated_activity.title}")

          # 验证更新后的配置
          case Ash.get(InviteCashActivity, updated_activity.id, action: :get_with_configs) do
            {:ok, loaded_updated_activity} ->
              IO.puts("✅ 更新后配置验证成功")
              IO.puts("配置数量: #{length(loaded_updated_activity.invite_reward_configs)}")

              Enum.each(loaded_updated_activity.invite_reward_configs, fn config ->
                IO.puts("  - 轮次 #{config.round_number}: #{config.task_type} -> #{config.reward_type} (#{config.reward_amount})")
              end)

            {:error, error} ->
              IO.puts("❌ 更新后配置验证失败: #{inspect(error)}")
          end

        {:error, error} ->
          IO.puts("❌ 合并更新失败: #{inspect(error)}")
      end

      # 4. 测试替换更新（替换所有配置）
      IO.puts("\n=== 测试4: 替换更新（替换所有配置） ===")

      replacement_configs = [
        %{
          "round_number" => 1,
          "task_type" => "invite_register",
          "reward_type" => "cash",
          "min_reward" => Decimal.new("100"),
          "max_reward" => Decimal.new("200"),
          "reward_amount" => Decimal.new("150"),
          "required_progress" => 1,
          "probability" => Decimal.new("1.0"),
          "description" => "新的邀请注册奖励",
          "sort_order" => 1
        },
        %{
          "round_number" => 2,
          "task_type" => "invite_retention",
          "reward_type" => "coins",
          "min_reward" => Decimal.new("300"),
          "max_reward" => Decimal.new("500"),
          "reward_amount" => Decimal.new("400"),
          "required_progress" => 7,
          "probability" => Decimal.new("0.9"),
          "description" => "邀请留存奖励",
          "sort_order" => 2
        }
      ]

      replace_params = %{
        description: "完全替换配置的活动",
        reward_configs: replacement_configs,
        replace_configs: true  # 替换模式
      }

      case Ash.update(activity_with_configs, replace_params, action: :update_with_configs) do
        {:ok, replaced_activity} ->
          IO.puts("✅ 替换更新成功!")
          IO.puts("更新后描述: #{replaced_activity.description}")

          # 验证替换后的配置
          case Ash.get(InviteCashActivity, replaced_activity.id, action: :get_with_configs) do
            {:ok, loaded_replaced_activity} ->
              IO.puts("✅ 替换后配置验证成功")
              IO.puts("配置数量: #{length(loaded_replaced_activity.invite_reward_configs)}")

              Enum.each(loaded_replaced_activity.invite_reward_configs, fn config ->
                IO.puts("  - 轮次 #{config.round_number}: #{config.task_type} -> #{config.reward_type} (#{config.reward_amount})")
              end)

            {:error, error} ->
              IO.puts("❌ 替换后配置验证失败: #{inspect(error)}")
          end

        {:error, error} ->
          IO.puts("❌ 替换更新失败: #{inspect(error)}")
      end

    {:error, error} ->
      IO.puts("❌ 带配置活动创建失败: #{inspect(error)}")
  end

  # 5. 测试只更新活动信息（不涉及配置）
  IO.puts("\n=== 测试5: 只更新活动信息 ===")

  case Ash.read(InviteCashActivity, action: :list_with_configs) do
    {:ok, activities} when length(activities) > 0 ->
      test_activity = List.first(activities)

      simple_update_params = %{
        title: "#{test_activity.title}（已修改）",
        total_reward: Decimal.add(test_activity.total_reward, Decimal.new("1000"))
      }

      case Ash.update(test_activity, simple_update_params, action: :update_with_configs) do
        {:ok, updated_simple_activity} ->
          IO.puts("✅ 简单更新成功!")
          IO.puts("更新后标题: #{updated_simple_activity.title}")
          IO.puts("更新后总奖励: #{updated_simple_activity.total_reward}")

        {:error, error} ->
          IO.puts("❌ 简单更新失败: #{inspect(error)}")
      end

    {:ok, []} ->
      IO.puts("⚠️  没有找到可用于测试的活动")

    {:error, error} ->
      IO.puts("❌ 获取活动列表失败: #{inspect(error)}")
  end

  IO.puts("\n测试完成!")
rescue
  e ->
    IO.puts("❌ 测试过程中发生错误: #{inspect(e)}")
end
