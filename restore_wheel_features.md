# 恢复充值转盘完整功能指南

## 迁移完成后需要恢复的功能

### 1. 恢复资源字段约束

在 `lib/teen/resources/activity_system/recharge_wheel.ex` 中：

```elixir
# 将这些字段改回 allow_nil? false
attribute :title, :string do
  allow_nil? false  # 改回 false
  public? true
  description "转盘活动标题"
  constraints max_length: 100
end

attribute :jackpot_pool, :decimal do
  allow_nil? false  # 改回 false
  public? true
  description "奖金池金额（分）"
  constraints min: Decimal.new("0")
  default Decimal.new("0")
end
```

### 2. 恢复计算字段

取消注释 calculations 部分：

```elixir
calculations do
  calculate :is_active, :boolean, expr(
    status == :enabled and 
    (is_nil(start_date) or start_date <= ^Date.utc_today()) and 
    (is_nil(end_date) or end_date >= ^Date.utc_today())
  ) do
    description "是否当前有效"
  end

  calculate :activity_period, :string, expr(
    cond do
      is_nil(start_date) and is_nil(end_date) -> "长期有效"
      is_nil(start_date) -> fragment("'至' || ?", end_date)
      is_nil(end_date) -> fragment("? || '起长期有效'", start_date)
      true -> fragment("? || '至' || ?", start_date, end_date)
    end
  ) do
    description "活动期间"
  end

  calculate :jackpot_display, :string, expr(
    fragment("? || '分'", jackpot_pool)
  ) do
    description "奖金池显示"
  end
end
```

### 3. 恢复唯一性约束

```elixir
identities do
  identity :unique_recharge_amount, [:cumulative_recharge]
  identity :unique_title, [:title]  # 取消注释
end
```

### 4. 恢复 LiveView 字段

在 `lib/teen/live/activity_system/recharge_wheel_live.ex` 中取消注释所有新字段。

### 5. 更新 create 动作验证

```elixir
create :create do
  accept [:title, :description, :cumulative_recharge, :wheel_spins, :jackpot_pool, :start_date, :end_date, :status]

  change fn changeset, _context ->
    changeset
    |> Ash.Changeset.change_attribute(:status, :enabled)
    |> Ash.Changeset.change_attribute(:jackpot_pool, Decimal.new("0"))
  end

  validate present(:title), message: "转盘标题不能为空"  # 恢复验证
  validate present(:cumulative_recharge), message: "累计充值金额不能为空"
end
```

## 验证步骤

1. 运行迁移后重启服务器
2. 访问 `/admin/recharge-wheels` 确认页面正常
3. 尝试创建新的转盘活动
4. 验证所有新字段都正常工作
5. 测试活动期间计算和奖金池管理功能

## 完整功能特性

迁移完成后，充值转盘将具备：

- ✅ 转盘活动标题和描述
- ✅ 活动时间管理（开始/结束日期）
- ✅ 奖金池管理（增加/扣除/余额检查）
- ✅ 用户参与资格检查
- ✅ 转盘次数计算逻辑
- ✅ 活动期间显示格式化
- ✅ 数据验证和约束
- ✅ 管理界面优化
